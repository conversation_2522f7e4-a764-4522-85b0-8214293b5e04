@echo off
REM 设置控制台编码为UTF-8，支持中文和emoji显示
chcp 65001 >nul
setlocal enabledelayedexpansion
REM 设置Python环境变量，确保UTF-8输出
set PYTHONIOENCODING=utf-8

REM JSON 文件上传工具 - Windows 简化版本 (MD5增量上传)
REM 使用方法:
REM   upload_simple.bat          # 正常上传模式（只上传变化的文件）
REM   upload_simple.bat --init   # 初始化MD5记录模式
REM   upload_simple.bat -i       # 初始化MD5记录模式（简写）

echo ====================================================
echo 📦 JSON 文件上传工具 (MD5增量上传)
echo ====================================================
echo 🔄 只上传MD5发生变化的JSON文件
echo.

REM 检查 Python 环境
echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到 Python，请先安装 Python 3.6+
    echo 💡 下载地址: https://www.python.org/downloads/
    echo 💡 安装后请确保 Python 已添加到系统 PATH
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ 找到 Python: %%i
echo.

REM 检查依赖
echo 📦 检查依赖...
python -c "import obs" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未找到 OBS SDK，正在安装...
    pip install esdk-obs-python
    if !errorlevel! neq 0 (
        echo ❌ 依赖安装失败
        echo 💡 提示：请确保网络连接正常，或尝试以管理员身份运行
        pause
        exit /b 1
    )
    echo ✅ 依赖安装成功
) else (
    echo ✅ OBS SDK 已安装
)

echo.

REM 检查命令行参数
set INIT_MODE=false
if "%1"=="--init" set INIT_MODE=true
if "%1"=="-i" set INIT_MODE=true

if "%INIT_MODE%"=="true" (
    echo 🔄 MD5记录初始化模式
    echo.
    echo 📝 初始化MD5记录...
    python upload_json_files_proxy.py --init
) else (
    echo 🚀 正常上传模式（只上传变化的文件）
    echo.
    echo 🔗 运行代理模式...
    echo 📝 检查 Vite 开发服务器是否运行...

    REM 简化的服务器检查
    echo 💡 请确保 Vite 开发服务器正在运行 (npm run dev)
    echo 💡 如果服务器未运行，上传将会失败
    echo.

    python upload_json_files_proxy.py
)

echo.
echo ✅ 脚本执行完成
echo.
echo 按任意键退出...
pause >nul
