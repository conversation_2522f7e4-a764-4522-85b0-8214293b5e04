import { game } from './generated/game_messages';
import { transformBuffer } from './protobuf-until';

/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */
export class ProtobufHandler {
  // 消息类型映射：从protobuf枚举到protobuf消息类型
  static readonly s2cMessageMap = new Map<game.S2CPacketType, any>([
    [game.S2CPacketType.S2C_PLAYER_ENTER, game.PlayerEnter],
    [game.S2CPacketType.S2C_PLAYER_LEAVE, game.PlayerLeave],
    [game.S2CPacketType.S2C_PLAYER_POSITION, game.PlayerPosition],
    [game.S2CPacketType.S2C_PLAYER_ANIMATION, game.PlayerAnimation],
    [game.S2CPacketType.S2C_PLAYER_UPDATE, game.PlayerUpdate],
    [game.S2CPacketType.S2C_PET_POSITION, game.PetPosition],
    [game.S2CPacketType.S2C_PET_ANIMATION, game.PetAnimation],
    [game.S2CPacketType.S2C_CHAT_ENTER, game.ChatEnter],
    [game.S2CPacketType.S2C_CHAT_LEAVE, game.ChatLeave],
    [game.S2CPacketType.S2C_CHAT_MESSAGE, game.ChatMessage],
    [game.S2CPacketType.S2C_CHAT_MESSAGE_DELETE, game.ChatMessageDelete],
    [game.S2CPacketType.S2C_RED_PACKET_UPDATE, game.RedPacketUpdate],
    [game.S2CPacketType.S2C_RED_PACKET_CACHE, game.RedPacketCache],
    [game.S2CPacketType.S2C_UPDATE_TREE, game.UpdateTreeList],
    [game.S2CPacketType.S2C_UPDATE_TREE_REFRESH, game.UpdateTreeRefresh],
    [game.S2CPacketType.S2C_UPDATE_ROCK, game.UpdateRockList],
    [game.S2CPacketType.S2C_UPDATE_ROCK_REFRESH, game.UpdateRockRefresh],
    [game.S2CPacketType.S2C_REWARD_MATERIAL, game.UpdateMaterial],
    [game.S2CPacketType.S2C_REWARD_RANDOM_EVENT, game.UpdateRandomEvent],
    [game.S2CPacketType.S2C_UPDATE_ITEM, game.UpdateItem],
    [game.S2CPacketType.S2C_UPDATE_PICK_UP_POINT, game.UpdatePickUpPoint],
    [game.S2CPacketType.S2C_NOTICE_NEW_DAY, null],
    [game.S2CPacketType.S2C_PLAYER_POSITION_UPDATE, game.PlayerPosition],
    [game.S2CPacketType.S2C_PLAYER_FISHING, game.PlayerFishing],
  ]);
  private static instance: ProtobufHandler;

  public static getInstance(): ProtobufHandler {
    if (!ProtobufHandler.instance) {
      ProtobufHandler.instance = new ProtobufHandler();
    }
    return ProtobufHandler.instance;
  }

  /**
   * 将protobuf二进制数据解码为JSON
   * @param buffer protobuf二进制数据
   * @returns 解码后的JSON数据
   */
  public decodeMessage(buffer: Buffer | ArrayBuffer | Uint8Array): { pid: game.S2CPacketType; data: any } {
    try {

      // 验证输入数据
      const dataLength = buffer instanceof ArrayBuffer ? buffer.byteLength : buffer?.length;
      if (!buffer || dataLength === 0) {
        throw new Error('Empty or null buffer provided');
      }

      // 确保buffer是正确的格式，转换为Uint8Array
      const processedBuffer = transformBuffer(buffer);
      if (!processedBuffer) {
        throw new Error(`Invalid buffer type: ${typeof buffer}, constructor:`);
      }

      // 解码包装消息
      const gameMessage = game.GameMessage.decode(processedBuffer);

      // 获取对应的消息类型
      const pid = gameMessage.pid;

      if (!pid) {
        throw new Error(`Unknown packet type: ${gameMessage.pid}`);
      }

      // 转换为JSON格式
      let jsonData = {};

      const MessageClass = ProtobufHandler.s2cMessageMap.get(pid);
      if (MessageClass) {
        // 解码内部消息
        const message = MessageClass.decode(gameMessage.data);
        jsonData = message.toJSON();
      }

      return {
        pid,
        data: jsonData,
      };
    } catch (error) {
      // console.error('Error decoding protobuf message:', {
      //   error: error.message,
      //   stack: error.stack,
      //   bufferInfo: {
      //     type: typeof buffer,
      //     length: buffer?.length,
      //     isBuffer: Buffer.isBuffer(buffer),
      //     firstBytes: buffer?.length > 0 ? Array.from(buffer.slice(0, 10)) : 'empty'
      //   }
      // });
      throw error;
    }
  }

}

export default ProtobufHandler;
