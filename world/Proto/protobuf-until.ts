/**
 * Protobuf消息处理器
 * 负责在JSON和Protobuf之间进行转换
 */

import md5 from 'md5';

/**
 * 将JSON数据编码为protobuf二进制数据
 * @param buffer 消息类型
 * @returns protobuf二进制数据
 */
export function transformBuffer(buffer: Buffer | ArrayBuffer | Uint8Array): Uint8Array | undefined {
  try {
    let processedBuffer: Uint8Array;
    if (Buffer.isBuffer(buffer)) {
      processedBuffer = new Uint8Array(buffer);
    } else if (buffer instanceof ArrayBuffer) {
      processedBuffer = new Uint8Array(buffer);
    } else if (buffer instanceof Uint8Array) {
      processedBuffer = buffer;
    } else {
      return;
    }
    return processedBuffer;
  } catch (error) {
    console.error('Error encoding protobuf message:', error);
    throw error;
  }
}

export function sign(pid: number, data: any, timestamp: number) {
  return md5(`${pid}${JSON.stringify(data)}${timestamp}`);
}
