/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

var $protobuf = require("protobufjs/minimal");

// Common aliases
var $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
var $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

$root.game = (function() {

    /**
     * Namespace game.
     * @exports game
     * @namespace
     */
    var game = {};

    /**
     * S2CPacketType enum.
     * @name game.S2CPacketType
     * @enum {number}
     * @property {number} S2C_PLAYER_NONE=0 S2C_PLAYER_NONE value
     * @property {number} S2C_PLAYER_ENTER=1 S2C_PLAYER_ENTER value
     * @property {number} S2C_PLAYER_LEAVE=2 S2C_PLAYER_LEAVE value
     * @property {number} S2C_PLAYER_POSITION=10 S2C_PLAYER_POSITION value
     * @property {number} S2C_PLAYER_ANIMATION=11 S2C_PLAYER_ANIMATION value
     * @property {number} S2C_PLAYER_UPDATE=12 S2C_PLAYER_UPDATE value
     * @property {number} S2C_PLAYER_FISHING=13 S2C_PLAYER_FISHING value
     * @property {number} S2C_PET_POSITION=50 S2C_PET_POSITION value
     * @property {number} S2C_PET_ANIMATION=51 S2C_PET_ANIMATION value
     * @property {number} S2C_CHAT_ENTER=100 S2C_CHAT_ENTER value
     * @property {number} S2C_CHAT_LEAVE=101 S2C_CHAT_LEAVE value
     * @property {number} S2C_CHAT_MESSAGE=102 S2C_CHAT_MESSAGE value
     * @property {number} S2C_CHAT_MESSAGE_DELETE=103 S2C_CHAT_MESSAGE_DELETE value
     * @property {number} S2C_RED_PACKET_UPDATE=201 S2C_RED_PACKET_UPDATE value
     * @property {number} S2C_RED_PACKET_REWARD=202 S2C_RED_PACKET_REWARD value
     * @property {number} S2C_RED_PACKET_CACHE=203 S2C_RED_PACKET_CACHE value
     * @property {number} S2C_UPDATE_ITEM=1001 S2C_UPDATE_ITEM value
     * @property {number} S2C_UPDATE_TREE=1002 S2C_UPDATE_TREE value
     * @property {number} S2C_UPDATE_TREE_REFRESH=1003 S2C_UPDATE_TREE_REFRESH value
     * @property {number} S2C_UPDATE_ROCK=1004 S2C_UPDATE_ROCK value
     * @property {number} S2C_UPDATE_ROCK_REFRESH=1005 S2C_UPDATE_ROCK_REFRESH value
     * @property {number} S2C_UPDATE_PICK_UP_POINT=1006 S2C_UPDATE_PICK_UP_POINT value
     * @property {number} S2C_REWARD_MATERIAL=1101 S2C_REWARD_MATERIAL value
     * @property {number} S2C_REWARD_RANDOM_EVENT=1102 S2C_REWARD_RANDOM_EVENT value
     * @property {number} S2C_NOTICE_NEW_DAY=1200 S2C_NOTICE_NEW_DAY value
     * @property {number} S2C_PLAYER_POSITION_UPDATE=2001 S2C_PLAYER_POSITION_UPDATE value
     */
    game.S2CPacketType = (function() {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "S2C_PLAYER_NONE"] = 0;
        values[valuesById[1] = "S2C_PLAYER_ENTER"] = 1;
        values[valuesById[2] = "S2C_PLAYER_LEAVE"] = 2;
        values[valuesById[10] = "S2C_PLAYER_POSITION"] = 10;
        values[valuesById[11] = "S2C_PLAYER_ANIMATION"] = 11;
        values[valuesById[12] = "S2C_PLAYER_UPDATE"] = 12;
        values[valuesById[13] = "S2C_PLAYER_FISHING"] = 13;
        values[valuesById[50] = "S2C_PET_POSITION"] = 50;
        values[valuesById[51] = "S2C_PET_ANIMATION"] = 51;
        values[valuesById[100] = "S2C_CHAT_ENTER"] = 100;
        values[valuesById[101] = "S2C_CHAT_LEAVE"] = 101;
        values[valuesById[102] = "S2C_CHAT_MESSAGE"] = 102;
        values[valuesById[103] = "S2C_CHAT_MESSAGE_DELETE"] = 103;
        values[valuesById[201] = "S2C_RED_PACKET_UPDATE"] = 201;
        values[valuesById[202] = "S2C_RED_PACKET_REWARD"] = 202;
        values[valuesById[203] = "S2C_RED_PACKET_CACHE"] = 203;
        values[valuesById[1001] = "S2C_UPDATE_ITEM"] = 1001;
        values[valuesById[1002] = "S2C_UPDATE_TREE"] = 1002;
        values[valuesById[1003] = "S2C_UPDATE_TREE_REFRESH"] = 1003;
        values[valuesById[1004] = "S2C_UPDATE_ROCK"] = 1004;
        values[valuesById[1005] = "S2C_UPDATE_ROCK_REFRESH"] = 1005;
        values[valuesById[1006] = "S2C_UPDATE_PICK_UP_POINT"] = 1006;
        values[valuesById[1101] = "S2C_REWARD_MATERIAL"] = 1101;
        values[valuesById[1102] = "S2C_REWARD_RANDOM_EVENT"] = 1102;
        values[valuesById[1200] = "S2C_NOTICE_NEW_DAY"] = 1200;
        values[valuesById[2001] = "S2C_PLAYER_POSITION_UPDATE"] = 2001;
        return values;
    })();

    /**
     * C2SPacketType enum.
     * @name game.C2SPacketType
     * @enum {number}
     * @property {number} C2S_PLAYER_NONE=0 C2S_PLAYER_NONE value
     * @property {number} C2S_PLAYER_ENTER=1 C2S_PLAYER_ENTER value
     * @property {number} C2S_PLAYER_LEAVE=2 C2S_PLAYER_LEAVE value
     * @property {number} C2S_PLAYER_POSITION=10 C2S_PLAYER_POSITION value
     * @property {number} C2S_PLAYER_ANIMATION=11 C2S_PLAYER_ANIMATION value
     * @property {number} C2S_PLAYER_UPDATE=12 C2S_PLAYER_UPDATE value
     * @property {number} C2S_PLAYER_FISHING=13 C2S_PLAYER_FISHING value
     * @property {number} C2S_PET_POSITION=50 C2S_PET_POSITION value
     * @property {number} C2S_PET_ANIMATION=51 C2S_PET_ANIMATION value
     * @property {number} C2S_CHAT_ENTER=100 C2S_CHAT_ENTER value
     * @property {number} C2S_CHAT_LEAVE=101 C2S_CHAT_LEAVE value
     * @property {number} C2S_CHAT_MESSAGE=102 C2S_CHAT_MESSAGE value
     * @property {number} C2S_CHAT_MESSAGE_DELETE=103 C2S_CHAT_MESSAGE_DELETE value
     * @property {number} C2S_CUT_TREE=1001 C2S_CUT_TREE value
     * @property {number} C2S_MINING_ROCK=1002 C2S_MINING_ROCK value
     * @property {number} C2S_FISHING_SUCCESS=1003 C2S_FISHING_SUCCESS value
     * @property {number} C2S_PICK_UP_DROP=1004 C2S_PICK_UP_DROP value
     * @property {number} C2S_PLAYER_MAP_UPDATE=2000 C2S_PLAYER_MAP_UPDATE value
     * @property {number} C2S_PLAYER_POSITION_UPDATE=2001 C2S_PLAYER_POSITION_UPDATE value
     * @property {number} C2S_LARK_MESSAGE=10001 C2S_LARK_MESSAGE value
     */
    game.C2SPacketType = (function() {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "C2S_PLAYER_NONE"] = 0;
        values[valuesById[1] = "C2S_PLAYER_ENTER"] = 1;
        values[valuesById[2] = "C2S_PLAYER_LEAVE"] = 2;
        values[valuesById[10] = "C2S_PLAYER_POSITION"] = 10;
        values[valuesById[11] = "C2S_PLAYER_ANIMATION"] = 11;
        values[valuesById[12] = "C2S_PLAYER_UPDATE"] = 12;
        values[valuesById[13] = "C2S_PLAYER_FISHING"] = 13;
        values[valuesById[50] = "C2S_PET_POSITION"] = 50;
        values[valuesById[51] = "C2S_PET_ANIMATION"] = 51;
        values[valuesById[100] = "C2S_CHAT_ENTER"] = 100;
        values[valuesById[101] = "C2S_CHAT_LEAVE"] = 101;
        values[valuesById[102] = "C2S_CHAT_MESSAGE"] = 102;
        values[valuesById[103] = "C2S_CHAT_MESSAGE_DELETE"] = 103;
        values[valuesById[1001] = "C2S_CUT_TREE"] = 1001;
        values[valuesById[1002] = "C2S_MINING_ROCK"] = 1002;
        values[valuesById[1003] = "C2S_FISHING_SUCCESS"] = 1003;
        values[valuesById[1004] = "C2S_PICK_UP_DROP"] = 1004;
        values[valuesById[2000] = "C2S_PLAYER_MAP_UPDATE"] = 2000;
        values[valuesById[2001] = "C2S_PLAYER_POSITION_UPDATE"] = 2001;
        values[valuesById[10001] = "C2S_LARK_MESSAGE"] = 10001;
        return values;
    })();

    /**
     * ClientRequestTypes enum.
     * @name game.ClientRequestTypes
     * @enum {number}
     * @property {number} NONE=0 NONE value
     * @property {number} PICK_UP_RED_PACKET=1 PICK_UP_RED_PACKET value
     */
    game.ClientRequestTypes = (function() {
        var valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "NONE"] = 0;
        values[valuesById[1] = "PICK_UP_RED_PACKET"] = 1;
        return values;
    })();

    game.GameMessage = (function() {

        /**
         * Properties of a GameMessage.
         * @memberof game
         * @interface IGameMessage
         * @property {number|null} [pid] GameMessage pid
         * @property {Uint8Array|null} [data] GameMessage data
         * @property {number|Long|null} [timestamp] GameMessage timestamp
         */

        /**
         * Constructs a new GameMessage.
         * @memberof game
         * @classdesc Represents a GameMessage.
         * @implements IGameMessage
         * @constructor
         * @param {game.IGameMessage=} [properties] Properties to set
         */
        function GameMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * GameMessage pid.
         * @member {number} pid
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.pid = 0;

        /**
         * GameMessage data.
         * @member {Uint8Array} data
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.data = $util.newBuffer([]);

        /**
         * GameMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.GameMessage
         * @instance
         */
        GameMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @function create
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage=} [properties] Properties to set
         * @returns {game.GameMessage} GameMessage instance
         */
        GameMessage.create = function create(properties) {
            return new GameMessage(properties);
        };

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encode
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.data != null && Object.hasOwnProperty.call(message, "data"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.data);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {game.IGameMessage} message GameMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        GameMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.GameMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.data = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.GameMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.GameMessage} GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        GameMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a GameMessage message.
         * @function verify
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        GameMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.data != null && message.hasOwnProperty("data"))
                if (!(message.data && typeof message.data.length === "number" || $util.isString(message.data)))
                    return "data: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.GameMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.GameMessage} GameMessage
         */
        GameMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.GameMessage)
                return object;
            var message = new $root.game.GameMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.data != null)
                if (typeof object.data === "string")
                    $util.base64.decode(object.data, message.data = $util.newBuffer($util.base64.length(object.data)), 0);
                else if (object.data.length >= 0)
                    message.data = object.data;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.GameMessage
         * @static
         * @param {game.GameMessage} message GameMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        GameMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.data = "";
                else {
                    object.data = [];
                    if (options.bytes !== Array)
                        object.data = $util.newBuffer(object.data);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.data != null && message.hasOwnProperty("data"))
                object.data = options.bytes === String ? $util.base64.encode(message.data, 0, message.data.length) : options.bytes === Array ? Array.prototype.slice.call(message.data) : message.data;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this GameMessage to JSON.
         * @function toJSON
         * @memberof game.GameMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        GameMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for GameMessage
         * @function getTypeUrl
         * @memberof game.GameMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        GameMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.GameMessage";
        };

        return GameMessage;
    })();

    game.PlayerActionMessage = (function() {

        /**
         * Properties of a PlayerActionMessage.
         * @memberof game
         * @interface IPlayerActionMessage
         * @property {number|null} [pid] PlayerActionMessage pid
         * @property {Uint8Array|null} [actionData] PlayerActionMessage actionData
         * @property {number|Long|null} [timestamp] PlayerActionMessage timestamp
         */

        /**
         * Constructs a new PlayerActionMessage.
         * @memberof game
         * @classdesc Represents a PlayerActionMessage.
         * @implements IPlayerActionMessage
         * @constructor
         * @param {game.IPlayerActionMessage=} [properties] Properties to set
         */
        function PlayerActionMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerActionMessage pid.
         * @member {number} pid
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.pid = 0;

        /**
         * PlayerActionMessage actionData.
         * @member {Uint8Array} actionData
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.actionData = $util.newBuffer([]);

        /**
         * PlayerActionMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerActionMessage
         * @instance
         */
        PlayerActionMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerActionMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage=} [properties] Properties to set
         * @returns {game.PlayerActionMessage} PlayerActionMessage instance
         */
        PlayerActionMessage.create = function create(properties) {
            return new PlayerActionMessage(properties);
        };

        /**
         * Encodes the specified PlayerActionMessage message. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage} message PlayerActionMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerActionMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.actionData != null && Object.hasOwnProperty.call(message, "actionData"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.actionData);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerActionMessage message, length delimited. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.IPlayerActionMessage} message PlayerActionMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerActionMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerActionMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerActionMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerActionMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.actionData = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerActionMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerActionMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerActionMessage message.
         * @function verify
         * @memberof game.PlayerActionMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerActionMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.actionData != null && message.hasOwnProperty("actionData"))
                if (!(message.actionData && typeof message.actionData.length === "number" || $util.isString(message.actionData)))
                    return "actionData: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerActionMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerActionMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerActionMessage} PlayerActionMessage
         */
        PlayerActionMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerActionMessage)
                return object;
            var message = new $root.game.PlayerActionMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.actionData != null)
                if (typeof object.actionData === "string")
                    $util.base64.decode(object.actionData, message.actionData = $util.newBuffer($util.base64.length(object.actionData)), 0);
                else if (object.actionData.length >= 0)
                    message.actionData = object.actionData;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerActionMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerActionMessage
         * @static
         * @param {game.PlayerActionMessage} message PlayerActionMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerActionMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.actionData = "";
                else {
                    object.actionData = [];
                    if (options.bytes !== Array)
                        object.actionData = $util.newBuffer(object.actionData);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.actionData != null && message.hasOwnProperty("actionData"))
                object.actionData = options.bytes === String ? $util.base64.encode(message.actionData, 0, message.actionData.length) : options.bytes === Array ? Array.prototype.slice.call(message.actionData) : message.actionData;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerActionMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerActionMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerActionMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerActionMessage
         * @function getTypeUrl
         * @memberof game.PlayerActionMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerActionMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerActionMessage";
        };

        return PlayerActionMessage;
    })();

    game.PlayerRequestMessage = (function() {

        /**
         * Properties of a PlayerRequestMessage.
         * @memberof game
         * @interface IPlayerRequestMessage
         * @property {number|null} [pid] PlayerRequestMessage pid
         * @property {Uint8Array|null} [requestData] PlayerRequestMessage requestData
         * @property {number|Long|null} [timestamp] PlayerRequestMessage timestamp
         */

        /**
         * Constructs a new PlayerRequestMessage.
         * @memberof game
         * @classdesc Represents a PlayerRequestMessage.
         * @implements IPlayerRequestMessage
         * @constructor
         * @param {game.IPlayerRequestMessage=} [properties] Properties to set
         */
        function PlayerRequestMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerRequestMessage pid.
         * @member {number} pid
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.pid = 0;

        /**
         * PlayerRequestMessage requestData.
         * @member {Uint8Array} requestData
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.requestData = $util.newBuffer([]);

        /**
         * PlayerRequestMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerRequestMessage
         * @instance
         */
        PlayerRequestMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerRequestMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage=} [properties] Properties to set
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage instance
         */
        PlayerRequestMessage.create = function create(properties) {
            return new PlayerRequestMessage(properties);
        };

        /**
         * Encodes the specified PlayerRequestMessage message. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage} message PlayerRequestMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerRequestMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.requestData != null && Object.hasOwnProperty.call(message, "requestData"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.requestData);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerRequestMessage message, length delimited. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.IPlayerRequestMessage} message PlayerRequestMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerRequestMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerRequestMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerRequestMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.requestData = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerRequestMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerRequestMessage message.
         * @function verify
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerRequestMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.requestData != null && message.hasOwnProperty("requestData"))
                if (!(message.requestData && typeof message.requestData.length === "number" || $util.isString(message.requestData)))
                    return "requestData: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerRequestMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerRequestMessage} PlayerRequestMessage
         */
        PlayerRequestMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerRequestMessage)
                return object;
            var message = new $root.game.PlayerRequestMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.requestData != null)
                if (typeof object.requestData === "string")
                    $util.base64.decode(object.requestData, message.requestData = $util.newBuffer($util.base64.length(object.requestData)), 0);
                else if (object.requestData.length >= 0)
                    message.requestData = object.requestData;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerRequestMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {game.PlayerRequestMessage} message PlayerRequestMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerRequestMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.requestData = "";
                else {
                    object.requestData = [];
                    if (options.bytes !== Array)
                        object.requestData = $util.newBuffer(object.requestData);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.requestData != null && message.hasOwnProperty("requestData"))
                object.requestData = options.bytes === String ? $util.base64.encode(message.requestData, 0, message.requestData.length) : options.bytes === Array ? Array.prototype.slice.call(message.requestData) : message.requestData;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerRequestMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerRequestMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerRequestMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerRequestMessage
         * @function getTypeUrl
         * @memberof game.PlayerRequestMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerRequestMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerRequestMessage";
        };

        return PlayerRequestMessage;
    })();

    game.PlayerChatMessage = (function() {

        /**
         * Properties of a PlayerChatMessage.
         * @memberof game
         * @interface IPlayerChatMessage
         * @property {number|null} [chatId] PlayerChatMessage chatId
         * @property {number|null} [pid] PlayerChatMessage pid
         * @property {Uint8Array|null} [chatData] PlayerChatMessage chatData
         * @property {number|Long|null} [timestamp] PlayerChatMessage timestamp
         */

        /**
         * Constructs a new PlayerChatMessage.
         * @memberof game
         * @classdesc Represents a PlayerChatMessage.
         * @implements IPlayerChatMessage
         * @constructor
         * @param {game.IPlayerChatMessage=} [properties] Properties to set
         */
        function PlayerChatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerChatMessage chatId.
         * @member {number} chatId
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.chatId = 0;

        /**
         * PlayerChatMessage pid.
         * @member {number} pid
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.pid = 0;

        /**
         * PlayerChatMessage chatData.
         * @member {Uint8Array} chatData
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.chatData = $util.newBuffer([]);

        /**
         * PlayerChatMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerChatMessage
         * @instance
         */
        PlayerChatMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new PlayerChatMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage=} [properties] Properties to set
         * @returns {game.PlayerChatMessage} PlayerChatMessage instance
         */
        PlayerChatMessage.create = function create(properties) {
            return new PlayerChatMessage(properties);
        };

        /**
         * Encodes the specified PlayerChatMessage message. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage} message PlayerChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.pid);
            if (message.chatData != null && Object.hasOwnProperty.call(message, "chatData"))
                writer.uint32(/* id 3, wireType 2 =*/26).bytes(message.chatData);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.timestamp);
            return writer;
        };

        /**
         * Encodes the specified PlayerChatMessage message, length delimited. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.IPlayerChatMessage} message PlayerChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerChatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerChatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                case 2: {
                        message.pid = reader.int32();
                        break;
                    }
                case 3: {
                        message.chatData = reader.bytes();
                        break;
                    }
                case 4: {
                        message.timestamp = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerChatMessage message.
         * @function verify
         * @memberof game.PlayerChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.chatData != null && message.hasOwnProperty("chatData"))
                if (!(message.chatData && typeof message.chatData.length === "number" || $util.isString(message.chatData)))
                    return "chatData: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            return null;
        };

        /**
         * Creates a PlayerChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerChatMessage} PlayerChatMessage
         */
        PlayerChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerChatMessage)
                return object;
            var message = new $root.game.PlayerChatMessage();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.chatData != null)
                if (typeof object.chatData === "string")
                    $util.base64.decode(object.chatData, message.chatData = $util.newBuffer($util.base64.length(object.chatData)), 0);
                else if (object.chatData.length >= 0)
                    message.chatData = object.chatData;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a PlayerChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerChatMessage
         * @static
         * @param {game.PlayerChatMessage} message PlayerChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.chatId = 0;
                object.pid = 0;
                if (options.bytes === String)
                    object.chatData = "";
                else {
                    object.chatData = [];
                    if (options.bytes !== Array)
                        object.chatData = $util.newBuffer(object.chatData);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
            }
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.chatData != null && message.hasOwnProperty("chatData"))
                object.chatData = options.bytes === String ? $util.base64.encode(message.chatData, 0, message.chatData.length) : options.bytes === Array ? Array.prototype.slice.call(message.chatData) : message.chatData;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            return object;
        };

        /**
         * Converts this PlayerChatMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerChatMessage
         * @function getTypeUrl
         * @memberof game.PlayerChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerChatMessage";
        };

        return PlayerChatMessage;
    })();

    game.PlayerLogicMessage = (function() {

        /**
         * Properties of a PlayerLogicMessage.
         * @memberof game
         * @interface IPlayerLogicMessage
         * @property {number|null} [pid] PlayerLogicMessage pid
         * @property {Uint8Array|null} [logicData] PlayerLogicMessage logicData
         * @property {number|Long|null} [timestamp] PlayerLogicMessage timestamp
         * @property {string|null} [sign] PlayerLogicMessage sign
         */

        /**
         * Constructs a new PlayerLogicMessage.
         * @memberof game
         * @classdesc Represents a PlayerLogicMessage.
         * @implements IPlayerLogicMessage
         * @constructor
         * @param {game.IPlayerLogicMessage=} [properties] Properties to set
         */
        function PlayerLogicMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerLogicMessage pid.
         * @member {number} pid
         * @memberof game.PlayerLogicMessage
         * @instance
         */
        PlayerLogicMessage.prototype.pid = 0;

        /**
         * PlayerLogicMessage logicData.
         * @member {Uint8Array} logicData
         * @memberof game.PlayerLogicMessage
         * @instance
         */
        PlayerLogicMessage.prototype.logicData = $util.newBuffer([]);

        /**
         * PlayerLogicMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.PlayerLogicMessage
         * @instance
         */
        PlayerLogicMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * PlayerLogicMessage sign.
         * @member {string} sign
         * @memberof game.PlayerLogicMessage
         * @instance
         */
        PlayerLogicMessage.prototype.sign = "";

        /**
         * Creates a new PlayerLogicMessage instance using the specified properties.
         * @function create
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {game.IPlayerLogicMessage=} [properties] Properties to set
         * @returns {game.PlayerLogicMessage} PlayerLogicMessage instance
         */
        PlayerLogicMessage.create = function create(properties) {
            return new PlayerLogicMessage(properties);
        };

        /**
         * Encodes the specified PlayerLogicMessage message. Does not implicitly {@link game.PlayerLogicMessage.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {game.IPlayerLogicMessage} message PlayerLogicMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLogicMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pid != null && Object.hasOwnProperty.call(message, "pid"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.pid);
            if (message.logicData != null && Object.hasOwnProperty.call(message, "logicData"))
                writer.uint32(/* id 2, wireType 2 =*/18).bytes(message.logicData);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            if (message.sign != null && Object.hasOwnProperty.call(message, "sign"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.sign);
            return writer;
        };

        /**
         * Encodes the specified PlayerLogicMessage message, length delimited. Does not implicitly {@link game.PlayerLogicMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {game.IPlayerLogicMessage} message PlayerLogicMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLogicMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerLogicMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerLogicMessage} PlayerLogicMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLogicMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerLogicMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.pid = reader.int32();
                        break;
                    }
                case 2: {
                        message.logicData = reader.bytes();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 4: {
                        message.sign = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerLogicMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerLogicMessage} PlayerLogicMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLogicMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerLogicMessage message.
         * @function verify
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerLogicMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pid != null && message.hasOwnProperty("pid"))
                if (!$util.isInteger(message.pid))
                    return "pid: integer expected";
            if (message.logicData != null && message.hasOwnProperty("logicData"))
                if (!(message.logicData && typeof message.logicData.length === "number" || $util.isString(message.logicData)))
                    return "logicData: buffer expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.sign != null && message.hasOwnProperty("sign"))
                if (!$util.isString(message.sign))
                    return "sign: string expected";
            return null;
        };

        /**
         * Creates a PlayerLogicMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerLogicMessage} PlayerLogicMessage
         */
        PlayerLogicMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerLogicMessage)
                return object;
            var message = new $root.game.PlayerLogicMessage();
            if (object.pid != null)
                message.pid = object.pid | 0;
            if (object.logicData != null)
                if (typeof object.logicData === "string")
                    $util.base64.decode(object.logicData, message.logicData = $util.newBuffer($util.base64.length(object.logicData)), 0);
                else if (object.logicData.length >= 0)
                    message.logicData = object.logicData;
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.sign != null)
                message.sign = String(object.sign);
            return message;
        };

        /**
         * Creates a plain object from a PlayerLogicMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {game.PlayerLogicMessage} message PlayerLogicMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerLogicMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.pid = 0;
                if (options.bytes === String)
                    object.logicData = "";
                else {
                    object.logicData = [];
                    if (options.bytes !== Array)
                        object.logicData = $util.newBuffer(object.logicData);
                }
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.sign = "";
            }
            if (message.pid != null && message.hasOwnProperty("pid"))
                object.pid = message.pid;
            if (message.logicData != null && message.hasOwnProperty("logicData"))
                object.logicData = options.bytes === String ? $util.base64.encode(message.logicData, 0, message.logicData.length) : options.bytes === Array ? Array.prototype.slice.call(message.logicData) : message.logicData;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.sign != null && message.hasOwnProperty("sign"))
                object.sign = message.sign;
            return object;
        };

        /**
         * Converts this PlayerLogicMessage to JSON.
         * @function toJSON
         * @memberof game.PlayerLogicMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerLogicMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerLogicMessage
         * @function getTypeUrl
         * @memberof game.PlayerLogicMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerLogicMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerLogicMessage";
        };

        return PlayerLogicMessage;
    })();

    game.PlayerPosition = (function() {

        /**
         * Properties of a PlayerPosition.
         * @memberof game
         * @interface IPlayerPosition
         * @property {string|null} [btcAddress] PlayerPosition btcAddress
         * @property {number|null} [x] PlayerPosition x
         * @property {number|null} [y] PlayerPosition y
         * @property {number|null} [z] PlayerPosition z
         * @property {number|null} [rotationX] PlayerPosition rotationX
         * @property {number|null} [rotationY] PlayerPosition rotationY
         * @property {number|null} [rotationZ] PlayerPosition rotationZ
         * @property {number|null} [rotationW] PlayerPosition rotationW
         */

        /**
         * Constructs a new PlayerPosition.
         * @memberof game
         * @classdesc Represents a PlayerPosition.
         * @implements IPlayerPosition
         * @constructor
         * @param {game.IPlayerPosition=} [properties] Properties to set
         */
        function PlayerPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerPosition btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.btcAddress = "";

        /**
         * PlayerPosition x.
         * @member {number} x
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.x = 0;

        /**
         * PlayerPosition y.
         * @member {number} y
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.y = 0;

        /**
         * PlayerPosition z.
         * @member {number} z
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.z = 0;

        /**
         * PlayerPosition rotationX.
         * @member {number} rotationX
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationX = 0;

        /**
         * PlayerPosition rotationY.
         * @member {number} rotationY
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationY = 0;

        /**
         * PlayerPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationZ = 0;

        /**
         * PlayerPosition rotationW.
         * @member {number} rotationW
         * @memberof game.PlayerPosition
         * @instance
         */
        PlayerPosition.prototype.rotationW = 0;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @function create
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition=} [properties] Properties to set
         * @returns {game.PlayerPosition} PlayerPosition instance
         */
        PlayerPosition.create = function create(properties) {
            return new PlayerPosition(properties);
        };

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 8, wireType 5 =*/69).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {game.IPlayerPosition} message PlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.x = reader.float();
                        break;
                    }
                case 3: {
                        message.y = reader.float();
                        break;
                    }
                case 4: {
                        message.z = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 8: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerPosition} PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerPosition message.
         * @function verify
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerPosition} PlayerPosition
         */
        PlayerPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerPosition)
                return object;
            var message = new $root.game.PlayerPosition();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerPosition
         * @static
         * @param {game.PlayerPosition} message PlayerPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this PlayerPosition to JSON.
         * @function toJSON
         * @memberof game.PlayerPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerPosition
         * @function getTypeUrl
         * @memberof game.PlayerPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerPosition";
        };

        return PlayerPosition;
    })();

    game.PlayerAnimation = (function() {

        /**
         * Properties of a PlayerAnimation.
         * @memberof game
         * @interface IPlayerAnimation
         * @property {string|null} [btcAddress] PlayerAnimation btcAddress
         * @property {string|null} [curAnimation] PlayerAnimation curAnimation
         */

        /**
         * Constructs a new PlayerAnimation.
         * @memberof game
         * @classdesc Represents a PlayerAnimation.
         * @implements IPlayerAnimation
         * @constructor
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         */
        function PlayerAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerAnimation btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.btcAddress = "";

        /**
         * PlayerAnimation curAnimation.
         * @member {string} curAnimation
         * @memberof game.PlayerAnimation
         * @instance
         */
        PlayerAnimation.prototype.curAnimation = "";

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @function create
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation=} [properties] Properties to set
         * @returns {game.PlayerAnimation} PlayerAnimation instance
         */
        PlayerAnimation.create = function create(properties) {
            return new PlayerAnimation(properties);
        };

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.curAnimation != null && Object.hasOwnProperty.call(message, "curAnimation"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.curAnimation);
            return writer;
        };

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.IPlayerAnimation} message PlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.curAnimation = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerAnimation} PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerAnimation message.
         * @function verify
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                if (!$util.isString(message.curAnimation))
                    return "curAnimation: string expected";
            return null;
        };

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerAnimation} PlayerAnimation
         */
        PlayerAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerAnimation)
                return object;
            var message = new $root.game.PlayerAnimation();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.curAnimation != null)
                message.curAnimation = String(object.curAnimation);
            return message;
        };

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerAnimation
         * @static
         * @param {game.PlayerAnimation} message PlayerAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.curAnimation = "";
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                object.curAnimation = message.curAnimation;
            return object;
        };

        /**
         * Converts this PlayerAnimation to JSON.
         * @function toJSON
         * @memberof game.PlayerAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerAnimation
         * @function getTypeUrl
         * @memberof game.PlayerAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerAnimation";
        };

        return PlayerAnimation;
    })();

    game.PlayerUpdate = (function() {

        /**
         * Properties of a PlayerUpdate.
         * @memberof game
         * @interface IPlayerUpdate
         * @property {string|null} [btcAddress] PlayerUpdate btcAddress
         * @property {number|null} [itemId] PlayerUpdate itemId
         * @property {number|null} [pizzaCount] PlayerUpdate pizzaCount
         * @property {string|null} [petId] PlayerUpdate petId
         * @property {string|null} [pizzaTick] PlayerUpdate pizzaTick
         */

        /**
         * Constructs a new PlayerUpdate.
         * @memberof game
         * @classdesc Represents a PlayerUpdate.
         * @implements IPlayerUpdate
         * @constructor
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         */
        function PlayerUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerUpdate btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.btcAddress = "";

        /**
         * PlayerUpdate itemId.
         * @member {number} itemId
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.itemId = 0;

        /**
         * PlayerUpdate pizzaCount.
         * @member {number} pizzaCount
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.pizzaCount = 0;

        /**
         * PlayerUpdate petId.
         * @member {string} petId
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.petId = "";

        /**
         * PlayerUpdate pizzaTick.
         * @member {string} pizzaTick
         * @memberof game.PlayerUpdate
         * @instance
         */
        PlayerUpdate.prototype.pizzaTick = "";

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @function create
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate=} [properties] Properties to set
         * @returns {game.PlayerUpdate} PlayerUpdate instance
         */
        PlayerUpdate.create = function create(properties) {
            return new PlayerUpdate(properties);
        };

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.itemId != null && Object.hasOwnProperty.call(message, "itemId"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.itemId);
            if (message.pizzaCount != null && Object.hasOwnProperty.call(message, "pizzaCount"))
                writer.uint32(/* id 3, wireType 0 =*/24).int32(message.pizzaCount);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.petId);
            if (message.pizzaTick != null && Object.hasOwnProperty.call(message, "pizzaTick"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.pizzaTick);
            return writer;
        };

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.IPlayerUpdate} message PlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.itemId = reader.int32();
                        break;
                    }
                case 3: {
                        message.pizzaCount = reader.int32();
                        break;
                    }
                case 4: {
                        message.petId = reader.string();
                        break;
                    }
                case 5: {
                        message.pizzaTick = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerUpdate} PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerUpdate message.
         * @function verify
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                if (!$util.isInteger(message.itemId))
                    return "itemId: integer expected";
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                if (!$util.isInteger(message.pizzaCount))
                    return "pizzaCount: integer expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.pizzaTick != null && message.hasOwnProperty("pizzaTick"))
                if (!$util.isString(message.pizzaTick))
                    return "pizzaTick: string expected";
            return null;
        };

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerUpdate} PlayerUpdate
         */
        PlayerUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerUpdate)
                return object;
            var message = new $root.game.PlayerUpdate();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.itemId != null)
                message.itemId = object.itemId | 0;
            if (object.pizzaCount != null)
                message.pizzaCount = object.pizzaCount | 0;
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.pizzaTick != null)
                message.pizzaTick = String(object.pizzaTick);
            return message;
        };

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerUpdate
         * @static
         * @param {game.PlayerUpdate} message PlayerUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.itemId = 0;
                object.pizzaCount = 0;
                object.petId = "";
                object.pizzaTick = "";
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                object.itemId = message.itemId;
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                object.pizzaCount = message.pizzaCount;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.pizzaTick != null && message.hasOwnProperty("pizzaTick"))
                object.pizzaTick = message.pizzaTick;
            return object;
        };

        /**
         * Converts this PlayerUpdate to JSON.
         * @function toJSON
         * @memberof game.PlayerUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerUpdate
         * @function getTypeUrl
         * @memberof game.PlayerUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerUpdate";
        };

        return PlayerUpdate;
    })();

    game.PlayerEnter = (function() {

        /**
         * Properties of a PlayerEnter.
         * @memberof game
         * @interface IPlayerEnter
         * @property {string|null} [btcAddress] PlayerEnter btcAddress
         * @property {game.IAvatarData|null} [avatarData] PlayerEnter avatarData
         * @property {game.IPlayerPosition|null} [position] PlayerEnter position
         */

        /**
         * Constructs a new PlayerEnter.
         * @memberof game
         * @classdesc Represents a PlayerEnter.
         * @implements IPlayerEnter
         * @constructor
         * @param {game.IPlayerEnter=} [properties] Properties to set
         */
        function PlayerEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerEnter btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.btcAddress = "";

        /**
         * PlayerEnter avatarData.
         * @member {game.IAvatarData|null|undefined} avatarData
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.avatarData = null;

        /**
         * PlayerEnter position.
         * @member {game.IPlayerPosition|null|undefined} position
         * @memberof game.PlayerEnter
         * @instance
         */
        PlayerEnter.prototype.position = null;

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @function create
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter=} [properties] Properties to set
         * @returns {game.PlayerEnter} PlayerEnter instance
         */
        PlayerEnter.create = function create(properties) {
            return new PlayerEnter(properties);
        };

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.avatarData != null && Object.hasOwnProperty.call(message, "avatarData"))
                $root.game.AvatarData.encode(message.avatarData, writer.uint32(/* id 2, wireType 2 =*/18).fork()).ldelim();
            if (message.position != null && Object.hasOwnProperty.call(message, "position"))
                $root.game.PlayerPosition.encode(message.position, writer.uint32(/* id 3, wireType 2 =*/26).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {game.IPlayerEnter} message PlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.avatarData = $root.game.AvatarData.decode(reader, reader.uint32());
                        break;
                    }
                case 3: {
                        message.position = $root.game.PlayerPosition.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerEnter} PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerEnter message.
         * @function verify
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.avatarData != null && message.hasOwnProperty("avatarData")) {
                var error = $root.game.AvatarData.verify(message.avatarData);
                if (error)
                    return "avatarData." + error;
            }
            if (message.position != null && message.hasOwnProperty("position")) {
                var error = $root.game.PlayerPosition.verify(message.position);
                if (error)
                    return "position." + error;
            }
            return null;
        };

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerEnter} PlayerEnter
         */
        PlayerEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerEnter)
                return object;
            var message = new $root.game.PlayerEnter();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.avatarData != null) {
                if (typeof object.avatarData !== "object")
                    throw TypeError(".game.PlayerEnter.avatarData: object expected");
                message.avatarData = $root.game.AvatarData.fromObject(object.avatarData);
            }
            if (object.position != null) {
                if (typeof object.position !== "object")
                    throw TypeError(".game.PlayerEnter.position: object expected");
                message.position = $root.game.PlayerPosition.fromObject(object.position);
            }
            return message;
        };

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerEnter
         * @static
         * @param {game.PlayerEnter} message PlayerEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.avatarData = null;
                object.position = null;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.avatarData != null && message.hasOwnProperty("avatarData"))
                object.avatarData = $root.game.AvatarData.toObject(message.avatarData, options);
            if (message.position != null && message.hasOwnProperty("position"))
                object.position = $root.game.PlayerPosition.toObject(message.position, options);
            return object;
        };

        /**
         * Converts this PlayerEnter to JSON.
         * @function toJSON
         * @memberof game.PlayerEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerEnter
         * @function getTypeUrl
         * @memberof game.PlayerEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerEnter";
        };

        return PlayerEnter;
    })();

    game.PlayerLeave = (function() {

        /**
         * Properties of a PlayerLeave.
         * @memberof game
         * @interface IPlayerLeave
         * @property {string|null} [btcAddress] PlayerLeave btcAddress
         */

        /**
         * Constructs a new PlayerLeave.
         * @memberof game
         * @classdesc Represents a PlayerLeave.
         * @implements IPlayerLeave
         * @constructor
         * @param {game.IPlayerLeave=} [properties] Properties to set
         */
        function PlayerLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerLeave btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerLeave
         * @instance
         */
        PlayerLeave.prototype.btcAddress = "";

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @function create
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave=} [properties] Properties to set
         * @returns {game.PlayerLeave} PlayerLeave instance
         */
        PlayerLeave.create = function create(properties) {
            return new PlayerLeave(properties);
        };

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            return writer;
        };

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {game.IPlayerLeave} message PlayerLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerLeave} PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerLeave message.
         * @function verify
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            return null;
        };

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerLeave} PlayerLeave
         */
        PlayerLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerLeave)
                return object;
            var message = new $root.game.PlayerLeave();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            return message;
        };

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerLeave
         * @static
         * @param {game.PlayerLeave} message PlayerLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.btcAddress = "";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            return object;
        };

        /**
         * Converts this PlayerLeave to JSON.
         * @function toJSON
         * @memberof game.PlayerLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerLeave
         * @function getTypeUrl
         * @memberof game.PlayerLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerLeave";
        };

        return PlayerLeave;
    })();

    game.PlayerFishing = (function() {

        /**
         * Properties of a PlayerFishing.
         * @memberof game
         * @interface IPlayerFishing
         * @property {string|null} [btcAddress] PlayerFishing btcAddress
         * @property {number|null} [fishId] PlayerFishing fishId
         */

        /**
         * Constructs a new PlayerFishing.
         * @memberof game
         * @classdesc Represents a PlayerFishing.
         * @implements IPlayerFishing
         * @constructor
         * @param {game.IPlayerFishing=} [properties] Properties to set
         */
        function PlayerFishing(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PlayerFishing btcAddress.
         * @member {string} btcAddress
         * @memberof game.PlayerFishing
         * @instance
         */
        PlayerFishing.prototype.btcAddress = "";

        /**
         * PlayerFishing fishId.
         * @member {number} fishId
         * @memberof game.PlayerFishing
         * @instance
         */
        PlayerFishing.prototype.fishId = 0;

        /**
         * Creates a new PlayerFishing instance using the specified properties.
         * @function create
         * @memberof game.PlayerFishing
         * @static
         * @param {game.IPlayerFishing=} [properties] Properties to set
         * @returns {game.PlayerFishing} PlayerFishing instance
         */
        PlayerFishing.create = function create(properties) {
            return new PlayerFishing(properties);
        };

        /**
         * Encodes the specified PlayerFishing message. Does not implicitly {@link game.PlayerFishing.verify|verify} messages.
         * @function encode
         * @memberof game.PlayerFishing
         * @static
         * @param {game.IPlayerFishing} message PlayerFishing message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerFishing.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.btcAddress != null && Object.hasOwnProperty.call(message, "btcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.btcAddress);
            if (message.fishId != null && Object.hasOwnProperty.call(message, "fishId"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.fishId);
            return writer;
        };

        /**
         * Encodes the specified PlayerFishing message, length delimited. Does not implicitly {@link game.PlayerFishing.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PlayerFishing
         * @static
         * @param {game.IPlayerFishing} message PlayerFishing message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PlayerFishing.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PlayerFishing message from the specified reader or buffer.
         * @function decode
         * @memberof game.PlayerFishing
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PlayerFishing} PlayerFishing
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerFishing.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PlayerFishing();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.btcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.fishId = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PlayerFishing message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PlayerFishing
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PlayerFishing} PlayerFishing
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PlayerFishing.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PlayerFishing message.
         * @function verify
         * @memberof game.PlayerFishing
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PlayerFishing.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                if (!$util.isString(message.btcAddress))
                    return "btcAddress: string expected";
            if (message.fishId != null && message.hasOwnProperty("fishId"))
                if (!$util.isInteger(message.fishId))
                    return "fishId: integer expected";
            return null;
        };

        /**
         * Creates a PlayerFishing message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PlayerFishing
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PlayerFishing} PlayerFishing
         */
        PlayerFishing.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PlayerFishing)
                return object;
            var message = new $root.game.PlayerFishing();
            if (object.btcAddress != null)
                message.btcAddress = String(object.btcAddress);
            if (object.fishId != null)
                message.fishId = object.fishId | 0;
            return message;
        };

        /**
         * Creates a plain object from a PlayerFishing message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PlayerFishing
         * @static
         * @param {game.PlayerFishing} message PlayerFishing
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PlayerFishing.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.btcAddress = "";
                object.fishId = 0;
            }
            if (message.btcAddress != null && message.hasOwnProperty("btcAddress"))
                object.btcAddress = message.btcAddress;
            if (message.fishId != null && message.hasOwnProperty("fishId"))
                object.fishId = message.fishId;
            return object;
        };

        /**
         * Converts this PlayerFishing to JSON.
         * @function toJSON
         * @memberof game.PlayerFishing
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PlayerFishing.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PlayerFishing
         * @function getTypeUrl
         * @memberof game.PlayerFishing
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PlayerFishing.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PlayerFishing";
        };

        return PlayerFishing;
    })();

    game.AvatarData = (function() {

        /**
         * Properties of an AvatarData.
         * @memberof game
         * @interface IAvatarData
         * @property {string|null} [shirtId] AvatarData shirtId
         * @property {string|null} [shirtTextureId] AvatarData shirtTextureId
         * @property {string|null} [shirtColor] AvatarData shirtColor
         * @property {string|null} [pantsId] AvatarData pantsId
         * @property {string|null} [shoesId] AvatarData shoesId
         * @property {string|null} [hatId] AvatarData hatId
         * @property {string|null} [glovesId] AvatarData glovesId
         */

        /**
         * Constructs a new AvatarData.
         * @memberof game
         * @classdesc Represents an AvatarData.
         * @implements IAvatarData
         * @constructor
         * @param {game.IAvatarData=} [properties] Properties to set
         */
        function AvatarData(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * AvatarData shirtId.
         * @member {string} shirtId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtId = "";

        /**
         * AvatarData shirtTextureId.
         * @member {string} shirtTextureId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtTextureId = "";

        /**
         * AvatarData shirtColor.
         * @member {string} shirtColor
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shirtColor = "";

        /**
         * AvatarData pantsId.
         * @member {string} pantsId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.pantsId = "";

        /**
         * AvatarData shoesId.
         * @member {string} shoesId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.shoesId = "";

        /**
         * AvatarData hatId.
         * @member {string} hatId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.hatId = "";

        /**
         * AvatarData glovesId.
         * @member {string} glovesId
         * @memberof game.AvatarData
         * @instance
         */
        AvatarData.prototype.glovesId = "";

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @function create
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData=} [properties] Properties to set
         * @returns {game.AvatarData} AvatarData instance
         */
        AvatarData.create = function create(properties) {
            return new AvatarData(properties);
        };

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encode
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.shirtId != null && Object.hasOwnProperty.call(message, "shirtId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.shirtId);
            if (message.shirtTextureId != null && Object.hasOwnProperty.call(message, "shirtTextureId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.shirtTextureId);
            if (message.shirtColor != null && Object.hasOwnProperty.call(message, "shirtColor"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.shirtColor);
            if (message.pantsId != null && Object.hasOwnProperty.call(message, "pantsId"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.pantsId);
            if (message.shoesId != null && Object.hasOwnProperty.call(message, "shoesId"))
                writer.uint32(/* id 5, wireType 2 =*/42).string(message.shoesId);
            if (message.hatId != null && Object.hasOwnProperty.call(message, "hatId"))
                writer.uint32(/* id 6, wireType 2 =*/50).string(message.hatId);
            if (message.glovesId != null && Object.hasOwnProperty.call(message, "glovesId"))
                writer.uint32(/* id 7, wireType 2 =*/58).string(message.glovesId);
            return writer;
        };

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {game.IAvatarData} message AvatarData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        AvatarData.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @function decode
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.AvatarData();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.shirtId = reader.string();
                        break;
                    }
                case 2: {
                        message.shirtTextureId = reader.string();
                        break;
                    }
                case 3: {
                        message.shirtColor = reader.string();
                        break;
                    }
                case 4: {
                        message.pantsId = reader.string();
                        break;
                    }
                case 5: {
                        message.shoesId = reader.string();
                        break;
                    }
                case 6: {
                        message.hatId = reader.string();
                        break;
                    }
                case 7: {
                        message.glovesId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.AvatarData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.AvatarData} AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        AvatarData.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an AvatarData message.
         * @function verify
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        AvatarData.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                if (!$util.isString(message.shirtId))
                    return "shirtId: string expected";
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                if (!$util.isString(message.shirtTextureId))
                    return "shirtTextureId: string expected";
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                if (!$util.isString(message.shirtColor))
                    return "shirtColor: string expected";
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                if (!$util.isString(message.pantsId))
                    return "pantsId: string expected";
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                if (!$util.isString(message.shoesId))
                    return "shoesId: string expected";
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                if (!$util.isString(message.hatId))
                    return "hatId: string expected";
            if (message.glovesId != null && message.hasOwnProperty("glovesId"))
                if (!$util.isString(message.glovesId))
                    return "glovesId: string expected";
            return null;
        };

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.AvatarData
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.AvatarData} AvatarData
         */
        AvatarData.fromObject = function fromObject(object) {
            if (object instanceof $root.game.AvatarData)
                return object;
            var message = new $root.game.AvatarData();
            if (object.shirtId != null)
                message.shirtId = String(object.shirtId);
            if (object.shirtTextureId != null)
                message.shirtTextureId = String(object.shirtTextureId);
            if (object.shirtColor != null)
                message.shirtColor = String(object.shirtColor);
            if (object.pantsId != null)
                message.pantsId = String(object.pantsId);
            if (object.shoesId != null)
                message.shoesId = String(object.shoesId);
            if (object.hatId != null)
                message.hatId = String(object.hatId);
            if (object.glovesId != null)
                message.glovesId = String(object.glovesId);
            return message;
        };

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.AvatarData
         * @static
         * @param {game.AvatarData} message AvatarData
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        AvatarData.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.shirtId = "";
                object.shirtTextureId = "";
                object.shirtColor = "";
                object.pantsId = "";
                object.shoesId = "";
                object.hatId = "";
                object.glovesId = "";
            }
            if (message.shirtId != null && message.hasOwnProperty("shirtId"))
                object.shirtId = message.shirtId;
            if (message.shirtTextureId != null && message.hasOwnProperty("shirtTextureId"))
                object.shirtTextureId = message.shirtTextureId;
            if (message.shirtColor != null && message.hasOwnProperty("shirtColor"))
                object.shirtColor = message.shirtColor;
            if (message.pantsId != null && message.hasOwnProperty("pantsId"))
                object.pantsId = message.pantsId;
            if (message.shoesId != null && message.hasOwnProperty("shoesId"))
                object.shoesId = message.shoesId;
            if (message.hatId != null && message.hasOwnProperty("hatId"))
                object.hatId = message.hatId;
            if (message.glovesId != null && message.hasOwnProperty("glovesId"))
                object.glovesId = message.glovesId;
            return object;
        };

        /**
         * Converts this AvatarData to JSON.
         * @function toJSON
         * @memberof game.AvatarData
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        AvatarData.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for AvatarData
         * @function getTypeUrl
         * @memberof game.AvatarData
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        AvatarData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.AvatarData";
        };

        return AvatarData;
    })();

    game.PetPosition = (function() {

        /**
         * Properties of a PetPosition.
         * @memberof game
         * @interface IPetPosition
         * @property {string|null} [ownerBtcAddress] PetPosition ownerBtcAddress
         * @property {number|null} [x] PetPosition x
         * @property {number|null} [y] PetPosition y
         * @property {number|null} [z] PetPosition z
         * @property {number|null} [rotationX] PetPosition rotationX
         * @property {number|null} [rotationY] PetPosition rotationY
         * @property {number|null} [rotationZ] PetPosition rotationZ
         * @property {number|null} [rotationW] PetPosition rotationW
         */

        /**
         * Constructs a new PetPosition.
         * @memberof game
         * @classdesc Represents a PetPosition.
         * @implements IPetPosition
         * @constructor
         * @param {game.IPetPosition=} [properties] Properties to set
         */
        function PetPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetPosition ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.ownerBtcAddress = "";

        /**
         * PetPosition x.
         * @member {number} x
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.x = 0;

        /**
         * PetPosition y.
         * @member {number} y
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.y = 0;

        /**
         * PetPosition z.
         * @member {number} z
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.z = 0;

        /**
         * PetPosition rotationX.
         * @member {number} rotationX
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationX = 0;

        /**
         * PetPosition rotationY.
         * @member {number} rotationY
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationY = 0;

        /**
         * PetPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationZ = 0;

        /**
         * PetPosition rotationW.
         * @member {number} rotationW
         * @memberof game.PetPosition
         * @instance
         */
        PetPosition.prototype.rotationW = 0;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @function create
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition=} [properties] Properties to set
         * @returns {game.PetPosition} PetPosition instance
         */
        PetPosition.create = function create(properties) {
            return new PetPosition(properties);
        };

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encode
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 8, wireType 5 =*/69).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {game.IPetPosition} message PetPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.x = reader.float();
                        break;
                    }
                case 3: {
                        message.y = reader.float();
                        break;
                    }
                case 4: {
                        message.z = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 8: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetPosition} PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetPosition message.
         * @function verify
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetPosition} PetPosition
         */
        PetPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetPosition)
                return object;
            var message = new $root.game.PetPosition();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetPosition
         * @static
         * @param {game.PetPosition} message PetPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this PetPosition to JSON.
         * @function toJSON
         * @memberof game.PetPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetPosition
         * @function getTypeUrl
         * @memberof game.PetPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetPosition";
        };

        return PetPosition;
    })();

    game.PetAnimation = (function() {

        /**
         * Properties of a PetAnimation.
         * @memberof game
         * @interface IPetAnimation
         * @property {string|null} [ownerBtcAddress] PetAnimation ownerBtcAddress
         * @property {string|null} [animationName] PetAnimation animationName
         */

        /**
         * Constructs a new PetAnimation.
         * @memberof game
         * @classdesc Represents a PetAnimation.
         * @implements IPetAnimation
         * @constructor
         * @param {game.IPetAnimation=} [properties] Properties to set
         */
        function PetAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * PetAnimation ownerBtcAddress.
         * @member {string} ownerBtcAddress
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.ownerBtcAddress = "";

        /**
         * PetAnimation animationName.
         * @member {string} animationName
         * @memberof game.PetAnimation
         * @instance
         */
        PetAnimation.prototype.animationName = "";

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @function create
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation=} [properties] Properties to set
         * @returns {game.PetAnimation} PetAnimation instance
         */
        PetAnimation.create = function create(properties) {
            return new PetAnimation(properties);
        };

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.ownerBtcAddress != null && Object.hasOwnProperty.call(message, "ownerBtcAddress"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.ownerBtcAddress);
            if (message.animationName != null && Object.hasOwnProperty.call(message, "animationName"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.animationName);
            return writer;
        };

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {game.IPetAnimation} message PetAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        PetAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.PetAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.ownerBtcAddress = reader.string();
                        break;
                    }
                case 2: {
                        message.animationName = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.PetAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.PetAnimation} PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        PetAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a PetAnimation message.
         * @function verify
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        PetAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                if (!$util.isString(message.ownerBtcAddress))
                    return "ownerBtcAddress: string expected";
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                if (!$util.isString(message.animationName))
                    return "animationName: string expected";
            return null;
        };

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.PetAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.PetAnimation} PetAnimation
         */
        PetAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.PetAnimation)
                return object;
            var message = new $root.game.PetAnimation();
            if (object.ownerBtcAddress != null)
                message.ownerBtcAddress = String(object.ownerBtcAddress);
            if (object.animationName != null)
                message.animationName = String(object.animationName);
            return message;
        };

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.PetAnimation
         * @static
         * @param {game.PetAnimation} message PetAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        PetAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.ownerBtcAddress = "";
                object.animationName = "";
            }
            if (message.ownerBtcAddress != null && message.hasOwnProperty("ownerBtcAddress"))
                object.ownerBtcAddress = message.ownerBtcAddress;
            if (message.animationName != null && message.hasOwnProperty("animationName"))
                object.animationName = message.animationName;
            return object;
        };

        /**
         * Converts this PetAnimation to JSON.
         * @function toJSON
         * @memberof game.PetAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        PetAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for PetAnimation
         * @function getTypeUrl
         * @memberof game.PetAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        PetAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.PetAnimation";
        };

        return PetAnimation;
    })();

    game.ChatEnter = (function() {

        /**
         * Properties of a ChatEnter.
         * @memberof game
         * @interface IChatEnter
         * @property {number|null} [chatId] ChatEnter chatId
         */

        /**
         * Constructs a new ChatEnter.
         * @memberof game
         * @classdesc Represents a ChatEnter.
         * @implements IChatEnter
         * @constructor
         * @param {game.IChatEnter=} [properties] Properties to set
         */
        function ChatEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatEnter chatId.
         * @member {number} chatId
         * @memberof game.ChatEnter
         * @instance
         */
        ChatEnter.prototype.chatId = 0;

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @function create
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter=} [properties] Properties to set
         * @returns {game.ChatEnter} ChatEnter instance
         */
        ChatEnter.create = function create(properties) {
            return new ChatEnter(properties);
        };

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encode
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            return writer;
        };

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {game.IChatEnter} message ChatEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatEnter} ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatEnter message.
         * @function verify
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            return null;
        };

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatEnter} ChatEnter
         */
        ChatEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatEnter)
                return object;
            var message = new $root.game.ChatEnter();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            return message;
        };

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatEnter
         * @static
         * @param {game.ChatEnter} message ChatEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.chatId = 0;
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            return object;
        };

        /**
         * Converts this ChatEnter to JSON.
         * @function toJSON
         * @memberof game.ChatEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatEnter
         * @function getTypeUrl
         * @memberof game.ChatEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatEnter";
        };

        return ChatEnter;
    })();

    game.ChatLeave = (function() {

        /**
         * Properties of a ChatLeave.
         * @memberof game
         * @interface IChatLeave
         * @property {number|null} [chatId] ChatLeave chatId
         */

        /**
         * Constructs a new ChatLeave.
         * @memberof game
         * @classdesc Represents a ChatLeave.
         * @implements IChatLeave
         * @constructor
         * @param {game.IChatLeave=} [properties] Properties to set
         */
        function ChatLeave(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatLeave chatId.
         * @member {number} chatId
         * @memberof game.ChatLeave
         * @instance
         */
        ChatLeave.prototype.chatId = 0;

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @function create
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave=} [properties] Properties to set
         * @returns {game.ChatLeave} ChatLeave instance
         */
        ChatLeave.create = function create(properties) {
            return new ChatLeave(properties);
        };

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encode
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.chatId != null && Object.hasOwnProperty.call(message, "chatId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.chatId);
            return writer;
        };

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {game.IChatLeave} message ChatLeave message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatLeave.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatLeave();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.chatId = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatLeave
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatLeave} ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatLeave.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatLeave message.
         * @function verify
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatLeave.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                if (!$util.isInteger(message.chatId))
                    return "chatId: integer expected";
            return null;
        };

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatLeave
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatLeave} ChatLeave
         */
        ChatLeave.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatLeave)
                return object;
            var message = new $root.game.ChatLeave();
            if (object.chatId != null)
                message.chatId = object.chatId | 0;
            return message;
        };

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatLeave
         * @static
         * @param {game.ChatLeave} message ChatLeave
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatLeave.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.chatId = 0;
            if (message.chatId != null && message.hasOwnProperty("chatId"))
                object.chatId = message.chatId;
            return object;
        };

        /**
         * Converts this ChatLeave to JSON.
         * @function toJSON
         * @memberof game.ChatLeave
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatLeave.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatLeave
         * @function getTypeUrl
         * @memberof game.ChatLeave
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatLeave.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatLeave";
        };

        return ChatLeave;
    })();

    game.ChatMessage = (function() {

        /**
         * Properties of a ChatMessage.
         * @memberof game
         * @interface IChatMessage
         * @property {string|null} [uuid] ChatMessage uuid
         * @property {string|null} [playerId] ChatMessage playerId
         * @property {string|null} [content] ChatMessage content
         * @property {string|null} [replyTo] ChatMessage replyTo
         * @property {number|Long|null} [timestamp] ChatMessage timestamp
         * @property {number|null} [tabType] ChatMessage tabType
         * @property {boolean|null} [isAdmin] ChatMessage isAdmin
         * @property {boolean|null} [isTelegram] ChatMessage isTelegram
         * @property {boolean|null} [isSystem] ChatMessage isSystem
         */

        /**
         * Constructs a new ChatMessage.
         * @memberof game
         * @classdesc Represents a ChatMessage.
         * @implements IChatMessage
         * @constructor
         * @param {game.IChatMessage=} [properties] Properties to set
         */
        function ChatMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatMessage uuid.
         * @member {string} uuid
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.uuid = "";

        /**
         * ChatMessage playerId.
         * @member {string} playerId
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.playerId = "";

        /**
         * ChatMessage content.
         * @member {string} content
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.content = "";

        /**
         * ChatMessage replyTo.
         * @member {string} replyTo
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.replyTo = "";

        /**
         * ChatMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * ChatMessage tabType.
         * @member {number} tabType
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.tabType = 0;

        /**
         * ChatMessage isAdmin.
         * @member {boolean} isAdmin
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.isAdmin = false;

        /**
         * ChatMessage isTelegram.
         * @member {boolean} isTelegram
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.isTelegram = false;

        /**
         * ChatMessage isSystem.
         * @member {boolean} isSystem
         * @memberof game.ChatMessage
         * @instance
         */
        ChatMessage.prototype.isSystem = false;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @function create
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage=} [properties] Properties to set
         * @returns {game.ChatMessage} ChatMessage instance
         */
        ChatMessage.create = function create(properties) {
            return new ChatMessage(properties);
        };

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encode
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.uuid != null && Object.hasOwnProperty.call(message, "uuid"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.uuid);
            if (message.playerId != null && Object.hasOwnProperty.call(message, "playerId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.playerId);
            if (message.content != null && Object.hasOwnProperty.call(message, "content"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.content);
            if (message.replyTo != null && Object.hasOwnProperty.call(message, "replyTo"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.replyTo);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 5, wireType 0 =*/40).int64(message.timestamp);
            if (message.tabType != null && Object.hasOwnProperty.call(message, "tabType"))
                writer.uint32(/* id 6, wireType 0 =*/48).int32(message.tabType);
            if (message.isAdmin != null && Object.hasOwnProperty.call(message, "isAdmin"))
                writer.uint32(/* id 7, wireType 0 =*/56).bool(message.isAdmin);
            if (message.isTelegram != null && Object.hasOwnProperty.call(message, "isTelegram"))
                writer.uint32(/* id 8, wireType 0 =*/64).bool(message.isTelegram);
            if (message.isSystem != null && Object.hasOwnProperty.call(message, "isSystem"))
                writer.uint32(/* id 9, wireType 0 =*/72).bool(message.isSystem);
            return writer;
        };

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {game.IChatMessage} message ChatMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.uuid = reader.string();
                        break;
                    }
                case 2: {
                        message.playerId = reader.string();
                        break;
                    }
                case 3: {
                        message.content = reader.string();
                        break;
                    }
                case 4: {
                        message.replyTo = reader.string();
                        break;
                    }
                case 5: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 6: {
                        message.tabType = reader.int32();
                        break;
                    }
                case 7: {
                        message.isAdmin = reader.bool();
                        break;
                    }
                case 8: {
                        message.isTelegram = reader.bool();
                        break;
                    }
                case 9: {
                        message.isSystem = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatMessage} ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatMessage message.
         * @function verify
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                if (!$util.isString(message.uuid))
                    return "uuid: string expected";
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                if (!$util.isString(message.playerId))
                    return "playerId: string expected";
            if (message.content != null && message.hasOwnProperty("content"))
                if (!$util.isString(message.content))
                    return "content: string expected";
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                if (!$util.isString(message.replyTo))
                    return "replyTo: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                if (!$util.isInteger(message.tabType))
                    return "tabType: integer expected";
            if (message.isAdmin != null && message.hasOwnProperty("isAdmin"))
                if (typeof message.isAdmin !== "boolean")
                    return "isAdmin: boolean expected";
            if (message.isTelegram != null && message.hasOwnProperty("isTelegram"))
                if (typeof message.isTelegram !== "boolean")
                    return "isTelegram: boolean expected";
            if (message.isSystem != null && message.hasOwnProperty("isSystem"))
                if (typeof message.isSystem !== "boolean")
                    return "isSystem: boolean expected";
            return null;
        };

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatMessage} ChatMessage
         */
        ChatMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatMessage)
                return object;
            var message = new $root.game.ChatMessage();
            if (object.uuid != null)
                message.uuid = String(object.uuid);
            if (object.playerId != null)
                message.playerId = String(object.playerId);
            if (object.content != null)
                message.content = String(object.content);
            if (object.replyTo != null)
                message.replyTo = String(object.replyTo);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.tabType != null)
                message.tabType = object.tabType | 0;
            if (object.isAdmin != null)
                message.isAdmin = Boolean(object.isAdmin);
            if (object.isTelegram != null)
                message.isTelegram = Boolean(object.isTelegram);
            if (object.isSystem != null)
                message.isSystem = Boolean(object.isSystem);
            return message;
        };

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatMessage
         * @static
         * @param {game.ChatMessage} message ChatMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.uuid = "";
                object.playerId = "";
                object.content = "";
                object.replyTo = "";
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.tabType = 0;
                object.isAdmin = false;
                object.isTelegram = false;
                object.isSystem = false;
            }
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                object.uuid = message.uuid;
            if (message.playerId != null && message.hasOwnProperty("playerId"))
                object.playerId = message.playerId;
            if (message.content != null && message.hasOwnProperty("content"))
                object.content = message.content;
            if (message.replyTo != null && message.hasOwnProperty("replyTo"))
                object.replyTo = message.replyTo;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                object.tabType = message.tabType;
            if (message.isAdmin != null && message.hasOwnProperty("isAdmin"))
                object.isAdmin = message.isAdmin;
            if (message.isTelegram != null && message.hasOwnProperty("isTelegram"))
                object.isTelegram = message.isTelegram;
            if (message.isSystem != null && message.hasOwnProperty("isSystem"))
                object.isSystem = message.isSystem;
            return object;
        };

        /**
         * Converts this ChatMessage to JSON.
         * @function toJSON
         * @memberof game.ChatMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatMessage
         * @function getTypeUrl
         * @memberof game.ChatMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatMessage";
        };

        return ChatMessage;
    })();

    game.CommonMessage = (function() {

        /**
         * Properties of a CommonMessage.
         * @memberof game
         * @interface ICommonMessage
         * @property {Array.<string>|null} [messageList] CommonMessage messageList
         */

        /**
         * Constructs a new CommonMessage.
         * @memberof game
         * @classdesc Represents a CommonMessage.
         * @implements ICommonMessage
         * @constructor
         * @param {game.ICommonMessage=} [properties] Properties to set
         */
        function CommonMessage(properties) {
            this.messageList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * CommonMessage messageList.
         * @member {Array.<string>} messageList
         * @memberof game.CommonMessage
         * @instance
         */
        CommonMessage.prototype.messageList = $util.emptyArray;

        /**
         * Creates a new CommonMessage instance using the specified properties.
         * @function create
         * @memberof game.CommonMessage
         * @static
         * @param {game.ICommonMessage=} [properties] Properties to set
         * @returns {game.CommonMessage} CommonMessage instance
         */
        CommonMessage.create = function create(properties) {
            return new CommonMessage(properties);
        };

        /**
         * Encodes the specified CommonMessage message. Does not implicitly {@link game.CommonMessage.verify|verify} messages.
         * @function encode
         * @memberof game.CommonMessage
         * @static
         * @param {game.ICommonMessage} message CommonMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        CommonMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.messageList != null && message.messageList.length)
                for (var i = 0; i < message.messageList.length; ++i)
                    writer.uint32(/* id 1, wireType 2 =*/10).string(message.messageList[i]);
            return writer;
        };

        /**
         * Encodes the specified CommonMessage message, length delimited. Does not implicitly {@link game.CommonMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.CommonMessage
         * @static
         * @param {game.ICommonMessage} message CommonMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        CommonMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a CommonMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.CommonMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.CommonMessage} CommonMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        CommonMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.CommonMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.messageList && message.messageList.length))
                            message.messageList = [];
                        message.messageList.push(reader.string());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a CommonMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.CommonMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.CommonMessage} CommonMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        CommonMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a CommonMessage message.
         * @function verify
         * @memberof game.CommonMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        CommonMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.messageList != null && message.hasOwnProperty("messageList")) {
                if (!Array.isArray(message.messageList))
                    return "messageList: array expected";
                for (var i = 0; i < message.messageList.length; ++i)
                    if (!$util.isString(message.messageList[i]))
                        return "messageList: string[] expected";
            }
            return null;
        };

        /**
         * Creates a CommonMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.CommonMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.CommonMessage} CommonMessage
         */
        CommonMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.CommonMessage)
                return object;
            var message = new $root.game.CommonMessage();
            if (object.messageList) {
                if (!Array.isArray(object.messageList))
                    throw TypeError(".game.CommonMessage.messageList: array expected");
                message.messageList = [];
                for (var i = 0; i < object.messageList.length; ++i)
                    message.messageList[i] = String(object.messageList[i]);
            }
            return message;
        };

        /**
         * Creates a plain object from a CommonMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.CommonMessage
         * @static
         * @param {game.CommonMessage} message CommonMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        CommonMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.messageList = [];
            if (message.messageList && message.messageList.length) {
                object.messageList = [];
                for (var j = 0; j < message.messageList.length; ++j)
                    object.messageList[j] = message.messageList[j];
            }
            return object;
        };

        /**
         * Converts this CommonMessage to JSON.
         * @function toJSON
         * @memberof game.CommonMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        CommonMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for CommonMessage
         * @function getTypeUrl
         * @memberof game.CommonMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        CommonMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.CommonMessage";
        };

        return CommonMessage;
    })();

    game.ChatMessageDelete = (function() {

        /**
         * Properties of a ChatMessageDelete.
         * @memberof game
         * @interface IChatMessageDelete
         * @property {string|null} [uuid] ChatMessageDelete uuid
         * @property {number|null} [tabType] ChatMessageDelete tabType
         */

        /**
         * Constructs a new ChatMessageDelete.
         * @memberof game
         * @classdesc Represents a ChatMessageDelete.
         * @implements IChatMessageDelete
         * @constructor
         * @param {game.IChatMessageDelete=} [properties] Properties to set
         */
        function ChatMessageDelete(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ChatMessageDelete uuid.
         * @member {string} uuid
         * @memberof game.ChatMessageDelete
         * @instance
         */
        ChatMessageDelete.prototype.uuid = "";

        /**
         * ChatMessageDelete tabType.
         * @member {number} tabType
         * @memberof game.ChatMessageDelete
         * @instance
         */
        ChatMessageDelete.prototype.tabType = 0;

        /**
         * Creates a new ChatMessageDelete instance using the specified properties.
         * @function create
         * @memberof game.ChatMessageDelete
         * @static
         * @param {game.IChatMessageDelete=} [properties] Properties to set
         * @returns {game.ChatMessageDelete} ChatMessageDelete instance
         */
        ChatMessageDelete.create = function create(properties) {
            return new ChatMessageDelete(properties);
        };

        /**
         * Encodes the specified ChatMessageDelete message. Does not implicitly {@link game.ChatMessageDelete.verify|verify} messages.
         * @function encode
         * @memberof game.ChatMessageDelete
         * @static
         * @param {game.IChatMessageDelete} message ChatMessageDelete message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessageDelete.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.uuid != null && Object.hasOwnProperty.call(message, "uuid"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.uuid);
            if (message.tabType != null && Object.hasOwnProperty.call(message, "tabType"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.tabType);
            return writer;
        };

        /**
         * Encodes the specified ChatMessageDelete message, length delimited. Does not implicitly {@link game.ChatMessageDelete.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ChatMessageDelete
         * @static
         * @param {game.IChatMessageDelete} message ChatMessageDelete message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ChatMessageDelete.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ChatMessageDelete message from the specified reader or buffer.
         * @function decode
         * @memberof game.ChatMessageDelete
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ChatMessageDelete} ChatMessageDelete
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessageDelete.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ChatMessageDelete();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.uuid = reader.string();
                        break;
                    }
                case 2: {
                        message.tabType = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ChatMessageDelete message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ChatMessageDelete
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ChatMessageDelete} ChatMessageDelete
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ChatMessageDelete.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ChatMessageDelete message.
         * @function verify
         * @memberof game.ChatMessageDelete
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ChatMessageDelete.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                if (!$util.isString(message.uuid))
                    return "uuid: string expected";
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                if (!$util.isInteger(message.tabType))
                    return "tabType: integer expected";
            return null;
        };

        /**
         * Creates a ChatMessageDelete message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ChatMessageDelete
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ChatMessageDelete} ChatMessageDelete
         */
        ChatMessageDelete.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ChatMessageDelete)
                return object;
            var message = new $root.game.ChatMessageDelete();
            if (object.uuid != null)
                message.uuid = String(object.uuid);
            if (object.tabType != null)
                message.tabType = object.tabType | 0;
            return message;
        };

        /**
         * Creates a plain object from a ChatMessageDelete message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ChatMessageDelete
         * @static
         * @param {game.ChatMessageDelete} message ChatMessageDelete
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ChatMessageDelete.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.uuid = "";
                object.tabType = 0;
            }
            if (message.uuid != null && message.hasOwnProperty("uuid"))
                object.uuid = message.uuid;
            if (message.tabType != null && message.hasOwnProperty("tabType"))
                object.tabType = message.tabType;
            return object;
        };

        /**
         * Converts this ChatMessageDelete to JSON.
         * @function toJSON
         * @memberof game.ChatMessageDelete
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ChatMessageDelete.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ChatMessageDelete
         * @function getTypeUrl
         * @memberof game.ChatMessageDelete
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ChatMessageDelete.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ChatMessageDelete";
        };

        return ChatMessageDelete;
    })();

    game.RedPacket = (function() {

        /**
         * Properties of a RedPacket.
         * @memberof game
         * @interface IRedPacket
         * @property {string|null} [redPacketRecordId] RedPacket redPacketRecordId
         * @property {string|null} [createAddress] RedPacket createAddress
         */

        /**
         * Constructs a new RedPacket.
         * @memberof game
         * @classdesc Represents a RedPacket.
         * @implements IRedPacket
         * @constructor
         * @param {game.IRedPacket=} [properties] Properties to set
         */
        function RedPacket(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacket redPacketRecordId.
         * @member {string} redPacketRecordId
         * @memberof game.RedPacket
         * @instance
         */
        RedPacket.prototype.redPacketRecordId = "";

        /**
         * RedPacket createAddress.
         * @member {string} createAddress
         * @memberof game.RedPacket
         * @instance
         */
        RedPacket.prototype.createAddress = "";

        /**
         * Creates a new RedPacket instance using the specified properties.
         * @function create
         * @memberof game.RedPacket
         * @static
         * @param {game.IRedPacket=} [properties] Properties to set
         * @returns {game.RedPacket} RedPacket instance
         */
        RedPacket.create = function create(properties) {
            return new RedPacket(properties);
        };

        /**
         * Encodes the specified RedPacket message. Does not implicitly {@link game.RedPacket.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacket
         * @static
         * @param {game.IRedPacket} message RedPacket message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacket.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.redPacketRecordId != null && Object.hasOwnProperty.call(message, "redPacketRecordId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.redPacketRecordId);
            if (message.createAddress != null && Object.hasOwnProperty.call(message, "createAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.createAddress);
            return writer;
        };

        /**
         * Encodes the specified RedPacket message, length delimited. Does not implicitly {@link game.RedPacket.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacket
         * @static
         * @param {game.IRedPacket} message RedPacket message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacket.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacket message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacket
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacket} RedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacket.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacket();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.redPacketRecordId = reader.string();
                        break;
                    }
                case 2: {
                        message.createAddress = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacket message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacket
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacket} RedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacket.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacket message.
         * @function verify
         * @memberof game.RedPacket
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacket.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.redPacketRecordId != null && message.hasOwnProperty("redPacketRecordId"))
                if (!$util.isString(message.redPacketRecordId))
                    return "redPacketRecordId: string expected";
            if (message.createAddress != null && message.hasOwnProperty("createAddress"))
                if (!$util.isString(message.createAddress))
                    return "createAddress: string expected";
            return null;
        };

        /**
         * Creates a RedPacket message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacket
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacket} RedPacket
         */
        RedPacket.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacket)
                return object;
            var message = new $root.game.RedPacket();
            if (object.redPacketRecordId != null)
                message.redPacketRecordId = String(object.redPacketRecordId);
            if (object.createAddress != null)
                message.createAddress = String(object.createAddress);
            return message;
        };

        /**
         * Creates a plain object from a RedPacket message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacket
         * @static
         * @param {game.RedPacket} message RedPacket
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacket.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.redPacketRecordId = "";
                object.createAddress = "";
            }
            if (message.redPacketRecordId != null && message.hasOwnProperty("redPacketRecordId"))
                object.redPacketRecordId = message.redPacketRecordId;
            if (message.createAddress != null && message.hasOwnProperty("createAddress"))
                object.createAddress = message.createAddress;
            return object;
        };

        /**
         * Converts this RedPacket to JSON.
         * @function toJSON
         * @memberof game.RedPacket
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacket.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacket
         * @function getTypeUrl
         * @memberof game.RedPacket
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacket.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacket";
        };

        return RedPacket;
    })();

    game.RedPacketPoint = (function() {

        /**
         * Properties of a RedPacketPoint.
         * @memberof game
         * @interface IRedPacketPoint
         * @property {number|null} [configId] RedPacketPoint configId
         * @property {Array.<game.IRedPacket>|null} [redPacketList] RedPacketPoint redPacketList
         */

        /**
         * Constructs a new RedPacketPoint.
         * @memberof game
         * @classdesc Represents a RedPacketPoint.
         * @implements IRedPacketPoint
         * @constructor
         * @param {game.IRedPacketPoint=} [properties] Properties to set
         */
        function RedPacketPoint(properties) {
            this.redPacketList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketPoint configId.
         * @member {number} configId
         * @memberof game.RedPacketPoint
         * @instance
         */
        RedPacketPoint.prototype.configId = 0;

        /**
         * RedPacketPoint redPacketList.
         * @member {Array.<game.IRedPacket>} redPacketList
         * @memberof game.RedPacketPoint
         * @instance
         */
        RedPacketPoint.prototype.redPacketList = $util.emptyArray;

        /**
         * Creates a new RedPacketPoint instance using the specified properties.
         * @function create
         * @memberof game.RedPacketPoint
         * @static
         * @param {game.IRedPacketPoint=} [properties] Properties to set
         * @returns {game.RedPacketPoint} RedPacketPoint instance
         */
        RedPacketPoint.create = function create(properties) {
            return new RedPacketPoint(properties);
        };

        /**
         * Encodes the specified RedPacketPoint message. Does not implicitly {@link game.RedPacketPoint.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketPoint
         * @static
         * @param {game.IRedPacketPoint} message RedPacketPoint message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketPoint.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.configId != null && Object.hasOwnProperty.call(message, "configId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.configId);
            if (message.redPacketList != null && message.redPacketList.length)
                for (var i = 0; i < message.redPacketList.length; ++i)
                    $root.game.RedPacket.encode(message.redPacketList[i], writer.uint32(/* id 2, wireType 2 =*/18).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified RedPacketPoint message, length delimited. Does not implicitly {@link game.RedPacketPoint.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketPoint
         * @static
         * @param {game.IRedPacketPoint} message RedPacketPoint message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketPoint.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketPoint message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketPoint
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketPoint} RedPacketPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketPoint.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketPoint();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.configId = reader.int32();
                        break;
                    }
                case 2: {
                        if (!(message.redPacketList && message.redPacketList.length))
                            message.redPacketList = [];
                        message.redPacketList.push($root.game.RedPacket.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketPoint message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketPoint
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketPoint} RedPacketPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketPoint.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketPoint message.
         * @function verify
         * @memberof game.RedPacketPoint
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketPoint.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.configId != null && message.hasOwnProperty("configId"))
                if (!$util.isInteger(message.configId))
                    return "configId: integer expected";
            if (message.redPacketList != null && message.hasOwnProperty("redPacketList")) {
                if (!Array.isArray(message.redPacketList))
                    return "redPacketList: array expected";
                for (var i = 0; i < message.redPacketList.length; ++i) {
                    var error = $root.game.RedPacket.verify(message.redPacketList[i]);
                    if (error)
                        return "redPacketList." + error;
                }
            }
            return null;
        };

        /**
         * Creates a RedPacketPoint message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketPoint
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketPoint} RedPacketPoint
         */
        RedPacketPoint.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketPoint)
                return object;
            var message = new $root.game.RedPacketPoint();
            if (object.configId != null)
                message.configId = object.configId | 0;
            if (object.redPacketList) {
                if (!Array.isArray(object.redPacketList))
                    throw TypeError(".game.RedPacketPoint.redPacketList: array expected");
                message.redPacketList = [];
                for (var i = 0; i < object.redPacketList.length; ++i) {
                    if (typeof object.redPacketList[i] !== "object")
                        throw TypeError(".game.RedPacketPoint.redPacketList: object expected");
                    message.redPacketList[i] = $root.game.RedPacket.fromObject(object.redPacketList[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from a RedPacketPoint message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketPoint
         * @static
         * @param {game.RedPacketPoint} message RedPacketPoint
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketPoint.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.redPacketList = [];
            if (options.defaults)
                object.configId = 0;
            if (message.configId != null && message.hasOwnProperty("configId"))
                object.configId = message.configId;
            if (message.redPacketList && message.redPacketList.length) {
                object.redPacketList = [];
                for (var j = 0; j < message.redPacketList.length; ++j)
                    object.redPacketList[j] = $root.game.RedPacket.toObject(message.redPacketList[j], options);
            }
            return object;
        };

        /**
         * Converts this RedPacketPoint to JSON.
         * @function toJSON
         * @memberof game.RedPacketPoint
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketPoint.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketPoint
         * @function getTypeUrl
         * @memberof game.RedPacketPoint
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketPoint.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketPoint";
        };

        return RedPacketPoint;
    })();

    game.RedPacketUpdate = (function() {

        /**
         * Properties of a RedPacketUpdate.
         * @memberof game
         * @interface IRedPacketUpdate
         * @property {Array.<game.IRedPacketPoint>|null} [pointList] RedPacketUpdate pointList
         */

        /**
         * Constructs a new RedPacketUpdate.
         * @memberof game
         * @classdesc Represents a RedPacketUpdate.
         * @implements IRedPacketUpdate
         * @constructor
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         */
        function RedPacketUpdate(properties) {
            this.pointList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketUpdate pointList.
         * @member {Array.<game.IRedPacketPoint>} pointList
         * @memberof game.RedPacketUpdate
         * @instance
         */
        RedPacketUpdate.prototype.pointList = $util.emptyArray;

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @function create
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate=} [properties] Properties to set
         * @returns {game.RedPacketUpdate} RedPacketUpdate instance
         */
        RedPacketUpdate.create = function create(properties) {
            return new RedPacketUpdate(properties);
        };

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pointList != null && message.pointList.length)
                for (var i = 0; i < message.pointList.length; ++i)
                    $root.game.RedPacketPoint.encode(message.pointList[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.IRedPacketUpdate} message RedPacketUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.pointList && message.pointList.length))
                            message.pointList = [];
                        message.pointList.push($root.game.RedPacketPoint.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketUpdate message.
         * @function verify
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pointList != null && message.hasOwnProperty("pointList")) {
                if (!Array.isArray(message.pointList))
                    return "pointList: array expected";
                for (var i = 0; i < message.pointList.length; ++i) {
                    var error = $root.game.RedPacketPoint.verify(message.pointList[i]);
                    if (error)
                        return "pointList." + error;
                }
            }
            return null;
        };

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketUpdate} RedPacketUpdate
         */
        RedPacketUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketUpdate)
                return object;
            var message = new $root.game.RedPacketUpdate();
            if (object.pointList) {
                if (!Array.isArray(object.pointList))
                    throw TypeError(".game.RedPacketUpdate.pointList: array expected");
                message.pointList = [];
                for (var i = 0; i < object.pointList.length; ++i) {
                    if (typeof object.pointList[i] !== "object")
                        throw TypeError(".game.RedPacketUpdate.pointList: object expected");
                    message.pointList[i] = $root.game.RedPacketPoint.fromObject(object.pointList[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketUpdate
         * @static
         * @param {game.RedPacketUpdate} message RedPacketUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.pointList = [];
            if (message.pointList && message.pointList.length) {
                object.pointList = [];
                for (var j = 0; j < message.pointList.length; ++j)
                    object.pointList[j] = $root.game.RedPacketPoint.toObject(message.pointList[j], options);
            }
            return object;
        };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @function toJSON
         * @memberof game.RedPacketUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketUpdate
         * @function getTypeUrl
         * @memberof game.RedPacketUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketUpdate";
        };

        return RedPacketUpdate;
    })();

    game.RedPacketCache = (function() {

        /**
         * Properties of a RedPacketCache.
         * @memberof game
         * @interface IRedPacketCache
         * @property {Array.<string>|null} [pickedList] RedPacketCache pickedList
         */

        /**
         * Constructs a new RedPacketCache.
         * @memberof game
         * @classdesc Represents a RedPacketCache.
         * @implements IRedPacketCache
         * @constructor
         * @param {game.IRedPacketCache=} [properties] Properties to set
         */
        function RedPacketCache(properties) {
            this.pickedList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketCache pickedList.
         * @member {Array.<string>} pickedList
         * @memberof game.RedPacketCache
         * @instance
         */
        RedPacketCache.prototype.pickedList = $util.emptyArray;

        /**
         * Creates a new RedPacketCache instance using the specified properties.
         * @function create
         * @memberof game.RedPacketCache
         * @static
         * @param {game.IRedPacketCache=} [properties] Properties to set
         * @returns {game.RedPacketCache} RedPacketCache instance
         */
        RedPacketCache.create = function create(properties) {
            return new RedPacketCache(properties);
        };

        /**
         * Encodes the specified RedPacketCache message. Does not implicitly {@link game.RedPacketCache.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketCache
         * @static
         * @param {game.IRedPacketCache} message RedPacketCache message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketCache.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.pickedList != null && message.pickedList.length)
                for (var i = 0; i < message.pickedList.length; ++i)
                    writer.uint32(/* id 1, wireType 2 =*/10).string(message.pickedList[i]);
            return writer;
        };

        /**
         * Encodes the specified RedPacketCache message, length delimited. Does not implicitly {@link game.RedPacketCache.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketCache
         * @static
         * @param {game.IRedPacketCache} message RedPacketCache message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketCache.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketCache message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketCache
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketCache} RedPacketCache
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketCache.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketCache();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.pickedList && message.pickedList.length))
                            message.pickedList = [];
                        message.pickedList.push(reader.string());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketCache message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketCache
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketCache} RedPacketCache
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketCache.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketCache message.
         * @function verify
         * @memberof game.RedPacketCache
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketCache.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.pickedList != null && message.hasOwnProperty("pickedList")) {
                if (!Array.isArray(message.pickedList))
                    return "pickedList: array expected";
                for (var i = 0; i < message.pickedList.length; ++i)
                    if (!$util.isString(message.pickedList[i]))
                        return "pickedList: string[] expected";
            }
            return null;
        };

        /**
         * Creates a RedPacketCache message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketCache
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketCache} RedPacketCache
         */
        RedPacketCache.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketCache)
                return object;
            var message = new $root.game.RedPacketCache();
            if (object.pickedList) {
                if (!Array.isArray(object.pickedList))
                    throw TypeError(".game.RedPacketCache.pickedList: array expected");
                message.pickedList = [];
                for (var i = 0; i < object.pickedList.length; ++i)
                    message.pickedList[i] = String(object.pickedList[i]);
            }
            return message;
        };

        /**
         * Creates a plain object from a RedPacketCache message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketCache
         * @static
         * @param {game.RedPacketCache} message RedPacketCache
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketCache.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.pickedList = [];
            if (message.pickedList && message.pickedList.length) {
                object.pickedList = [];
                for (var j = 0; j < message.pickedList.length; ++j)
                    object.pickedList[j] = message.pickedList[j];
            }
            return object;
        };

        /**
         * Converts this RedPacketCache to JSON.
         * @function toJSON
         * @memberof game.RedPacketCache
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketCache.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketCache
         * @function getTypeUrl
         * @memberof game.RedPacketCache
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketCache.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketCache";
        };

        return RedPacketCache;
    })();

    game.RedPacketReward = (function() {

        /**
         * Properties of a RedPacketReward.
         * @memberof game
         * @interface IRedPacketReward
         * @property {string|null} [packetId] RedPacketReward packetId
         * @property {string|null} [receiverAddress] RedPacketReward receiverAddress
         * @property {number|null} [amount] RedPacketReward amount
         * @property {number|Long|null} [receivedAt] RedPacketReward receivedAt
         */

        /**
         * Constructs a new RedPacketReward.
         * @memberof game
         * @classdesc Represents a RedPacketReward.
         * @implements IRedPacketReward
         * @constructor
         * @param {game.IRedPacketReward=} [properties] Properties to set
         */
        function RedPacketReward(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RedPacketReward packetId.
         * @member {string} packetId
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.packetId = "";

        /**
         * RedPacketReward receiverAddress.
         * @member {string} receiverAddress
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receiverAddress = "";

        /**
         * RedPacketReward amount.
         * @member {number} amount
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.amount = 0;

        /**
         * RedPacketReward receivedAt.
         * @member {number|Long} receivedAt
         * @memberof game.RedPacketReward
         * @instance
         */
        RedPacketReward.prototype.receivedAt = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @function create
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward=} [properties] Properties to set
         * @returns {game.RedPacketReward} RedPacketReward instance
         */
        RedPacketReward.create = function create(properties) {
            return new RedPacketReward(properties);
        };

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encode
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.packetId != null && Object.hasOwnProperty.call(message, "packetId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.packetId);
            if (message.receiverAddress != null && Object.hasOwnProperty.call(message, "receiverAddress"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.receiverAddress);
            if (message.amount != null && Object.hasOwnProperty.call(message, "amount"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.amount);
            if (message.receivedAt != null && Object.hasOwnProperty.call(message, "receivedAt"))
                writer.uint32(/* id 4, wireType 0 =*/32).int64(message.receivedAt);
            return writer;
        };

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {game.IRedPacketReward} message RedPacketReward message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RedPacketReward.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @function decode
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RedPacketReward();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.packetId = reader.string();
                        break;
                    }
                case 2: {
                        message.receiverAddress = reader.string();
                        break;
                    }
                case 3: {
                        message.amount = reader.float();
                        break;
                    }
                case 4: {
                        message.receivedAt = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RedPacketReward
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RedPacketReward} RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RedPacketReward.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RedPacketReward message.
         * @function verify
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RedPacketReward.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                if (!$util.isString(message.packetId))
                    return "packetId: string expected";
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                if (!$util.isString(message.receiverAddress))
                    return "receiverAddress: string expected";
            if (message.amount != null && message.hasOwnProperty("amount"))
                if (typeof message.amount !== "number")
                    return "amount: number expected";
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (!$util.isInteger(message.receivedAt) && !(message.receivedAt && $util.isInteger(message.receivedAt.low) && $util.isInteger(message.receivedAt.high)))
                    return "receivedAt: integer|Long expected";
            return null;
        };

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RedPacketReward
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RedPacketReward} RedPacketReward
         */
        RedPacketReward.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RedPacketReward)
                return object;
            var message = new $root.game.RedPacketReward();
            if (object.packetId != null)
                message.packetId = String(object.packetId);
            if (object.receiverAddress != null)
                message.receiverAddress = String(object.receiverAddress);
            if (object.amount != null)
                message.amount = Number(object.amount);
            if (object.receivedAt != null)
                if ($util.Long)
                    (message.receivedAt = $util.Long.fromValue(object.receivedAt)).unsigned = false;
                else if (typeof object.receivedAt === "string")
                    message.receivedAt = parseInt(object.receivedAt, 10);
                else if (typeof object.receivedAt === "number")
                    message.receivedAt = object.receivedAt;
                else if (typeof object.receivedAt === "object")
                    message.receivedAt = new $util.LongBits(object.receivedAt.low >>> 0, object.receivedAt.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RedPacketReward
         * @static
         * @param {game.RedPacketReward} message RedPacketReward
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RedPacketReward.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.packetId = "";
                object.receiverAddress = "";
                object.amount = 0;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.receivedAt = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.receivedAt = options.longs === String ? "0" : 0;
            }
            if (message.packetId != null && message.hasOwnProperty("packetId"))
                object.packetId = message.packetId;
            if (message.receiverAddress != null && message.hasOwnProperty("receiverAddress"))
                object.receiverAddress = message.receiverAddress;
            if (message.amount != null && message.hasOwnProperty("amount"))
                object.amount = options.json && !isFinite(message.amount) ? String(message.amount) : message.amount;
            if (message.receivedAt != null && message.hasOwnProperty("receivedAt"))
                if (typeof message.receivedAt === "number")
                    object.receivedAt = options.longs === String ? String(message.receivedAt) : message.receivedAt;
                else
                    object.receivedAt = options.longs === String ? $util.Long.prototype.toString.call(message.receivedAt) : options.longs === Number ? new $util.LongBits(message.receivedAt.low >>> 0, message.receivedAt.high >>> 0).toNumber() : message.receivedAt;
            return object;
        };

        /**
         * Converts this RedPacketReward to JSON.
         * @function toJSON
         * @memberof game.RedPacketReward
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RedPacketReward.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RedPacketReward
         * @function getTypeUrl
         * @memberof game.RedPacketReward
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RedPacketReward.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RedPacketReward";
        };

        return RedPacketReward;
    })();

    game.ClientPlayerPosition = (function() {

        /**
         * Properties of a ClientPlayerPosition.
         * @memberof game
         * @interface IClientPlayerPosition
         * @property {number|null} [x] ClientPlayerPosition x
         * @property {number|null} [y] ClientPlayerPosition y
         * @property {number|null} [z] ClientPlayerPosition z
         * @property {number|null} [rotationX] ClientPlayerPosition rotationX
         * @property {number|null} [rotationY] ClientPlayerPosition rotationY
         * @property {number|null} [rotationZ] ClientPlayerPosition rotationZ
         * @property {number|null} [rotationW] ClientPlayerPosition rotationW
         */

        /**
         * Constructs a new ClientPlayerPosition.
         * @memberof game
         * @classdesc Represents a ClientPlayerPosition.
         * @implements IClientPlayerPosition
         * @constructor
         * @param {game.IClientPlayerPosition=} [properties] Properties to set
         */
        function ClientPlayerPosition(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerPosition x.
         * @member {number} x
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.x = 0;

        /**
         * ClientPlayerPosition y.
         * @member {number} y
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.y = 0;

        /**
         * ClientPlayerPosition z.
         * @member {number} z
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.z = 0;

        /**
         * ClientPlayerPosition rotationX.
         * @member {number} rotationX
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationX = 0;

        /**
         * ClientPlayerPosition rotationY.
         * @member {number} rotationY
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationY = 0;

        /**
         * ClientPlayerPosition rotationZ.
         * @member {number} rotationZ
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationZ = 0;

        /**
         * ClientPlayerPosition rotationW.
         * @member {number} rotationW
         * @memberof game.ClientPlayerPosition
         * @instance
         */
        ClientPlayerPosition.prototype.rotationW = 0;

        /**
         * Creates a new ClientPlayerPosition instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition=} [properties] Properties to set
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition instance
         */
        ClientPlayerPosition.create = function create(properties) {
            return new ClientPlayerPosition(properties);
        };

        /**
         * Encodes the specified ClientPlayerPosition message. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition} message ClientPlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerPosition.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.x != null && Object.hasOwnProperty.call(message, "x"))
                writer.uint32(/* id 1, wireType 5 =*/13).float(message.x);
            if (message.y != null && Object.hasOwnProperty.call(message, "y"))
                writer.uint32(/* id 2, wireType 5 =*/21).float(message.y);
            if (message.z != null && Object.hasOwnProperty.call(message, "z"))
                writer.uint32(/* id 3, wireType 5 =*/29).float(message.z);
            if (message.rotationX != null && Object.hasOwnProperty.call(message, "rotationX"))
                writer.uint32(/* id 4, wireType 5 =*/37).float(message.rotationX);
            if (message.rotationY != null && Object.hasOwnProperty.call(message, "rotationY"))
                writer.uint32(/* id 5, wireType 5 =*/45).float(message.rotationY);
            if (message.rotationZ != null && Object.hasOwnProperty.call(message, "rotationZ"))
                writer.uint32(/* id 6, wireType 5 =*/53).float(message.rotationZ);
            if (message.rotationW != null && Object.hasOwnProperty.call(message, "rotationW"))
                writer.uint32(/* id 7, wireType 5 =*/61).float(message.rotationW);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerPosition message, length delimited. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.IClientPlayerPosition} message ClientPlayerPosition message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerPosition.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerPosition.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerPosition();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.x = reader.float();
                        break;
                    }
                case 2: {
                        message.y = reader.float();
                        break;
                    }
                case 3: {
                        message.z = reader.float();
                        break;
                    }
                case 4: {
                        message.rotationX = reader.float();
                        break;
                    }
                case 5: {
                        message.rotationY = reader.float();
                        break;
                    }
                case 6: {
                        message.rotationZ = reader.float();
                        break;
                    }
                case 7: {
                        message.rotationW = reader.float();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerPosition.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerPosition message.
         * @function verify
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerPosition.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.x != null && message.hasOwnProperty("x"))
                if (typeof message.x !== "number")
                    return "x: number expected";
            if (message.y != null && message.hasOwnProperty("y"))
                if (typeof message.y !== "number")
                    return "y: number expected";
            if (message.z != null && message.hasOwnProperty("z"))
                if (typeof message.z !== "number")
                    return "z: number expected";
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                if (typeof message.rotationX !== "number")
                    return "rotationX: number expected";
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                if (typeof message.rotationY !== "number")
                    return "rotationY: number expected";
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                if (typeof message.rotationZ !== "number")
                    return "rotationZ: number expected";
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                if (typeof message.rotationW !== "number")
                    return "rotationW: number expected";
            return null;
        };

        /**
         * Creates a ClientPlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerPosition} ClientPlayerPosition
         */
        ClientPlayerPosition.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerPosition)
                return object;
            var message = new $root.game.ClientPlayerPosition();
            if (object.x != null)
                message.x = Number(object.x);
            if (object.y != null)
                message.y = Number(object.y);
            if (object.z != null)
                message.z = Number(object.z);
            if (object.rotationX != null)
                message.rotationX = Number(object.rotationX);
            if (object.rotationY != null)
                message.rotationY = Number(object.rotationY);
            if (object.rotationZ != null)
                message.rotationZ = Number(object.rotationZ);
            if (object.rotationW != null)
                message.rotationW = Number(object.rotationW);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerPosition message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {game.ClientPlayerPosition} message ClientPlayerPosition
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerPosition.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.x = 0;
                object.y = 0;
                object.z = 0;
                object.rotationX = 0;
                object.rotationY = 0;
                object.rotationZ = 0;
                object.rotationW = 0;
            }
            if (message.x != null && message.hasOwnProperty("x"))
                object.x = options.json && !isFinite(message.x) ? String(message.x) : message.x;
            if (message.y != null && message.hasOwnProperty("y"))
                object.y = options.json && !isFinite(message.y) ? String(message.y) : message.y;
            if (message.z != null && message.hasOwnProperty("z"))
                object.z = options.json && !isFinite(message.z) ? String(message.z) : message.z;
            if (message.rotationX != null && message.hasOwnProperty("rotationX"))
                object.rotationX = options.json && !isFinite(message.rotationX) ? String(message.rotationX) : message.rotationX;
            if (message.rotationY != null && message.hasOwnProperty("rotationY"))
                object.rotationY = options.json && !isFinite(message.rotationY) ? String(message.rotationY) : message.rotationY;
            if (message.rotationZ != null && message.hasOwnProperty("rotationZ"))
                object.rotationZ = options.json && !isFinite(message.rotationZ) ? String(message.rotationZ) : message.rotationZ;
            if (message.rotationW != null && message.hasOwnProperty("rotationW"))
                object.rotationW = options.json && !isFinite(message.rotationW) ? String(message.rotationW) : message.rotationW;
            return object;
        };

        /**
         * Converts this ClientPlayerPosition to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerPosition
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerPosition.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerPosition
         * @function getTypeUrl
         * @memberof game.ClientPlayerPosition
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerPosition.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerPosition";
        };

        return ClientPlayerPosition;
    })();

    game.ClientPlayerAnimation = (function() {

        /**
         * Properties of a ClientPlayerAnimation.
         * @memberof game
         * @interface IClientPlayerAnimation
         * @property {string|null} [curAnimation] ClientPlayerAnimation curAnimation
         */

        /**
         * Constructs a new ClientPlayerAnimation.
         * @memberof game
         * @classdesc Represents a ClientPlayerAnimation.
         * @implements IClientPlayerAnimation
         * @constructor
         * @param {game.IClientPlayerAnimation=} [properties] Properties to set
         */
        function ClientPlayerAnimation(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerAnimation curAnimation.
         * @member {string} curAnimation
         * @memberof game.ClientPlayerAnimation
         * @instance
         */
        ClientPlayerAnimation.prototype.curAnimation = "";

        /**
         * Creates a new ClientPlayerAnimation instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation=} [properties] Properties to set
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation instance
         */
        ClientPlayerAnimation.create = function create(properties) {
            return new ClientPlayerAnimation(properties);
        };

        /**
         * Encodes the specified ClientPlayerAnimation message. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation} message ClientPlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerAnimation.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.curAnimation != null && Object.hasOwnProperty.call(message, "curAnimation"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.curAnimation);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerAnimation message, length delimited. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.IClientPlayerAnimation} message ClientPlayerAnimation message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerAnimation.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerAnimation.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerAnimation();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.curAnimation = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerAnimation.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerAnimation message.
         * @function verify
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerAnimation.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                if (!$util.isString(message.curAnimation))
                    return "curAnimation: string expected";
            return null;
        };

        /**
         * Creates a ClientPlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerAnimation} ClientPlayerAnimation
         */
        ClientPlayerAnimation.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerAnimation)
                return object;
            var message = new $root.game.ClientPlayerAnimation();
            if (object.curAnimation != null)
                message.curAnimation = String(object.curAnimation);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerAnimation message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {game.ClientPlayerAnimation} message ClientPlayerAnimation
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerAnimation.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.curAnimation = "";
            if (message.curAnimation != null && message.hasOwnProperty("curAnimation"))
                object.curAnimation = message.curAnimation;
            return object;
        };

        /**
         * Converts this ClientPlayerAnimation to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerAnimation
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerAnimation.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerAnimation
         * @function getTypeUrl
         * @memberof game.ClientPlayerAnimation
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerAnimation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerAnimation";
        };

        return ClientPlayerAnimation;
    })();

    game.ClientPlayerUpdate = (function() {

        /**
         * Properties of a ClientPlayerUpdate.
         * @memberof game
         * @interface IClientPlayerUpdate
         * @property {number|null} [itemId] ClientPlayerUpdate itemId
         * @property {number|null} [pizzaCount] ClientPlayerUpdate pizzaCount
         * @property {string|null} [petId] ClientPlayerUpdate petId
         * @property {string|null} [pizzaTick] ClientPlayerUpdate pizzaTick
         */

        /**
         * Constructs a new ClientPlayerUpdate.
         * @memberof game
         * @classdesc Represents a ClientPlayerUpdate.
         * @implements IClientPlayerUpdate
         * @constructor
         * @param {game.IClientPlayerUpdate=} [properties] Properties to set
         */
        function ClientPlayerUpdate(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerUpdate itemId.
         * @member {number} itemId
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.itemId = 0;

        /**
         * ClientPlayerUpdate pizzaCount.
         * @member {number} pizzaCount
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.pizzaCount = 0;

        /**
         * ClientPlayerUpdate petId.
         * @member {string} petId
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.petId = "";

        /**
         * ClientPlayerUpdate pizzaTick.
         * @member {string} pizzaTick
         * @memberof game.ClientPlayerUpdate
         * @instance
         */
        ClientPlayerUpdate.prototype.pizzaTick = "";

        /**
         * Creates a new ClientPlayerUpdate instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate=} [properties] Properties to set
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate instance
         */
        ClientPlayerUpdate.create = function create(properties) {
            return new ClientPlayerUpdate(properties);
        };

        /**
         * Encodes the specified ClientPlayerUpdate message. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate} message ClientPlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerUpdate.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.itemId != null && Object.hasOwnProperty.call(message, "itemId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.itemId);
            if (message.pizzaCount != null && Object.hasOwnProperty.call(message, "pizzaCount"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.pizzaCount);
            if (message.petId != null && Object.hasOwnProperty.call(message, "petId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.petId);
            if (message.pizzaTick != null && Object.hasOwnProperty.call(message, "pizzaTick"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.pizzaTick);
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerUpdate message, length delimited. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.IClientPlayerUpdate} message ClientPlayerUpdate message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerUpdate.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerUpdate.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerUpdate();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.itemId = reader.int32();
                        break;
                    }
                case 2: {
                        message.pizzaCount = reader.int32();
                        break;
                    }
                case 3: {
                        message.petId = reader.string();
                        break;
                    }
                case 4: {
                        message.pizzaTick = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerUpdate.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerUpdate message.
         * @function verify
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerUpdate.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                if (!$util.isInteger(message.itemId))
                    return "itemId: integer expected";
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                if (!$util.isInteger(message.pizzaCount))
                    return "pizzaCount: integer expected";
            if (message.petId != null && message.hasOwnProperty("petId"))
                if (!$util.isString(message.petId))
                    return "petId: string expected";
            if (message.pizzaTick != null && message.hasOwnProperty("pizzaTick"))
                if (!$util.isString(message.pizzaTick))
                    return "pizzaTick: string expected";
            return null;
        };

        /**
         * Creates a ClientPlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerUpdate} ClientPlayerUpdate
         */
        ClientPlayerUpdate.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerUpdate)
                return object;
            var message = new $root.game.ClientPlayerUpdate();
            if (object.itemId != null)
                message.itemId = object.itemId | 0;
            if (object.pizzaCount != null)
                message.pizzaCount = object.pizzaCount | 0;
            if (object.petId != null)
                message.petId = String(object.petId);
            if (object.pizzaTick != null)
                message.pizzaTick = String(object.pizzaTick);
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerUpdate message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {game.ClientPlayerUpdate} message ClientPlayerUpdate
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerUpdate.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.itemId = 0;
                object.pizzaCount = 0;
                object.petId = "";
                object.pizzaTick = "";
            }
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                object.itemId = message.itemId;
            if (message.pizzaCount != null && message.hasOwnProperty("pizzaCount"))
                object.pizzaCount = message.pizzaCount;
            if (message.petId != null && message.hasOwnProperty("petId"))
                object.petId = message.petId;
            if (message.pizzaTick != null && message.hasOwnProperty("pizzaTick"))
                object.pizzaTick = message.pizzaTick;
            return object;
        };

        /**
         * Converts this ClientPlayerUpdate to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerUpdate
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerUpdate.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerUpdate
         * @function getTypeUrl
         * @memberof game.ClientPlayerUpdate
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerUpdate.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerUpdate";
        };

        return ClientPlayerUpdate;
    })();

    game.ClientPlayerEnter = (function() {

        /**
         * Properties of a ClientPlayerEnter.
         * @memberof game
         * @interface IClientPlayerEnter
         * @property {game.IAvatarData|null} [avatarData] ClientPlayerEnter avatarData
         */

        /**
         * Constructs a new ClientPlayerEnter.
         * @memberof game
         * @classdesc Represents a ClientPlayerEnter.
         * @implements IClientPlayerEnter
         * @constructor
         * @param {game.IClientPlayerEnter=} [properties] Properties to set
         */
        function ClientPlayerEnter(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPlayerEnter avatarData.
         * @member {game.IAvatarData|null|undefined} avatarData
         * @memberof game.ClientPlayerEnter
         * @instance
         */
        ClientPlayerEnter.prototype.avatarData = null;

        /**
         * Creates a new ClientPlayerEnter instance using the specified properties.
         * @function create
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter=} [properties] Properties to set
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter instance
         */
        ClientPlayerEnter.create = function create(properties) {
            return new ClientPlayerEnter(properties);
        };

        /**
         * Encodes the specified ClientPlayerEnter message. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter} message ClientPlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerEnter.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.avatarData != null && Object.hasOwnProperty.call(message, "avatarData"))
                $root.game.AvatarData.encode(message.avatarData, writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified ClientPlayerEnter message, length delimited. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.IClientPlayerEnter} message ClientPlayerEnter message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPlayerEnter.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerEnter.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPlayerEnter();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.avatarData = $root.game.AvatarData.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPlayerEnter.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPlayerEnter message.
         * @function verify
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPlayerEnter.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.avatarData != null && message.hasOwnProperty("avatarData")) {
                var error = $root.game.AvatarData.verify(message.avatarData);
                if (error)
                    return "avatarData." + error;
            }
            return null;
        };

        /**
         * Creates a ClientPlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPlayerEnter} ClientPlayerEnter
         */
        ClientPlayerEnter.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPlayerEnter)
                return object;
            var message = new $root.game.ClientPlayerEnter();
            if (object.avatarData != null) {
                if (typeof object.avatarData !== "object")
                    throw TypeError(".game.ClientPlayerEnter.avatarData: object expected");
                message.avatarData = $root.game.AvatarData.fromObject(object.avatarData);
            }
            return message;
        };

        /**
         * Creates a plain object from a ClientPlayerEnter message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {game.ClientPlayerEnter} message ClientPlayerEnter
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPlayerEnter.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.avatarData = null;
            if (message.avatarData != null && message.hasOwnProperty("avatarData"))
                object.avatarData = $root.game.AvatarData.toObject(message.avatarData, options);
            return object;
        };

        /**
         * Converts this ClientPlayerEnter to JSON.
         * @function toJSON
         * @memberof game.ClientPlayerEnter
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPlayerEnter.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPlayerEnter
         * @function getTypeUrl
         * @memberof game.ClientPlayerEnter
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPlayerEnter.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPlayerEnter";
        };

        return ClientPlayerEnter;
    })();

    game.ReqPickUpRedPacket = (function() {

        /**
         * Properties of a ReqPickUpRedPacket.
         * @memberof game
         * @interface IReqPickUpRedPacket
         * @property {number|null} [configId] ReqPickUpRedPacket configId
         * @property {string|null} [redPacketRecordId] ReqPickUpRedPacket redPacketRecordId
         */

        /**
         * Constructs a new ReqPickUpRedPacket.
         * @memberof game
         * @classdesc Represents a ReqPickUpRedPacket.
         * @implements IReqPickUpRedPacket
         * @constructor
         * @param {game.IReqPickUpRedPacket=} [properties] Properties to set
         */
        function ReqPickUpRedPacket(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ReqPickUpRedPacket configId.
         * @member {number} configId
         * @memberof game.ReqPickUpRedPacket
         * @instance
         */
        ReqPickUpRedPacket.prototype.configId = 0;

        /**
         * ReqPickUpRedPacket redPacketRecordId.
         * @member {string} redPacketRecordId
         * @memberof game.ReqPickUpRedPacket
         * @instance
         */
        ReqPickUpRedPacket.prototype.redPacketRecordId = "";

        /**
         * Creates a new ReqPickUpRedPacket instance using the specified properties.
         * @function create
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {game.IReqPickUpRedPacket=} [properties] Properties to set
         * @returns {game.ReqPickUpRedPacket} ReqPickUpRedPacket instance
         */
        ReqPickUpRedPacket.create = function create(properties) {
            return new ReqPickUpRedPacket(properties);
        };

        /**
         * Encodes the specified ReqPickUpRedPacket message. Does not implicitly {@link game.ReqPickUpRedPacket.verify|verify} messages.
         * @function encode
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {game.IReqPickUpRedPacket} message ReqPickUpRedPacket message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ReqPickUpRedPacket.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.configId != null && Object.hasOwnProperty.call(message, "configId"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.configId);
            if (message.redPacketRecordId != null && Object.hasOwnProperty.call(message, "redPacketRecordId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.redPacketRecordId);
            return writer;
        };

        /**
         * Encodes the specified ReqPickUpRedPacket message, length delimited. Does not implicitly {@link game.ReqPickUpRedPacket.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {game.IReqPickUpRedPacket} message ReqPickUpRedPacket message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ReqPickUpRedPacket.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ReqPickUpRedPacket message from the specified reader or buffer.
         * @function decode
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ReqPickUpRedPacket} ReqPickUpRedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ReqPickUpRedPacket.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ReqPickUpRedPacket();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.configId = reader.int32();
                        break;
                    }
                case 2: {
                        message.redPacketRecordId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ReqPickUpRedPacket message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ReqPickUpRedPacket} ReqPickUpRedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ReqPickUpRedPacket.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ReqPickUpRedPacket message.
         * @function verify
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ReqPickUpRedPacket.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.configId != null && message.hasOwnProperty("configId"))
                if (!$util.isInteger(message.configId))
                    return "configId: integer expected";
            if (message.redPacketRecordId != null && message.hasOwnProperty("redPacketRecordId"))
                if (!$util.isString(message.redPacketRecordId))
                    return "redPacketRecordId: string expected";
            return null;
        };

        /**
         * Creates a ReqPickUpRedPacket message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ReqPickUpRedPacket} ReqPickUpRedPacket
         */
        ReqPickUpRedPacket.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ReqPickUpRedPacket)
                return object;
            var message = new $root.game.ReqPickUpRedPacket();
            if (object.configId != null)
                message.configId = object.configId | 0;
            if (object.redPacketRecordId != null)
                message.redPacketRecordId = String(object.redPacketRecordId);
            return message;
        };

        /**
         * Creates a plain object from a ReqPickUpRedPacket message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {game.ReqPickUpRedPacket} message ReqPickUpRedPacket
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ReqPickUpRedPacket.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.configId = 0;
                object.redPacketRecordId = "";
            }
            if (message.configId != null && message.hasOwnProperty("configId"))
                object.configId = message.configId;
            if (message.redPacketRecordId != null && message.hasOwnProperty("redPacketRecordId"))
                object.redPacketRecordId = message.redPacketRecordId;
            return object;
        };

        /**
         * Converts this ReqPickUpRedPacket to JSON.
         * @function toJSON
         * @memberof game.ReqPickUpRedPacket
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ReqPickUpRedPacket.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ReqPickUpRedPacket
         * @function getTypeUrl
         * @memberof game.ReqPickUpRedPacket
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ReqPickUpRedPacket.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ReqPickUpRedPacket";
        };

        return ReqPickUpRedPacket;
    })();

    game.ClientCutTree = (function() {

        /**
         * Properties of a ClientCutTree.
         * @memberof game
         * @interface IClientCutTree
         * @property {number|null} [treeTag] ClientCutTree treeTag
         * @property {string|null} [useItemId] ClientCutTree useItemId
         * @property {string|null} [treeServerId] ClientCutTree treeServerId
         */

        /**
         * Constructs a new ClientCutTree.
         * @memberof game
         * @classdesc Represents a ClientCutTree.
         * @implements IClientCutTree
         * @constructor
         * @param {game.IClientCutTree=} [properties] Properties to set
         */
        function ClientCutTree(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientCutTree treeTag.
         * @member {number} treeTag
         * @memberof game.ClientCutTree
         * @instance
         */
        ClientCutTree.prototype.treeTag = 0;

        /**
         * ClientCutTree useItemId.
         * @member {string} useItemId
         * @memberof game.ClientCutTree
         * @instance
         */
        ClientCutTree.prototype.useItemId = "";

        /**
         * ClientCutTree treeServerId.
         * @member {string} treeServerId
         * @memberof game.ClientCutTree
         * @instance
         */
        ClientCutTree.prototype.treeServerId = "";

        /**
         * Creates a new ClientCutTree instance using the specified properties.
         * @function create
         * @memberof game.ClientCutTree
         * @static
         * @param {game.IClientCutTree=} [properties] Properties to set
         * @returns {game.ClientCutTree} ClientCutTree instance
         */
        ClientCutTree.create = function create(properties) {
            return new ClientCutTree(properties);
        };

        /**
         * Encodes the specified ClientCutTree message. Does not implicitly {@link game.ClientCutTree.verify|verify} messages.
         * @function encode
         * @memberof game.ClientCutTree
         * @static
         * @param {game.IClientCutTree} message ClientCutTree message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientCutTree.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.treeTag != null && Object.hasOwnProperty.call(message, "treeTag"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.treeTag);
            if (message.useItemId != null && Object.hasOwnProperty.call(message, "useItemId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.useItemId);
            if (message.treeServerId != null && Object.hasOwnProperty.call(message, "treeServerId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.treeServerId);
            return writer;
        };

        /**
         * Encodes the specified ClientCutTree message, length delimited. Does not implicitly {@link game.ClientCutTree.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientCutTree
         * @static
         * @param {game.IClientCutTree} message ClientCutTree message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientCutTree.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientCutTree message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientCutTree
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientCutTree} ClientCutTree
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientCutTree.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientCutTree();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.treeTag = reader.int32();
                        break;
                    }
                case 2: {
                        message.useItemId = reader.string();
                        break;
                    }
                case 3: {
                        message.treeServerId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientCutTree message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientCutTree
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientCutTree} ClientCutTree
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientCutTree.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientCutTree message.
         * @function verify
         * @memberof game.ClientCutTree
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientCutTree.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.treeTag != null && message.hasOwnProperty("treeTag"))
                if (!$util.isInteger(message.treeTag))
                    return "treeTag: integer expected";
            if (message.useItemId != null && message.hasOwnProperty("useItemId"))
                if (!$util.isString(message.useItemId))
                    return "useItemId: string expected";
            if (message.treeServerId != null && message.hasOwnProperty("treeServerId"))
                if (!$util.isString(message.treeServerId))
                    return "treeServerId: string expected";
            return null;
        };

        /**
         * Creates a ClientCutTree message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientCutTree
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientCutTree} ClientCutTree
         */
        ClientCutTree.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientCutTree)
                return object;
            var message = new $root.game.ClientCutTree();
            if (object.treeTag != null)
                message.treeTag = object.treeTag | 0;
            if (object.useItemId != null)
                message.useItemId = String(object.useItemId);
            if (object.treeServerId != null)
                message.treeServerId = String(object.treeServerId);
            return message;
        };

        /**
         * Creates a plain object from a ClientCutTree message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientCutTree
         * @static
         * @param {game.ClientCutTree} message ClientCutTree
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientCutTree.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.treeTag = 0;
                object.useItemId = "";
                object.treeServerId = "";
            }
            if (message.treeTag != null && message.hasOwnProperty("treeTag"))
                object.treeTag = message.treeTag;
            if (message.useItemId != null && message.hasOwnProperty("useItemId"))
                object.useItemId = message.useItemId;
            if (message.treeServerId != null && message.hasOwnProperty("treeServerId"))
                object.treeServerId = message.treeServerId;
            return object;
        };

        /**
         * Converts this ClientCutTree to JSON.
         * @function toJSON
         * @memberof game.ClientCutTree
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientCutTree.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientCutTree
         * @function getTypeUrl
         * @memberof game.ClientCutTree
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientCutTree.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientCutTree";
        };

        return ClientCutTree;
    })();

    game.ClientMiningRock = (function() {

        /**
         * Properties of a ClientMiningRock.
         * @memberof game
         * @interface IClientMiningRock
         * @property {number|null} [rockTag] ClientMiningRock rockTag
         * @property {string|null} [useItemId] ClientMiningRock useItemId
         */

        /**
         * Constructs a new ClientMiningRock.
         * @memberof game
         * @classdesc Represents a ClientMiningRock.
         * @implements IClientMiningRock
         * @constructor
         * @param {game.IClientMiningRock=} [properties] Properties to set
         */
        function ClientMiningRock(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientMiningRock rockTag.
         * @member {number} rockTag
         * @memberof game.ClientMiningRock
         * @instance
         */
        ClientMiningRock.prototype.rockTag = 0;

        /**
         * ClientMiningRock useItemId.
         * @member {string} useItemId
         * @memberof game.ClientMiningRock
         * @instance
         */
        ClientMiningRock.prototype.useItemId = "";

        /**
         * Creates a new ClientMiningRock instance using the specified properties.
         * @function create
         * @memberof game.ClientMiningRock
         * @static
         * @param {game.IClientMiningRock=} [properties] Properties to set
         * @returns {game.ClientMiningRock} ClientMiningRock instance
         */
        ClientMiningRock.create = function create(properties) {
            return new ClientMiningRock(properties);
        };

        /**
         * Encodes the specified ClientMiningRock message. Does not implicitly {@link game.ClientMiningRock.verify|verify} messages.
         * @function encode
         * @memberof game.ClientMiningRock
         * @static
         * @param {game.IClientMiningRock} message ClientMiningRock message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientMiningRock.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.rockTag != null && Object.hasOwnProperty.call(message, "rockTag"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.rockTag);
            if (message.useItemId != null && Object.hasOwnProperty.call(message, "useItemId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.useItemId);
            return writer;
        };

        /**
         * Encodes the specified ClientMiningRock message, length delimited. Does not implicitly {@link game.ClientMiningRock.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientMiningRock
         * @static
         * @param {game.IClientMiningRock} message ClientMiningRock message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientMiningRock.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientMiningRock message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientMiningRock
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientMiningRock} ClientMiningRock
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientMiningRock.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientMiningRock();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.rockTag = reader.int32();
                        break;
                    }
                case 2: {
                        message.useItemId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientMiningRock message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientMiningRock
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientMiningRock} ClientMiningRock
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientMiningRock.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientMiningRock message.
         * @function verify
         * @memberof game.ClientMiningRock
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientMiningRock.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.rockTag != null && message.hasOwnProperty("rockTag"))
                if (!$util.isInteger(message.rockTag))
                    return "rockTag: integer expected";
            if (message.useItemId != null && message.hasOwnProperty("useItemId"))
                if (!$util.isString(message.useItemId))
                    return "useItemId: string expected";
            return null;
        };

        /**
         * Creates a ClientMiningRock message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientMiningRock
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientMiningRock} ClientMiningRock
         */
        ClientMiningRock.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientMiningRock)
                return object;
            var message = new $root.game.ClientMiningRock();
            if (object.rockTag != null)
                message.rockTag = object.rockTag | 0;
            if (object.useItemId != null)
                message.useItemId = String(object.useItemId);
            return message;
        };

        /**
         * Creates a plain object from a ClientMiningRock message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientMiningRock
         * @static
         * @param {game.ClientMiningRock} message ClientMiningRock
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientMiningRock.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.rockTag = 0;
                object.useItemId = "";
            }
            if (message.rockTag != null && message.hasOwnProperty("rockTag"))
                object.rockTag = message.rockTag;
            if (message.useItemId != null && message.hasOwnProperty("useItemId"))
                object.useItemId = message.useItemId;
            return object;
        };

        /**
         * Converts this ClientMiningRock to JSON.
         * @function toJSON
         * @memberof game.ClientMiningRock
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientMiningRock.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientMiningRock
         * @function getTypeUrl
         * @memberof game.ClientMiningRock
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientMiningRock.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientMiningRock";
        };

        return ClientMiningRock;
    })();

    game.ClientFishingSuccess = (function() {

        /**
         * Properties of a ClientFishingSuccess.
         * @memberof game
         * @interface IClientFishingSuccess
         * @property {string|null} [fishRecordId] ClientFishingSuccess fishRecordId
         */

        /**
         * Constructs a new ClientFishingSuccess.
         * @memberof game
         * @classdesc Represents a ClientFishingSuccess.
         * @implements IClientFishingSuccess
         * @constructor
         * @param {game.IClientFishingSuccess=} [properties] Properties to set
         */
        function ClientFishingSuccess(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientFishingSuccess fishRecordId.
         * @member {string} fishRecordId
         * @memberof game.ClientFishingSuccess
         * @instance
         */
        ClientFishingSuccess.prototype.fishRecordId = "";

        /**
         * Creates a new ClientFishingSuccess instance using the specified properties.
         * @function create
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {game.IClientFishingSuccess=} [properties] Properties to set
         * @returns {game.ClientFishingSuccess} ClientFishingSuccess instance
         */
        ClientFishingSuccess.create = function create(properties) {
            return new ClientFishingSuccess(properties);
        };

        /**
         * Encodes the specified ClientFishingSuccess message. Does not implicitly {@link game.ClientFishingSuccess.verify|verify} messages.
         * @function encode
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {game.IClientFishingSuccess} message ClientFishingSuccess message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientFishingSuccess.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.fishRecordId != null && Object.hasOwnProperty.call(message, "fishRecordId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.fishRecordId);
            return writer;
        };

        /**
         * Encodes the specified ClientFishingSuccess message, length delimited. Does not implicitly {@link game.ClientFishingSuccess.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {game.IClientFishingSuccess} message ClientFishingSuccess message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientFishingSuccess.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientFishingSuccess message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientFishingSuccess} ClientFishingSuccess
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientFishingSuccess.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientFishingSuccess();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.fishRecordId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientFishingSuccess message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientFishingSuccess} ClientFishingSuccess
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientFishingSuccess.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientFishingSuccess message.
         * @function verify
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientFishingSuccess.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.fishRecordId != null && message.hasOwnProperty("fishRecordId"))
                if (!$util.isString(message.fishRecordId))
                    return "fishRecordId: string expected";
            return null;
        };

        /**
         * Creates a ClientFishingSuccess message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientFishingSuccess} ClientFishingSuccess
         */
        ClientFishingSuccess.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientFishingSuccess)
                return object;
            var message = new $root.game.ClientFishingSuccess();
            if (object.fishRecordId != null)
                message.fishRecordId = String(object.fishRecordId);
            return message;
        };

        /**
         * Creates a plain object from a ClientFishingSuccess message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {game.ClientFishingSuccess} message ClientFishingSuccess
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientFishingSuccess.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.fishRecordId = "";
            if (message.fishRecordId != null && message.hasOwnProperty("fishRecordId"))
                object.fishRecordId = message.fishRecordId;
            return object;
        };

        /**
         * Converts this ClientFishingSuccess to JSON.
         * @function toJSON
         * @memberof game.ClientFishingSuccess
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientFishingSuccess.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientFishingSuccess
         * @function getTypeUrl
         * @memberof game.ClientFishingSuccess
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientFishingSuccess.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientFishingSuccess";
        };

        return ClientFishingSuccess;
    })();

    game.ClientPickUpDrop = (function() {

        /**
         * Properties of a ClientPickUpDrop.
         * @memberof game
         * @interface IClientPickUpDrop
         * @property {string|null} [dropTag] ClientPickUpDrop dropTag
         */

        /**
         * Constructs a new ClientPickUpDrop.
         * @memberof game
         * @classdesc Represents a ClientPickUpDrop.
         * @implements IClientPickUpDrop
         * @constructor
         * @param {game.IClientPickUpDrop=} [properties] Properties to set
         */
        function ClientPickUpDrop(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ClientPickUpDrop dropTag.
         * @member {string} dropTag
         * @memberof game.ClientPickUpDrop
         * @instance
         */
        ClientPickUpDrop.prototype.dropTag = "";

        /**
         * Creates a new ClientPickUpDrop instance using the specified properties.
         * @function create
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {game.IClientPickUpDrop=} [properties] Properties to set
         * @returns {game.ClientPickUpDrop} ClientPickUpDrop instance
         */
        ClientPickUpDrop.create = function create(properties) {
            return new ClientPickUpDrop(properties);
        };

        /**
         * Encodes the specified ClientPickUpDrop message. Does not implicitly {@link game.ClientPickUpDrop.verify|verify} messages.
         * @function encode
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {game.IClientPickUpDrop} message ClientPickUpDrop message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPickUpDrop.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.dropTag != null && Object.hasOwnProperty.call(message, "dropTag"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.dropTag);
            return writer;
        };

        /**
         * Encodes the specified ClientPickUpDrop message, length delimited. Does not implicitly {@link game.ClientPickUpDrop.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {game.IClientPickUpDrop} message ClientPickUpDrop message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ClientPickUpDrop.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a ClientPickUpDrop message from the specified reader or buffer.
         * @function decode
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.ClientPickUpDrop} ClientPickUpDrop
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPickUpDrop.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.ClientPickUpDrop();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 2: {
                        message.dropTag = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a ClientPickUpDrop message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.ClientPickUpDrop} ClientPickUpDrop
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ClientPickUpDrop.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a ClientPickUpDrop message.
         * @function verify
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ClientPickUpDrop.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.dropTag != null && message.hasOwnProperty("dropTag"))
                if (!$util.isString(message.dropTag))
                    return "dropTag: string expected";
            return null;
        };

        /**
         * Creates a ClientPickUpDrop message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.ClientPickUpDrop} ClientPickUpDrop
         */
        ClientPickUpDrop.fromObject = function fromObject(object) {
            if (object instanceof $root.game.ClientPickUpDrop)
                return object;
            var message = new $root.game.ClientPickUpDrop();
            if (object.dropTag != null)
                message.dropTag = String(object.dropTag);
            return message;
        };

        /**
         * Creates a plain object from a ClientPickUpDrop message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {game.ClientPickUpDrop} message ClientPickUpDrop
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ClientPickUpDrop.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.dropTag = "";
            if (message.dropTag != null && message.hasOwnProperty("dropTag"))
                object.dropTag = message.dropTag;
            return object;
        };

        /**
         * Converts this ClientPickUpDrop to JSON.
         * @function toJSON
         * @memberof game.ClientPickUpDrop
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ClientPickUpDrop.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ClientPickUpDrop
         * @function getTypeUrl
         * @memberof game.ClientPickUpDrop
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ClientPickUpDrop.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.ClientPickUpDrop";
        };

        return ClientPickUpDrop;
    })();

    game.TreeData = (function() {

        /**
         * Properties of a TreeData.
         * @memberof game
         * @interface ITreeData
         * @property {number|null} [tag] TreeData tag
         * @property {boolean|null} [isAlive] TreeData isAlive
         * @property {string|null} [treeServerId] TreeData treeServerId
         */

        /**
         * Constructs a new TreeData.
         * @memberof game
         * @classdesc Represents a TreeData.
         * @implements ITreeData
         * @constructor
         * @param {game.ITreeData=} [properties] Properties to set
         */
        function TreeData(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * TreeData tag.
         * @member {number} tag
         * @memberof game.TreeData
         * @instance
         */
        TreeData.prototype.tag = 0;

        /**
         * TreeData isAlive.
         * @member {boolean} isAlive
         * @memberof game.TreeData
         * @instance
         */
        TreeData.prototype.isAlive = false;

        /**
         * TreeData treeServerId.
         * @member {string} treeServerId
         * @memberof game.TreeData
         * @instance
         */
        TreeData.prototype.treeServerId = "";

        /**
         * Creates a new TreeData instance using the specified properties.
         * @function create
         * @memberof game.TreeData
         * @static
         * @param {game.ITreeData=} [properties] Properties to set
         * @returns {game.TreeData} TreeData instance
         */
        TreeData.create = function create(properties) {
            return new TreeData(properties);
        };

        /**
         * Encodes the specified TreeData message. Does not implicitly {@link game.TreeData.verify|verify} messages.
         * @function encode
         * @memberof game.TreeData
         * @static
         * @param {game.ITreeData} message TreeData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        TreeData.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.tag != null && Object.hasOwnProperty.call(message, "tag"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.tag);
            if (message.isAlive != null && Object.hasOwnProperty.call(message, "isAlive"))
                writer.uint32(/* id 2, wireType 0 =*/16).bool(message.isAlive);
            if (message.treeServerId != null && Object.hasOwnProperty.call(message, "treeServerId"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.treeServerId);
            return writer;
        };

        /**
         * Encodes the specified TreeData message, length delimited. Does not implicitly {@link game.TreeData.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.TreeData
         * @static
         * @param {game.ITreeData} message TreeData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        TreeData.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a TreeData message from the specified reader or buffer.
         * @function decode
         * @memberof game.TreeData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.TreeData} TreeData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        TreeData.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.TreeData();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.tag = reader.int32();
                        break;
                    }
                case 2: {
                        message.isAlive = reader.bool();
                        break;
                    }
                case 3: {
                        message.treeServerId = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a TreeData message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.TreeData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.TreeData} TreeData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        TreeData.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a TreeData message.
         * @function verify
         * @memberof game.TreeData
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        TreeData.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.tag != null && message.hasOwnProperty("tag"))
                if (!$util.isInteger(message.tag))
                    return "tag: integer expected";
            if (message.isAlive != null && message.hasOwnProperty("isAlive"))
                if (typeof message.isAlive !== "boolean")
                    return "isAlive: boolean expected";
            if (message.treeServerId != null && message.hasOwnProperty("treeServerId"))
                if (!$util.isString(message.treeServerId))
                    return "treeServerId: string expected";
            return null;
        };

        /**
         * Creates a TreeData message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.TreeData
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.TreeData} TreeData
         */
        TreeData.fromObject = function fromObject(object) {
            if (object instanceof $root.game.TreeData)
                return object;
            var message = new $root.game.TreeData();
            if (object.tag != null)
                message.tag = object.tag | 0;
            if (object.isAlive != null)
                message.isAlive = Boolean(object.isAlive);
            if (object.treeServerId != null)
                message.treeServerId = String(object.treeServerId);
            return message;
        };

        /**
         * Creates a plain object from a TreeData message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.TreeData
         * @static
         * @param {game.TreeData} message TreeData
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        TreeData.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.tag = 0;
                object.isAlive = false;
                object.treeServerId = "";
            }
            if (message.tag != null && message.hasOwnProperty("tag"))
                object.tag = message.tag;
            if (message.isAlive != null && message.hasOwnProperty("isAlive"))
                object.isAlive = message.isAlive;
            if (message.treeServerId != null && message.hasOwnProperty("treeServerId"))
                object.treeServerId = message.treeServerId;
            return object;
        };

        /**
         * Converts this TreeData to JSON.
         * @function toJSON
         * @memberof game.TreeData
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        TreeData.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for TreeData
         * @function getTypeUrl
         * @memberof game.TreeData
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        TreeData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.TreeData";
        };

        return TreeData;
    })();

    game.UpdateTreeList = (function() {

        /**
         * Properties of an UpdateTreeList.
         * @memberof game
         * @interface IUpdateTreeList
         * @property {Array.<game.ITreeData>|null} [treeDataList] UpdateTreeList treeDataList
         */

        /**
         * Constructs a new UpdateTreeList.
         * @memberof game
         * @classdesc Represents an UpdateTreeList.
         * @implements IUpdateTreeList
         * @constructor
         * @param {game.IUpdateTreeList=} [properties] Properties to set
         */
        function UpdateTreeList(properties) {
            this.treeDataList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateTreeList treeDataList.
         * @member {Array.<game.ITreeData>} treeDataList
         * @memberof game.UpdateTreeList
         * @instance
         */
        UpdateTreeList.prototype.treeDataList = $util.emptyArray;

        /**
         * Creates a new UpdateTreeList instance using the specified properties.
         * @function create
         * @memberof game.UpdateTreeList
         * @static
         * @param {game.IUpdateTreeList=} [properties] Properties to set
         * @returns {game.UpdateTreeList} UpdateTreeList instance
         */
        UpdateTreeList.create = function create(properties) {
            return new UpdateTreeList(properties);
        };

        /**
         * Encodes the specified UpdateTreeList message. Does not implicitly {@link game.UpdateTreeList.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateTreeList
         * @static
         * @param {game.IUpdateTreeList} message UpdateTreeList message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateTreeList.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.treeDataList != null && message.treeDataList.length)
                for (var i = 0; i < message.treeDataList.length; ++i)
                    $root.game.TreeData.encode(message.treeDataList[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified UpdateTreeList message, length delimited. Does not implicitly {@link game.UpdateTreeList.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateTreeList
         * @static
         * @param {game.IUpdateTreeList} message UpdateTreeList message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateTreeList.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateTreeList message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateTreeList
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateTreeList} UpdateTreeList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateTreeList.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateTreeList();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.treeDataList && message.treeDataList.length))
                            message.treeDataList = [];
                        message.treeDataList.push($root.game.TreeData.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateTreeList message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateTreeList
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateTreeList} UpdateTreeList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateTreeList.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateTreeList message.
         * @function verify
         * @memberof game.UpdateTreeList
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateTreeList.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.treeDataList != null && message.hasOwnProperty("treeDataList")) {
                if (!Array.isArray(message.treeDataList))
                    return "treeDataList: array expected";
                for (var i = 0; i < message.treeDataList.length; ++i) {
                    var error = $root.game.TreeData.verify(message.treeDataList[i]);
                    if (error)
                        return "treeDataList." + error;
                }
            }
            return null;
        };

        /**
         * Creates an UpdateTreeList message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateTreeList
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateTreeList} UpdateTreeList
         */
        UpdateTreeList.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateTreeList)
                return object;
            var message = new $root.game.UpdateTreeList();
            if (object.treeDataList) {
                if (!Array.isArray(object.treeDataList))
                    throw TypeError(".game.UpdateTreeList.treeDataList: array expected");
                message.treeDataList = [];
                for (var i = 0; i < object.treeDataList.length; ++i) {
                    if (typeof object.treeDataList[i] !== "object")
                        throw TypeError(".game.UpdateTreeList.treeDataList: object expected");
                    message.treeDataList[i] = $root.game.TreeData.fromObject(object.treeDataList[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from an UpdateTreeList message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateTreeList
         * @static
         * @param {game.UpdateTreeList} message UpdateTreeList
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateTreeList.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.treeDataList = [];
            if (message.treeDataList && message.treeDataList.length) {
                object.treeDataList = [];
                for (var j = 0; j < message.treeDataList.length; ++j)
                    object.treeDataList[j] = $root.game.TreeData.toObject(message.treeDataList[j], options);
            }
            return object;
        };

        /**
         * Converts this UpdateTreeList to JSON.
         * @function toJSON
         * @memberof game.UpdateTreeList
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateTreeList.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateTreeList
         * @function getTypeUrl
         * @memberof game.UpdateTreeList
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateTreeList.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateTreeList";
        };

        return UpdateTreeList;
    })();

    game.RockData = (function() {

        /**
         * Properties of a RockData.
         * @memberof game
         * @interface IRockData
         * @property {number|null} [tag] RockData tag
         * @property {boolean|null} [isAlive] RockData isAlive
         */

        /**
         * Constructs a new RockData.
         * @memberof game
         * @classdesc Represents a RockData.
         * @implements IRockData
         * @constructor
         * @param {game.IRockData=} [properties] Properties to set
         */
        function RockData(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * RockData tag.
         * @member {number} tag
         * @memberof game.RockData
         * @instance
         */
        RockData.prototype.tag = 0;

        /**
         * RockData isAlive.
         * @member {boolean} isAlive
         * @memberof game.RockData
         * @instance
         */
        RockData.prototype.isAlive = false;

        /**
         * Creates a new RockData instance using the specified properties.
         * @function create
         * @memberof game.RockData
         * @static
         * @param {game.IRockData=} [properties] Properties to set
         * @returns {game.RockData} RockData instance
         */
        RockData.create = function create(properties) {
            return new RockData(properties);
        };

        /**
         * Encodes the specified RockData message. Does not implicitly {@link game.RockData.verify|verify} messages.
         * @function encode
         * @memberof game.RockData
         * @static
         * @param {game.IRockData} message RockData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RockData.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.tag != null && Object.hasOwnProperty.call(message, "tag"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.tag);
            if (message.isAlive != null && Object.hasOwnProperty.call(message, "isAlive"))
                writer.uint32(/* id 2, wireType 0 =*/16).bool(message.isAlive);
            return writer;
        };

        /**
         * Encodes the specified RockData message, length delimited. Does not implicitly {@link game.RockData.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.RockData
         * @static
         * @param {game.IRockData} message RockData message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        RockData.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a RockData message from the specified reader or buffer.
         * @function decode
         * @memberof game.RockData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.RockData} RockData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RockData.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.RockData();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.tag = reader.int32();
                        break;
                    }
                case 2: {
                        message.isAlive = reader.bool();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a RockData message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.RockData
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.RockData} RockData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        RockData.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a RockData message.
         * @function verify
         * @memberof game.RockData
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        RockData.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.tag != null && message.hasOwnProperty("tag"))
                if (!$util.isInteger(message.tag))
                    return "tag: integer expected";
            if (message.isAlive != null && message.hasOwnProperty("isAlive"))
                if (typeof message.isAlive !== "boolean")
                    return "isAlive: boolean expected";
            return null;
        };

        /**
         * Creates a RockData message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.RockData
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.RockData} RockData
         */
        RockData.fromObject = function fromObject(object) {
            if (object instanceof $root.game.RockData)
                return object;
            var message = new $root.game.RockData();
            if (object.tag != null)
                message.tag = object.tag | 0;
            if (object.isAlive != null)
                message.isAlive = Boolean(object.isAlive);
            return message;
        };

        /**
         * Creates a plain object from a RockData message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.RockData
         * @static
         * @param {game.RockData} message RockData
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        RockData.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.tag = 0;
                object.isAlive = false;
            }
            if (message.tag != null && message.hasOwnProperty("tag"))
                object.tag = message.tag;
            if (message.isAlive != null && message.hasOwnProperty("isAlive"))
                object.isAlive = message.isAlive;
            return object;
        };

        /**
         * Converts this RockData to JSON.
         * @function toJSON
         * @memberof game.RockData
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        RockData.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for RockData
         * @function getTypeUrl
         * @memberof game.RockData
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        RockData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.RockData";
        };

        return RockData;
    })();

    game.UpdateRockList = (function() {

        /**
         * Properties of an UpdateRockList.
         * @memberof game
         * @interface IUpdateRockList
         * @property {Array.<game.IRockData>|null} [rockDataList] UpdateRockList rockDataList
         */

        /**
         * Constructs a new UpdateRockList.
         * @memberof game
         * @classdesc Represents an UpdateRockList.
         * @implements IUpdateRockList
         * @constructor
         * @param {game.IUpdateRockList=} [properties] Properties to set
         */
        function UpdateRockList(properties) {
            this.rockDataList = [];
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateRockList rockDataList.
         * @member {Array.<game.IRockData>} rockDataList
         * @memberof game.UpdateRockList
         * @instance
         */
        UpdateRockList.prototype.rockDataList = $util.emptyArray;

        /**
         * Creates a new UpdateRockList instance using the specified properties.
         * @function create
         * @memberof game.UpdateRockList
         * @static
         * @param {game.IUpdateRockList=} [properties] Properties to set
         * @returns {game.UpdateRockList} UpdateRockList instance
         */
        UpdateRockList.create = function create(properties) {
            return new UpdateRockList(properties);
        };

        /**
         * Encodes the specified UpdateRockList message. Does not implicitly {@link game.UpdateRockList.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateRockList
         * @static
         * @param {game.IUpdateRockList} message UpdateRockList message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRockList.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.rockDataList != null && message.rockDataList.length)
                for (var i = 0; i < message.rockDataList.length; ++i)
                    $root.game.RockData.encode(message.rockDataList[i], writer.uint32(/* id 1, wireType 2 =*/10).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified UpdateRockList message, length delimited. Does not implicitly {@link game.UpdateRockList.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateRockList
         * @static
         * @param {game.IUpdateRockList} message UpdateRockList message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRockList.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateRockList message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateRockList
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateRockList} UpdateRockList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRockList.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateRockList();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        if (!(message.rockDataList && message.rockDataList.length))
                            message.rockDataList = [];
                        message.rockDataList.push($root.game.RockData.decode(reader, reader.uint32()));
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateRockList message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateRockList
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateRockList} UpdateRockList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRockList.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateRockList message.
         * @function verify
         * @memberof game.UpdateRockList
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateRockList.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.rockDataList != null && message.hasOwnProperty("rockDataList")) {
                if (!Array.isArray(message.rockDataList))
                    return "rockDataList: array expected";
                for (var i = 0; i < message.rockDataList.length; ++i) {
                    var error = $root.game.RockData.verify(message.rockDataList[i]);
                    if (error)
                        return "rockDataList." + error;
                }
            }
            return null;
        };

        /**
         * Creates an UpdateRockList message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateRockList
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateRockList} UpdateRockList
         */
        UpdateRockList.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateRockList)
                return object;
            var message = new $root.game.UpdateRockList();
            if (object.rockDataList) {
                if (!Array.isArray(object.rockDataList))
                    throw TypeError(".game.UpdateRockList.rockDataList: array expected");
                message.rockDataList = [];
                for (var i = 0; i < object.rockDataList.length; ++i) {
                    if (typeof object.rockDataList[i] !== "object")
                        throw TypeError(".game.UpdateRockList.rockDataList: object expected");
                    message.rockDataList[i] = $root.game.RockData.fromObject(object.rockDataList[i]);
                }
            }
            return message;
        };

        /**
         * Creates a plain object from an UpdateRockList message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateRockList
         * @static
         * @param {game.UpdateRockList} message UpdateRockList
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateRockList.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.arrays || options.defaults)
                object.rockDataList = [];
            if (message.rockDataList && message.rockDataList.length) {
                object.rockDataList = [];
                for (var j = 0; j < message.rockDataList.length; ++j)
                    object.rockDataList[j] = $root.game.RockData.toObject(message.rockDataList[j], options);
            }
            return object;
        };

        /**
         * Converts this UpdateRockList to JSON.
         * @function toJSON
         * @memberof game.UpdateRockList
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateRockList.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateRockList
         * @function getTypeUrl
         * @memberof game.UpdateRockList
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateRockList.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateRockList";
        };

        return UpdateRockList;
    })();

    game.UpdateTreeRefresh = (function() {

        /**
         * Properties of an UpdateTreeRefresh.
         * @memberof game
         * @interface IUpdateTreeRefresh
         * @property {number|null} [leftTime] UpdateTreeRefresh leftTime
         */

        /**
         * Constructs a new UpdateTreeRefresh.
         * @memberof game
         * @classdesc Represents an UpdateTreeRefresh.
         * @implements IUpdateTreeRefresh
         * @constructor
         * @param {game.IUpdateTreeRefresh=} [properties] Properties to set
         */
        function UpdateTreeRefresh(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateTreeRefresh leftTime.
         * @member {number} leftTime
         * @memberof game.UpdateTreeRefresh
         * @instance
         */
        UpdateTreeRefresh.prototype.leftTime = 0;

        /**
         * Creates a new UpdateTreeRefresh instance using the specified properties.
         * @function create
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {game.IUpdateTreeRefresh=} [properties] Properties to set
         * @returns {game.UpdateTreeRefresh} UpdateTreeRefresh instance
         */
        UpdateTreeRefresh.create = function create(properties) {
            return new UpdateTreeRefresh(properties);
        };

        /**
         * Encodes the specified UpdateTreeRefresh message. Does not implicitly {@link game.UpdateTreeRefresh.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {game.IUpdateTreeRefresh} message UpdateTreeRefresh message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateTreeRefresh.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.leftTime != null && Object.hasOwnProperty.call(message, "leftTime"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.leftTime);
            return writer;
        };

        /**
         * Encodes the specified UpdateTreeRefresh message, length delimited. Does not implicitly {@link game.UpdateTreeRefresh.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {game.IUpdateTreeRefresh} message UpdateTreeRefresh message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateTreeRefresh.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateTreeRefresh message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateTreeRefresh} UpdateTreeRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateTreeRefresh.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateTreeRefresh();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.leftTime = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateTreeRefresh message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateTreeRefresh} UpdateTreeRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateTreeRefresh.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateTreeRefresh message.
         * @function verify
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateTreeRefresh.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.leftTime != null && message.hasOwnProperty("leftTime"))
                if (!$util.isInteger(message.leftTime))
                    return "leftTime: integer expected";
            return null;
        };

        /**
         * Creates an UpdateTreeRefresh message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateTreeRefresh} UpdateTreeRefresh
         */
        UpdateTreeRefresh.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateTreeRefresh)
                return object;
            var message = new $root.game.UpdateTreeRefresh();
            if (object.leftTime != null)
                message.leftTime = object.leftTime | 0;
            return message;
        };

        /**
         * Creates a plain object from an UpdateTreeRefresh message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {game.UpdateTreeRefresh} message UpdateTreeRefresh
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateTreeRefresh.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.leftTime = 0;
            if (message.leftTime != null && message.hasOwnProperty("leftTime"))
                object.leftTime = message.leftTime;
            return object;
        };

        /**
         * Converts this UpdateTreeRefresh to JSON.
         * @function toJSON
         * @memberof game.UpdateTreeRefresh
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateTreeRefresh.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateTreeRefresh
         * @function getTypeUrl
         * @memberof game.UpdateTreeRefresh
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateTreeRefresh.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateTreeRefresh";
        };

        return UpdateTreeRefresh;
    })();

    game.UpdateRockRefresh = (function() {

        /**
         * Properties of an UpdateRockRefresh.
         * @memberof game
         * @interface IUpdateRockRefresh
         * @property {number|null} [leftTime] UpdateRockRefresh leftTime
         */

        /**
         * Constructs a new UpdateRockRefresh.
         * @memberof game
         * @classdesc Represents an UpdateRockRefresh.
         * @implements IUpdateRockRefresh
         * @constructor
         * @param {game.IUpdateRockRefresh=} [properties] Properties to set
         */
        function UpdateRockRefresh(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateRockRefresh leftTime.
         * @member {number} leftTime
         * @memberof game.UpdateRockRefresh
         * @instance
         */
        UpdateRockRefresh.prototype.leftTime = 0;

        /**
         * Creates a new UpdateRockRefresh instance using the specified properties.
         * @function create
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {game.IUpdateRockRefresh=} [properties] Properties to set
         * @returns {game.UpdateRockRefresh} UpdateRockRefresh instance
         */
        UpdateRockRefresh.create = function create(properties) {
            return new UpdateRockRefresh(properties);
        };

        /**
         * Encodes the specified UpdateRockRefresh message. Does not implicitly {@link game.UpdateRockRefresh.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {game.IUpdateRockRefresh} message UpdateRockRefresh message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRockRefresh.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.leftTime != null && Object.hasOwnProperty.call(message, "leftTime"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.leftTime);
            return writer;
        };

        /**
         * Encodes the specified UpdateRockRefresh message, length delimited. Does not implicitly {@link game.UpdateRockRefresh.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {game.IUpdateRockRefresh} message UpdateRockRefresh message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRockRefresh.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateRockRefresh message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateRockRefresh} UpdateRockRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRockRefresh.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateRockRefresh();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.leftTime = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateRockRefresh message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateRockRefresh} UpdateRockRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRockRefresh.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateRockRefresh message.
         * @function verify
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateRockRefresh.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.leftTime != null && message.hasOwnProperty("leftTime"))
                if (!$util.isInteger(message.leftTime))
                    return "leftTime: integer expected";
            return null;
        };

        /**
         * Creates an UpdateRockRefresh message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateRockRefresh} UpdateRockRefresh
         */
        UpdateRockRefresh.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateRockRefresh)
                return object;
            var message = new $root.game.UpdateRockRefresh();
            if (object.leftTime != null)
                message.leftTime = object.leftTime | 0;
            return message;
        };

        /**
         * Creates a plain object from an UpdateRockRefresh message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {game.UpdateRockRefresh} message UpdateRockRefresh
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateRockRefresh.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.leftTime = 0;
            if (message.leftTime != null && message.hasOwnProperty("leftTime"))
                object.leftTime = message.leftTime;
            return object;
        };

        /**
         * Converts this UpdateRockRefresh to JSON.
         * @function toJSON
         * @memberof game.UpdateRockRefresh
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateRockRefresh.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateRockRefresh
         * @function getTypeUrl
         * @memberof game.UpdateRockRefresh
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateRockRefresh.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateRockRefresh";
        };

        return UpdateRockRefresh;
    })();

    game.UpdateItem = (function() {

        /**
         * Properties of an UpdateItem.
         * @memberof game
         * @interface IUpdateItem
         * @property {string|null} [itemId] UpdateItem itemId
         * @property {number|null} [durability] UpdateItem durability
         */

        /**
         * Constructs a new UpdateItem.
         * @memberof game
         * @classdesc Represents an UpdateItem.
         * @implements IUpdateItem
         * @constructor
         * @param {game.IUpdateItem=} [properties] Properties to set
         */
        function UpdateItem(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateItem itemId.
         * @member {string} itemId
         * @memberof game.UpdateItem
         * @instance
         */
        UpdateItem.prototype.itemId = "";

        /**
         * UpdateItem durability.
         * @member {number} durability
         * @memberof game.UpdateItem
         * @instance
         */
        UpdateItem.prototype.durability = 0;

        /**
         * Creates a new UpdateItem instance using the specified properties.
         * @function create
         * @memberof game.UpdateItem
         * @static
         * @param {game.IUpdateItem=} [properties] Properties to set
         * @returns {game.UpdateItem} UpdateItem instance
         */
        UpdateItem.create = function create(properties) {
            return new UpdateItem(properties);
        };

        /**
         * Encodes the specified UpdateItem message. Does not implicitly {@link game.UpdateItem.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateItem
         * @static
         * @param {game.IUpdateItem} message UpdateItem message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateItem.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.itemId != null && Object.hasOwnProperty.call(message, "itemId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.itemId);
            if (message.durability != null && Object.hasOwnProperty.call(message, "durability"))
                writer.uint32(/* id 2, wireType 0 =*/16).int32(message.durability);
            return writer;
        };

        /**
         * Encodes the specified UpdateItem message, length delimited. Does not implicitly {@link game.UpdateItem.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateItem
         * @static
         * @param {game.IUpdateItem} message UpdateItem message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateItem.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateItem message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateItem
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateItem} UpdateItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateItem.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateItem();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.itemId = reader.string();
                        break;
                    }
                case 2: {
                        message.durability = reader.int32();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateItem message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateItem
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateItem} UpdateItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateItem.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateItem message.
         * @function verify
         * @memberof game.UpdateItem
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateItem.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                if (!$util.isString(message.itemId))
                    return "itemId: string expected";
            if (message.durability != null && message.hasOwnProperty("durability"))
                if (!$util.isInteger(message.durability))
                    return "durability: integer expected";
            return null;
        };

        /**
         * Creates an UpdateItem message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateItem
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateItem} UpdateItem
         */
        UpdateItem.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateItem)
                return object;
            var message = new $root.game.UpdateItem();
            if (object.itemId != null)
                message.itemId = String(object.itemId);
            if (object.durability != null)
                message.durability = object.durability | 0;
            return message;
        };

        /**
         * Creates a plain object from an UpdateItem message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateItem
         * @static
         * @param {game.UpdateItem} message UpdateItem
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateItem.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.itemId = "";
                object.durability = 0;
            }
            if (message.itemId != null && message.hasOwnProperty("itemId"))
                object.itemId = message.itemId;
            if (message.durability != null && message.hasOwnProperty("durability"))
                object.durability = message.durability;
            return object;
        };

        /**
         * Converts this UpdateItem to JSON.
         * @function toJSON
         * @memberof game.UpdateItem
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateItem.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateItem
         * @function getTypeUrl
         * @memberof game.UpdateItem
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateItem.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateItem";
        };

        return UpdateItem;
    })();

    game.UpdateMaterial = (function() {

        /**
         * Properties of an UpdateMaterial.
         * @memberof game
         * @interface IUpdateMaterial
         * @property {string|null} [materialTag] UpdateMaterial materialTag
         */

        /**
         * Constructs a new UpdateMaterial.
         * @memberof game
         * @classdesc Represents an UpdateMaterial.
         * @implements IUpdateMaterial
         * @constructor
         * @param {game.IUpdateMaterial=} [properties] Properties to set
         */
        function UpdateMaterial(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateMaterial materialTag.
         * @member {string} materialTag
         * @memberof game.UpdateMaterial
         * @instance
         */
        UpdateMaterial.prototype.materialTag = "";

        /**
         * Creates a new UpdateMaterial instance using the specified properties.
         * @function create
         * @memberof game.UpdateMaterial
         * @static
         * @param {game.IUpdateMaterial=} [properties] Properties to set
         * @returns {game.UpdateMaterial} UpdateMaterial instance
         */
        UpdateMaterial.create = function create(properties) {
            return new UpdateMaterial(properties);
        };

        /**
         * Encodes the specified UpdateMaterial message. Does not implicitly {@link game.UpdateMaterial.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateMaterial
         * @static
         * @param {game.IUpdateMaterial} message UpdateMaterial message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateMaterial.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.materialTag != null && Object.hasOwnProperty.call(message, "materialTag"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.materialTag);
            return writer;
        };

        /**
         * Encodes the specified UpdateMaterial message, length delimited. Does not implicitly {@link game.UpdateMaterial.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateMaterial
         * @static
         * @param {game.IUpdateMaterial} message UpdateMaterial message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateMaterial.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateMaterial message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateMaterial
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateMaterial} UpdateMaterial
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateMaterial.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateMaterial();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.materialTag = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateMaterial message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateMaterial
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateMaterial} UpdateMaterial
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateMaterial.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateMaterial message.
         * @function verify
         * @memberof game.UpdateMaterial
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateMaterial.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.materialTag != null && message.hasOwnProperty("materialTag"))
                if (!$util.isString(message.materialTag))
                    return "materialTag: string expected";
            return null;
        };

        /**
         * Creates an UpdateMaterial message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateMaterial
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateMaterial} UpdateMaterial
         */
        UpdateMaterial.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateMaterial)
                return object;
            var message = new $root.game.UpdateMaterial();
            if (object.materialTag != null)
                message.materialTag = String(object.materialTag);
            return message;
        };

        /**
         * Creates a plain object from an UpdateMaterial message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateMaterial
         * @static
         * @param {game.UpdateMaterial} message UpdateMaterial
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateMaterial.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults)
                object.materialTag = "";
            if (message.materialTag != null && message.hasOwnProperty("materialTag"))
                object.materialTag = message.materialTag;
            return object;
        };

        /**
         * Converts this UpdateMaterial to JSON.
         * @function toJSON
         * @memberof game.UpdateMaterial
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateMaterial.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateMaterial
         * @function getTypeUrl
         * @memberof game.UpdateMaterial
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateMaterial.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateMaterial";
        };

        return UpdateMaterial;
    })();

    game.UpdateRandomEvent = (function() {

        /**
         * Properties of an UpdateRandomEvent.
         * @memberof game
         * @interface IUpdateRandomEvent
         * @property {string|null} [tag] UpdateRandomEvent tag
         * @property {string|null} [quantity] UpdateRandomEvent quantity
         * @property {string|null} [eventType] UpdateRandomEvent eventType
         */

        /**
         * Constructs a new UpdateRandomEvent.
         * @memberof game
         * @classdesc Represents an UpdateRandomEvent.
         * @implements IUpdateRandomEvent
         * @constructor
         * @param {game.IUpdateRandomEvent=} [properties] Properties to set
         */
        function UpdateRandomEvent(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdateRandomEvent tag.
         * @member {string} tag
         * @memberof game.UpdateRandomEvent
         * @instance
         */
        UpdateRandomEvent.prototype.tag = "";

        /**
         * UpdateRandomEvent quantity.
         * @member {string} quantity
         * @memberof game.UpdateRandomEvent
         * @instance
         */
        UpdateRandomEvent.prototype.quantity = "";

        /**
         * UpdateRandomEvent eventType.
         * @member {string} eventType
         * @memberof game.UpdateRandomEvent
         * @instance
         */
        UpdateRandomEvent.prototype.eventType = "";

        /**
         * Creates a new UpdateRandomEvent instance using the specified properties.
         * @function create
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {game.IUpdateRandomEvent=} [properties] Properties to set
         * @returns {game.UpdateRandomEvent} UpdateRandomEvent instance
         */
        UpdateRandomEvent.create = function create(properties) {
            return new UpdateRandomEvent(properties);
        };

        /**
         * Encodes the specified UpdateRandomEvent message. Does not implicitly {@link game.UpdateRandomEvent.verify|verify} messages.
         * @function encode
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {game.IUpdateRandomEvent} message UpdateRandomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRandomEvent.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.tag != null && Object.hasOwnProperty.call(message, "tag"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.tag);
            if (message.quantity != null && Object.hasOwnProperty.call(message, "quantity"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.quantity);
            if (message.eventType != null && Object.hasOwnProperty.call(message, "eventType"))
                writer.uint32(/* id 3, wireType 2 =*/26).string(message.eventType);
            return writer;
        };

        /**
         * Encodes the specified UpdateRandomEvent message, length delimited. Does not implicitly {@link game.UpdateRandomEvent.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {game.IUpdateRandomEvent} message UpdateRandomEvent message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdateRandomEvent.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdateRandomEvent message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdateRandomEvent} UpdateRandomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRandomEvent.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdateRandomEvent();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.tag = reader.string();
                        break;
                    }
                case 2: {
                        message.quantity = reader.string();
                        break;
                    }
                case 3: {
                        message.eventType = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdateRandomEvent message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdateRandomEvent} UpdateRandomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdateRandomEvent.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdateRandomEvent message.
         * @function verify
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdateRandomEvent.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.tag != null && message.hasOwnProperty("tag"))
                if (!$util.isString(message.tag))
                    return "tag: string expected";
            if (message.quantity != null && message.hasOwnProperty("quantity"))
                if (!$util.isString(message.quantity))
                    return "quantity: string expected";
            if (message.eventType != null && message.hasOwnProperty("eventType"))
                if (!$util.isString(message.eventType))
                    return "eventType: string expected";
            return null;
        };

        /**
         * Creates an UpdateRandomEvent message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdateRandomEvent} UpdateRandomEvent
         */
        UpdateRandomEvent.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdateRandomEvent)
                return object;
            var message = new $root.game.UpdateRandomEvent();
            if (object.tag != null)
                message.tag = String(object.tag);
            if (object.quantity != null)
                message.quantity = String(object.quantity);
            if (object.eventType != null)
                message.eventType = String(object.eventType);
            return message;
        };

        /**
         * Creates a plain object from an UpdateRandomEvent message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {game.UpdateRandomEvent} message UpdateRandomEvent
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdateRandomEvent.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.tag = "";
                object.quantity = "";
                object.eventType = "";
            }
            if (message.tag != null && message.hasOwnProperty("tag"))
                object.tag = message.tag;
            if (message.quantity != null && message.hasOwnProperty("quantity"))
                object.quantity = message.quantity;
            if (message.eventType != null && message.hasOwnProperty("eventType"))
                object.eventType = message.eventType;
            return object;
        };

        /**
         * Converts this UpdateRandomEvent to JSON.
         * @function toJSON
         * @memberof game.UpdateRandomEvent
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdateRandomEvent.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdateRandomEvent
         * @function getTypeUrl
         * @memberof game.UpdateRandomEvent
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdateRandomEvent.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdateRandomEvent";
        };

        return UpdateRandomEvent;
    })();

    game.UpdatePickUpPoint = (function() {

        /**
         * Properties of an UpdatePickUpPoint.
         * @memberof game
         * @interface IUpdatePickUpPoint
         * @property {string|null} [tag] UpdatePickUpPoint tag
         * @property {boolean|null} [isPickedUp] UpdatePickUpPoint isPickedUp
         * @property {number|Long|null} [coolDown] UpdatePickUpPoint coolDown
         */

        /**
         * Constructs a new UpdatePickUpPoint.
         * @memberof game
         * @classdesc Represents an UpdatePickUpPoint.
         * @implements IUpdatePickUpPoint
         * @constructor
         * @param {game.IUpdatePickUpPoint=} [properties] Properties to set
         */
        function UpdatePickUpPoint(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * UpdatePickUpPoint tag.
         * @member {string} tag
         * @memberof game.UpdatePickUpPoint
         * @instance
         */
        UpdatePickUpPoint.prototype.tag = "";

        /**
         * UpdatePickUpPoint isPickedUp.
         * @member {boolean} isPickedUp
         * @memberof game.UpdatePickUpPoint
         * @instance
         */
        UpdatePickUpPoint.prototype.isPickedUp = false;

        /**
         * UpdatePickUpPoint coolDown.
         * @member {number|Long} coolDown
         * @memberof game.UpdatePickUpPoint
         * @instance
         */
        UpdatePickUpPoint.prototype.coolDown = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Creates a new UpdatePickUpPoint instance using the specified properties.
         * @function create
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {game.IUpdatePickUpPoint=} [properties] Properties to set
         * @returns {game.UpdatePickUpPoint} UpdatePickUpPoint instance
         */
        UpdatePickUpPoint.create = function create(properties) {
            return new UpdatePickUpPoint(properties);
        };

        /**
         * Encodes the specified UpdatePickUpPoint message. Does not implicitly {@link game.UpdatePickUpPoint.verify|verify} messages.
         * @function encode
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {game.IUpdatePickUpPoint} message UpdatePickUpPoint message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdatePickUpPoint.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.tag != null && Object.hasOwnProperty.call(message, "tag"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.tag);
            if (message.isPickedUp != null && Object.hasOwnProperty.call(message, "isPickedUp"))
                writer.uint32(/* id 2, wireType 0 =*/16).bool(message.isPickedUp);
            if (message.coolDown != null && Object.hasOwnProperty.call(message, "coolDown"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.coolDown);
            return writer;
        };

        /**
         * Encodes the specified UpdatePickUpPoint message, length delimited. Does not implicitly {@link game.UpdatePickUpPoint.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {game.IUpdatePickUpPoint} message UpdatePickUpPoint message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        UpdatePickUpPoint.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an UpdatePickUpPoint message from the specified reader or buffer.
         * @function decode
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.UpdatePickUpPoint} UpdatePickUpPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdatePickUpPoint.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.UpdatePickUpPoint();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.tag = reader.string();
                        break;
                    }
                case 2: {
                        message.isPickedUp = reader.bool();
                        break;
                    }
                case 3: {
                        message.coolDown = reader.int64();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an UpdatePickUpPoint message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.UpdatePickUpPoint} UpdatePickUpPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        UpdatePickUpPoint.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an UpdatePickUpPoint message.
         * @function verify
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        UpdatePickUpPoint.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.tag != null && message.hasOwnProperty("tag"))
                if (!$util.isString(message.tag))
                    return "tag: string expected";
            if (message.isPickedUp != null && message.hasOwnProperty("isPickedUp"))
                if (typeof message.isPickedUp !== "boolean")
                    return "isPickedUp: boolean expected";
            if (message.coolDown != null && message.hasOwnProperty("coolDown"))
                if (!$util.isInteger(message.coolDown) && !(message.coolDown && $util.isInteger(message.coolDown.low) && $util.isInteger(message.coolDown.high)))
                    return "coolDown: integer|Long expected";
            return null;
        };

        /**
         * Creates an UpdatePickUpPoint message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.UpdatePickUpPoint} UpdatePickUpPoint
         */
        UpdatePickUpPoint.fromObject = function fromObject(object) {
            if (object instanceof $root.game.UpdatePickUpPoint)
                return object;
            var message = new $root.game.UpdatePickUpPoint();
            if (object.tag != null)
                message.tag = String(object.tag);
            if (object.isPickedUp != null)
                message.isPickedUp = Boolean(object.isPickedUp);
            if (object.coolDown != null)
                if ($util.Long)
                    (message.coolDown = $util.Long.fromValue(object.coolDown)).unsigned = false;
                else if (typeof object.coolDown === "string")
                    message.coolDown = parseInt(object.coolDown, 10);
                else if (typeof object.coolDown === "number")
                    message.coolDown = object.coolDown;
                else if (typeof object.coolDown === "object")
                    message.coolDown = new $util.LongBits(object.coolDown.low >>> 0, object.coolDown.high >>> 0).toNumber();
            return message;
        };

        /**
         * Creates a plain object from an UpdatePickUpPoint message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {game.UpdatePickUpPoint} message UpdatePickUpPoint
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        UpdatePickUpPoint.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.tag = "";
                object.isPickedUp = false;
                if ($util.Long) {
                    var long = new $util.Long(0, 0, false);
                    object.coolDown = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.coolDown = options.longs === String ? "0" : 0;
            }
            if (message.tag != null && message.hasOwnProperty("tag"))
                object.tag = message.tag;
            if (message.isPickedUp != null && message.hasOwnProperty("isPickedUp"))
                object.isPickedUp = message.isPickedUp;
            if (message.coolDown != null && message.hasOwnProperty("coolDown"))
                if (typeof message.coolDown === "number")
                    object.coolDown = options.longs === String ? String(message.coolDown) : message.coolDown;
                else
                    object.coolDown = options.longs === String ? $util.Long.prototype.toString.call(message.coolDown) : options.longs === Number ? new $util.LongBits(message.coolDown.low >>> 0, message.coolDown.high >>> 0).toNumber() : message.coolDown;
            return object;
        };

        /**
         * Converts this UpdatePickUpPoint to JSON.
         * @function toJSON
         * @memberof game.UpdatePickUpPoint
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        UpdatePickUpPoint.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for UpdatePickUpPoint
         * @function getTypeUrl
         * @memberof game.UpdatePickUpPoint
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        UpdatePickUpPoint.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.UpdatePickUpPoint";
        };

        return UpdatePickUpPoint;
    })();

    game.LarkMessage = (function() {

        /**
         * Properties of a LarkMessage.
         * @memberof game
         * @interface ILarkMessage
         * @property {string|null} [title] LarkMessage title
         * @property {string|null} [connect] LarkMessage connect
         */

        /**
         * Constructs a new LarkMessage.
         * @memberof game
         * @classdesc Represents a LarkMessage.
         * @implements ILarkMessage
         * @constructor
         * @param {game.ILarkMessage=} [properties] Properties to set
         */
        function LarkMessage(properties) {
            if (properties)
                for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * LarkMessage title.
         * @member {string} title
         * @memberof game.LarkMessage
         * @instance
         */
        LarkMessage.prototype.title = "";

        /**
         * LarkMessage connect.
         * @member {string} connect
         * @memberof game.LarkMessage
         * @instance
         */
        LarkMessage.prototype.connect = "";

        /**
         * Creates a new LarkMessage instance using the specified properties.
         * @function create
         * @memberof game.LarkMessage
         * @static
         * @param {game.ILarkMessage=} [properties] Properties to set
         * @returns {game.LarkMessage} LarkMessage instance
         */
        LarkMessage.create = function create(properties) {
            return new LarkMessage(properties);
        };

        /**
         * Encodes the specified LarkMessage message. Does not implicitly {@link game.LarkMessage.verify|verify} messages.
         * @function encode
         * @memberof game.LarkMessage
         * @static
         * @param {game.ILarkMessage} message LarkMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LarkMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.title != null && Object.hasOwnProperty.call(message, "title"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.title);
            if (message.connect != null && Object.hasOwnProperty.call(message, "connect"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.connect);
            return writer;
        };

        /**
         * Encodes the specified LarkMessage message, length delimited. Does not implicitly {@link game.LarkMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof game.LarkMessage
         * @static
         * @param {game.ILarkMessage} message LarkMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        LarkMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a LarkMessage message from the specified reader or buffer.
         * @function decode
         * @memberof game.LarkMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {game.LarkMessage} LarkMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LarkMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            var end = length === undefined ? reader.len : reader.pos + length, message = new $root.game.LarkMessage();
            while (reader.pos < end) {
                var tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.title = reader.string();
                        break;
                    }
                case 2: {
                        message.connect = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a LarkMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof game.LarkMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {game.LarkMessage} LarkMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        LarkMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a LarkMessage message.
         * @function verify
         * @memberof game.LarkMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        LarkMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.title != null && message.hasOwnProperty("title"))
                if (!$util.isString(message.title))
                    return "title: string expected";
            if (message.connect != null && message.hasOwnProperty("connect"))
                if (!$util.isString(message.connect))
                    return "connect: string expected";
            return null;
        };

        /**
         * Creates a LarkMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof game.LarkMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {game.LarkMessage} LarkMessage
         */
        LarkMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.game.LarkMessage)
                return object;
            var message = new $root.game.LarkMessage();
            if (object.title != null)
                message.title = String(object.title);
            if (object.connect != null)
                message.connect = String(object.connect);
            return message;
        };

        /**
         * Creates a plain object from a LarkMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof game.LarkMessage
         * @static
         * @param {game.LarkMessage} message LarkMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        LarkMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            var object = {};
            if (options.defaults) {
                object.title = "";
                object.connect = "";
            }
            if (message.title != null && message.hasOwnProperty("title"))
                object.title = message.title;
            if (message.connect != null && message.hasOwnProperty("connect"))
                object.connect = message.connect;
            return object;
        };

        /**
         * Converts this LarkMessage to JSON.
         * @function toJSON
         * @memberof game.LarkMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        LarkMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for LarkMessage
         * @function getTypeUrl
         * @memberof game.LarkMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        LarkMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/game.LarkMessage";
        };

        return LarkMessage;
    })();

    return game;
})();

module.exports = $root;
