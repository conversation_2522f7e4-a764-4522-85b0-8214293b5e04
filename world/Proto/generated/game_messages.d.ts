import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace game. */
export namespace game {

    /** S2CPacketType enum. */
    enum S2CPacketType {
        S2C_PLAYER_NONE = 0,
        S2C_PLAYER_ENTER = 1,
        S2C_PLAYER_LEAVE = 2,
        S2C_PLAYER_POSITION = 10,
        S2C_PLAYER_ANIMATION = 11,
        S2C_PLAYER_UPDATE = 12,
        S2C_PLAYER_FISHING = 13,
        S2C_PET_POSITION = 50,
        S2C_PET_ANIMATION = 51,
        S2C_CHAT_ENTER = 100,
        S2C_CHAT_LEAVE = 101,
        S2C_CHAT_MESSAGE = 102,
        S2C_CHAT_MESSAGE_DELETE = 103,
        S2C_RED_PACKET_UPDATE = 201,
        S2C_RED_PACKET_REWARD = 202,
        S2C_RED_PACKET_CACHE = 203,
        S2C_UPDATE_ITEM = 1001,
        S2C_UPDATE_TREE = 1002,
        S2C_UPDATE_TREE_REFRESH = 1003,
        S2C_UPDATE_ROCK = 1004,
        S2C_UPDATE_ROCK_REFRESH = 1005,
        S2C_UPDATE_PICK_UP_POINT = 1006,
        S2C_REWARD_MATERIAL = 1101,
        S2C_REWARD_RANDOM_EVENT = 1102,
        S2C_NOTICE_NEW_DAY = 1200,
        S2C_PLAYER_POSITION_UPDATE = 2001
    }

    /** C2SPacketType enum. */
    enum C2SPacketType {
        C2S_PLAYER_NONE = 0,
        C2S_PLAYER_ENTER = 1,
        C2S_PLAYER_LEAVE = 2,
        C2S_PLAYER_POSITION = 10,
        C2S_PLAYER_ANIMATION = 11,
        C2S_PLAYER_UPDATE = 12,
        C2S_PLAYER_FISHING = 13,
        C2S_PET_POSITION = 50,
        C2S_PET_ANIMATION = 51,
        C2S_CHAT_ENTER = 100,
        C2S_CHAT_LEAVE = 101,
        C2S_CHAT_MESSAGE = 102,
        C2S_CHAT_MESSAGE_DELETE = 103,
        C2S_CUT_TREE = 1001,
        C2S_MINING_ROCK = 1002,
        C2S_FISHING_SUCCESS = 1003,
        C2S_PICK_UP_DROP = 1004,
        C2S_PLAYER_MAP_UPDATE = 2000,
        C2S_PLAYER_POSITION_UPDATE = 2001,
        C2S_LARK_MESSAGE = 10001
    }

    /** ClientRequestTypes enum. */
    enum ClientRequestTypes {
        NONE = 0,
        PICK_UP_RED_PACKET = 1
    }

    /** Properties of a GameMessage. */
    interface IGameMessage {

        /** GameMessage pid */
        pid?: (number|null);

        /** GameMessage data */
        data?: (Uint8Array|null);

        /** GameMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a GameMessage. */
    class GameMessage implements IGameMessage {

        /**
         * Constructs a new GameMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IGameMessage);

        /** GameMessage pid. */
        public pid: number;

        /** GameMessage data. */
        public data: Uint8Array;

        /** GameMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new GameMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns GameMessage instance
         */
        public static create(properties?: game.IGameMessage): game.GameMessage;

        /**
         * Encodes the specified GameMessage message. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified GameMessage message, length delimited. Does not implicitly {@link game.GameMessage.verify|verify} messages.
         * @param message GameMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IGameMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a GameMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.GameMessage;

        /**
         * Decodes a GameMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns GameMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.GameMessage;

        /**
         * Verifies a GameMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a GameMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns GameMessage
         */
        public static fromObject(object: { [k: string]: any }): game.GameMessage;

        /**
         * Creates a plain object from a GameMessage message. Also converts values to other types if specified.
         * @param message GameMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.GameMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this GameMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for GameMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerActionMessage. */
    interface IPlayerActionMessage {

        /** PlayerActionMessage pid */
        pid?: (number|null);

        /** PlayerActionMessage actionData */
        actionData?: (Uint8Array|null);

        /** PlayerActionMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerActionMessage. */
    class PlayerActionMessage implements IPlayerActionMessage {

        /**
         * Constructs a new PlayerActionMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerActionMessage);

        /** PlayerActionMessage pid. */
        public pid: number;

        /** PlayerActionMessage actionData. */
        public actionData: Uint8Array;

        /** PlayerActionMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerActionMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerActionMessage instance
         */
        public static create(properties?: game.IPlayerActionMessage): game.PlayerActionMessage;

        /**
         * Encodes the specified PlayerActionMessage message. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @param message PlayerActionMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerActionMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerActionMessage message, length delimited. Does not implicitly {@link game.PlayerActionMessage.verify|verify} messages.
         * @param message PlayerActionMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerActionMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerActionMessage;

        /**
         * Decodes a PlayerActionMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerActionMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerActionMessage;

        /**
         * Verifies a PlayerActionMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerActionMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerActionMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerActionMessage;

        /**
         * Creates a plain object from a PlayerActionMessage message. Also converts values to other types if specified.
         * @param message PlayerActionMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerActionMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerActionMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerActionMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerRequestMessage. */
    interface IPlayerRequestMessage {

        /** PlayerRequestMessage pid */
        pid?: (number|null);

        /** PlayerRequestMessage requestData */
        requestData?: (Uint8Array|null);

        /** PlayerRequestMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerRequestMessage. */
    class PlayerRequestMessage implements IPlayerRequestMessage {

        /**
         * Constructs a new PlayerRequestMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerRequestMessage);

        /** PlayerRequestMessage pid. */
        public pid: number;

        /** PlayerRequestMessage requestData. */
        public requestData: Uint8Array;

        /** PlayerRequestMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerRequestMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerRequestMessage instance
         */
        public static create(properties?: game.IPlayerRequestMessage): game.PlayerRequestMessage;

        /**
         * Encodes the specified PlayerRequestMessage message. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @param message PlayerRequestMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerRequestMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerRequestMessage message, length delimited. Does not implicitly {@link game.PlayerRequestMessage.verify|verify} messages.
         * @param message PlayerRequestMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerRequestMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerRequestMessage;

        /**
         * Decodes a PlayerRequestMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerRequestMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerRequestMessage;

        /**
         * Verifies a PlayerRequestMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerRequestMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerRequestMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerRequestMessage;

        /**
         * Creates a plain object from a PlayerRequestMessage message. Also converts values to other types if specified.
         * @param message PlayerRequestMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerRequestMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerRequestMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerRequestMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerChatMessage. */
    interface IPlayerChatMessage {

        /** PlayerChatMessage chatId */
        chatId?: (number|null);

        /** PlayerChatMessage pid */
        pid?: (number|null);

        /** PlayerChatMessage chatData */
        chatData?: (Uint8Array|null);

        /** PlayerChatMessage timestamp */
        timestamp?: (number|Long|null);
    }

    /** Represents a PlayerChatMessage. */
    class PlayerChatMessage implements IPlayerChatMessage {

        /**
         * Constructs a new PlayerChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerChatMessage);

        /** PlayerChatMessage chatId. */
        public chatId: number;

        /** PlayerChatMessage pid. */
        public pid: number;

        /** PlayerChatMessage chatData. */
        public chatData: Uint8Array;

        /** PlayerChatMessage timestamp. */
        public timestamp: (number|Long);

        /**
         * Creates a new PlayerChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerChatMessage instance
         */
        public static create(properties?: game.IPlayerChatMessage): game.PlayerChatMessage;

        /**
         * Encodes the specified PlayerChatMessage message. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @param message PlayerChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerChatMessage message, length delimited. Does not implicitly {@link game.PlayerChatMessage.verify|verify} messages.
         * @param message PlayerChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerChatMessage;

        /**
         * Decodes a PlayerChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerChatMessage;

        /**
         * Verifies a PlayerChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerChatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerChatMessage;

        /**
         * Creates a plain object from a PlayerChatMessage message. Also converts values to other types if specified.
         * @param message PlayerChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerLogicMessage. */
    interface IPlayerLogicMessage {

        /** PlayerLogicMessage pid */
        pid?: (number|null);

        /** PlayerLogicMessage logicData */
        logicData?: (Uint8Array|null);

        /** PlayerLogicMessage timestamp */
        timestamp?: (number|Long|null);

        /** PlayerLogicMessage sign */
        sign?: (string|null);
    }

    /** Represents a PlayerLogicMessage. */
    class PlayerLogicMessage implements IPlayerLogicMessage {

        /**
         * Constructs a new PlayerLogicMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerLogicMessage);

        /** PlayerLogicMessage pid. */
        public pid: number;

        /** PlayerLogicMessage logicData. */
        public logicData: Uint8Array;

        /** PlayerLogicMessage timestamp. */
        public timestamp: (number|Long);

        /** PlayerLogicMessage sign. */
        public sign: string;

        /**
         * Creates a new PlayerLogicMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerLogicMessage instance
         */
        public static create(properties?: game.IPlayerLogicMessage): game.PlayerLogicMessage;

        /**
         * Encodes the specified PlayerLogicMessage message. Does not implicitly {@link game.PlayerLogicMessage.verify|verify} messages.
         * @param message PlayerLogicMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerLogicMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerLogicMessage message, length delimited. Does not implicitly {@link game.PlayerLogicMessage.verify|verify} messages.
         * @param message PlayerLogicMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerLogicMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerLogicMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerLogicMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerLogicMessage;

        /**
         * Decodes a PlayerLogicMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerLogicMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerLogicMessage;

        /**
         * Verifies a PlayerLogicMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerLogicMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerLogicMessage
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerLogicMessage;

        /**
         * Creates a plain object from a PlayerLogicMessage message. Also converts values to other types if specified.
         * @param message PlayerLogicMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerLogicMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerLogicMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerLogicMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerPosition. */
    interface IPlayerPosition {

        /** PlayerPosition btcAddress */
        btcAddress?: (string|null);

        /** PlayerPosition x */
        x?: (number|null);

        /** PlayerPosition y */
        y?: (number|null);

        /** PlayerPosition z */
        z?: (number|null);

        /** PlayerPosition rotationX */
        rotationX?: (number|null);

        /** PlayerPosition rotationY */
        rotationY?: (number|null);

        /** PlayerPosition rotationZ */
        rotationZ?: (number|null);

        /** PlayerPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a PlayerPosition. */
    class PlayerPosition implements IPlayerPosition {

        /**
         * Constructs a new PlayerPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerPosition);

        /** PlayerPosition btcAddress. */
        public btcAddress: string;

        /** PlayerPosition x. */
        public x: number;

        /** PlayerPosition y. */
        public y: number;

        /** PlayerPosition z. */
        public z: number;

        /** PlayerPosition rotationX. */
        public rotationX: number;

        /** PlayerPosition rotationY. */
        public rotationY: number;

        /** PlayerPosition rotationZ. */
        public rotationZ: number;

        /** PlayerPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new PlayerPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerPosition instance
         */
        public static create(properties?: game.IPlayerPosition): game.PlayerPosition;

        /**
         * Encodes the specified PlayerPosition message. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerPosition message, length delimited. Does not implicitly {@link game.PlayerPosition.verify|verify} messages.
         * @param message PlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerPosition;

        /**
         * Decodes a PlayerPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerPosition;

        /**
         * Verifies a PlayerPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerPosition;

        /**
         * Creates a plain object from a PlayerPosition message. Also converts values to other types if specified.
         * @param message PlayerPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerAnimation. */
    interface IPlayerAnimation {

        /** PlayerAnimation btcAddress */
        btcAddress?: (string|null);

        /** PlayerAnimation curAnimation */
        curAnimation?: (string|null);
    }

    /** Represents a PlayerAnimation. */
    class PlayerAnimation implements IPlayerAnimation {

        /**
         * Constructs a new PlayerAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerAnimation);

        /** PlayerAnimation btcAddress. */
        public btcAddress: string;

        /** PlayerAnimation curAnimation. */
        public curAnimation: string;

        /**
         * Creates a new PlayerAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerAnimation instance
         */
        public static create(properties?: game.IPlayerAnimation): game.PlayerAnimation;

        /**
         * Encodes the specified PlayerAnimation message. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerAnimation message, length delimited. Does not implicitly {@link game.PlayerAnimation.verify|verify} messages.
         * @param message PlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerAnimation;

        /**
         * Decodes a PlayerAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerAnimation;

        /**
         * Verifies a PlayerAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerAnimation;

        /**
         * Creates a plain object from a PlayerAnimation message. Also converts values to other types if specified.
         * @param message PlayerAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerUpdate. */
    interface IPlayerUpdate {

        /** PlayerUpdate btcAddress */
        btcAddress?: (string|null);

        /** PlayerUpdate itemId */
        itemId?: (number|null);

        /** PlayerUpdate pizzaCount */
        pizzaCount?: (number|null);

        /** PlayerUpdate petId */
        petId?: (string|null);

        /** PlayerUpdate pizzaTick */
        pizzaTick?: (string|null);
    }

    /** Represents a PlayerUpdate. */
    class PlayerUpdate implements IPlayerUpdate {

        /**
         * Constructs a new PlayerUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerUpdate);

        /** PlayerUpdate btcAddress. */
        public btcAddress: string;

        /** PlayerUpdate itemId. */
        public itemId: number;

        /** PlayerUpdate pizzaCount. */
        public pizzaCount: number;

        /** PlayerUpdate petId. */
        public petId: string;

        /** PlayerUpdate pizzaTick. */
        public pizzaTick: string;

        /**
         * Creates a new PlayerUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerUpdate instance
         */
        public static create(properties?: game.IPlayerUpdate): game.PlayerUpdate;

        /**
         * Encodes the specified PlayerUpdate message. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerUpdate message, length delimited. Does not implicitly {@link game.PlayerUpdate.verify|verify} messages.
         * @param message PlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerUpdate;

        /**
         * Decodes a PlayerUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerUpdate;

        /**
         * Verifies a PlayerUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerUpdate;

        /**
         * Creates a plain object from a PlayerUpdate message. Also converts values to other types if specified.
         * @param message PlayerUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerEnter. */
    interface IPlayerEnter {

        /** PlayerEnter btcAddress */
        btcAddress?: (string|null);

        /** PlayerEnter avatarData */
        avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position */
        position?: (game.IPlayerPosition|null);
    }

    /** Represents a PlayerEnter. */
    class PlayerEnter implements IPlayerEnter {

        /**
         * Constructs a new PlayerEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerEnter);

        /** PlayerEnter btcAddress. */
        public btcAddress: string;

        /** PlayerEnter avatarData. */
        public avatarData?: (game.IAvatarData|null);

        /** PlayerEnter position. */
        public position?: (game.IPlayerPosition|null);

        /**
         * Creates a new PlayerEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerEnter instance
         */
        public static create(properties?: game.IPlayerEnter): game.PlayerEnter;

        /**
         * Encodes the specified PlayerEnter message. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerEnter message, length delimited. Does not implicitly {@link game.PlayerEnter.verify|verify} messages.
         * @param message PlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerEnter;

        /**
         * Decodes a PlayerEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerEnter;

        /**
         * Verifies a PlayerEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerEnter
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerEnter;

        /**
         * Creates a plain object from a PlayerEnter message. Also converts values to other types if specified.
         * @param message PlayerEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerLeave. */
    interface IPlayerLeave {

        /** PlayerLeave btcAddress */
        btcAddress?: (string|null);
    }

    /** Represents a PlayerLeave. */
    class PlayerLeave implements IPlayerLeave {

        /**
         * Constructs a new PlayerLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerLeave);

        /** PlayerLeave btcAddress. */
        public btcAddress: string;

        /**
         * Creates a new PlayerLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerLeave instance
         */
        public static create(properties?: game.IPlayerLeave): game.PlayerLeave;

        /**
         * Encodes the specified PlayerLeave message. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerLeave message, length delimited. Does not implicitly {@link game.PlayerLeave.verify|verify} messages.
         * @param message PlayerLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerLeave;

        /**
         * Decodes a PlayerLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerLeave;

        /**
         * Verifies a PlayerLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerLeave
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerLeave;

        /**
         * Creates a plain object from a PlayerLeave message. Also converts values to other types if specified.
         * @param message PlayerLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PlayerFishing. */
    interface IPlayerFishing {

        /** PlayerFishing btcAddress */
        btcAddress?: (string|null);

        /** PlayerFishing fishId */
        fishId?: (number|null);
    }

    /** Represents a PlayerFishing. */
    class PlayerFishing implements IPlayerFishing {

        /**
         * Constructs a new PlayerFishing.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPlayerFishing);

        /** PlayerFishing btcAddress. */
        public btcAddress: string;

        /** PlayerFishing fishId. */
        public fishId: number;

        /**
         * Creates a new PlayerFishing instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PlayerFishing instance
         */
        public static create(properties?: game.IPlayerFishing): game.PlayerFishing;

        /**
         * Encodes the specified PlayerFishing message. Does not implicitly {@link game.PlayerFishing.verify|verify} messages.
         * @param message PlayerFishing message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPlayerFishing, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PlayerFishing message, length delimited. Does not implicitly {@link game.PlayerFishing.verify|verify} messages.
         * @param message PlayerFishing message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPlayerFishing, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PlayerFishing message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PlayerFishing
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PlayerFishing;

        /**
         * Decodes a PlayerFishing message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PlayerFishing
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PlayerFishing;

        /**
         * Verifies a PlayerFishing message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PlayerFishing message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PlayerFishing
         */
        public static fromObject(object: { [k: string]: any }): game.PlayerFishing;

        /**
         * Creates a plain object from a PlayerFishing message. Also converts values to other types if specified.
         * @param message PlayerFishing
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PlayerFishing, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PlayerFishing to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PlayerFishing
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an AvatarData. */
    interface IAvatarData {

        /** AvatarData shirtId */
        shirtId?: (string|null);

        /** AvatarData shirtTextureId */
        shirtTextureId?: (string|null);

        /** AvatarData shirtColor */
        shirtColor?: (string|null);

        /** AvatarData pantsId */
        pantsId?: (string|null);

        /** AvatarData shoesId */
        shoesId?: (string|null);

        /** AvatarData hatId */
        hatId?: (string|null);

        /** AvatarData glovesId */
        glovesId?: (string|null);
    }

    /** Represents an AvatarData. */
    class AvatarData implements IAvatarData {

        /**
         * Constructs a new AvatarData.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IAvatarData);

        /** AvatarData shirtId. */
        public shirtId: string;

        /** AvatarData shirtTextureId. */
        public shirtTextureId: string;

        /** AvatarData shirtColor. */
        public shirtColor: string;

        /** AvatarData pantsId. */
        public pantsId: string;

        /** AvatarData shoesId. */
        public shoesId: string;

        /** AvatarData hatId. */
        public hatId: string;

        /** AvatarData glovesId. */
        public glovesId: string;

        /**
         * Creates a new AvatarData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns AvatarData instance
         */
        public static create(properties?: game.IAvatarData): game.AvatarData;

        /**
         * Encodes the specified AvatarData message. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified AvatarData message, length delimited. Does not implicitly {@link game.AvatarData.verify|verify} messages.
         * @param message AvatarData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IAvatarData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AvatarData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.AvatarData;

        /**
         * Decodes an AvatarData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns AvatarData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.AvatarData;

        /**
         * Verifies an AvatarData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AvatarData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AvatarData
         */
        public static fromObject(object: { [k: string]: any }): game.AvatarData;

        /**
         * Creates a plain object from an AvatarData message. Also converts values to other types if specified.
         * @param message AvatarData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.AvatarData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AvatarData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for AvatarData
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetPosition. */
    interface IPetPosition {

        /** PetPosition ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetPosition x */
        x?: (number|null);

        /** PetPosition y */
        y?: (number|null);

        /** PetPosition z */
        z?: (number|null);

        /** PetPosition rotationX */
        rotationX?: (number|null);

        /** PetPosition rotationY */
        rotationY?: (number|null);

        /** PetPosition rotationZ */
        rotationZ?: (number|null);

        /** PetPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a PetPosition. */
    class PetPosition implements IPetPosition {

        /**
         * Constructs a new PetPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetPosition);

        /** PetPosition ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetPosition x. */
        public x: number;

        /** PetPosition y. */
        public y: number;

        /** PetPosition z. */
        public z: number;

        /** PetPosition rotationX. */
        public rotationX: number;

        /** PetPosition rotationY. */
        public rotationY: number;

        /** PetPosition rotationZ. */
        public rotationZ: number;

        /** PetPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new PetPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetPosition instance
         */
        public static create(properties?: game.IPetPosition): game.PetPosition;

        /**
         * Encodes the specified PetPosition message. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetPosition message, length delimited. Does not implicitly {@link game.PetPosition.verify|verify} messages.
         * @param message PetPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetPosition;

        /**
         * Decodes a PetPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetPosition;

        /**
         * Verifies a PetPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetPosition
         */
        public static fromObject(object: { [k: string]: any }): game.PetPosition;

        /**
         * Creates a plain object from a PetPosition message. Also converts values to other types if specified.
         * @param message PetPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a PetAnimation. */
    interface IPetAnimation {

        /** PetAnimation ownerBtcAddress */
        ownerBtcAddress?: (string|null);

        /** PetAnimation animationName */
        animationName?: (string|null);
    }

    /** Represents a PetAnimation. */
    class PetAnimation implements IPetAnimation {

        /**
         * Constructs a new PetAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IPetAnimation);

        /** PetAnimation ownerBtcAddress. */
        public ownerBtcAddress: string;

        /** PetAnimation animationName. */
        public animationName: string;

        /**
         * Creates a new PetAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns PetAnimation instance
         */
        public static create(properties?: game.IPetAnimation): game.PetAnimation;

        /**
         * Encodes the specified PetAnimation message. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified PetAnimation message, length delimited. Does not implicitly {@link game.PetAnimation.verify|verify} messages.
         * @param message PetAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IPetAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.PetAnimation;

        /**
         * Decodes a PetAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns PetAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.PetAnimation;

        /**
         * Verifies a PetAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a PetAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns PetAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.PetAnimation;

        /**
         * Creates a plain object from a PetAnimation message. Also converts values to other types if specified.
         * @param message PetAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.PetAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this PetAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for PetAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatEnter. */
    interface IChatEnter {

        /** ChatEnter chatId */
        chatId?: (number|null);
    }

    /** Represents a ChatEnter. */
    class ChatEnter implements IChatEnter {

        /**
         * Constructs a new ChatEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatEnter);

        /** ChatEnter chatId. */
        public chatId: number;

        /**
         * Creates a new ChatEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatEnter instance
         */
        public static create(properties?: game.IChatEnter): game.ChatEnter;

        /**
         * Encodes the specified ChatEnter message. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatEnter message, length delimited. Does not implicitly {@link game.ChatEnter.verify|verify} messages.
         * @param message ChatEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatEnter;

        /**
         * Decodes a ChatEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatEnter;

        /**
         * Verifies a ChatEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatEnter
         */
        public static fromObject(object: { [k: string]: any }): game.ChatEnter;

        /**
         * Creates a plain object from a ChatEnter message. Also converts values to other types if specified.
         * @param message ChatEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatLeave. */
    interface IChatLeave {

        /** ChatLeave chatId */
        chatId?: (number|null);
    }

    /** Represents a ChatLeave. */
    class ChatLeave implements IChatLeave {

        /**
         * Constructs a new ChatLeave.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatLeave);

        /** ChatLeave chatId. */
        public chatId: number;

        /**
         * Creates a new ChatLeave instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatLeave instance
         */
        public static create(properties?: game.IChatLeave): game.ChatLeave;

        /**
         * Encodes the specified ChatLeave message. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatLeave message, length delimited. Does not implicitly {@link game.ChatLeave.verify|verify} messages.
         * @param message ChatLeave message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatLeave, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatLeave;

        /**
         * Decodes a ChatLeave message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatLeave
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatLeave;

        /**
         * Verifies a ChatLeave message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatLeave message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatLeave
         */
        public static fromObject(object: { [k: string]: any }): game.ChatLeave;

        /**
         * Creates a plain object from a ChatLeave message. Also converts values to other types if specified.
         * @param message ChatLeave
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatLeave, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatLeave to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatLeave
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatMessage. */
    interface IChatMessage {

        /** ChatMessage uuid */
        uuid?: (string|null);

        /** ChatMessage playerId */
        playerId?: (string|null);

        /** ChatMessage content */
        content?: (string|null);

        /** ChatMessage replyTo */
        replyTo?: (string|null);

        /** ChatMessage timestamp */
        timestamp?: (number|Long|null);

        /** ChatMessage tabType */
        tabType?: (number|null);

        /** ChatMessage isAdmin */
        isAdmin?: (boolean|null);

        /** ChatMessage isTelegram */
        isTelegram?: (boolean|null);

        /** ChatMessage isSystem */
        isSystem?: (boolean|null);
    }

    /** Represents a ChatMessage. */
    class ChatMessage implements IChatMessage {

        /**
         * Constructs a new ChatMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatMessage);

        /** ChatMessage uuid. */
        public uuid: string;

        /** ChatMessage playerId. */
        public playerId: string;

        /** ChatMessage content. */
        public content: string;

        /** ChatMessage replyTo. */
        public replyTo: string;

        /** ChatMessage timestamp. */
        public timestamp: (number|Long);

        /** ChatMessage tabType. */
        public tabType: number;

        /** ChatMessage isAdmin. */
        public isAdmin: boolean;

        /** ChatMessage isTelegram. */
        public isTelegram: boolean;

        /** ChatMessage isSystem. */
        public isSystem: boolean;

        /**
         * Creates a new ChatMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatMessage instance
         */
        public static create(properties?: game.IChatMessage): game.ChatMessage;

        /**
         * Encodes the specified ChatMessage message. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatMessage message, length delimited. Does not implicitly {@link game.ChatMessage.verify|verify} messages.
         * @param message ChatMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatMessage;

        /**
         * Decodes a ChatMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatMessage;

        /**
         * Verifies a ChatMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatMessage
         */
        public static fromObject(object: { [k: string]: any }): game.ChatMessage;

        /**
         * Creates a plain object from a ChatMessage message. Also converts values to other types if specified.
         * @param message ChatMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a CommonMessage. */
    interface ICommonMessage {

        /** CommonMessage messageList */
        messageList?: (string[]|null);
    }

    /** Represents a CommonMessage. */
    class CommonMessage implements ICommonMessage {

        /**
         * Constructs a new CommonMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.ICommonMessage);

        /** CommonMessage messageList. */
        public messageList: string[];

        /**
         * Creates a new CommonMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns CommonMessage instance
         */
        public static create(properties?: game.ICommonMessage): game.CommonMessage;

        /**
         * Encodes the specified CommonMessage message. Does not implicitly {@link game.CommonMessage.verify|verify} messages.
         * @param message CommonMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.ICommonMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified CommonMessage message, length delimited. Does not implicitly {@link game.CommonMessage.verify|verify} messages.
         * @param message CommonMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.ICommonMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a CommonMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns CommonMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.CommonMessage;

        /**
         * Decodes a CommonMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns CommonMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.CommonMessage;

        /**
         * Verifies a CommonMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a CommonMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns CommonMessage
         */
        public static fromObject(object: { [k: string]: any }): game.CommonMessage;

        /**
         * Creates a plain object from a CommonMessage message. Also converts values to other types if specified.
         * @param message CommonMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.CommonMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this CommonMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for CommonMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ChatMessageDelete. */
    interface IChatMessageDelete {

        /** ChatMessageDelete uuid */
        uuid?: (string|null);

        /** ChatMessageDelete tabType */
        tabType?: (number|null);
    }

    /** Represents a ChatMessageDelete. */
    class ChatMessageDelete implements IChatMessageDelete {

        /**
         * Constructs a new ChatMessageDelete.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IChatMessageDelete);

        /** ChatMessageDelete uuid. */
        public uuid: string;

        /** ChatMessageDelete tabType. */
        public tabType: number;

        /**
         * Creates a new ChatMessageDelete instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ChatMessageDelete instance
         */
        public static create(properties?: game.IChatMessageDelete): game.ChatMessageDelete;

        /**
         * Encodes the specified ChatMessageDelete message. Does not implicitly {@link game.ChatMessageDelete.verify|verify} messages.
         * @param message ChatMessageDelete message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IChatMessageDelete, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ChatMessageDelete message, length delimited. Does not implicitly {@link game.ChatMessageDelete.verify|verify} messages.
         * @param message ChatMessageDelete message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IChatMessageDelete, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ChatMessageDelete message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ChatMessageDelete
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ChatMessageDelete;

        /**
         * Decodes a ChatMessageDelete message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ChatMessageDelete
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ChatMessageDelete;

        /**
         * Verifies a ChatMessageDelete message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ChatMessageDelete message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ChatMessageDelete
         */
        public static fromObject(object: { [k: string]: any }): game.ChatMessageDelete;

        /**
         * Creates a plain object from a ChatMessageDelete message. Also converts values to other types if specified.
         * @param message ChatMessageDelete
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ChatMessageDelete, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ChatMessageDelete to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ChatMessageDelete
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacket. */
    interface IRedPacket {

        /** RedPacket redPacketRecordId */
        redPacketRecordId?: (string|null);

        /** RedPacket createAddress */
        createAddress?: (string|null);
    }

    /** Represents a RedPacket. */
    class RedPacket implements IRedPacket {

        /**
         * Constructs a new RedPacket.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacket);

        /** RedPacket redPacketRecordId. */
        public redPacketRecordId: string;

        /** RedPacket createAddress. */
        public createAddress: string;

        /**
         * Creates a new RedPacket instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacket instance
         */
        public static create(properties?: game.IRedPacket): game.RedPacket;

        /**
         * Encodes the specified RedPacket message. Does not implicitly {@link game.RedPacket.verify|verify} messages.
         * @param message RedPacket message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacket, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacket message, length delimited. Does not implicitly {@link game.RedPacket.verify|verify} messages.
         * @param message RedPacket message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacket, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacket message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacket;

        /**
         * Decodes a RedPacket message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacket;

        /**
         * Verifies a RedPacket message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacket message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacket
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacket;

        /**
         * Creates a plain object from a RedPacket message. Also converts values to other types if specified.
         * @param message RedPacket
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacket, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacket to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacket
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketPoint. */
    interface IRedPacketPoint {

        /** RedPacketPoint configId */
        configId?: (number|null);

        /** RedPacketPoint redPacketList */
        redPacketList?: (game.IRedPacket[]|null);
    }

    /** Represents a RedPacketPoint. */
    class RedPacketPoint implements IRedPacketPoint {

        /**
         * Constructs a new RedPacketPoint.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketPoint);

        /** RedPacketPoint configId. */
        public configId: number;

        /** RedPacketPoint redPacketList. */
        public redPacketList: game.IRedPacket[];

        /**
         * Creates a new RedPacketPoint instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketPoint instance
         */
        public static create(properties?: game.IRedPacketPoint): game.RedPacketPoint;

        /**
         * Encodes the specified RedPacketPoint message. Does not implicitly {@link game.RedPacketPoint.verify|verify} messages.
         * @param message RedPacketPoint message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketPoint, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketPoint message, length delimited. Does not implicitly {@link game.RedPacketPoint.verify|verify} messages.
         * @param message RedPacketPoint message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketPoint, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketPoint message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketPoint;

        /**
         * Decodes a RedPacketPoint message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketPoint;

        /**
         * Verifies a RedPacketPoint message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketPoint message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketPoint
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketPoint;

        /**
         * Creates a plain object from a RedPacketPoint message. Also converts values to other types if specified.
         * @param message RedPacketPoint
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketPoint, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketPoint to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketPoint
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketUpdate. */
    interface IRedPacketUpdate {

        /** RedPacketUpdate pointList */
        pointList?: (game.IRedPacketPoint[]|null);
    }

    /** Represents a RedPacketUpdate. */
    class RedPacketUpdate implements IRedPacketUpdate {

        /**
         * Constructs a new RedPacketUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketUpdate);

        /** RedPacketUpdate pointList. */
        public pointList: game.IRedPacketPoint[];

        /**
         * Creates a new RedPacketUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketUpdate instance
         */
        public static create(properties?: game.IRedPacketUpdate): game.RedPacketUpdate;

        /**
         * Encodes the specified RedPacketUpdate message. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketUpdate message, length delimited. Does not implicitly {@link game.RedPacketUpdate.verify|verify} messages.
         * @param message RedPacketUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketUpdate;

        /**
         * Decodes a RedPacketUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketUpdate;

        /**
         * Verifies a RedPacketUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketUpdate;

        /**
         * Creates a plain object from a RedPacketUpdate message. Also converts values to other types if specified.
         * @param message RedPacketUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketCache. */
    interface IRedPacketCache {

        /** RedPacketCache pickedList */
        pickedList?: (string[]|null);
    }

    /** Represents a RedPacketCache. */
    class RedPacketCache implements IRedPacketCache {

        /**
         * Constructs a new RedPacketCache.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketCache);

        /** RedPacketCache pickedList. */
        public pickedList: string[];

        /**
         * Creates a new RedPacketCache instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketCache instance
         */
        public static create(properties?: game.IRedPacketCache): game.RedPacketCache;

        /**
         * Encodes the specified RedPacketCache message. Does not implicitly {@link game.RedPacketCache.verify|verify} messages.
         * @param message RedPacketCache message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketCache, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketCache message, length delimited. Does not implicitly {@link game.RedPacketCache.verify|verify} messages.
         * @param message RedPacketCache message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketCache, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketCache message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketCache
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketCache;

        /**
         * Decodes a RedPacketCache message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketCache
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketCache;

        /**
         * Verifies a RedPacketCache message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketCache message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketCache
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketCache;

        /**
         * Creates a plain object from a RedPacketCache message. Also converts values to other types if specified.
         * @param message RedPacketCache
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketCache, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketCache to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketCache
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RedPacketReward. */
    interface IRedPacketReward {

        /** RedPacketReward packetId */
        packetId?: (string|null);

        /** RedPacketReward receiverAddress */
        receiverAddress?: (string|null);

        /** RedPacketReward amount */
        amount?: (number|null);

        /** RedPacketReward receivedAt */
        receivedAt?: (number|Long|null);
    }

    /** Represents a RedPacketReward. */
    class RedPacketReward implements IRedPacketReward {

        /**
         * Constructs a new RedPacketReward.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRedPacketReward);

        /** RedPacketReward packetId. */
        public packetId: string;

        /** RedPacketReward receiverAddress. */
        public receiverAddress: string;

        /** RedPacketReward amount. */
        public amount: number;

        /** RedPacketReward receivedAt. */
        public receivedAt: (number|Long);

        /**
         * Creates a new RedPacketReward instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RedPacketReward instance
         */
        public static create(properties?: game.IRedPacketReward): game.RedPacketReward;

        /**
         * Encodes the specified RedPacketReward message. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RedPacketReward message, length delimited. Does not implicitly {@link game.RedPacketReward.verify|verify} messages.
         * @param message RedPacketReward message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRedPacketReward, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RedPacketReward;

        /**
         * Decodes a RedPacketReward message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RedPacketReward
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RedPacketReward;

        /**
         * Verifies a RedPacketReward message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RedPacketReward message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RedPacketReward
         */
        public static fromObject(object: { [k: string]: any }): game.RedPacketReward;

        /**
         * Creates a plain object from a RedPacketReward message. Also converts values to other types if specified.
         * @param message RedPacketReward
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RedPacketReward, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RedPacketReward to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RedPacketReward
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerPosition. */
    interface IClientPlayerPosition {

        /** ClientPlayerPosition x */
        x?: (number|null);

        /** ClientPlayerPosition y */
        y?: (number|null);

        /** ClientPlayerPosition z */
        z?: (number|null);

        /** ClientPlayerPosition rotationX */
        rotationX?: (number|null);

        /** ClientPlayerPosition rotationY */
        rotationY?: (number|null);

        /** ClientPlayerPosition rotationZ */
        rotationZ?: (number|null);

        /** ClientPlayerPosition rotationW */
        rotationW?: (number|null);
    }

    /** Represents a ClientPlayerPosition. */
    class ClientPlayerPosition implements IClientPlayerPosition {

        /**
         * Constructs a new ClientPlayerPosition.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerPosition);

        /** ClientPlayerPosition x. */
        public x: number;

        /** ClientPlayerPosition y. */
        public y: number;

        /** ClientPlayerPosition z. */
        public z: number;

        /** ClientPlayerPosition rotationX. */
        public rotationX: number;

        /** ClientPlayerPosition rotationY. */
        public rotationY: number;

        /** ClientPlayerPosition rotationZ. */
        public rotationZ: number;

        /** ClientPlayerPosition rotationW. */
        public rotationW: number;

        /**
         * Creates a new ClientPlayerPosition instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerPosition instance
         */
        public static create(properties?: game.IClientPlayerPosition): game.ClientPlayerPosition;

        /**
         * Encodes the specified ClientPlayerPosition message. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @param message ClientPlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerPosition message, length delimited. Does not implicitly {@link game.ClientPlayerPosition.verify|verify} messages.
         * @param message ClientPlayerPosition message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerPosition, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerPosition;

        /**
         * Decodes a ClientPlayerPosition message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerPosition
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerPosition;

        /**
         * Verifies a ClientPlayerPosition message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerPosition message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerPosition
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerPosition;

        /**
         * Creates a plain object from a ClientPlayerPosition message. Also converts values to other types if specified.
         * @param message ClientPlayerPosition
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerPosition, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerPosition to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerPosition
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerAnimation. */
    interface IClientPlayerAnimation {

        /** ClientPlayerAnimation curAnimation */
        curAnimation?: (string|null);
    }

    /** Represents a ClientPlayerAnimation. */
    class ClientPlayerAnimation implements IClientPlayerAnimation {

        /**
         * Constructs a new ClientPlayerAnimation.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerAnimation);

        /** ClientPlayerAnimation curAnimation. */
        public curAnimation: string;

        /**
         * Creates a new ClientPlayerAnimation instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerAnimation instance
         */
        public static create(properties?: game.IClientPlayerAnimation): game.ClientPlayerAnimation;

        /**
         * Encodes the specified ClientPlayerAnimation message. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @param message ClientPlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerAnimation message, length delimited. Does not implicitly {@link game.ClientPlayerAnimation.verify|verify} messages.
         * @param message ClientPlayerAnimation message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerAnimation, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerAnimation;

        /**
         * Decodes a ClientPlayerAnimation message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerAnimation
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerAnimation;

        /**
         * Verifies a ClientPlayerAnimation message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerAnimation message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerAnimation
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerAnimation;

        /**
         * Creates a plain object from a ClientPlayerAnimation message. Also converts values to other types if specified.
         * @param message ClientPlayerAnimation
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerAnimation, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerAnimation to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerAnimation
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerUpdate. */
    interface IClientPlayerUpdate {

        /** ClientPlayerUpdate itemId */
        itemId?: (number|null);

        /** ClientPlayerUpdate pizzaCount */
        pizzaCount?: (number|null);

        /** ClientPlayerUpdate petId */
        petId?: (string|null);

        /** ClientPlayerUpdate pizzaTick */
        pizzaTick?: (string|null);
    }

    /** Represents a ClientPlayerUpdate. */
    class ClientPlayerUpdate implements IClientPlayerUpdate {

        /**
         * Constructs a new ClientPlayerUpdate.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerUpdate);

        /** ClientPlayerUpdate itemId. */
        public itemId: number;

        /** ClientPlayerUpdate pizzaCount. */
        public pizzaCount: number;

        /** ClientPlayerUpdate petId. */
        public petId: string;

        /** ClientPlayerUpdate pizzaTick. */
        public pizzaTick: string;

        /**
         * Creates a new ClientPlayerUpdate instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerUpdate instance
         */
        public static create(properties?: game.IClientPlayerUpdate): game.ClientPlayerUpdate;

        /**
         * Encodes the specified ClientPlayerUpdate message. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @param message ClientPlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerUpdate message, length delimited. Does not implicitly {@link game.ClientPlayerUpdate.verify|verify} messages.
         * @param message ClientPlayerUpdate message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerUpdate, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerUpdate;

        /**
         * Decodes a ClientPlayerUpdate message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerUpdate
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerUpdate;

        /**
         * Verifies a ClientPlayerUpdate message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerUpdate message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerUpdate
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerUpdate;

        /**
         * Creates a plain object from a ClientPlayerUpdate message. Also converts values to other types if specified.
         * @param message ClientPlayerUpdate
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerUpdate, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerUpdate to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerUpdate
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPlayerEnter. */
    interface IClientPlayerEnter {

        /** ClientPlayerEnter avatarData */
        avatarData?: (game.IAvatarData|null);
    }

    /** Represents a ClientPlayerEnter. */
    class ClientPlayerEnter implements IClientPlayerEnter {

        /**
         * Constructs a new ClientPlayerEnter.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPlayerEnter);

        /** ClientPlayerEnter avatarData. */
        public avatarData?: (game.IAvatarData|null);

        /**
         * Creates a new ClientPlayerEnter instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPlayerEnter instance
         */
        public static create(properties?: game.IClientPlayerEnter): game.ClientPlayerEnter;

        /**
         * Encodes the specified ClientPlayerEnter message. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @param message ClientPlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPlayerEnter message, length delimited. Does not implicitly {@link game.ClientPlayerEnter.verify|verify} messages.
         * @param message ClientPlayerEnter message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPlayerEnter, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPlayerEnter;

        /**
         * Decodes a ClientPlayerEnter message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPlayerEnter
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPlayerEnter;

        /**
         * Verifies a ClientPlayerEnter message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPlayerEnter message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPlayerEnter
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPlayerEnter;

        /**
         * Creates a plain object from a ClientPlayerEnter message. Also converts values to other types if specified.
         * @param message ClientPlayerEnter
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPlayerEnter, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPlayerEnter to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPlayerEnter
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ReqPickUpRedPacket. */
    interface IReqPickUpRedPacket {

        /** ReqPickUpRedPacket configId */
        configId?: (number|null);

        /** ReqPickUpRedPacket redPacketRecordId */
        redPacketRecordId?: (string|null);
    }

    /** Represents a ReqPickUpRedPacket. */
    class ReqPickUpRedPacket implements IReqPickUpRedPacket {

        /**
         * Constructs a new ReqPickUpRedPacket.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IReqPickUpRedPacket);

        /** ReqPickUpRedPacket configId. */
        public configId: number;

        /** ReqPickUpRedPacket redPacketRecordId. */
        public redPacketRecordId: string;

        /**
         * Creates a new ReqPickUpRedPacket instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ReqPickUpRedPacket instance
         */
        public static create(properties?: game.IReqPickUpRedPacket): game.ReqPickUpRedPacket;

        /**
         * Encodes the specified ReqPickUpRedPacket message. Does not implicitly {@link game.ReqPickUpRedPacket.verify|verify} messages.
         * @param message ReqPickUpRedPacket message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IReqPickUpRedPacket, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ReqPickUpRedPacket message, length delimited. Does not implicitly {@link game.ReqPickUpRedPacket.verify|verify} messages.
         * @param message ReqPickUpRedPacket message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IReqPickUpRedPacket, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ReqPickUpRedPacket message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ReqPickUpRedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ReqPickUpRedPacket;

        /**
         * Decodes a ReqPickUpRedPacket message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ReqPickUpRedPacket
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ReqPickUpRedPacket;

        /**
         * Verifies a ReqPickUpRedPacket message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ReqPickUpRedPacket message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ReqPickUpRedPacket
         */
        public static fromObject(object: { [k: string]: any }): game.ReqPickUpRedPacket;

        /**
         * Creates a plain object from a ReqPickUpRedPacket message. Also converts values to other types if specified.
         * @param message ReqPickUpRedPacket
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ReqPickUpRedPacket, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ReqPickUpRedPacket to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ReqPickUpRedPacket
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientCutTree. */
    interface IClientCutTree {

        /** ClientCutTree treeTag */
        treeTag?: (number|null);

        /** ClientCutTree useItemId */
        useItemId?: (string|null);

        /** ClientCutTree treeServerId */
        treeServerId?: (string|null);
    }

    /** Represents a ClientCutTree. */
    class ClientCutTree implements IClientCutTree {

        /**
         * Constructs a new ClientCutTree.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientCutTree);

        /** ClientCutTree treeTag. */
        public treeTag: number;

        /** ClientCutTree useItemId. */
        public useItemId: string;

        /** ClientCutTree treeServerId. */
        public treeServerId: string;

        /**
         * Creates a new ClientCutTree instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientCutTree instance
         */
        public static create(properties?: game.IClientCutTree): game.ClientCutTree;

        /**
         * Encodes the specified ClientCutTree message. Does not implicitly {@link game.ClientCutTree.verify|verify} messages.
         * @param message ClientCutTree message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientCutTree, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientCutTree message, length delimited. Does not implicitly {@link game.ClientCutTree.verify|verify} messages.
         * @param message ClientCutTree message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientCutTree, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientCutTree message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientCutTree
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientCutTree;

        /**
         * Decodes a ClientCutTree message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientCutTree
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientCutTree;

        /**
         * Verifies a ClientCutTree message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientCutTree message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientCutTree
         */
        public static fromObject(object: { [k: string]: any }): game.ClientCutTree;

        /**
         * Creates a plain object from a ClientCutTree message. Also converts values to other types if specified.
         * @param message ClientCutTree
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientCutTree, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientCutTree to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientCutTree
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientMiningRock. */
    interface IClientMiningRock {

        /** ClientMiningRock rockTag */
        rockTag?: (number|null);

        /** ClientMiningRock useItemId */
        useItemId?: (string|null);
    }

    /** Represents a ClientMiningRock. */
    class ClientMiningRock implements IClientMiningRock {

        /**
         * Constructs a new ClientMiningRock.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientMiningRock);

        /** ClientMiningRock rockTag. */
        public rockTag: number;

        /** ClientMiningRock useItemId. */
        public useItemId: string;

        /**
         * Creates a new ClientMiningRock instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientMiningRock instance
         */
        public static create(properties?: game.IClientMiningRock): game.ClientMiningRock;

        /**
         * Encodes the specified ClientMiningRock message. Does not implicitly {@link game.ClientMiningRock.verify|verify} messages.
         * @param message ClientMiningRock message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientMiningRock, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientMiningRock message, length delimited. Does not implicitly {@link game.ClientMiningRock.verify|verify} messages.
         * @param message ClientMiningRock message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientMiningRock, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientMiningRock message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientMiningRock
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientMiningRock;

        /**
         * Decodes a ClientMiningRock message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientMiningRock
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientMiningRock;

        /**
         * Verifies a ClientMiningRock message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientMiningRock message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientMiningRock
         */
        public static fromObject(object: { [k: string]: any }): game.ClientMiningRock;

        /**
         * Creates a plain object from a ClientMiningRock message. Also converts values to other types if specified.
         * @param message ClientMiningRock
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientMiningRock, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientMiningRock to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientMiningRock
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientFishingSuccess. */
    interface IClientFishingSuccess {

        /** ClientFishingSuccess fishRecordId */
        fishRecordId?: (string|null);
    }

    /** Represents a ClientFishingSuccess. */
    class ClientFishingSuccess implements IClientFishingSuccess {

        /**
         * Constructs a new ClientFishingSuccess.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientFishingSuccess);

        /** ClientFishingSuccess fishRecordId. */
        public fishRecordId: string;

        /**
         * Creates a new ClientFishingSuccess instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientFishingSuccess instance
         */
        public static create(properties?: game.IClientFishingSuccess): game.ClientFishingSuccess;

        /**
         * Encodes the specified ClientFishingSuccess message. Does not implicitly {@link game.ClientFishingSuccess.verify|verify} messages.
         * @param message ClientFishingSuccess message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientFishingSuccess, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientFishingSuccess message, length delimited. Does not implicitly {@link game.ClientFishingSuccess.verify|verify} messages.
         * @param message ClientFishingSuccess message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientFishingSuccess, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientFishingSuccess message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientFishingSuccess
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientFishingSuccess;

        /**
         * Decodes a ClientFishingSuccess message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientFishingSuccess
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientFishingSuccess;

        /**
         * Verifies a ClientFishingSuccess message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientFishingSuccess message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientFishingSuccess
         */
        public static fromObject(object: { [k: string]: any }): game.ClientFishingSuccess;

        /**
         * Creates a plain object from a ClientFishingSuccess message. Also converts values to other types if specified.
         * @param message ClientFishingSuccess
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientFishingSuccess, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientFishingSuccess to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientFishingSuccess
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a ClientPickUpDrop. */
    interface IClientPickUpDrop {

        /** ClientPickUpDrop dropTag */
        dropTag?: (string|null);
    }

    /** Represents a ClientPickUpDrop. */
    class ClientPickUpDrop implements IClientPickUpDrop {

        /**
         * Constructs a new ClientPickUpDrop.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IClientPickUpDrop);

        /** ClientPickUpDrop dropTag. */
        public dropTag: string;

        /**
         * Creates a new ClientPickUpDrop instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ClientPickUpDrop instance
         */
        public static create(properties?: game.IClientPickUpDrop): game.ClientPickUpDrop;

        /**
         * Encodes the specified ClientPickUpDrop message. Does not implicitly {@link game.ClientPickUpDrop.verify|verify} messages.
         * @param message ClientPickUpDrop message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IClientPickUpDrop, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ClientPickUpDrop message, length delimited. Does not implicitly {@link game.ClientPickUpDrop.verify|verify} messages.
         * @param message ClientPickUpDrop message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IClientPickUpDrop, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a ClientPickUpDrop message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ClientPickUpDrop
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.ClientPickUpDrop;

        /**
         * Decodes a ClientPickUpDrop message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ClientPickUpDrop
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.ClientPickUpDrop;

        /**
         * Verifies a ClientPickUpDrop message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a ClientPickUpDrop message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ClientPickUpDrop
         */
        public static fromObject(object: { [k: string]: any }): game.ClientPickUpDrop;

        /**
         * Creates a plain object from a ClientPickUpDrop message. Also converts values to other types if specified.
         * @param message ClientPickUpDrop
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.ClientPickUpDrop, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ClientPickUpDrop to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ClientPickUpDrop
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a TreeData. */
    interface ITreeData {

        /** TreeData tag */
        tag?: (number|null);

        /** TreeData isAlive */
        isAlive?: (boolean|null);

        /** TreeData treeServerId */
        treeServerId?: (string|null);
    }

    /** Represents a TreeData. */
    class TreeData implements ITreeData {

        /**
         * Constructs a new TreeData.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.ITreeData);

        /** TreeData tag. */
        public tag: number;

        /** TreeData isAlive. */
        public isAlive: boolean;

        /** TreeData treeServerId. */
        public treeServerId: string;

        /**
         * Creates a new TreeData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns TreeData instance
         */
        public static create(properties?: game.ITreeData): game.TreeData;

        /**
         * Encodes the specified TreeData message. Does not implicitly {@link game.TreeData.verify|verify} messages.
         * @param message TreeData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.ITreeData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified TreeData message, length delimited. Does not implicitly {@link game.TreeData.verify|verify} messages.
         * @param message TreeData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.ITreeData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a TreeData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns TreeData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.TreeData;

        /**
         * Decodes a TreeData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns TreeData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.TreeData;

        /**
         * Verifies a TreeData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a TreeData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns TreeData
         */
        public static fromObject(object: { [k: string]: any }): game.TreeData;

        /**
         * Creates a plain object from a TreeData message. Also converts values to other types if specified.
         * @param message TreeData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.TreeData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this TreeData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for TreeData
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateTreeList. */
    interface IUpdateTreeList {

        /** UpdateTreeList treeDataList */
        treeDataList?: (game.ITreeData[]|null);
    }

    /** Represents an UpdateTreeList. */
    class UpdateTreeList implements IUpdateTreeList {

        /**
         * Constructs a new UpdateTreeList.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateTreeList);

        /** UpdateTreeList treeDataList. */
        public treeDataList: game.ITreeData[];

        /**
         * Creates a new UpdateTreeList instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateTreeList instance
         */
        public static create(properties?: game.IUpdateTreeList): game.UpdateTreeList;

        /**
         * Encodes the specified UpdateTreeList message. Does not implicitly {@link game.UpdateTreeList.verify|verify} messages.
         * @param message UpdateTreeList message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateTreeList, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateTreeList message, length delimited. Does not implicitly {@link game.UpdateTreeList.verify|verify} messages.
         * @param message UpdateTreeList message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateTreeList, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateTreeList message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateTreeList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateTreeList;

        /**
         * Decodes an UpdateTreeList message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateTreeList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateTreeList;

        /**
         * Verifies an UpdateTreeList message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateTreeList message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateTreeList
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateTreeList;

        /**
         * Creates a plain object from an UpdateTreeList message. Also converts values to other types if specified.
         * @param message UpdateTreeList
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateTreeList, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateTreeList to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateTreeList
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RockData. */
    interface IRockData {

        /** RockData tag */
        tag?: (number|null);

        /** RockData isAlive */
        isAlive?: (boolean|null);
    }

    /** Represents a RockData. */
    class RockData implements IRockData {

        /**
         * Constructs a new RockData.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IRockData);

        /** RockData tag. */
        public tag: number;

        /** RockData isAlive. */
        public isAlive: boolean;

        /**
         * Creates a new RockData instance using the specified properties.
         * @param [properties] Properties to set
         * @returns RockData instance
         */
        public static create(properties?: game.IRockData): game.RockData;

        /**
         * Encodes the specified RockData message. Does not implicitly {@link game.RockData.verify|verify} messages.
         * @param message RockData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IRockData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified RockData message, length delimited. Does not implicitly {@link game.RockData.verify|verify} messages.
         * @param message RockData message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IRockData, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RockData message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RockData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.RockData;

        /**
         * Decodes a RockData message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns RockData
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.RockData;

        /**
         * Verifies a RockData message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RockData message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RockData
         */
        public static fromObject(object: { [k: string]: any }): game.RockData;

        /**
         * Creates a plain object from a RockData message. Also converts values to other types if specified.
         * @param message RockData
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.RockData, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RockData to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RockData
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateRockList. */
    interface IUpdateRockList {

        /** UpdateRockList rockDataList */
        rockDataList?: (game.IRockData[]|null);
    }

    /** Represents an UpdateRockList. */
    class UpdateRockList implements IUpdateRockList {

        /**
         * Constructs a new UpdateRockList.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateRockList);

        /** UpdateRockList rockDataList. */
        public rockDataList: game.IRockData[];

        /**
         * Creates a new UpdateRockList instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateRockList instance
         */
        public static create(properties?: game.IUpdateRockList): game.UpdateRockList;

        /**
         * Encodes the specified UpdateRockList message. Does not implicitly {@link game.UpdateRockList.verify|verify} messages.
         * @param message UpdateRockList message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateRockList, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateRockList message, length delimited. Does not implicitly {@link game.UpdateRockList.verify|verify} messages.
         * @param message UpdateRockList message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateRockList, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateRockList message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateRockList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateRockList;

        /**
         * Decodes an UpdateRockList message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateRockList
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateRockList;

        /**
         * Verifies an UpdateRockList message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateRockList message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateRockList
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateRockList;

        /**
         * Creates a plain object from an UpdateRockList message. Also converts values to other types if specified.
         * @param message UpdateRockList
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateRockList, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateRockList to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateRockList
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateTreeRefresh. */
    interface IUpdateTreeRefresh {

        /** UpdateTreeRefresh leftTime */
        leftTime?: (number|null);
    }

    /** Represents an UpdateTreeRefresh. */
    class UpdateTreeRefresh implements IUpdateTreeRefresh {

        /**
         * Constructs a new UpdateTreeRefresh.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateTreeRefresh);

        /** UpdateTreeRefresh leftTime. */
        public leftTime: number;

        /**
         * Creates a new UpdateTreeRefresh instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateTreeRefresh instance
         */
        public static create(properties?: game.IUpdateTreeRefresh): game.UpdateTreeRefresh;

        /**
         * Encodes the specified UpdateTreeRefresh message. Does not implicitly {@link game.UpdateTreeRefresh.verify|verify} messages.
         * @param message UpdateTreeRefresh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateTreeRefresh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateTreeRefresh message, length delimited. Does not implicitly {@link game.UpdateTreeRefresh.verify|verify} messages.
         * @param message UpdateTreeRefresh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateTreeRefresh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateTreeRefresh message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateTreeRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateTreeRefresh;

        /**
         * Decodes an UpdateTreeRefresh message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateTreeRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateTreeRefresh;

        /**
         * Verifies an UpdateTreeRefresh message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateTreeRefresh message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateTreeRefresh
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateTreeRefresh;

        /**
         * Creates a plain object from an UpdateTreeRefresh message. Also converts values to other types if specified.
         * @param message UpdateTreeRefresh
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateTreeRefresh, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateTreeRefresh to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateTreeRefresh
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateRockRefresh. */
    interface IUpdateRockRefresh {

        /** UpdateRockRefresh leftTime */
        leftTime?: (number|null);
    }

    /** Represents an UpdateRockRefresh. */
    class UpdateRockRefresh implements IUpdateRockRefresh {

        /**
         * Constructs a new UpdateRockRefresh.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateRockRefresh);

        /** UpdateRockRefresh leftTime. */
        public leftTime: number;

        /**
         * Creates a new UpdateRockRefresh instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateRockRefresh instance
         */
        public static create(properties?: game.IUpdateRockRefresh): game.UpdateRockRefresh;

        /**
         * Encodes the specified UpdateRockRefresh message. Does not implicitly {@link game.UpdateRockRefresh.verify|verify} messages.
         * @param message UpdateRockRefresh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateRockRefresh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateRockRefresh message, length delimited. Does not implicitly {@link game.UpdateRockRefresh.verify|verify} messages.
         * @param message UpdateRockRefresh message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateRockRefresh, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateRockRefresh message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateRockRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateRockRefresh;

        /**
         * Decodes an UpdateRockRefresh message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateRockRefresh
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateRockRefresh;

        /**
         * Verifies an UpdateRockRefresh message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateRockRefresh message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateRockRefresh
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateRockRefresh;

        /**
         * Creates a plain object from an UpdateRockRefresh message. Also converts values to other types if specified.
         * @param message UpdateRockRefresh
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateRockRefresh, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateRockRefresh to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateRockRefresh
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateItem. */
    interface IUpdateItem {

        /** UpdateItem itemId */
        itemId?: (string|null);

        /** UpdateItem durability */
        durability?: (number|null);
    }

    /** Represents an UpdateItem. */
    class UpdateItem implements IUpdateItem {

        /**
         * Constructs a new UpdateItem.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateItem);

        /** UpdateItem itemId. */
        public itemId: string;

        /** UpdateItem durability. */
        public durability: number;

        /**
         * Creates a new UpdateItem instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateItem instance
         */
        public static create(properties?: game.IUpdateItem): game.UpdateItem;

        /**
         * Encodes the specified UpdateItem message. Does not implicitly {@link game.UpdateItem.verify|verify} messages.
         * @param message UpdateItem message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateItem message, length delimited. Does not implicitly {@link game.UpdateItem.verify|verify} messages.
         * @param message UpdateItem message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateItem, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateItem message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateItem;

        /**
         * Decodes an UpdateItem message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateItem
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateItem;

        /**
         * Verifies an UpdateItem message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateItem message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateItem
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateItem;

        /**
         * Creates a plain object from an UpdateItem message. Also converts values to other types if specified.
         * @param message UpdateItem
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateItem to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateItem
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateMaterial. */
    interface IUpdateMaterial {

        /** UpdateMaterial materialTag */
        materialTag?: (string|null);
    }

    /** Represents an UpdateMaterial. */
    class UpdateMaterial implements IUpdateMaterial {

        /**
         * Constructs a new UpdateMaterial.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateMaterial);

        /** UpdateMaterial materialTag. */
        public materialTag: string;

        /**
         * Creates a new UpdateMaterial instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateMaterial instance
         */
        public static create(properties?: game.IUpdateMaterial): game.UpdateMaterial;

        /**
         * Encodes the specified UpdateMaterial message. Does not implicitly {@link game.UpdateMaterial.verify|verify} messages.
         * @param message UpdateMaterial message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateMaterial, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateMaterial message, length delimited. Does not implicitly {@link game.UpdateMaterial.verify|verify} messages.
         * @param message UpdateMaterial message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateMaterial, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateMaterial message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateMaterial
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateMaterial;

        /**
         * Decodes an UpdateMaterial message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateMaterial
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateMaterial;

        /**
         * Verifies an UpdateMaterial message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateMaterial message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateMaterial
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateMaterial;

        /**
         * Creates a plain object from an UpdateMaterial message. Also converts values to other types if specified.
         * @param message UpdateMaterial
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateMaterial, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateMaterial to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateMaterial
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdateRandomEvent. */
    interface IUpdateRandomEvent {

        /** UpdateRandomEvent tag */
        tag?: (string|null);

        /** UpdateRandomEvent quantity */
        quantity?: (string|null);

        /** UpdateRandomEvent eventType */
        eventType?: (string|null);
    }

    /** Represents an UpdateRandomEvent. */
    class UpdateRandomEvent implements IUpdateRandomEvent {

        /**
         * Constructs a new UpdateRandomEvent.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdateRandomEvent);

        /** UpdateRandomEvent tag. */
        public tag: string;

        /** UpdateRandomEvent quantity. */
        public quantity: string;

        /** UpdateRandomEvent eventType. */
        public eventType: string;

        /**
         * Creates a new UpdateRandomEvent instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdateRandomEvent instance
         */
        public static create(properties?: game.IUpdateRandomEvent): game.UpdateRandomEvent;

        /**
         * Encodes the specified UpdateRandomEvent message. Does not implicitly {@link game.UpdateRandomEvent.verify|verify} messages.
         * @param message UpdateRandomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdateRandomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdateRandomEvent message, length delimited. Does not implicitly {@link game.UpdateRandomEvent.verify|verify} messages.
         * @param message UpdateRandomEvent message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdateRandomEvent, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdateRandomEvent message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdateRandomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdateRandomEvent;

        /**
         * Decodes an UpdateRandomEvent message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdateRandomEvent
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdateRandomEvent;

        /**
         * Verifies an UpdateRandomEvent message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdateRandomEvent message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdateRandomEvent
         */
        public static fromObject(object: { [k: string]: any }): game.UpdateRandomEvent;

        /**
         * Creates a plain object from an UpdateRandomEvent message. Also converts values to other types if specified.
         * @param message UpdateRandomEvent
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdateRandomEvent, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdateRandomEvent to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdateRandomEvent
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an UpdatePickUpPoint. */
    interface IUpdatePickUpPoint {

        /** UpdatePickUpPoint tag */
        tag?: (string|null);

        /** UpdatePickUpPoint isPickedUp */
        isPickedUp?: (boolean|null);

        /** UpdatePickUpPoint coolDown */
        coolDown?: (number|Long|null);
    }

    /** Represents an UpdatePickUpPoint. */
    class UpdatePickUpPoint implements IUpdatePickUpPoint {

        /**
         * Constructs a new UpdatePickUpPoint.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.IUpdatePickUpPoint);

        /** UpdatePickUpPoint tag. */
        public tag: string;

        /** UpdatePickUpPoint isPickedUp. */
        public isPickedUp: boolean;

        /** UpdatePickUpPoint coolDown. */
        public coolDown: (number|Long);

        /**
         * Creates a new UpdatePickUpPoint instance using the specified properties.
         * @param [properties] Properties to set
         * @returns UpdatePickUpPoint instance
         */
        public static create(properties?: game.IUpdatePickUpPoint): game.UpdatePickUpPoint;

        /**
         * Encodes the specified UpdatePickUpPoint message. Does not implicitly {@link game.UpdatePickUpPoint.verify|verify} messages.
         * @param message UpdatePickUpPoint message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.IUpdatePickUpPoint, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified UpdatePickUpPoint message, length delimited. Does not implicitly {@link game.UpdatePickUpPoint.verify|verify} messages.
         * @param message UpdatePickUpPoint message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.IUpdatePickUpPoint, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an UpdatePickUpPoint message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UpdatePickUpPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.UpdatePickUpPoint;

        /**
         * Decodes an UpdatePickUpPoint message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns UpdatePickUpPoint
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.UpdatePickUpPoint;

        /**
         * Verifies an UpdatePickUpPoint message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an UpdatePickUpPoint message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UpdatePickUpPoint
         */
        public static fromObject(object: { [k: string]: any }): game.UpdatePickUpPoint;

        /**
         * Creates a plain object from an UpdatePickUpPoint message. Also converts values to other types if specified.
         * @param message UpdatePickUpPoint
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.UpdatePickUpPoint, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UpdatePickUpPoint to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UpdatePickUpPoint
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a LarkMessage. */
    interface ILarkMessage {

        /** LarkMessage title */
        title?: (string|null);

        /** LarkMessage connect */
        connect?: (string|null);
    }

    /** Represents a LarkMessage. */
    class LarkMessage implements ILarkMessage {

        /**
         * Constructs a new LarkMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: game.ILarkMessage);

        /** LarkMessage title. */
        public title: string;

        /** LarkMessage connect. */
        public connect: string;

        /**
         * Creates a new LarkMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns LarkMessage instance
         */
        public static create(properties?: game.ILarkMessage): game.LarkMessage;

        /**
         * Encodes the specified LarkMessage message. Does not implicitly {@link game.LarkMessage.verify|verify} messages.
         * @param message LarkMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: game.ILarkMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified LarkMessage message, length delimited. Does not implicitly {@link game.LarkMessage.verify|verify} messages.
         * @param message LarkMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: game.ILarkMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a LarkMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns LarkMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): game.LarkMessage;

        /**
         * Decodes a LarkMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns LarkMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): game.LarkMessage;

        /**
         * Verifies a LarkMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a LarkMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns LarkMessage
         */
        public static fromObject(object: { [k: string]: any }): game.LarkMessage;

        /**
         * Creates a plain object from a LarkMessage message. Also converts values to other types if specified.
         * @param message LarkMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: game.LarkMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this LarkMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for LarkMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
