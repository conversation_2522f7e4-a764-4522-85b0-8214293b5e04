/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useRef, useState } from 'react';
import { PetObjectData } from '@/world/Pet/PetObject';
import { useFrame } from '@react-three/fiber';

class GlobalPetManager {
  private petMap = new Map<string, PetObjectData>();

  constructor() {}

  addPet(target: THREE.Object3D, type: number) {
    const uuid = THREE.MathUtils.generateUUID();
    const petData = new PetObjectData(uuid, target);
    this.petMap.set(uuid, petData);
  }

  getPet(uuid: string) {
    return this.petMap.get(uuid);
  }

  deletePet(uuid: string) {
    this.petMap.delete(uuid);
  }

  clearPet() {
    this.petMap.clear();
  }

  getElement() {
    const oldPetCount = useRef(0);
    const [petDataList, setPetDataList] = useState<PetObjectData[]>([]);

    useFrame((state) => {
      const newPetCount = this.petMap.size;
      if (newPetCount !== oldPetCount.current) {
        oldPetCount.current = newPetCount;
        setPetDataList([...this.petMap.values()]);
      }
    });

    return (
      <>
        {petDataList.map((petData, index) => {
          return petData.getReact();
        })}
      </>
    );
  }
}

const PetManager = new GlobalPetManager();
export default PetManager;

export function UsePetManager() {
  return <>{PetManager.getElement()}</>;
}
