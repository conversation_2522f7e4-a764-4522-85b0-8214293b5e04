import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { useFrame, useThree } from '@react-three/fiber';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';

export default function PetMesh({ rootOjb, url }: { rootOjb: THREE.Object3D; url: string }) {
  const { scene } = useThree();
  const [petRoot, setPetRoot] = useState<THREE.Object3D | null>();

  useEffect(() => {
    const root = new THREE.Object3D();
    scene.add(root);
    setPetRoot(root);
    return () => {
      scene.remove(root);
      setPetRoot(null);
    };
  }, []);

  useEffect(() => {
    if (petRoot) {
      petRoot.clear();
      let cancel = false;
      const loader = new GLTFLoader();
      url = getCdnLink('./assets/temp_pet.glb');
      loader.load(url, (gltf) => {
        if (cancel) return;
        gltf.scene.name = url;

        gltf.scene.traverse((child) => {
          const mesh = child as THREE.Mesh;
          if (mesh) {
            const material = mesh.material as THREE.MeshStandardMaterial;
            if (material) {
              mesh.material = new THREE.MeshBasicMaterial({
                color: 0xffffff,
                map: material.map,
              });
              mesh.castShadow = false;
              mesh.receiveShadow = false;
            }
          }
        });
        petRoot.add(gltf.scene);
      });
      return () => {
        cancel = true;
        petRoot.clear();
      };
    }
  }, [petRoot]);

  useFrame(() => {
    if (petRoot) {
      const worldPos = new THREE.Vector3();
      const worldQuat = new THREE.Quaternion();
      rootOjb.getWorldPosition(worldPos);
      rootOjb.getWorldQuaternion(worldQuat);

      petRoot.position.copy(worldPos);
      petRoot.quaternion.copy(worldQuat);
    }
  });

  return null;
}
