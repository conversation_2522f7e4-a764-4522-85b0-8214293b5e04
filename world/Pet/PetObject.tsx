import React, { Suspense, useEffect, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { GLTF } from 'three-stdlib';
import Pet<PERSON>esh from '@/world/Pet/PetMesh';
import { useAnimations } from '@react-three/drei';

function IdleAnimation(props: { animations: THREE.AnimationClip[]; children: React.ReactNode }) {
  const group = useRef<THREE.Group>(null);

  /**
   * Character animations setup
   */
  const { actions } = useAnimations(props.animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export class PetObjectData {
  // isChange: boolean = false
  // curAnimation: string | undefined
  id: string;
  target: THREE.Object3D;
  type: number = 0;
  root: THREE.Object3D | null = null;

  constructor(id: string, target: THREE.Object3D) {
    this.id = id;
    this.target = target;
  }

  getReact(children?: React.ReactNode) {
    return <PetObject data={this}>{children}</PetObject>;
  }
}

function PetObject({ data, children }: { data: PetObjectData; children: React.ReactNode }) {
  const ref = useRef<THREE.Group>(null);
  const startFollow = useRef(false);
  const smoothFactor = 0.25; // 插值系数，尝试调整这个值

  const [glb, setGlb] = useState<GLTF | null>(null);
  const [petRoot, setPetRoot] = useState<THREE.Object3D | null>();
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);

  useEffect(() => {
    const loader = new GLTFLoader();

    loader.load('./assets/98.glb', (gltf) => {
      setGlb(gltf as any);
    });
  }, []);

  useEffect(() => {
    if (glb && ref.current) {
      data.root = ref.current;
      // let box = new THREE.Box3().setFromObject(glb.scene);
      // const height = box.max.y - box.min.y
      // const centerHeight = height / 2
      const group = new THREE.Group();
      group.position.set(0, 1, 0);
      group.add(glb.scene);
      glb.scene.traverse((child: THREE.Object3D) => {
        if (child.name === 'Bone') {
          setPetRoot(child);
        }
      });
      setAnimations(glb.animations);
      ref.current.add(group);
      data.target.userData.pet = ref.current;
      return () => {
        data.root = null;
        data.target.userData.pet = null;
      };
    }
  }, [glb]);

  useFrame(() => {
    if (ref.current && data.target) {
      const currentPos = ref.current.position;
      const currentQuat = ref.current.quaternion;
      const targetPosition = new THREE.Vector3();
      const targetQuaternion = new THREE.Quaternion();
      data.target.getWorldPosition(targetPosition);
      data.target.getWorldQuaternion(targetQuaternion);

      const distance = currentPos.distanceTo(targetPosition);

      // 设置一个阈值，当距离小于该值时，停止移动
      const stopThreshold = 0.8; // 可以根据需要调整
      const startThreshold = 1; // 可以根据需要调整

      // 动态调整平滑因子，距离越远，平滑因子越大
      const dynamicSmoothFactor = Math.min(smoothFactor * (distance / 10), 1); // 根据距离调整平滑因子

      if (distance > stopThreshold) {
        if (distance > startThreshold) {
          startFollow.current = true;
        }
        if (startFollow.current) {
          // 插值平滑位置
          currentPos.lerp(targetPosition, dynamicSmoothFactor);
        }
        // 使用 copy 方法复制目标四元数
        currentQuat.slerp(targetQuaternion, dynamicSmoothFactor);
      } else {
        startFollow.current = false;
        // 使用 copy 方法复制目标四元数
        currentQuat.slerp(targetQuaternion, dynamicSmoothFactor);
      }
    }
  });

  return (
    <>
      <IdleAnimation animations={animations}>
        <group ref={ref}>{children}</group>
      </IdleAnimation>
      {petRoot && <PetMesh rootOjb={petRoot} url={''} />}
    </>
  );
}

export default PetObject;
