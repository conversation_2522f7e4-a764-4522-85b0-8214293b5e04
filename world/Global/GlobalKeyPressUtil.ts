class GlobalKeyPressUtil {
  private isInit = false;
  private disableMap = new Map<string, string[]>();

  private downRegisterMap = new Map<string, ((event: KeyboardEvent) => void)[]>();
  private upRegisterMap = new Map<string, ((event: KeyboardEvent) => void)[]>();

  setCallback(callback: (enable: boolean) => void) {
    this.callback = callback;
    this.callback(this.disableMap.size == 0);
  }

  setEnable(enable: boolean, disableKey = 'default', superKeys: string[] = []) {
    if (!enable) {
      this.disableMap.set(disableKey, superKeys);
      //当操控禁用时，调用所有up方法
      this.upRegisterMap.forEach((callbackList, key) => {
        callbackList.forEach((callback) => {
          callback({ key } as KeyboardEvent);
        });
      });
    } else {
      this.disableMap.delete(disableKey);
    }
    this.callback(this.disableMap.size == 0);
  }

  getEnable() {
    return this.disableMap.size == 0;
  }

  registerKeyPress(
    keys: string[],
    downCallback: (event: KeyboardEvent) => void,
    upCallback?: (event: KeyboardEvent) => void
  ) {
    this.init();
    const downRegisterMap = this.downRegisterMap;
    keys.forEach((key) => {
      if (!downRegisterMap.has(key)) {
        downRegisterMap.set(key, []);
      }
      downRegisterMap.get(key)?.push(downCallback);
    });
    if (upCallback) {
      keys.forEach((key) => {
        if (!this.upRegisterMap.has(key)) {
          this.upRegisterMap.set(key, []);
        }
        this.upRegisterMap.get(key)?.push(downCallback);
      });
    }
    return () => {
      keys.forEach((key) => {
        const callbackList = downRegisterMap.get(key) || [];
        for (let index = 0; index < callbackList.length; index++) {
          const callback = callbackList[index];
          if (callback === downCallback) {
            callbackList.splice(index, 1);
            break;
          }
        }
      });
      if (upCallback) {
        keys.forEach((key) => {
          const callbackList = this.upRegisterMap.get(key) || [];
          for (let index = 0; index < callbackList.length; index++) {
            const callback = callbackList[index];
            if (callback === downCallback) {
              callbackList.splice(index, 1);
              break;
            }
          }
        });
      }
    };
  }

  private callback: (enable: boolean) => void = () => undefined;

  private init() {
    if (this.isInit) {
      return;
    }
    this.isInit = true;
    window.addEventListener('keydown', (event) => {
      const superKeys = Array.from(this.disableMap.values())[this.disableMap.size - 1];
      if (this.disableMap.size == 0 || superKeys.includes(event.key)) {
        const callbackList = this.downRegisterMap.get(event.key) || [];
        callbackList.forEach((callback) => {
          callback(event);
        });
      }
    });
    window.addEventListener('keyup', (event) => {
      const superKeys = Array.from(this.disableMap.values())[this.disableMap.size - 1];
      if (this.disableMap.size == 0 || superKeys.includes(event.key)) {
        const callbackList = this.upRegisterMap.get(event.key) || [];
        callbackList.forEach((callback) => {
          callback(event);
        });
      }
    });
  }
}

export const KeyPressUtil = new GlobalKeyPressUtil();
