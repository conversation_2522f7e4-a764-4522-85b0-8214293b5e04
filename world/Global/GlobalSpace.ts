import * as THREE from 'three';
import { generateUUID } from 'three/src/math/MathUtils';
import AvatarObject from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject';
import createUseGame from '../../src/stores/useGame';
import { GameOpItem } from 'components/GameWindow/GameOpWindow';

const gameActionMap: Map<string, GameOpItem> = new Map();
const gameActionCallbackList: ((map: Map<string, GameOpItem>) => void)[] = [];

export enum GAME_OP_TYPE {
  PotatoOp = 'PotatoOp',
  ChatOp = 'ChatOp',
  HitTree = 'HitTree',
  Mining = 'Mining',
  CustomOp = 'CustomOp',
  ShowTip = 'ShowTip',
}

let myUseGame: any = null;

function getMyUseGame() {
  if (!myUseGame) {
    myUseGame = createUseGame();
  }

  return myUseGame;
}

function updateGameOpList() {
  for (const argumentsKey in gameActionCallbackList) {
    const callback = gameActionCallbackList[argumentsKey];
    if (callback) {
      callback(gameActionMap);
    }
  }
}

function watchGameOpChange(callback: (map: Map<string, GameOpItem>) => void) {
  gameActionCallbackList.push(callback);
}

function addGameOp(
  type: GAME_OP_TYPE,
  callback: () => void,
  sortIndex = 0,
  text?: string,
  icon_src?: string
) {
  const uuid = THREE.MathUtils.generateUUID();
  gameActionMap.set(uuid, { type, callback, sortIndex, text, icon_src });
  updateGameOpList();
  return uuid;
}

function removeGameOp(uuid: string) {
  gameActionMap.delete(uuid);
  updateGameOpList();
}

const characterPositionCallbackMap: Map<string, (position: THREE.Vector3) => void> = new Map();

function updateCharacterPosition(position: THREE.Vector3) {
  for (const [argumentsKey, callback] of characterPositionCallbackMap) {
    callback(position);
  }
}

function whatCharacterPosition(callback: (position: THREE.Vector3) => void) {
  const key = generateUUID();
  characterPositionCallbackMap.set(key, callback);
  return key;
}

function cancelCharacterPositionCallback(key: string) {
  characterPositionCallbackMap.delete(key);
}

const hoverList: THREE.Object3D[] = [];

function addHoverObject(object: THREE.Object3D) {
  hoverList.push(object); // hoverList
}

function removeHoverObject(object: THREE.Object3D) {
  const index = hoverList.indexOf(object);
  if (index > -1) {
    hoverList.splice(index, 1);
  }
}

function getHoverObjectList() {
  return hoverList;
}

const clickList: THREE.Object3D[] = [];

function addClickObject(object: THREE.Object3D) {
  clickList.push(object); // hoverList
}

function removeClickObject(object: THREE.Object3D) {
  const index = clickList.indexOf(object);
  if (index > -1) {
    clickList.splice(index, 1);
  }
}

function getClickObjectList() {
  return clickList;
}

export default {
  getMyUseGame,
  watchGameOpChange,
  addGameOp,
  removeGameOp,
  whatCharacterPosition,
  updateCharacterPosition,
  cancelCharacterPositionCallback,
  addHoverObject,
  getHoverObjectList,
  removeHoverObject,
  addClickObject,
  getClickObjectList,
  removeClickObject,
};
