import * as THREE from 'three';
import { IAssistantAnswer, SCENE_TYPE } from '@/constant/type';
import AvatarObject from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject';
import { useFrame } from '@react-three/fiber';
import React, { useEffect, useRef, useState } from 'react';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
  TransformData,
} from './GlobalSpaceEvent';
import { OtherPlayerData } from '../Character/OtherPlayer';
import { generateUUID } from 'three/src/math/MathUtils';
import createUseGame from '../../src/stores/useGame';
import AvatarData from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import { Raycaster } from 'three/src/Three.Core';
import { VTTSubtitle } from '../Util/SpeakUtil';
import GlobalSpace, { GAME_OP_TYPE } from './GlobalSpace';
import { AnswerTask } from '../SceneUI/TopAnswerUI';
import { POPOVER_HEIGHT } from '@/constant';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';

import BatterySvg from '@/public/image/gameOpIcon/battery.svg';
import { AudioSystem } from '@/world/Global/GlobalAudioSystem';

export type ButlerData = {
  butlerPosition: { x: number; y: number; z: number };
  butlerQuaternion: { x: number; y: number; z: number; w: number };
  butlerSceneType: SCENE_TYPE;
  usePet: string;
  vttData: VTTSubtitle[];
  mp3Url: string;
  visitorPositionList: { x: number; y: number; z: number }[];
};

class GlobalButlerUtil {
  private data: ButlerData | null = null;
  private avatarObj: AvatarObject | null = null;
  private playerObj: THREE.Object3D | null = null;
  private otherPlayerData: OtherPlayerData;
  private scene: THREE.Scene | null = null;
  private isVisitor = false;
  private isEnterVisitor = false;

  constructor() {
    const id = generateUUID();
    const useGame = createUseGame();
    const avatarData = new AvatarData();
    this.otherPlayerData = new OtherPlayerData(id, avatarData, useGame);
  }

  setVisitor(isVisitor: boolean) {
    this.isVisitor = isVisitor;
    if (!isVisitor) {
      this.clearButlerData();
    }
  }

  initButlerData(data: ButlerData | null, scene: THREE.Scene) {
    if (this.data) {
      // console.error('butler data has already init')
      return;
    }
    this.data = {
      butlerPosition: { x: 0, y: 0, z: 0 },
      butlerQuaternion: { x: 0, y: 0, z: 0, w: 1 },
      butlerSceneType: SCENE_TYPE.None,
      usePet: '',
      vttData: [],
      mp3Url: '',
      //默认访客在中间位置出生
      visitorPositionList: [],
    };
    this.scene = scene;
    this.otherPlayerData.position.copy(this.data.butlerPosition);
    this.otherPlayerData.quaternion.copy(this.data.butlerQuaternion);
    this.otherPlayerData.curAnimation = 'idle';
    this.otherPlayerData.isChange = true;
    if (data) {
      this.data = data;
      this.otherPlayerData.loadVTT(this.data.vttData, this.data.mp3Url);
    }
  }

  clearButlerData() {
    this.data = null;
    this.avatarObj = null;
    this.playerObj = null;
    this.scene = null;
    GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
      characterType: CharacterType.None,
      position: new THREE.Vector3(0, 0, 0),
    });
  }

  resetData(data: ButlerData) {
    if (this.data && this.otherPlayerData && this.isVisitor) {
      const oldSceneType = String(data.butlerSceneType);
      if (oldSceneType === 'Island') {
        data.butlerSceneType = SCENE_TYPE.Island;
      }
      if (data.butlerPosition.x > -100) {
        data.butlerPosition.x -= 150;
        for (let i = 0; i < data.visitorPositionList.length; i++) {
          data.visitorPositionList[i].x -= 150;
        }
      }
      this.otherPlayerData.stopSpeak();
      this.data.vttData = data.vttData;
      this.data.mp3Url = data.mp3Url;
      this.data.visitorPositionList = data.visitorPositionList;
      this.data.usePet = data.usePet;
      this.data.butlerSceneType = data.butlerSceneType;
      this.data.butlerPosition = data.butlerPosition;
      this.data.butlerQuaternion = data.butlerQuaternion;

      this.otherPlayerData.position.copy(this.data.butlerPosition);
      this.otherPlayerData.quaternion.copy(this.data.butlerQuaternion);
      this.otherPlayerData.curAnimation = 'idle';
      this.otherPlayerData.usePet = data.usePet;
      if (data.usePet.length > 0) {
        this.otherPlayerData.meshScale = 2;
      }
      this.otherPlayerData.isChange = true;
      this.otherPlayerData.loadVTT(this.data.vttData, this.data.mp3Url);
    } else {
      setTimeout(() => {
        this.resetData(data);
      }, 100);
    }
  }

  setAvatarObj(avatarObj: AvatarObject) {
    this.avatarObj = avatarObj;
  }

  enterVisitor() {
    if (this.data && this.data.butlerSceneType) {
      GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game);
      const randomPos = this.getRandomVisitorTransform();
      const butlerPosition = this.data.butlerPosition;
      const direction = randomPos.clone().sub(butlerPosition).normalize();
      GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
        position: new THREE.Vector3(randomPos.x, randomPos.y, randomPos.z),
        characterType: CharacterType.Player,
        camDirection: direction,
        sceneType: this.data.butlerSceneType,
      });
      setTimeout(() => {
        this.isEnterVisitor = true;
      }, 1000);
    } else {
      GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
        position: new THREE.Vector3(0, 1, 0),
        characterType: CharacterType.Player,
        sceneType: SCENE_TYPE.Room,
      });
    }
  }

  getButlerData() {
    return this.data;
  }

  setPlayerObj(obj: THREE.Object3D) {
    this.playerObj = obj;
  }

  getOtherPlayerObj() {
    return this.playerObj;
  }

  updateVisitorTransform(justShowPoint = false) {
    if (this.data && this.scene) {
      const scene = this.scene;
      if (justShowPoint) {
        const sphereList: THREE.Mesh[] = [];
        // 你还可以在场景中高亮显示这些被遮挡的点
        this.data.visitorPositionList.forEach((point) => {
          const geometry = new THREE.SphereGeometry(0.1);
          const material = new THREE.MeshBasicMaterial({ color: 0xff0000 }); // 红色高亮
          const sphere = new THREE.Mesh(geometry, material);
          sphere.position.set(point.x, point.y, point.z);
          sphereList.push(sphere);
          scene.add(sphere);
        });

        setTimeout(() => {
          sphereList.forEach((sphere) => {
            scene.remove(sphere);
          });
        }, 5000);
        return;
      }

      function getSectorPoints(
        center: THREE.Vector3,
        radius: number,
        angle: number,
        quaternion: THREE.Quaternion,
        segments = 32
      ) {
        const points = [];
        const halfAngle = angle / 2;
        const angleStep = angle / segments;

        for (let i = -halfAngle; i <= halfAngle; i += angleStep) {
          const theta = THREE.MathUtils.degToRad(i + 90);
          const x = radius * Math.cos(theta);
          const z = radius * Math.sin(theta);
          const point = new THREE.Vector3(x, 0, z);

          // 应用四元数旋转
          point.applyQuaternion(quaternion);

          // 平移至中心点
          point.add(center);

          points.push(point);
        }

        return points;
      }

      // 设置中心点和半径
      const center = new THREE.Vector3().copy(this.data.butlerPosition); // 中心点坐标
      const quaternion = new THREE.Quaternion().copy(this.data.butlerQuaternion); // 中心点坐标
      center.y += 2;
      const radius = 5; // 访客出生点半径
      const angle = 120; // 扇形角度

      // 生成若干个坐标点
      const points = getSectorPoints(center, radius, angle, quaternion, 12);

      // 创建Raycaster实例
      const raycaster = new Raycaster();

      // 创建一个数组，用于存储被遮挡的点
      const noBlockedPoints: THREE.Vector3[] = [];

      //解决特效object导致的报错
      const list: THREE.Object3D[] = [];
      scene.traverse((value) => {
        if (value.type === 'Mesh') {
          list.push(value);
        }
      });
      // 遍历每个生成的点
      points.forEach((point) => {
        // 从中心点到生成的点发射射线
        const direction = point.clone().sub(center).normalize();
        raycaster.set(center, direction);
        raycaster.far = radius + 1; //多检测1的长度 , 给出生点留出体积

        // 获取射线与场景中物体的相交情况
        const intersects = raycaster.intersectObjects(list);

        // 如果射线与物体相交，说明有遮挡物
        if (intersects.length === 0) {
          noBlockedPoints.push(point);
        }
      });

      if (noBlockedPoints && noBlockedPoints.length === 0) {
        noBlockedPoints.push(center);
      }

      // 输出无遮挡的点
      this.data.visitorPositionList = noBlockedPoints;

      return true;
    }
    return false;
  }

  getRandomVisitorTransform() {
    if (this.data && this.data.visitorPositionList.length > 0) {
      const list = this.data.visitorPositionList;
      if (list && list.length > 0) {
        const index = Math.floor(Math.random() * list.length);
        return new THREE.Vector3(list[index].x, list[index].y, list[index].z);
      }
    }
    return new THREE.Vector3(0, 1, 0);
  }

  setNearButler(callback: () => void) {
    this.nearButler = callback;
  }

  setFarButler(callback: () => void) {
    this.farButler = callback;
  }

  hideButler() {
    if (this.data) {
      this.data.butlerSceneType = SCENE_TYPE.None;
    }
  }

  updateButlerTransform() {
    if (this.data && this.playerObj && this.avatarObj && this.otherPlayerData) {
      const data = this.data;
      const playerObj = this.playerObj;
      const avatarObj = this.avatarObj;
      const otherPlayerData = this.otherPlayerData;
      GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
        GlobalDataKey.SceneType,
        (type) => {
          GlobalSpaceEvent.ListenKeyDataChange<string>(
            GlobalDataKey.UsePetInscriptionId,
            (usePet) => {
              data.butlerSceneType = type;

              const position = new THREE.Vector3();
              const quaternion = new THREE.Quaternion();
              playerObj.getWorldPosition(position);
              playerObj.getWorldQuaternion(quaternion);
              data.usePet = usePet;
              data.butlerPosition = {
                x: position.x,
                y: position.y,
                z: position.z,
              };
              data.butlerQuaternion = {
                x: quaternion.x,
                y: quaternion.y,
                z: quaternion.z,
                w: quaternion.w,
              };
              otherPlayerData.position.copy(data.butlerPosition);
              otherPlayerData.quaternion.copy(data.butlerQuaternion);
              otherPlayerData.curAnimation = 'idle';
              otherPlayerData.usePet = data.usePet;
              if (data.usePet.length > 0) {
                otherPlayerData.meshScale = 2;
              }
              otherPlayerData.isChange = true;

              this.updateVisitorTransform();
            },
            true
          );
        },
        true
      );
      return true;
    }
    return false;
  }

  /**
   *
   * @param vtt_data 播报文案
   * @param mp3_url  播报音频链接
   * @param play 立即播放
   */
  updateBroadcast(vtt_data: VTTSubtitle[], mp3_url: string, play = false) {
    if (this.data && this.otherPlayerData) {
      const otherPlayerData = this.otherPlayerData;
      this.otherPlayerData.loadVTT(vtt_data, mp3_url);
      if (play) {
        this.otherPlayerData.wordSpeak(
          '',
          () => {
            otherPlayerData.curAnimation = 'Action_05|Action_06|Action_07';
            otherPlayerData.isChange = true;
          },
          () => {
            otherPlayerData.curAnimation = 'idle';
            otherPlayerData.isChange = true;
          }
        );
      }
      return true;
    }
    return false;
  }

  startSpeakAction() {
    if (this.data && this.otherPlayerData) {
      const otherPlayerData = this.otherPlayerData;
      otherPlayerData.curAnimation = 'Action_05|Action_06|Action_07';
      otherPlayerData.isChange = true;
      return true;
    }
    return false;
  }

  stopSpeakAction() {
    if (this.data && this.otherPlayerData) {
      const otherPlayerData = this.otherPlayerData;
      otherPlayerData.curAnimation = 'idle';
      otherPlayerData.isChange = true;
      return true;
    }
    return false;
  }

  createAnswer() {
    const task = this.otherPlayerData.createAnswer();
    this.otherPlayerData.stopSpeak();
    return task;
  }

  startAnswerAnim(task: AnswerTask, data: IAssistantAnswer, endCallback: () => void) {
    if (task !== this.otherPlayerData.answerTask) {
      //如果连续请求问题的话，可能存在不一致，旧的任务直接忽略执行
      console.log('ask too fast');
      return;
    }
    const otherPlayerData = this.otherPlayerData;
    otherPlayerData.curAnimation = 'Action_05|Action_06|Action_07';
    otherPlayerData.isChange = true;
    task.start(data, () => {
      otherPlayerData.curAnimation = 'idle';
      otherPlayerData.isChange = true;
      endCallback();
    });
  }

  //停止动画
  stopAnswerAnim() {
    this.otherPlayerData.stopAnswer();
  }

  closeAnswer() {
    this.otherPlayerData.clearAnswer();
  }

  getButlerElement() {
    if (this.data && this.otherPlayerData) {
      const myPlayer = GetMyPlayer();
      const otherPlayerData = this.otherPlayerData;
      const [sameScene, setSameScene] = useState<boolean>(false);
      const [showButton, setShowButton] = useState<boolean>(false);
      const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
      const groupRef = useRef<THREE.Group>(null);

      const updateHeight = () => {
        if (groupRef.current) {
          let box = new THREE.Box3().setFromObject(groupRef.current);
          return box.max.y - box.min.y + POPOVER_HEIGHT;
        }
        return 0;
      };
      useEffect(() => {
        const sceneTypeKey = GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
          GlobalDataKey.SceneType,
          (type) => {
            setSceneType(type);
          }
        );
        const butlerAvatarDataKey = GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(
          GlobalDataKey.ButlerAvatarData,
          (avatarData) => {
            otherPlayerData.avatarData = avatarData;
            otherPlayerData.isChange = true;
          }
        );

        return () => {
          GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, sceneTypeKey);
          GlobalSpaceEvent.RemoveListener(GlobalDataKey.ButlerAvatarData, butlerAvatarDataKey);
        };
      }, []);

      useEffect(() => {
        const opKeyList: string[] = [];
        if (showButton) {
          if (myPlayer.showEasterEggButton) {
            opKeyList.push(
              GlobalSpace.addGameOp(
                GAME_OP_TYPE.CustomOp,
                () => {
                  myPlayer.callAppApi(AppGameApiKey.finishFishEgg);
                  myPlayer.showEasterEggButton = false;
                  AudioSystem.playAudio('myPlayer', './sound/reward/reward_axe.mp3', () => {
                    return true;
                  });
                  opKeyList.forEach((key) => {
                    GlobalSpace.removeGameOp(key);
                  });
                },
                0,
                'Game Clues',
                BatterySvg.src
              )
            );
          }
        }
        return () => {
          opKeyList.forEach((key) => {
            GlobalSpace.removeGameOp(key);
          });
        };
      }, [showButton]);

      useEffect(() => {
        if (sameScene) {
          let enterRange = true;
          const cancelPositionKey = GlobalSpace.whatCharacterPosition((position) => {
            if (this.data && this.isEnterVisitor) {
              const worldPos = this.data.butlerPosition;
              const distance = position.distanceTo(worldPos);
              const nowEnterRange = distance < 3;
              if (nowEnterRange != enterRange) {
                if (nowEnterRange) {
                  this.nearButler();
                  setShowButton(true);
                  this.otherPlayerData.wordSpeak(
                    '',
                    () => {
                      otherPlayerData.curAnimation = 'Action_05|Action_06|Action_07';
                      otherPlayerData.isChange = true;
                    },
                    () => {
                      otherPlayerData.curAnimation = 'idle';
                      otherPlayerData.isChange = true;
                    }
                  );
                } else {
                  setShowButton(false);
                  this.farButler();
                }
                enterRange = nowEnterRange;
              }
            }
          });
          return () => {
            GlobalSpace.cancelCharacterPositionCallback(cancelPositionKey);
          };
        }
      }, [sameScene]);

      useFrame(({ clock }) => {
        if (this.data) {
          if (sameScene !== (sceneType === this.data.butlerSceneType)) {
            setSameScene(sceneType === this.data.butlerSceneType);
            if (sceneType === this.data.butlerSceneType) {
              otherPlayerData.isChange = true;
            }
          }
        }
      });

      return <group ref={groupRef}>{sameScene && otherPlayerData.getReact(updateHeight)}</group>;
    }
    return null;
  }

  private nearButler = () => {
    console.log('close butler');
  };

  private farButler = () => {
    console.log('far butler');
  };
}

export const ButlerUtil = new GlobalButlerUtil();
