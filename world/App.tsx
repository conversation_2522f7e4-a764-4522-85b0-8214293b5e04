import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Physics } from '@react-three/rapier';
import React, { memo, useEffect, useRef, useState } from 'react';
import CharacterModel from './Character/CharacterModel';
import AvatarObject from '../AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarObject';
import SceneManager from './Scene/SceneManager';
import * as THREE from 'three';
import HoverControls from './HoverControls';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
  TransformData,
} from './Global/GlobalSpaceEvent';
import Lights from './Scene/Lights';
import { Perf } from 'r3f-perf';
import Fog from './Scene/Fog';
import ButlerModel from './Character/ButlerModel';
import { ButlerData, ButlerUtil } from './Global/GlobalButlerUtil';
import PlayerManager from './Character/PlayerManager';
import AvatarData from '../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import { UsePetManager } from '@/world/Pet/PetManager';
import ParticleEditor from '@/world/Particles/ParticleEditor';
import { IS_MOBILE_ENV } from '@/constant';
import { EcctrlJoystick } from '@/src/EcctrlJoystick';

export function ResponsiveCanvas() {
  const { get } = useThree();

  const isFlipRef = useRef(false);

  useFrame(() => {
    if (!IS_MOBILE_ENV) return;
    const updateSize = get().setSize;
    const currentWidth = get().size.width;
    const width = window.innerWidth;
    const height = window.innerHeight;
    const canvasWidth = Math.max(width, height);
    const canvasHeight = Math.min(width, height);
    if (currentWidth !== canvasWidth) {
      updateSize(canvasWidth, canvasHeight);
    }
    const isFlip = width < height;
    if (isFlip !== isFlipRef.current) {
      isFlipRef.current = isFlip;
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.IsMobileFlip, isFlip);
    }
  });

  return null;
}

interface IProps {
  isVisitor: boolean;
  butlerData: ButlerData | null;
  onClaimPotato?: () => void;
}

function App({ onClaimPotato, butlerData, isVisitor }: IProps) {
  /**
   * Delay physics activate
   */
  const [physics, setPhysics] = useState(false);
  const [movement, setMovement] = useState(false);
  const [status, setStatus] = useState<SpaceStatus>(SpaceStatus.Avatar);
  const [avatarObject, setAvatarObject] = useState<AvatarObject | null>(null);
  const fpsOpen = localStorage.getItem('fpsOpen') === 'true';
  const debugLight = localStorage.getItem('debugLightAndFog') === 'true';
  useEffect(() => {
    let oldStatus = status;
    const physicsKey = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.PhysicsDebug,
      (value) => {
        setPhysics(value);
      }
    );

    const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (value) => {
        setMovement(value === SpaceStatus.Game);
        if (oldStatus === SpaceStatus.NFT && value !== SpaceStatus.NFT) {
          GlobalSpaceEvent.SetDataValue<number>(GlobalDataKey.LookingNftIndex, 0);
        }
        oldStatus = value;
        setStatus(value);
      }
    );

    let stopLoop = false;
    GlobalSpaceEvent.ListenKeyDataChange<AvatarObject>(
      GlobalDataKey.AvatarObject,
      (_avatarObject) => {
        ButlerUtil.setAvatarObj(_avatarObject);
        setAvatarObject(_avatarObject);
        const _renderLoop = function () {
          if (stopLoop) {
            _avatarObject.unWatchAvatarChange();
            return;
          }
          requestAnimationFrame(_renderLoop);
          if (SpaceStatus.Avatar === oldStatus) {
            _avatarObject._updateAnimation();
          }
        };
        _renderLoop();

        _avatarObject.watchAvatarChange(() => {
          if (isVisitor) {
            GlobalSpaceEvent.SetDataValue<AvatarData>(
              GlobalDataKey.ButlerAvatarData,
              _avatarObject.avatarData || new AvatarData()
            );
          } else {
            GlobalSpaceEvent.SetDataValue<AvatarData>(
              GlobalDataKey.MyAvatarData,
              _avatarObject.avatarData || new AvatarData()
            );
            GlobalSpaceEvent.SetDataValue<AvatarData>(
              GlobalDataKey.ButlerAvatarData,
              _avatarObject.avatarData || new AvatarData()
            );
          }
        });
      }
    );

    const debugInitData = localStorage.getItem('debugInitData');
    if (debugInitData) {
      const initData = JSON.parse(debugInitData);
      if (initData && initData.mapId && initData.position) {
        GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
          position: new THREE.Vector3(
            initData.position.x,
            initData.position.y,
            initData.position.z
          ),
          characterType: CharacterType.Player,
          sceneType: initData.mapId,
        });
      }
    }

    return () => {
      stopLoop = true;
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.PhysicsDebug, physicsKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey);
    };
  }, []);

  const hoverControlsRef: any = useRef(null);

  function handleMouseMove(event: any) {
    if (!hoverControlsRef.current) return;
    hoverControlsRef.current.handleMouseMove(event);
  }

  function handleMouseLeave(event: any) {
    if (!hoverControlsRef.current) return;
    hoverControlsRef.current.handleMouseLeave(event);
  }

  function handleMouseUp(event: any) {
    if (!hoverControlsRef.current) return;
    hoverControlsRef.current.handleMouseUp(event);
  }

  return (
    <>
      {IS_MOBILE_ENV && status === SpaceStatus.Game && <EcctrlJoystick />}
      <Canvas
        // gl={(canvas) => {
        //     const renderer = new WebGpg.WebGPURenderer({canvas});
        //     const fpsOpen = localStorage.getItem('fpsOpen')
        //     if (fpsOpen === 'true') {
        //         new THREE.WebGLRenderer({canvas});
        //     }
        //     renderer.init().then(() => {
        //         if (fpsOpen === 'true') {
        //             setFpsOpen(true)
        //         }
        //     });
        //     return renderer;
        // }}
        style={{
          width: '100%',
          height: '100%',
        }}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onMouseUp={handleMouseUp}
        shadows
        camera={{
          fov: 45,
          near: 0.1,
          far: 1000,
          // position: [8.5, 10, 4.6],
        }}>
        {fpsOpen && <Perf position="top-left" />}
        <HoverControls ref={hoverControlsRef} />
        <ParticleEditor />
        <Lights debug={debugLight} />
        <Fog debug={debugLight} />
        <Physics debug={physics} timeStep="vary" paused={false}>
          <ButlerModel butlerData={butlerData} />
          {avatarObject && <CharacterModel avatarObj={avatarObject} />}
          {<PlayerManager />}
          <UsePetManager />
          <SceneManager onClaimPotato={onClaimPotato} />
        </Physics>
        <ResponsiveCanvas />
      </Canvas>
    </>
  );
}

export default memo(App);
