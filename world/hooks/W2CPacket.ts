import { EasterEggType, IAppState } from '@/constant/type';

export enum W2C_PacketTypes {
  RedPacketReward = 1,
  UpdateMetaData = 2,
  EasterEggMetaData = 3,

  /**
   * 全服公告消息:通知彩蛋消息
   */
  WHOLE_SERVER_NOTICE_MSG = 10,
  /**
   * 触发彩蛋消息通知
   */
  TRIGGER_EASTER_EGG_MEG = 11,
  /**
   * 彩蛋成功完成消息通知
   */
  EASTER_EGG_COMPLETE_MSG = 12,

  /**
   * 砍树更新rule
   */
  TREE_UPDATE_BASIC_INFO_MSG = 13,

  /**
   * 彩蛋完成消息通知
   */
  EASTER_EGG_FAILED_MSG = 14,

  /**
   * 全服 pizzaRush 推送
   */
  PIZZA_RUSH_EASTER_EGG = 15,

  PLAYER_BASIC_INFO_ENERGY = 50,
}

export interface W2C_RedPacketReward {
  ownerAddress: string;
  tick: string;
  amount: number;
}

export interface W2C_UpdateMetaData {
  actionId: string;
  hatId: string;
  shirtId: string;
  pantsId: string;
  shoesId: string;
  glovesId: string;
  petId: string;
  shirtColor: string;
  shirtTextureId: string;
}
export interface ITreeEasterEggData {
  rule: string;
  status: string;
  goOn: boolean;
}
export interface W2C_EasterEggMetaData {
  easterEggType: EasterEggType;
  rule: string;
  message: string;
}
