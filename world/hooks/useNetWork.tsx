import * as THREE from 'three';
import AvatarData from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import GlobalSpaceEvent, { GlobalDataKey } from '../Global/GlobalSpaceEvent';
import { io, Socket } from 'socket.io-client';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import toast from 'react-hot-toast';
import { ChatManager } from '@/model/Chat/ChatManager';
import { RedPacketManager } from '@/model/RedPacket/RedPacketManager';
import { game } from '@/world/Proto/generated/game_messages';
import ProtobufHandler from '@/world/Proto/protobuf-handler';
import {
  encodePlayerAction,
  encodePlayerChat,
  encodePlayerCommon,
  encodePlayerLogic,
  encodePlayerRequest,
} from '@/world/Proto/client-protobuf-encoder';
import { ChatTabType } from '@/model/Chat/ChatType';
import { TreeConfig } from '@/world/Config/TreeConfig';
import { StoneConfig } from '@/world/Config/StoneConfig';
import { Events } from '@/utils/clientEvents';
import {
  setEasterEggReward,
  setRandomEventResult,
  setRockLeftTime,
  setTreeLeftTime,
  setWhackAMoleEasterEgg,
} from '@/store/app';
import {
  W2C_EasterEggMetaData,
  W2C_PacketTypes,
  W2C_RedPacketReward,
  W2C_UpdateMetaData,
} from '@/world/hooks/W2CPacket';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';
import { ItemDropConfig } from '@/world/Config/ItemDropConfig';
import { getFinallyKey, preCalculateMD5 } from '@/utils/socket';
import { NetPlayerManager } from '@/model/NetPlayer/NetPlayerManager';
import { decodePlayerCommon } from '@/world/Proto/client-protobuf-handler';
import { EasterEggType, IAppState } from '@/constant/type';
import { decodeBase64ToJSON, decodeBase64ToString } from '@/utils/base64';
import { getPizzaActivity } from '../Activity/PizzaActivity';
import { IEasterEggRewardOpenConfig } from '@/components/EasterEggReward';
import { playerEnergyZustandStore } from '@/contexts/playerEnergyContext/store';

// 基础Socket事件
export enum SocketEvents {
  // 连接事件
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  PLAYER_LOGIN = '1',
  // 房间事件
  JOIN_ROOM = '2',
  LEAVE_ROOM = '3',
  PLAYER_READY = '4',
  PLAYER_ACTION = '5',
  PLAYER_LOGIC = '6',
  PLAYER_REQUEST = '7',
  PLAYER_CHAT = '8',
  PLAYER_HEARTBEAT = '9',
  PLAYER_NOTICE = '10',
  WEB_NOTICE = '11',
  PLAYER_KICK = '12',
  MATCH_FOUND = '13',

  GET_ROOM_LIST = '14',

  ERROR = '999',
}

interface GameNodeInfo {
  id: string;
  mode: number;
  modeIndex: number;
  playerCount: number;
  maxPlayerCount: number;
}

let _mapId = 0;
let _mapIndex = 0;
let disconnectTimer: any;

let _socket: Socket | null = null;
let isEnterRoom = false;
let connectTime = 0;
let _petPosition: THREE.Vector3 = new THREE.Vector3();
let _petQuaternion: THREE.Quaternion = new THREE.Quaternion();
let _petAnimation = '';
let _animation = '';
let _petId = '';
let lastPing = 0;
const changeRoomCall: ((status: {
  isEnterRoom: boolean;
  mapId: number;
  mapIndex: number;
}) => void)[] = [];

function isConnected() {
  return connectTime > 0;
}

function watchRoomStatus(
  callback: (status: { isEnterRoom: boolean; mapId: number; mapIndex: number }) => void
) {
  changeRoomCall.push(callback);
}

function getRoomStatus() {
  return {
    isEnterRoom: isEnterRoom,
  };
}

//心跳包
function startHeartbeat() {
  setInterval(async () => {
    if (connectTime > 0 && _socket) {
      try {
        const sendTime = Date.now();
        const response = await _socket
          .timeout(5000)
          .emitWithAck(
            getFinallyKey(SocketEvents.PLAYER_HEARTBEAT),
            encodePlayerCommon([String(lastPing)])
          );
        const serverTime = response as number;
        if (serverTime) {
          const respTime = Date.now();
          lastPing = respTime - sendTime;
        }
      } catch (err) {
        console.error('ping error', err);
      }
    }
  }, 5000);
}

function connect(socket_url: string) {
  // console.log('Connecting to server...', host, port);
  if (_socket) {
    // console.log('Already connected to the server');
    return;
  }
  const myPlayer = GetMyPlayer();
  const _btcAddress = myPlayer.btcAddress || '';
  const _sessionId = myPlayer.sessionId || '';
  if (_btcAddress.length === 0) {
    return;
  }
  // 使用传入的host和port参数
  const socket = io(socket_url, {
    withCredentials: false,
    transports: ['websocket'],
    autoConnect: true,
    reconnection: true,
    reconnectionAttempts: 30,
    reconnectionDelay: 1000,
    // 确保二进制数据正确传输
    upgrade: false, // 禁用传输升级
    forceNew: true, // 避免连接共享
    rememberUpgrade: false,
  });
  _socket = socket;

  // 连接事件处理
  socket.on(SocketEvents.CONNECT, async () => {
    // console.log('Connected to the server');
    clearTimeout(disconnectTimer);
    connectTime = 0;
    GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
    const binaryData: Buffer = await socket
      .timeout(60 * 1000)
      .emitWithAck(SocketEvents.PLAYER_LOGIN, encodePlayerCommon([_btcAddress, _sessionId]));
    const [_times] = decodePlayerCommon(binaryData);
    const times = Number(_times);
    if (times > 0) {
      preCalculateMD5(_btcAddress, times);
      listenSocketMessage(socket);
      socket.emit(getFinallyKey(SocketEvents.PLAYER_READY), encodePlayerCommon([]));
      connectTime = Date.now();
      GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, true);
    }
  });

  socket.on(SocketEvents.DISCONNECT, () => {
    // console.log('Disconnected from gateway server');
    connectTime = 0;
    GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
    isEnterRoom = false;
    changeRoomCall.forEach((callback) => {
      callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
    });
    disconnectTimer = setTimeout(() => {
      //由于推出钱包时，会触发断开连接，但是myPlayer.btcAddress会延迟改变
      if (myPlayer.btcAddress.length > 0) {
        toast.error("Oops! You've been disconnected. Reconnecting…");
      }
    }, 5000);
    NetPlayerManager.getInstance().clearAllPlayer();
  });
}

function listenSocketMessage(socket: Socket) {
  const myPlayer = GetMyPlayer();
  socket.removeAllListeners(getFinallyKey(SocketEvents.PLAYER_KICK));
  socket.on(getFinallyKey(SocketEvents.PLAYER_KICK), () => {
    myPlayer.callAppApi(AppGameApiKey.disconnectWallet);
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.WEB_NOTICE));
  socket.on(getFinallyKey(SocketEvents.WEB_NOTICE), (data) => {
    webNotice(data);
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.PLAYER_NOTICE));
  socket.on(
    getFinallyKey(SocketEvents.PLAYER_NOTICE),
    (binaryData: ArrayBuffer | Uint8Array | Buffer) => {
      const { pid, data } = ProtobufHandler.getInstance().decodeMessage(binaryData);
      // const player = this.findOtherPlayer(btcAddress);
      switch (pid) {
        case game.S2CPacketType.S2C_PLAYER_ENTER:
          const enterData = data as game.PlayerEnter;
          // 有新玩家连接时的处理

          if (!NetPlayerManager.getInstance().findOtherPlayer(enterData.btcAddress)) {
            const avatarData = new AvatarData();
            avatarData.shirtId = enterData.avatarData?.shirtId || null;
            avatarData.shirtTextureId = enterData.avatarData?.shirtTextureId || null;
            avatarData.shirtColor = enterData.avatarData?.shirtColor || null;
            avatarData.pantsId = enterData.avatarData?.pantsId || null;
            avatarData.shoesId = enterData.avatarData?.shoesId || null;
            avatarData.hatId = enterData.avatarData?.hatId || null;
            avatarData.glovesId = enterData.avatarData?.glovesId || null;
            const otherPlayer = NetPlayerManager.getInstance().addOtherPlayer(
              enterData.btcAddress,
              avatarData
            );
            otherPlayer.position.set(
              enterData.position?.x || 0,
              enterData.position?.y || 0,
              enterData.position?.z || 0
            );
            otherPlayer.quaternion.set(
              enterData.position?.rotationX || 0,
              enterData.position?.rotationY || 0,
              enterData.position?.rotationZ || 0,
              enterData.position?.rotationW || 0
            );
            // otherPlayer.curAnimation = enterData.curAnimation;
            // otherPlayer.itemId = enterData.itemId;
            // otherPlayer.pizzaCount = enterData.pizzaCount;
            otherPlayer.isChange = true;
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_POSITION:
          // 更新其他玩家的位置和方向
          const positionData = data as game.PlayerPosition;
          const positionPlayer = NetPlayerManager.getInstance().findOtherPlayer(
            positionData.btcAddress
          );
          if (positionPlayer) {
            positionPlayer.position.set(
              positionData?.x || 0,
              positionData?.y || 0,
              positionData?.z || 0
            );
            positionPlayer.quaternion.set(
              positionData?.rotationX || 0,
              positionData?.rotationY || 0,
              positionData?.rotationZ || 0,
              positionData?.rotationW || 0
            );
            positionPlayer.timestamp = Date.now();
            positionPlayer.isChange = true;
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_ANIMATION:
          // console.log('Player update_animation:', data);
          const animationData = data as game.PlayerAnimation;
          const animationPlayer = NetPlayerManager.getInstance().findOtherPlayer(
            animationData.btcAddress
          );
          // 更新其他玩家的动作
          if (animationPlayer) {
            animationPlayer.curAnimation = animationData.curAnimation;
            if (animationData.curAnimation === 'Action_17') {
              //临时解决非主角玩家，钓鱼idle动作无法循环播放问题
              animationPlayer.lockAnimation = 'Action_17';
            } else {
              animationPlayer.lockAnimation = null;
            }
            animationPlayer.isChange = true;
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_UPDATE:
          const updateData = data as game.PlayerUpdate;
          const updatePlayer = NetPlayerManager.getInstance().findOtherPlayer(
            updateData.btcAddress
          );
          // 更新其他玩家的动作
          const { itemId, pizzaCount, petId, pizzaTick } = updateData;
          if (updatePlayer) {
            if (updatePlayer.itemId !== itemId) {
              updatePlayer.itemId = itemId;
              updatePlayer.isChange = true;
            }
            if (updatePlayer.pizzaCount !== pizzaCount) {
              updatePlayer.pizzaCount = pizzaCount;
              updatePlayer.isChange = true;
            }
            if (updatePlayer.pizzaTick !== pizzaTick) {
              updatePlayer.pizzaTick = pizzaTick;
              updatePlayer.isChange = true;
            }
            if (petId) {
              const pet_1 = NetPlayerManager.getInstance().findOtherPlayer(
                `${data.btcAddress}_pet`
              );
              if (pet_1) {
                if (pet_1.usePet !== petId) {
                  pet_1.usePet = petId;
                  pet_1.isChange = true;
                }
              } else {
                NetPlayerManager.getInstance().addOtherPlayer(
                  `${data.btcAddress}_pet`,
                  new AvatarData(),
                  petId
                );
              }
            } else {
              NetPlayerManager.getInstance().deleteOtherPlayer(`${data.btcAddress}_pet`);
            }
          }
          break;
        case game.S2CPacketType.S2C_PET_POSITION:
          const petPositionData = data as game.PetPosition;
          // 更新其他玩家的位置和方向
          const pet_2 = NetPlayerManager.getInstance().findOtherPlayer(
            `${petPositionData.ownerBtcAddress}_pet`
          );
          if (pet_2) {
            pet_2.position.set(
              petPositionData?.x || 0,
              petPositionData?.y || 0,
              petPositionData?.z || 0
            );
            pet_2.quaternion.set(
              petPositionData?.rotationX || 0,
              petPositionData?.rotationY || 0,
              petPositionData?.rotationZ || 0,
              petPositionData?.rotationW || 0
            );
            pet_2.timestamp = Date.now();
            pet_2.isChange = true;
          }
          break;
        case game.S2CPacketType.S2C_PET_ANIMATION:
          const petAnimationData = data as game.PetAnimation;
          // 更新其他玩家的动作
          const pet_3 = NetPlayerManager.getInstance().findOtherPlayer(
            `${petAnimationData.ownerBtcAddress}_pet`
          );
          if (pet_3) {
            pet_3.curAnimation = petAnimationData.animationName;
            pet_3.isChange = true;
          }
          break;
        case game.S2CPacketType.S2C_PLAYER_LEAVE:
          const leaveData = data as game.PlayerLeave;
          const leavePlayer = NetPlayerManager.getInstance().findOtherPlayer(leaveData.btcAddress);
          if (leavePlayer) {
            NetPlayerManager.getInstance().deleteOtherPlayer(leaveData.btcAddress);
            NetPlayerManager.getInstance().deleteOtherPlayer(`${leaveData.btcAddress}_pet`);
          }
          break;
        case game.S2CPacketType.S2C_CHAT_ENTER:
          ChatManager.getInstance().enterChatType(data.chatId);
          break;
        case game.S2CPacketType.S2C_CHAT_MESSAGE:
          const chatData = data as game.ChatMessage;
          const now = Date.now();
          if (
            chatData.tabType === ChatTabType.Room &&
            now - Number(chatData.timestamp) < 5 * 1000
          ) {
            //5秒内的消息 玩家头顶才会出现文字泡
            const player = NetPlayerManager.getInstance().findOtherPlayer(chatData.playerId);
            const content = chatData.content;
            if (player) {
              player.wordSpeak(
                content,
                () => undefined,
                () => undefined
              );
            }
          }
          ChatManager.getInstance().addChatMessage(chatData.tabType, chatData);
          break;
        case game.S2CPacketType.S2C_CHAT_MESSAGE_DELETE:
          const chatDeleteData = data as game.ChatMessageDelete;
          ChatManager.getInstance().deleteChatMessage(chatDeleteData.tabType, chatDeleteData.uuid);
          break;
        case game.S2CPacketType.S2C_CHAT_LEAVE:
          ChatManager.getInstance().outChatType(data.chatId);
          break;
        case game.S2CPacketType.S2C_RED_PACKET_UPDATE:
          const redPacketUpdate = data as game.RedPacketUpdate;
          const { pointList } = redPacketUpdate;
          if (pointList && pointList.length > 0) {
            RedPacketManager.getInstance().receiveRedPacket(pointList);
          }
          break;
        case game.S2CPacketType.S2C_RED_PACKET_CACHE:
          const redPacketCache = data as game.RedPacketCache;
          const { pickedList } = redPacketCache;
          myPlayer.pickedList = pickedList || [];
          break;
        case game.S2CPacketType.S2C_UPDATE_TREE:
          const updateTreeList = data as game.UpdateTreeList;
          const { treeDataList } = updateTreeList;

          TreeConfig.getInstance().updateTreeList(treeDataList);
          break;
        case game.S2CPacketType.S2C_UPDATE_TREE_REFRESH:
          const updateTreeRefresh = data as game.UpdateTreeRefresh;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setTreeLeftTime(updateTreeRefresh.leftTime || undefined));
          });
          break;
        case game.S2CPacketType.S2C_UPDATE_ROCK:
          const updateRockList = data as game.UpdateRockList;
          const { rockDataList } = updateRockList;
          StoneConfig.getInstance().updateRockList(rockDataList);
          break;
        case game.S2CPacketType.S2C_UPDATE_ROCK_REFRESH:
          const updateRockRefresh = data as game.UpdateRockRefresh;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(setRockLeftTime(updateRockRefresh.leftTime || undefined));
          });
          break;
        case game.S2CPacketType.S2C_UPDATE_ITEM:
          const updateItem = data as game.UpdateItem;
          myPlayer.callAppApi(
            AppGameApiKey.updateItemDurability,
            updateItem.itemId,
            updateItem.durability
          );
          break;
        case game.S2CPacketType.S2C_UPDATE_PICK_UP_POINT:
          const updatePickUpPoint = data as game.UpdatePickUpPoint;
          ItemDropConfig.getInstance().updateData([
            {
              dropItemTag: 0,
              isPickedUp: updatePickUpPoint.isPickedUp,
              tag: Number(updatePickUpPoint.tag),
              quantity: 1,
              coolDown: updatePickUpPoint.coolDown as number,
            },
          ]);
          break;
        case game.S2CPacketType.S2C_REWARD_MATERIAL:
          const rewardMaterial = data as game.UpdateMaterial;
          const { materialTag } = rewardMaterial;
          if (materialTag.startsWith('101')) {
            Events.emitItemCollected('/image/t2-1.png', 1);
          } else if (materialTag.startsWith('102')) {
            Events.emitItemCollected('/image/t2-2.png', 1);
          } else if (materialTag.startsWith('103')) {
            Events.emitItemCollected('/image/t2-3.png', 1);
          }
          break;
        case game.S2CPacketType.S2C_REWARD_RANDOM_EVENT:
          const rewardRandomEvent = data as game.UpdateRandomEvent;
          myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
            dispatch(
              setRandomEventResult({
                quantity: Number(rewardRandomEvent.quantity) || 0,
                tag: rewardRandomEvent.tag,
                eventType: rewardRandomEvent.eventType,
              })
            );
          });
          break;
        case game.S2CPacketType.S2C_NOTICE_NEW_DAY:
          myPlayer.callAppApi(AppGameApiKey.nextDay);
          break;
        case game.S2CPacketType.S2C_PLAYER_FISHING:
          const playerFishingData = data as game.PlayerFishing;
          const otherPlayer = NetPlayerManager.getInstance().findOtherPlayer(
            playerFishingData.btcAddress
          );
          if (otherPlayer) {
            otherPlayer.fishId = playerFishingData.fishId;
            otherPlayer.isChange = true;
          }
          break;
      }
    }
  );

  socket.removeAllListeners(getFinallyKey(SocketEvents.MATCH_FOUND));
  socket.on(getFinallyKey(SocketEvents.MATCH_FOUND), (data) => {
    try {
      const { mode, modeIndex } = data;
      _mapId = mode.split('_')[1] as unknown as number;
      _mapIndex = (modeIndex as number) || 0;
      NetPlayerManager.getInstance().clearAllPlayer();
      changeRoomCall.forEach((callback) => {
        callback({ isEnterRoom: true, mapId: _mapId, mapIndex: _mapIndex });
      });

      GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(
        GlobalDataKey.MyAvatarData,
        async (avatarData) => {
          socket.emit(
            getFinallyKey(SocketEvents.PLAYER_ACTION),
            encodePlayerAction(
              game.C2SPacketType.C2S_PLAYER_ENTER,
              game.ClientPlayerEnter.create({
                avatarData: game.AvatarData.create({
                  shirtId: avatarData.shirtId,
                  shirtTextureId: avatarData.shirtTextureId,
                  shirtColor: avatarData.shirtColor,
                  pantsId: avatarData.pantsId,
                  shoesId: avatarData.shoesId,
                  hatId: avatarData.hatId,
                  glovesId: avatarData.glovesId,
                }).toJSON(),
              })
            )
          );
          isEnterRoom = true;
          sendPosition(myPlayer.position, myPlayer.quaternion);
          sendAnimation(_animation);
          sendPetId(_petId);
          sendItemId();
          sendPizzaCount();
          sendPetAnimation(_petAnimation);
          sendPetPosition(_petPosition, _petQuaternion);
        },
        true
      );
    } catch (e) {
      toast.error('enter room error');
      leaveRoom();
    }
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.LEAVE_ROOM));
  socket.on(getFinallyKey(SocketEvents.LEAVE_ROOM), () => {
    try {
      leaveRoom();
    } catch (e) {}
  });

  socket.removeAllListeners(getFinallyKey(SocketEvents.ERROR));
  socket.on(getFinallyKey(SocketEvents.ERROR), (message) => {
    toast.error(message);
  });
}

function webNotice(notice: any) {
  const { pid, data } = notice;
  const myPlayer = GetMyPlayer();
  switch (pid) {
    case W2C_PacketTypes.RedPacketReward:
      const { tick, amount } = data as W2C_RedPacketReward;
      myPlayer.callAppApi(AppGameApiKey.showRewards, tick, amount);
      break;
    case W2C_PacketTypes.UpdateMetaData:
      const metaData = data as W2C_UpdateMetaData;
      myPlayer.callAppApi(AppGameApiKey.setMetaData, metaData as IAvatarMetadata);
      break;
    case W2C_PacketTypes.TRIGGER_EASTER_EGG_MEG:
      const originData = data as W2C_EasterEggMetaData;
      const { easterEggType, rule } = originData;

      if (easterEggType === EasterEggType.ORDER_TREE) {
        const easterRule = decodeBase64ToString(rule) as any;

        if (easterRule && Date.now() < data.endTime) {
          TreeConfig.getInstance().updateOrderTreeData(
            {
              rule: easterRule,
            } as any,
            false
          );
          myPlayer.callAppApi(AppGameApiKey.activityRule, 4);
          myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, data.endTime, 1);
        }

        break;
      }

      if (easterEggType === EasterEggType.WHACK_A_MOLE) {
        const base64EasterEgg = decodeBase64ToJSON(rule) as IAppState['whackAMoleEasterEgg'];
        myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
          dispatch(setWhackAMoleEasterEgg(base64EasterEgg));
        });
        myPlayer.callAppApi(AppGameApiKey.activityRule, 3);

        break;
      }

      break;

    case W2C_PacketTypes.EASTER_EGG_COMPLETE_MSG:
      const { rewardQuantity, rewardTag, rewardType, easterEggType: easterType } = data;
      if (easterType === EasterEggType.WHACK_A_MOLE) {
        myPlayer.callAppApi(AppGameApiKey.getDispatch, (dispatch: any) => {
          dispatch(
            setEasterEggReward({
              quantity: rewardQuantity,
              tag: rewardTag,
              eventType: rewardType,
            })
          );
        });
      } else {
        myPlayer.callAppApi(AppGameApiKey.showEasterEggRewards, {
          modalStyleType: 'axe',
          quantity: rewardQuantity,
          tag: rewardTag,
          eventType: rewardType,
          subTitle: 'Chop Master',
        } as IEasterEggRewardOpenConfig);

        TreeConfig.getInstance().clearEasterEggTree();
        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, 0, 2);
      }
      break;

    case W2C_PacketTypes.TREE_UPDATE_BASIC_INFO_MSG:
      const easterRule = decodeBase64ToString(data.rule);
      if (easterRule && Date.now() < data.endTime) {
        TreeConfig.getInstance().updateOrderTreeData(
          {
            rule: easterRule,
          } as any,
          true
        );

        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, data.endTime, 1);
      }
      break;

    case W2C_PacketTypes.EASTER_EGG_FAILED_MSG:
      if (data.easterEggType === EasterEggType.ORDER_TREE) {
        myPlayer.callAppApi(AppGameApiKey.showEasterEggFailed, data.rewardTag);
        TreeConfig.getInstance().clearEasterEggTree();
        myPlayer.callAppApi(AppGameApiKey.setOrderTreeTimeLeft, 0, 3);
      }
      break;

    case W2C_PacketTypes.PIZZA_RUSH_EASTER_EGG:
      const pizzaActivity = getPizzaActivity();
      pizzaActivity.socketUpdateActivityData(data);

      // 只有触发人才打开弹窗
      if (myPlayer.btcAddress === data.address) {
        myPlayer.callAppApi(AppGameApiKey.activityRule, 2);
      }

      break;

    case W2C_PacketTypes.PLAYER_BASIC_INFO_ENERGY:
      playerEnergyZustandStore.getState().updatePlayerInfo(data);

      break;
  }
}

function disConnect() {
  if (_socket && _socket.active) {
    _socket.close();
  }
  isEnterRoom = false;
  changeRoomCall.forEach((callback) => {
    callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
  });
  connectTime = 0;
  GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.IsSocketConnected, false);
  _socket = null;
  NetPlayerManager.getInstance().clearAllPlayer();
}

function sendMapId(mapIdKey: string) {
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PLAYER_MAP_UPDATE,
        game.CommonMessage.create({
          messageList: [mapIdKey],
        }).toJSON()
      )
    );
  }
}

function sendMapPosition(positionList: string[]) {
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PLAYER_POSITION_UPDATE,
        game.CommonMessage.create({
          messageList: positionList,
        }).toJSON()
      )
    );
  }
}

function sendPosition(position: THREE.Vector3, quaternion: THREE.Quaternion) {
  // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器z
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_POSITION,
        game.ClientPlayerPosition.create({
          x: Math.floor(position.x * 10) / 10,
          y: Math.floor(position.y * 10) / 10,
          z: Math.floor(position.z * 10) / 10,
          rotationX: Math.floor(quaternion.x * 100) / 100,
          rotationY: Math.floor(quaternion.y * 100) / 100,
          rotationZ: Math.floor(quaternion.z * 100) / 100,
          rotationW: Math.floor(quaternion.w * 100) / 100,
        }).toJSON()
      )
    );
  }
}

function sendAnimation(animation: string) {
  _animation = animation;
  // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器
  if (!isEnterRoom) {
    return;
  }
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_ANIMATION,
        game.ClientPlayerAnimation.create({
          curAnimation: animation,
        }).toJSON()
      )
    );
  }
}

function sendPetPosition(position: THREE.Vector3, quaternion: THREE.Quaternion) {
  _petPosition = position;
  _petQuaternion = quaternion;
  if (!isEnterRoom) {
    return;
  }
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PET_POSITION,
        game.ClientPlayerPosition.create({
          x: Math.floor(position.x * 100) / 100,
          y: Math.floor(position.y * 100) / 100,
          z: Math.floor(position.z * 100) / 100,
          rotationX: Math.floor(quaternion.x * 100) / 100,
          rotationY: Math.floor(quaternion.y * 100) / 100,
          rotationZ: Math.floor(quaternion.z * 100) / 100,
          rotationW: Math.floor(quaternion.w * 100) / 100,
        }).toJSON()
      )
    );
  }
}

function sendPetAnimation(animation: string) {
  _petAnimation = animation;
  // 如果 WebSocket 连接是打开的，发送位置和旋转数据到服务器
  if (!isEnterRoom) {
    return;
  }
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PET_ANIMATION,
        game.ClientPlayerAnimation.create({
          curAnimation: animation,
        }).toJSON()
      )
    );
  }
}

function sendItemId() {
  if (!isEnterRoom) {
    return;
  }
  const myPlayer = GetMyPlayer();
  const itemId = myPlayer.itemData?.id || 0;
  const pizzaCount = myPlayer.pizzaCount;
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          itemId,
        }).toJSON()
      )
    );
  }
}

function sendPizzaCount() {
  const myPlayer = GetMyPlayer();
  const pizzaCount = myPlayer.pizzaCount;
  const pizzaTick = myPlayer.pizzaTick;
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          pizzaCount,
          pizzaTick,
        }).toJSON()
      )
    );
  }
}

function sendPetId(petId: string) {
  _petId = petId;
  if (!isEnterRoom) {
    return;
  }
  const socket = _socket;
  if (socket && socket.active) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_UPDATE,
        game.ClientPlayerUpdate.create({
          petId,
        })
      )
    );
  }
}

function sendChatMsg(chatId: number, pid: game.C2SPacketType, data: game.ChatMessage) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerChat(chatId, pid, data.toJSON());
    socket.emit(getFinallyKey(SocketEvents.PLAYER_CHAT), binaryData);
  }
}

function sendChatMsgDelete(chatId: number, pid: game.C2SPacketType, data: game.ChatMessageDelete) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerChat(chatId, pid, data.toJSON());
    socket.emit(getFinallyKey(SocketEvents.PLAYER_CHAT), binaryData);
  }
}

function sendCutTree(treeTag: number, treeServerId: string, useItemId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_CUT_TREE,
        game.ClientCutTree.create({
          treeTag,
          treeServerId,
          useItemId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendMiningRock(rockTag: number, useItemId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_MINING_ROCK,
        game.ClientMiningRock.create({
          rockTag,
          useItemId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendFishingSuccess(fishRecordId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_FISHING_SUCCESS,
        game.ClientFishingSuccess.create({
          fishRecordId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendPickUpDrop(pickUpId: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_PICK_UP_DROP,
        game.ClientPickUpDrop.create({
          dropTag: pickUpId,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

function sendFishAction(fishId: number) {
  const socket = _socket;
  if (socket && socket.active) {
    if (!isEnterRoom) {
      return;
    }
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_ACTION),
      encodePlayerAction(
        game.C2SPacketType.C2S_PLAYER_FISHING,
        game.PlayerFishing.create({
          fishId,
        }).toJSON()
      )
    );
  }
}

function sendLarkMessage(title: string, connect: string) {
  const socket = _socket;
  if (socket && socket.active && isConnected()) {
    socket.emit(
      getFinallyKey(SocketEvents.PLAYER_LOGIC),
      encodePlayerLogic(
        game.C2SPacketType.C2S_LARK_MESSAGE,
        game.LarkMessage.create({
          title,
          connect,
        }).toJSON()
      )
    );
  } else {
    toast.error("Oops! You've been disconnected. Reconnecting…");
  }
}

async function getRoomList() {
  const socket = _socket;
  if (socket && socket.active) {
    const response: GameNodeInfo[] = await socket
      .timeout(10000)
      .emitWithAck(getFinallyKey(SocketEvents.GET_ROOM_LIST), encodePlayerCommon([]));
    return response;
  }
  return [];
}

async function request(pid: game.ClientRequestTypes, body: any) {
  const socket = _socket;
  if (socket && socket.active) {
    const binaryData = encodePlayerRequest(pid, body);
    return socket.emitWithAck(getFinallyKey(SocketEvents.PLAYER_REQUEST), binaryData);
  }
  return null;
}

function enterRoom(mapId: number, index: number) {
  if (!_socket) {
    return;
  }
  const socket = _socket;
  const myPlayer = GetMyPlayer();
  GlobalSpaceEvent.ListenKeyDataChange<AvatarData>(
    GlobalDataKey.MyAvatarData,
    (avatarData) => {
      socket.emit(
        getFinallyKey(SocketEvents.JOIN_ROOM),
        encodePlayerCommon([String(mapId), String(index)])
      );
    },
    true
  );
}

function leaveRoom() {
  if (!_socket) {
    return;
  }
  if (!isEnterRoom) {
    return;
  }
  _mapId = 0;
  _mapIndex = 0;
  const socket = _socket;
  isEnterRoom = false;
  changeRoomCall.forEach((callback) => {
    callback({ isEnterRoom: false, mapId: 0, mapIndex: 0 });
  });
  NetPlayerManager.getInstance().clearAllPlayer();
  socket.emit(getFinallyKey(SocketEvents.LEAVE_ROOM), encodePlayerCommon([]));
}

startHeartbeat();

export function useNetWork() {
  return {
    connect,
    disConnect,
    isConnected,
    watchRoomStatus,
    getRoomStatus,
    sendMapId,
    sendMapPosition,
    sendPosition,
    sendAnimation,
    sendPetPosition,
    sendPetAnimation,
    sendFishAction,
    sendItemId,
    sendPizzaCount,
    sendPetId,
    sendChatMsg,
    sendChatMsgDelete,
    sendCutTree,
    sendMiningRock,
    sendFishingSuccess,
    sendPickUpDrop,
    sendLarkMessage,
    getRoomList,
    request,
    enterRoom,
    leaveRoom,
  };
}
