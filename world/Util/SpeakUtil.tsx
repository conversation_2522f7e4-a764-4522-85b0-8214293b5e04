import AudioSystemComponent, { AudioSystem } from '../Global/GlobalAudioSystem';
import { generateUUID } from 'three/src/math/MathUtils';
import { useEffect, useState } from 'react';
import TopChatUI from '../SceneUI/TopChatUI';
import { useFrame } from '@react-three/fiber';

export type VTTSubtitle = {
  start: number;
  end: number;
  content: string;
};

export class SpeakUtilData {
  mp3_url: string = '';
  loadingMP3 = false;
  startSpeak = false;
  startTime = 0;
  speakIndex = 0;
  spaceData: VTTSubtitle[] = [];
  private id: string;

  constructor() {
    this.id = generateUUID();
  }

  speakCallback: () => void = () => {};

  speakEndCallback: () => void = () => {};

  getId() {
    return this.id;
  }

  loadVTT(vtt_data: VTTSubtitle[], mp3_url: string) {
    this.mp3_url = mp3_url;
    this.spaceData = vtt_data;
  }

  wordSpeak(text: string, startCallback: () => void, endCallback: () => void) {
    // console.error('wordSpeak', text)
    // const utterance = new SpeechSynthesisUtterance(text);
    // window.speechSynthesis.speak(utterance);
    if (text.length > 0) {
      this.mp3_url = '';
      this.spaceData = [
        {
          start: 0,
          end: 6,
          content: text,
        },
      ];
    }
    this.speakCallback = startCallback;
    this.speakEndCallback = endCallback;
    this.showSubtitles();
  }

  stopSpeak() {
    this.speakIndex = 9999;
    AudioSystem.playAudio('butlerSpeak_' + this.id, '', () => {
      return false;
    });
  }

  private showSubtitles() {
    if (this.startTime === 0) {
      if (this.mp3_url.length > 0) {
        this.loadingMP3 = true;
        this.speakIndex = -1;
        AudioSystem.playAudio('butlerSpeak_' + this.id, this.mp3_url, () => {
          if (this.speakIndex !== -1) {
            return false;
          }
          this.startSpeak = true;
          return true;
        });
      } else {
        this.startSpeak = true;
      }
    }
  }
}

export default function SpeakUtil({
  spaceUtilData,
  height,
}: {
  spaceUtilData: SpeakUtilData;
  height: number;
}) {
  const [showWord, setShowWord] = useState<string>('');

  useEffect(() => {
    //析构时 停止当前语音
    return () => {
      spaceUtilData.stopSpeak();
    };
  }, []);

  useFrame(() => {
    if (spaceUtilData.startSpeak) {
      spaceUtilData.startSpeak = false;
      if (spaceUtilData.spaceData.length > 0) {
        spaceUtilData.loadingMP3 = false;
        spaceUtilData.startTime = Date.now();
        spaceUtilData.speakIndex = 0;
        spaceUtilData.speakCallback();
      }
    }
    const curVTT = spaceUtilData.spaceData[spaceUtilData.speakIndex];
    if (spaceUtilData.startTime > 0) {
      if (curVTT) {
        if (Date.now() - spaceUtilData.startTime >= curVTT.start * 1000) {
          setShowWord(curVTT.content);
          if (Date.now() - spaceUtilData.startTime >= curVTT.end * 1000) {
            spaceUtilData.speakIndex = spaceUtilData.speakIndex + 1;
          }
        }
      } else {
        setShowWord('');
        spaceUtilData.startTime = 0;
        spaceUtilData.speakEndCallback();
      }
    } else {
      if (spaceUtilData.loadingMP3) {
        spaceUtilData.loadingMP3 = false;
        setShowWord('...');
      }
    }
  });

  return (
    <>
      <AudioSystemComponent _key={'butlerSpeak_' + spaceUtilData.getId()} />
      {showWord.length > 0 && height > 0 && <TopChatUI height={height} word={showWord} />}
    </>
  );
}
