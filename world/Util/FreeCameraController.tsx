import { use<PERSON><PERSON>e, useThree } from '@react-three/fiber';
import React, { useEffect, useMemo, useRef } from 'react';
import * as THREE from 'three';
import { OrbitControls } from '@react-three/drei';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

interface Velocity {
  forward: number;
  backward: number;
  left: number;
  right: number;
  up: number;
  down: number;
}

function CustomCameraController() {
  const { camera, gl } = useThree();
  const velocity = useRef<Velocity>({
    forward: 0,
    backward: 0,
    left: 0,
    right: 0,
    up: 0,
    down: 0,
  });
  const target = useRef<THREE.Vector3>();
  //前方坐标
  const forward = useMemo(() => {
    const direction = new THREE.Vector3();
    const worldPosition = new THREE.Vector3();
    camera.getWorldDirection(direction);
    camera.getWorldPosition(worldPosition);
    direction.normalize();

    return new THREE.Vector3(
      worldPosition.x + direction.x,
      worldPosition.y + direction.y,
      worldPosition.z + direction.z
    );
  }, []);

  useEffect(() => {
    KeyPressUtil.setEnable(false);
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'w': // 前进
        case 'W': // 前进
          velocity.current.forward = 0.1;
          break;
        case 's': // 后退
        case 'S': // 后退
          velocity.current.backward = 0.1;
          break;
        case 'a': // 左移
        case 'A':
          velocity.current.left = 0.1;
          break;
        case 'd': // 右移
        case 'D':
          velocity.current.right = 0.1;
          break;
        case 'q': // 向下
        case 'Q':
          velocity.current.down = 0.1;
          break;
        case 'e': // 向上
        case 'E':
          velocity.current.up = 0.1;
          break;
        default:
          break;
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'w':
        case 'W':
          velocity.current.forward = 0;
          break;
        case 's':
        case 'S':
          velocity.current.backward = 0;
          break;
        case 'a':
        case 'A':
          velocity.current.left = 0;
          break;
        case 'd':
        case 'D':
          velocity.current.right = 0;
          break;
        case 'q':
        case 'Q':
          velocity.current.down = 0;
          break;
        case 'e':
        case 'E':
          velocity.current.up = 0;
          break;
        default:
          break;
      }
    };

    const resetKey = () => {
      velocity.current.forward = 0;
      velocity.current.backward = 0;
      velocity.current.left = 0;
      velocity.current.right = 0;
      velocity.current.up = 0;
      velocity.current.down = 0;
    };
    resetKey();
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('focus', resetKey);

    return () => {
      KeyPressUtil.setEnable(true);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('focus', resetKey);
    };
  }, []);

  useFrame((state, delta) => {
    if (!camera) {
      return;
    }
    if (
      velocity.current.down > 0 ||
      velocity.current.up > 0 ||
      velocity.current.left > 0 ||
      velocity.current.right > 0 ||
      velocity.current.forward > 0 ||
      velocity.current.backward > 0
    ) {
      if (target.current) {
        const direction = new THREE.Vector3();
        const right = new THREE.Vector3();
        const up = new THREE.Vector3(0, 1, 0);

        camera.getWorldDirection(direction);

        // 计算右方向向量
        right.crossVectors(direction, up).normalize();

        // 计算移动
        const moveForward = direction
          .clone()
          .multiplyScalar((velocity.current.forward - velocity.current.backward) * delta * 40);
        const moveRight = right
          .clone()
          .multiplyScalar((velocity.current.right - velocity.current.left) * delta * 40);
        const moveUp = up
          .clone()
          .multiplyScalar((velocity.current.up - velocity.current.down) * delta * 40);

        // 更新摄像机位置
        target.current.add(moveForward);
        target.current.add(moveRight);
        target.current.add(moveUp);
        camera.position.add(moveForward);
        camera.position.add(moveRight);
        camera.position.add(moveUp);
        // camera.position.add(moveUp);
      }
    }
  });

  const controlsRef = useRef<any>(null);
  useEffect(() => {
    if (controlsRef.current) {
      target.current = controlsRef.current.target;
    }
  }, [camera, controlsRef.current]);
  return (
    <OrbitControls
      ref={controlsRef}
      target={[forward.x, forward.y, forward.z]}
      minDistance={1}
      maxDistance={1}
      dampingFactor={0.2}
      rotateSpeed={0.15}
    />
  );
}

export default CustomCameraController;
