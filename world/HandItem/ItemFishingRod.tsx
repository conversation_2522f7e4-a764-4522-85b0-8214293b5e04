import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { UseGameState } from '@/src/stores/useGame';
import { useAnimations } from '@react-three/drei';

function RodAnimation({
  itemObject,
  useGame,
  floatObject,
  animations,
}: {
  animations: THREE.AnimationClip[];
  itemObject: THREE.Object3D;
  floatObject: THREE.Object3D | null;
  useGame: any;
}) {
  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const { actions } = useAnimations(animations, itemObject);

  const animationSet = useGame((state: UseGameState) => state.animationSet);

  useEffect(() => {
    if (floatObject) {
      if (curAnimation === 'Action_fishing_00') {
        floatObject.visible = true;
        setTimeout(() => {
          floatObject.visible = false;
        }, 1300);
      } else if (curAnimation === 'Action_fishing_03') {
        floatObject.visible = true;
        setTimeout(() => {
          floatObject.visible = false;
        }, 600);
      } else {
        floatObject.visible = false;
      }
    }
  }, [floatObject, curAnimation]);

  useEffect(() => {
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return;
    }
    const finishCall = () => {
      setCurAnimation('Action_fishing_01');
    };
    // Play animation
    const action: THREE.AnimationAction | null = actions[curAnimation || ''];
    if (action) {
      // For jump and jump land animation, only play once and clamp when finish
      if (
        curAnimation !== 'Action_fishing_01' &&
        curAnimation !== 'Action_fishing_02' &&
        curAnimation !== 'Action_fishing_04'
      ) {
        action.reset().fadeIn(0.2).setLoop(THREE.LoopOnce, 0).play();
        action.clampWhenFinished = true;
      } else {
        action.reset().fadeIn(0.2).play();
      }

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [actions, curAnimation, animationSet, animations]);

  return null;
}

export default function ItemFishingRod({
  rodItemRoot,
  useGame,
  animations,
  playerUseGame,
}: {
  playerUseGame: any;
  rodItemRoot: THREE.Object3D;
  animations: THREE.AnimationClip[];
  useGame: any;
}) {
  const playerAction = playerUseGame((state: UseGameState) => state.curAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);

  const [float, setFloat] = useState<THREE.Object3D | null>(null);
  useEffect(() => {
    setCurAnimation('Action_fishing_04');

    rodItemRoot.traverse((obj) => {
      if (obj.name === 'Floats') {
        setFloat(obj);
        obj.visible = false;
      }
    });
  }, []);

  useEffect(() => {
    switch (playerAction) {
      case 'Action_16': // 甩干
        setCurAnimation('Action_fishing_00');
        break;
      case 'Action_17': // 等待
        setCurAnimation('Action_fishing_01');
        break;
      case 'Action_18': // 挣扎
        setCurAnimation('Action_fishing_02');
        break;
      case 'Action_20': // 收杆
        setCurAnimation('Action_fishing_03');
        break;
      default:
        setCurAnimation('Action_fishing_04');
    }
  }, [playerAction]);

  return (
    <RodAnimation
      itemObject={rodItemRoot}
      floatObject={float}
      animations={animations}
      useGame={useGame}
    />
  );
}
