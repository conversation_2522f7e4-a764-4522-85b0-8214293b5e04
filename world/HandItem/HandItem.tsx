import * as THREE from 'three';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { ItemConfig, ItemData, ItemType } from '@/world/Config/ItemConfig';
import { useControls } from 'leva';
import { GetMyPlayer } from '@/world/Character/MyPlayer';
import { UseGameState } from '@/src/stores/useGame';
import { getParticleSystem } from '@/world/Particles/ParticleSystem';
import { LoaderUtil } from '@/world/Util/LoaderUtil';
import { AudioSystem } from '@/world/Global/GlobalAudioSystem';
import ParticleObject from '@/world/Particles/ParticleObject';
import ItemFishingRod from '@/world/HandItem/ItemFishingRod';
import { useNetWork } from '@/world/hooks/useNetWork';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

let myHandItemServerId = '';
let myHandItemId = 0;
let timerList: any[] = [];

function changeHandItemId(
  setCurAnimation: any,
  myPlayer: any,
  serverItemId: string,
  itemId: number
) {
  if (serverItemId === myHandItemServerId && itemId === myHandItemId) {
    return;
  }
  myHandItemServerId = serverItemId;
  myHandItemId = itemId;

  timerList.forEach((timer) => {
    clearTimeout(timer);
  });
  timerList = [];

  if (itemId > 0) {
    if (myPlayer.isNewServerItemId(serverItemId)) {
      myPlayer.faceCamera();
      myPlayer.saveServerItemId(serverItemId);
      setCurAnimation('Action_11');
      timerList.push(
        setTimeout(() => {
          AudioSystem.playAudio('myPlayer', './sound/reward/reward_axe.mp3', () => {
            return true;
          });
        }, 650)
      );
      KeyPressUtil.setEnable(false);
      timerList.push(
        setTimeout(() => {
          KeyPressUtil.setEnable(true);
        }, 2500)
      );
    } else {
      KeyPressUtil.setEnable(true);
    }
  } else {
    if (serverItemId.length > 0) {
      myPlayer.faceCamera();
      const showDisappearEffect = () => {
        const particleSystem = getParticleSystem();
        const tempObject = new THREE.Object3D();
        tempObject.position.copy(myPlayer.position);
        tempObject.quaternion.copy(myPlayer.quaternion);
        const tempObject2 = new THREE.Object3D();
        tempObject2.position.set(0, 0.6, 0.5);
        tempObject.add(tempObject2);

        const effectPos = tempObject2.getWorldPosition(new THREE.Vector3());
        particleSystem.addParticle(
          effectPos,
          new THREE.Quaternion(),
          './particles/Effect_broken.json',
          0.3,
          3000
        );
      };

      setCurAnimation('Action_10');
      timerList.push(
        setTimeout(() => {
          showDisappearEffect();
        }, 450)
      );
      timerList.push(
        setTimeout(() => {
          AudioSystem.playAudio('myPlayer', './sound/break/axe_damage.mp3', () => {
            return true;
          });
        }, 400)
      );
      KeyPressUtil.setEnable(false);
      timerList.push(
        setTimeout(() => {
          myPlayer.destroyHandItem();
          KeyPressUtil.setEnable(true);
        }, 2500)
      );
    } else {
      KeyPressUtil.setEnable(true);
    }
  }
}

export interface HandItemDetail {
  itemId: number;
  isMe: boolean;
  curDurability: number;
  showEffect: boolean;
  serverId: string;
  useGame: any;
}

function TransformControl({
  itemObject,
  pos,
  rot,
  effectUrl,
  effectScale,
}: {
  itemObject: THREE.Object3D;
  pos: number[];
  rot: number[];
  effectUrl: string;
  effectScale: number;
}) {
  let transformDebug = null;
  const debugItemTransform = localStorage.getItem('debugItemTransform');
  if (debugItemTransform === 'true') {
    // Character Controls
    // eslint-disable-next-line react-hooks/rules-of-hooks
    transformDebug = useControls('Item Transform Controls ' + itemObject.name, {
      posX: {
        value: pos[0],
        min: -2,
        max: 2,
        step: 0.01,
      },
      posY: {
        value: pos[1],
        min: -2,
        max: 2,
        step: 0.01,
      },
      posZ: {
        value: pos[2],
        min: -2,
        max: 2,
        step: 0.01,
      },
      rotX: {
        value: rot[0],
        min: -180,
        max: 180,
        step: 0.1,
      },
      rotY: {
        value: rot[1],
        min: -180,
        max: 180,
        step: 0.1,
      },
      rotZ: {
        value: rot[2],
        min: -180,
        max: 180,
        step: 0.1,
      },
    });
    // Apply debug values
    pos = [transformDebug.posX, transformDebug.posY, transformDebug.posZ];
    rot = [transformDebug.rotX, transformDebug.rotY, transformDebug.rotZ];
  }

  const refEffect = useRef<THREE.Group>(null);

  useEffect(() => {
    if (effectUrl && itemObject) {
      if (refEffect.current) {
        itemObject.add(refEffect.current);
        return () => {
          if (refEffect.current) {
            itemObject.remove(refEffect.current);
          }
        };
      }
    }
  }, [effectUrl, itemObject]);

  useEffect(() => {
    if (itemObject) {
      itemObject.position.copy(new THREE.Vector3(...pos));
      itemObject.quaternion.setFromEuler(
        new THREE.Euler(
          (rot[0] * Math.PI) / 180,
          (rot[1] * Math.PI) / 180,
          (rot[2] * Math.PI) / 180
        )
      );
    }
  }, [itemObject, pos, rot]);

  return (
    <>
      {effectUrl.length > 0 && (
        <ParticleObject url={effectUrl} scale={effectScale} time={-1} ref={refEffect} />
      )}
    </>
  );
}

export default function HandItem({
  rightHand,
  itemDetail,
  useGame,
}: {
  rightHand: THREE.Object3D;
  useGame: any;
  itemDetail: HandItemDetail;
}) {
  const { scene } = useThree();
  const [itemRoot, setItemRoot] = useState<THREE.Object3D | null>();
  const myPlayer = GetMyPlayer();
  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const rightHandPos = useMemo(() => new THREE.Vector3(), []);
  const rightHandRot = useMemo(() => new THREE.Quaternion(), []);
  const [itemId, setItemId] = useState<number>(0);
  const [serverId, setServerId] = useState<string>('');
  const [itemData, setItemData] = useState<ItemData | null>(null);
  const [itemObject, setItemObject] = useState<THREE.Object3D | null>(null);
  const [effectUrl, setEffectUrl] = useState<string>('');
  const [effectScale, setEffectScale] = useState<number>(1);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const { sendItemId } = useNetWork();

  useEffect(() => {
    setEffectScale(1);
    setEffectUrl('');
    ItemConfig.getInstance().getData(itemId, (data) => {
      initializeAnimationSet(
        {
          idle: data && data.action_idle.length > 0 ? data.action_idle : 'idle',
          wearyIdle:
            data && data.action_weary_idle.length > 0 ? data.action_weary_idle : 'weary_idle',
          walk: data && data.action_walk.length > 0 ? data.action_walk : 'walk',
          wearyWalk:
            data && data.action_weary_walk.length > 0 ? data.action_weary_walk : 'weary_walk',
          run: data && data.action_run.length > 0 ? data.action_run : 'run',
          jump: 'jump_01',
          jumpIdle: 'jump_02',
          jumpLand: 'jump_03',
          fall: 'fall', // This is for falling from high sky
        },
        true
      );
      if (itemDetail.isMe) {
        myPlayer.changeHandItem(data);
        changeHandItemId(setCurAnimation, myPlayer, serverId, itemId);
        sendItemId();
      }
      setEffectUrl(data && data.effect_url ? data.effect_url : '');
      setEffectScale(data && data.effect_scale ? data.effect_scale : 1);
      setItemData(data);
    });
  }, [itemId, serverId]);

  useEffect(() => {
    const root = new THREE.Object3D();
    scene.add(root);
    setItemRoot(root);
    return () => {
      scene.remove(root);
      setItemRoot(null);
    };
  }, []);

  useEffect(() => {
    if (itemData) {
      let cancel = false;
      setAnimations([]);
      LoaderUtil.loadGlb(itemData.glb_url, (gltf) => {
        if (cancel) return;
        gltf.scene.name = itemData.name;

        gltf.scene.position.set(0, 0, 0);
        setAnimations(gltf.animations);
        setItemObject(null);
        setTimeout(() => {
          setItemObject(gltf.scene);
        }, 1);
      });
      return () => {
        cancel = true;
      };
    } else {
      setItemObject(null);
    }
  }, [itemData]);

  useEffect(() => {
    if (itemObject && itemRoot) {
      const oldObject = itemObject;
      itemRoot.clear();
      itemRoot.add(itemObject);
      return () => {
        if (itemDetail.serverId.length > 0 && itemDetail.curDurability === 0) {
          setTimeout(() => {
            itemRoot.remove(oldObject);
          }, 500);
        } else {
          itemRoot.remove(oldObject);
        }
      };
    }
  }, [itemObject, itemRoot]);

  useFrame(() => {
    if (itemRoot) {
      rightHand.getWorldPosition(rightHandPos);
      rightHand.getWorldQuaternion(rightHandRot);
      itemRoot.position.copy(rightHandPos);
      itemRoot.quaternion.copy(rightHandRot);
    }

    if (itemDetail.curDurability > 0) {
      setItemId(itemDetail.itemId);
      setServerId(itemDetail.serverId);
    } else {
      setServerId(itemDetail.serverId);
      setItemId(0);
    }

    // setShowEffect(itemDetail.showEffect)
  });

  return (
    <>
      {itemObject && itemData && (
        <TransformControl
          itemObject={itemObject}
          pos={itemData.position}
          rot={itemData.rotation}
          effectUrl={effectUrl}
          effectScale={effectScale}
        />
      )}
      {animations.length > 0 && itemObject && itemData && itemData.type == ItemType.FishingRod && (
        <ItemFishingRod
          playerUseGame={useGame}
          rodItemRoot={itemObject}
          animations={animations}
          useGame={itemDetail.useGame}
        />
      )}
    </>
  );
}
