import { ConfigManager } from '@/world/Config/ConfigManager';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import AvatarAction from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Part/AvatarAction';

export type AvatarActionData = {
  id: number;
  name: string;
  actionUrl: string;
  shortcut: string;
  soundUrl: string;
  playSoundTime: number;
  actionTime: number;
  delayLoading: number;
};

export class AvatarActionConfig {
  private static instance: AvatarActionConfig;

  private actionDataMap: Map<number, { url: string; data: AvatarActionData }>;
  private actionMap: Map<string, AvatarActionData> = new Map<string, AvatarActionData>();
  private actionList: AvatarAction[] = [];
  private delayActionList: AvatarAction[] = [];

  private constructor() {
    this.actionDataMap = new Map<number, { url: string; data: AvatarActionData }>();
    ConfigManager.getInstance().downloadConfig(
      './avatarAction/_totals.json',
      (data: AvatarActionData[]) => {
        for (let i = 0; i < data.length; i++) {
          const actionData = data[i];
          actionData.name = actionData.name || '';
          actionData.actionUrl = actionData.actionUrl || '';
          actionData.soundUrl = actionData.soundUrl || '';
          actionData.shortcut = actionData.shortcut || '';
          actionData.playSoundTime = actionData.playSoundTime || 0;
          actionData.actionTime = actionData.actionTime || 0;
          actionData.delayLoading = actionData.delayLoading || 0;
          this.actionMap.set(actionData.name, actionData);
          this.actionDataMap.set(actionData.id, { url: '', data: actionData });
        }
      }
    );
  }

  static getInstance() {
    if (!AvatarActionConfig.instance) {
      AvatarActionConfig.instance = new AvatarActionConfig();
    }
    return AvatarActionConfig.instance;
  }

  getData(id: number, cb: (data: AvatarActionData | null) => void) {
    if (this.actionDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.actionDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      if (id !== 0) {
        console.error('not found item config id: ' + id);
      }
      cb(null);
    }
  }

  getAction(name: string) {
    return this.actionMap.get(name);
  }

  getActionList(cb: (actionList: AvatarAction[], delayActionList: AvatarAction[]) => void) {
    if (this.actionDataMap.size === 0) {
      setTimeout(() => {
        this.getActionList(cb);
      }, 100);
      return;
    }
    if (this.actionMap.size < this.actionDataMap.size) {
      setTimeout(() => {
        this.getActionList(cb);
      }, 100);
      return;
    }
    if (this.actionList.length > 0) {
      cb(this.actionList, this.delayActionList);
      return;
    }
    this.actionMap.forEach((config) => {
      if (config.delayLoading === 1) {
        this.delayActionList.push(new AvatarAction('', getCdnLink(config.actionUrl)));
      } else {
        this.actionList.push(new AvatarAction('', getCdnLink(config.actionUrl)));
      }
    });
    cb(this.actionList, this.delayActionList);
  }

  getDataList(cb: (actionList: AvatarActionData[]) => void) {
    if (this.actionDataMap.size === 0) {
      setTimeout(() => {
        this.getDataList(cb);
      }, 100);
      return;
    }
    if (this.actionMap.size < this.actionDataMap.size) {
      setTimeout(() => {
        this.getDataList(cb);
      }, 100);
      return;
    }
    const list: AvatarActionData[] = [];
    this.actionMap.forEach((config) => {
      list.push(config);
    });
    cb(list);
  }
}
