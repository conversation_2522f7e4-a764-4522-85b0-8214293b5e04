import { ConfigManager } from '@/world/Config/ConfigManager';

export enum NpcType {
  None,
  NormalNpc = 1,
  TwitterNpc = 2,
}

export type NpcData = {
  id: number;
  name: string;
  position: number[];
  distance: number;
  yawY: number;
  type: NpcType;
  transformMapId: number;
  transformPosition: number[];
  chatId1: number;
  chatId2: number;
  yawOffset: number;
  glbUrl: string;
  glbScale: number;
  faceUrl: string;
  avatarData: {
    shirtId: string | null;
    shirtTextureId: string | null;
    shirtColor: string | null;
    pantsId: string | null;
    shoesId: string | null;
    hatId: string | null;
    glovesId: string | null;
  };
  handItemId: number;
  lockYaw: number;
  default_action: string;
};

export class NpcConfig {
  private static instance: NpcConfig;

  private npcDataMap: Map<number, { url: string; data: NpcData }>;

  private constructor() {
    this.npcDataMap = new Map<number, { url: string; data: NpcData }>();
    ConfigManager.getInstance().downloadConfig('./npc/_totals.json', (data: NpcData[]) => {
      for (let i = 0; i < data.length; i++) {
        const npcData = data[i];
        npcData.name = npcData.name || '';
        npcData.type = npcData.type || NpcType.None;
        npcData.position = npcData.position || [0, 0, 0];
        npcData.distance = npcData.distance || 3;
        npcData.yawY = npcData.yawY || 0;
        npcData.yawOffset = npcData.yawOffset || 0;
        npcData.glbUrl = npcData.glbUrl || '';
        npcData.faceUrl = npcData.faceUrl || '';
        npcData.glbScale = npcData.glbScale || 1;
        npcData.transformMapId = npcData.transformMapId || 2;
        npcData.transformPosition = npcData.transformPosition || [0, 0, 0];
        npcData.default_action = npcData.default_action || 'idle';
        npcData.lockYaw = npcData.lockYaw || 0;
        npcData.handItemId = npcData.handItemId || 0;
        this.npcDataMap.set(npcData.id, { url: '', data: npcData });
      }
    });
  }

  static getInstance() {
    if (!NpcConfig.instance) {
      NpcConfig.instance = new NpcConfig();
    }
    return NpcConfig.instance;
  }

  getData(id: number, cb: (data: NpcData) => void) {
    if (this.npcDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.npcDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      console.error('not found npc config id: ' + id);
    }
  }
}
