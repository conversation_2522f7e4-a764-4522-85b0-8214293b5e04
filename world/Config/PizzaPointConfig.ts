import { RewardType } from '@/constant/type';
import { ConfigManager } from '@/world/Config/ConfigManager';

export type PizzaPointData = {
  name: string;
  id: number;
  glbUrl: string;
  glbScale: number;
  position: number[];
  rotation: number[];
  distance: number;
};

export class PizzaPointConfig {
  private static instance: PizzaPointConfig;

  private pointDataMap: Map<number, { url: string; data: PizzaPointData }>;
  private curTick: string = RewardType.sPIZZA___000;

  private constructor() {
    this.pointDataMap = new Map<number, { url: string; data: PizzaPointData }>();
    ConfigManager.getInstance().downloadConfig(
      './pizzaPoint/_totals.json',
      (data: PizzaPointData[]) => {
        for (let i = 0; i < data.length; i++) {
          const pointData = data[i];
          pointData.name = pointData.name || '';
          pointData.position = pointData.position || [0, 0, 0];
          pointData.rotation = pointData.rotation || [0, 0, 0];
          pointData.glbUrl = pointData.glbUrl || '';
          pointData.glbScale = pointData.glbScale || 1;
          pointData.distance = pointData.distance || 2;
          this.pointDataMap.set(pointData.id, { url: '', data: pointData });
        }
      }
    );
  }

  static getInstance() {
    if (!PizzaPointConfig.instance) {
      PizzaPointConfig.instance = new PizzaPointConfig();
    }
    return PizzaPointConfig.instance;
  }

  getData(id: number, cb: (data: PizzaPointData | null) => void) {
    if (this.pointDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.pointDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      if (id !== 0) {
        console.error('not found itemDrop config id: ' + id);
      }
      cb(null);
    }
  }

  randomIds() {
    const list = Array.from(this.pointDataMap.keys());

    const randomList: PizzaPointData[] = [];
    let count = Math.floor(list.length * 0.8);
    while (count > 0) {
      count--;
      const random = Math.floor(Math.random() * list.length);
      this.getData(list[random], (data) => {
        if (data) {
          randomList.push(data);
        }
      });

      list.splice(random, 1);
    }

    return randomList;
  }

  getAllData() {
    const list = Array.from(this.pointDataMap.keys());

    const randomList: PizzaPointData[] = [];
    let count = list.length;
    while (count > 0) {
      count--;
      const random = Math.floor(Math.random() * list.length);
      this.getData(list[random], (data) => {
        if (data) {
          randomList.push(data);
        }
      });

      list.splice(random, 1);
    }

    return randomList;
  }

  setCurTick(tick: string) {
    this.curTick = tick;
  }

  getPizzaBoxUrl(tick?: string) {
    const curTick = tick || this.curTick;
    switch (curTick) {
      case RewardType.sPIZZA___000:
        return './assets/Prop/Prop_rush_box_pizza.glb';
      case RewardType.potato:
        return './assets/Prop/Prop_rush_box_potato.glb';
      case RewardType.wangcai:
        return './assets/Prop/Prop_rush_box_wangcai.glb';
      case RewardType.TheLonelyBit:
        return './assets/Prop/Prop_rush_box_tlb.glb';
      case RewardType.sQUAQ___000:
        return './assets/Prop/Prop_rush_box_domoducks.glb';
    }
    return './assets/Prop/Prop_rush_box_pizza.glb';
  }

  getPizzaBagUrl(tick?: string) {
    const curTick = tick || this.curTick;
    switch (curTick) {
      case RewardType.sPIZZA___000:
        return './assets/Prop/Prop_rush_bag_pizza.glb';
      case RewardType.potato:
        return './assets/Prop/Prop_rush_bag_potato.glb';
      case RewardType.wangcai:
        return './assets/Prop/Prop_rush_bag_wangcai.glb';
      case RewardType.TheLonelyBit:
        return './assets/Prop/Prop_rush_bag_tlb.glb';
      case RewardType.sQUAQ___000:
        return './assets/Prop/Prop_rush_bag_domoducks.glb';
    }
    return './assets/Prop/Prop_rush_bag_pizza.glb';
  }
}
