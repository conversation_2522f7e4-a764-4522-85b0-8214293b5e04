import { ConfigManager } from '@/world/Config/ConfigManager';

interface IUserDropItem {
  dropItemTag: number; // 掉落物品Tag
  isPickedUp: boolean; // 是否已拾取
  tag: number; // 掉落位置tag
  quantity: number; // 掉落数量
  coolDown: number; // 掉落数量
}

export type ItemDropData = {
  name: string;
  id: number;
  glbUrl: string;
  actionName: string;
  glbScale: number;
  position: number[];
  rotation: number[];
  distance: number;
  //not config
  isPickedUp: boolean;
  coolDown: number;
};

export class ItemDropConfig {
  private static instance: ItemDropConfig;

  private itemDropDataMap: Map<number, { url: string; data: ItemDropData }>;

  private constructor() {
    this.itemDropDataMap = new Map<number, { url: string; data: ItemDropData }>();
    ConfigManager.getInstance().downloadConfig(
      './itemDrop/_totals.json',
      (data: ItemDropData[]) => {
        for (let i = 0; i < data.length; i++) {
          const itemDropData = data[i];
          itemDropData.name = itemDropData.name || '';
          itemDropData.actionName = itemDropData.actionName || '';
          itemDropData.position = itemDropData.position || [0, 0, 0];
          itemDropData.rotation = itemDropData.rotation || [0, 0, 0];
          itemDropData.glbUrl = itemDropData.glbUrl || '';
          itemDropData.glbScale = itemDropData.glbScale || 1;
          itemDropData.distance = itemDropData.distance || 2;

          itemDropData.isPickedUp = true;
          itemDropData.coolDown = -1;
          this.itemDropDataMap.set(itemDropData.id, { url: '', data: itemDropData });
        }
      }
    );
  }

  static getInstance() {
    if (!ItemDropConfig.instance) {
      ItemDropConfig.instance = new ItemDropConfig();
    }
    return ItemDropConfig.instance;
  }

  getData(id: number, cb: (data: ItemDropData | null) => void) {
    if (this.itemDropDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.itemDropDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      if (id !== 0) {
        console.error('not found itemDrop config id: ' + id);
      }
      cb(null);
    }
  }

  updateData(list: IUserDropItem[]) {
    list.forEach((item) => {
      this.getData(Number(item.tag), (data) => {
        if (data) {
          data.isPickedUp = item.isPickedUp;
          data.coolDown = item.coolDown || -1;
        }
      });
    });
  }

  getLastRefreshTime() {
    let lastRefreshTime = 0;
    this.itemDropDataMap.forEach((item) => {
      if (item.data) {
        if (item.data.coolDown > 0) {
          if (lastRefreshTime === 0 || item.data.coolDown < lastRefreshTime) {
            lastRefreshTime = item.data.coolDown;
          }
        }
      }
    });
    return lastRefreshTime;
  }
}
