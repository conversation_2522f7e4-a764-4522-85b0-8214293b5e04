import { ConfigManager } from '@/world/Config/ConfigManager';

export type RedPacketData = {
  name: string;
  id: number;
  glbUrl1: string;
  glbUrl2: string;
  glbUrl3: string;
  glbScale: number;
  position: number[];
  rotation: number[];
  distance: number;
};

export class RedPacketConfig {
  private static instance: RedPacketConfig;

  private packetDataMap: Map<number, { url: string; data: RedPacketData }>;

  private constructor() {
    this.packetDataMap = new Map<number, { url: string; data: RedPacketData }>();
    ConfigManager.getInstance().downloadConfig(
      './redPacket/_totals.json',
      (data: RedPacketData[]) => {
        for (let i = 0; i < data.length; i++) {
          const packetData = data[i];
          packetData.name = packetData.name || '';
          packetData.position = packetData.position || [0, 0, 0];
          packetData.rotation = packetData.rotation || [0, 0, 0];
          packetData.glbUrl1 = packetData.glbUrl1 || '';
          packetData.glbUrl2 = packetData.glbUrl2 || '';
          packetData.glbUrl3 = packetData.glbUrl3 || '';
          packetData.glbScale = packetData.glbScale || 1;
          packetData.distance = packetData.distance || 2;
          this.packetDataMap.set(packetData.id, { url: '', data: packetData });
        }
      }
    );
  }

  static getInstance() {
    if (!RedPacketConfig.instance) {
      RedPacketConfig.instance = new RedPacketConfig();
    }
    return RedPacketConfig.instance;
  }

  getData(id: number, cb: (data: RedPacketData | null) => void) {
    if (this.packetDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb);
      }, 500);
      return;
    }

    const config = this.packetDataMap.get(id);
    if (config) {
      cb(config.data);
    } else {
      if (id !== 0) {
        console.error('not found itemDrop config id: ' + id);
      }
      cb(null);
    }
  }

  getAllData(cb: (list: RedPacketData[]) => void) {
    if (this.packetDataMap.size === 0) {
      setTimeout(() => {
        this.getAllData(cb);
      }, 500);
      return;
    }

    const list: RedPacketData[] = [];
    this.packetDataMap.forEach((config) => {
      list.push(config.data);
    });
    cb(list);
  }
}
