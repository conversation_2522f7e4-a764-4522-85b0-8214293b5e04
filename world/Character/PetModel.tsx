/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Ecctrl, { CustomEcctrlRigidBody } from '../../src/Ecctrl';
import createUseGame, { UseGameState } from '../../src/stores/useGame';
import GlobalSpace from '../Global/GlobalSpace';
import TransformTools from '../Scene/TransformTools';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey, TransformData } from '../Global/GlobalSpaceEvent';
import { useFrame } from '@react-three/fiber';
import { useNetWork } from '@/world/hooks/useNetWork';
import PlayerMesh from '@/world/Character/PlayerMesh';
import AvatarData from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';

function NetSender({ useGame, children }: { children: React.ReactNode; useGame: any }) {
  const groupRef = useRef<THREE.Group>(null);
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const { sendPetPosition, sendPetAnimation } = useNetWork();
  const lastUpdateRef = useRef(new THREE.Vector3()); // 记录上次更新的位置
  const worldPosRef = useRef(new THREE.Vector3());
  const worldQuaternionRef = useRef(new THREE.Quaternion());

  useEffect(() => {
    if (groupRef.current) {
      sendPetAnimation(curAnimation);
    }
  }, [curAnimation]);

  useFrame(() => {
    const group = groupRef.current;
    if (!group) return;
    group.getWorldPosition(worldPosRef.current);
    groupRef.current.getWorldQuaternion(worldQuaternionRef.current);

    if (lastUpdateRef.current.distanceTo(worldPosRef.current) < 0.1) {
      return;
    }
    lastUpdateRef.current.copy(worldPosRef.current);

    sendPetPosition(worldPosRef.current, worldQuaternionRef.current);
  });
  return (
    <group position={[0, -0.92, 0]} ref={groupRef}>
      {/* Replace character model here */}
      {children}
    </group>
  );
}

export default function PetModel({ petId }: { petId: string }) {
  const playerRef = useRef<CustomEcctrlRigidBody>(null);
  const useGame = useMemo(() => createUseGame(), []);
  const idle = useGame((state) => state.idle);
  const setCurAnimation = useGame((state) => state.setCurAnimation);
  const setMoveToPoint = useGame((state) => state.setMoveToPoint);
  const setTransformPoint = useGame((state) => state.setTransformPoint);
  const { sendPetId } = useNetWork();
  const [usePet, setUsePet] = useState<string>('');
  useEffect(() => {
    setUsePet(petId);
    sendPetId(petId);
  }, [petId]);

  useEffect(() => {
    let transformCd = 0;
    const cancelKey = GlobalSpace.whatCharacterPosition((playerPosition) => {
      if (playerRef.current) {
        const petPos = new THREE.Vector3().copy(playerRef.current.translation());
        const distance = playerPosition.distanceTo(petPos);
        const osTime = Date.now();
        if (transformCd < osTime && distance > 8) {
          const offsetList = [
            new THREE.Vector3(1, 0.5, 0),
            new THREE.Vector3(-1, 0.5, 0),
            new THREE.Vector3(0, 0.5, 1),
            new THREE.Vector3(0, 0.5, -1),
          ];
          const randomOffset = offsetList[Math.floor(Math.random() * offsetList.length)];
          randomOffset.add(playerPosition);
          transformCd = osTime + 3000;
          setTransformPoint(randomOffset);
          GlobalSpaceEvent.SetDataValue<TransformData>(GlobalDataKey.TransformData, {
            characterType: CharacterType.Pet,
            position: randomOffset,
          });
          setMoveToPoint(null);
          if (idle) {
            idle();
          }
          if (setCurAnimation) {
            setCurAnimation('Action_1001');
          }
        } else {
          setMoveToPoint(playerPosition, false);
        }
      }
    });
    return () => {
      GlobalSpace.cancelCharacterPositionCallback(cancelKey);
    };
  }, []);

  return (
    <>
      {petId.length > 0 && (
        <>
          <TransformTools characterType={CharacterType.Pet} useGame={useGame} />
          <Ecctrl
            mode="PointToMove"
            useGame={useGame}
            animated={true}
            disableFollowCam={true}
            position={[1, 1, 0]}
            ref={playerRef}>
            <NetSender useGame={useGame}>
              <PlayerMesh useGame={useGame} avatarData={new AvatarData()} usePet={usePet} />
            </NetSender>
          </Ecctrl>
        </>
      )}
    </>
  );
}
