/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import React, { useEffect, useState } from 'react';
import { OtherPlayerData } from './OtherPlayer';
import * as THREE from 'three';
import { POPOVER_HEIGHT } from '@/constant';
import { useFrame } from '@react-three/fiber';
import { NetPlayerManager } from '@/model/NetPlayer/NetPlayerManager';
import { NetPlayerListener } from '@/model/NetPlayer/NetPlayerListener';
import { NetPlayerEvent } from '@/model/NetPlayer/NetPlayerEvent';
import { useSelector } from 'react-redux';
import { ISettingState } from '@/constant/type';

function OtherPlayerRoot({ otherPlayer }: { otherPlayer: OtherPlayerData }) {
  const groupRef = React.useRef<THREE.Group>(null);
  const [connect, setConnect] = useState<boolean>(false);
  const updateHeight = () => {
    if (groupRef.current) {
      const box = new THREE.Box3().setFromObject(groupRef.current);
      return box.max.y - box.min.y + POPOVER_HEIGHT;
    }
    return 0;
  };
  useFrame(() => {
    setConnect(otherPlayer.id.length > 0);
  });
  return <group ref={groupRef}>{connect && otherPlayer.getReact(updateHeight)}</group>;
}

export default function PlayerManager() {
  const [otherPlayers, setOtherPlayers] = useState<OtherPlayerData[]>([]);
  const { maxPlayers } = useSelector(
    (state: { SettingReducer: ISettingState }) => state.SettingReducer
  );

  useEffect(() => {
    NetPlayerManager.getInstance().checkPlayerDistance(maxPlayers);
    const interval = setInterval(() => {
      NetPlayerManager.getInstance().checkPlayerDistance(maxPlayers);
    }, 5 * 1000);
    return () => {
      clearInterval(interval);
    };
  }, [maxPlayers]);

  useEffect(() => {
    NetPlayerListener.getInstance().addListener(NetPlayerEvent.NetPlayerChange, () => {
      const otherPlayers = NetPlayerManager.getInstance().getOtherPlayerList();
      const newPlayer = [];
      newPlayer.push(...otherPlayers);
      setOtherPlayers(newPlayer);
    });
  }, []);
  return (
    <>
      {otherPlayers.map((player, index) => {
        return <OtherPlayerRoot key={player.uuid} otherPlayer={player} />;
      })}
    </>
  );
}
