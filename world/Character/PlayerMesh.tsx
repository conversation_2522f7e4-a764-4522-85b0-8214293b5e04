import React, { useEffect, useRef, useState } from 'react';
import createUseGame, { UseGameState } from '@/src/stores/useGame';
import * as THREE from 'three';
import { useAnimations } from '@react-three/drei';
import AvatarData from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import { GLTF } from 'three-stdlib';
import AvatarFace from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/AvatarFace';
import { useFrame } from '@react-three/fiber';
import { AvatarUtil } from '@/AvatarOrdinalsBrowser/renderAvatar/AvatarUtil';
import HandItem, { HandItemDetail } from '@/world/HandItem/HandItem';
import AudioSystemComponent, { AudioSystem } from '@/world/Global/GlobalAudioSystem';
import { AvatarActionConfig } from '@/world/Config/AvatarActionConfig';
import PizzaBag from '@/world/Character/PizzaBag';
import { PizzaPointConfig } from '@/world/Config/PizzaPointConfig';
import { LoaderUtil } from '@/world/Util/LoaderUtil';
import { FishConfig } from '@/world/Config/FishConfig';
import AvatarAction from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Part/AvatarAction';

export interface ActivityData {
  pizzaCount: number;
  tick: string;
}

export interface HandFishData {
  fishId: number;
}

function PlayerAnimation({
  useGame,
  animationsRef,
  scene,
}: {
  useGame: any;
  animationsRef: THREE.AnimationClip[];
  scene: THREE.Object3D;
}) {
  const useGameIndex = useGame((state: UseGameState) => state.getIndex());
  /**
   * Character animations setup
   */
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const lockAnimation = useGame((state: UseGameState) => state.lockAnimation);
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([...animationsRef]);
  const { actions } = useAnimations(animations, scene);
  const resetAnimation = useGame((state: UseGameState) => state.reset);

  const animationSet = useGame((state: UseGameState) => state.animationSet);

  useEffect(() => {
    if (animationsRef.length !== animations.length) {
      setAnimations([...animationsRef]);
      return;
    }
    if (animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return;
    }
    //按|分割
    const actionList: string[] = curAnimation.split('|');
    //随机一个
    const actionKey = actionList[Math.floor(Math.random() * actionList.length)];

    const finishCall = () => {
      if (animationSet.fall !== curAnimation) {
        resetAnimation();
      }
      if (actionList.length > 1) {
        setTimeout(() => {
          setCurAnimation(curAnimation);
        }, 1);
      }
    };
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      // For jump and jump land animation, only play once and clamp when finish
      if (
        curAnimation !== lockAnimation &&
        curAnimation !== animationSet.idle &&
        curAnimation !== animationSet.wearyIdle &&
        curAnimation !== animationSet.wearyWalk &&
        curAnimation !== animationSet.jumpLand &&
        curAnimation !== animationSet.run &&
        curAnimation !== animationSet.walk
      ) {
        action.reset().fadeIn(0.2).setLoop(THREE.LoopOnce, 0).play();
        action.clampWhenFinished = true;
      } else {
        if (curAnimation === animationSet.jump) {
          action.reset().setLoop(THREE.LoopRepeat, Infinity).fadeIn(0.4).play();
        } else {
          action.reset().setLoop(THREE.LoopRepeat, Infinity).fadeIn(0.2).play();
        }
      }

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [curAnimation, animations]);

  useEffect(() => {
    if (curAnimation) {
      const timerList: any[] = [];
      const intervalList: any[] = [];
      const audioKey = 'playerMesh_' + useGameIndex;
      const actionData = AvatarActionConfig.getInstance().getAction(curAnimation);
      if (actionData) {
        const playSoundTime = actionData.playSoundTime;
        if (playSoundTime > 0) {
          timerList.push(
            setTimeout(() => {
              AudioSystem.playAudio(audioKey, actionData.soundUrl, () => {
                return true;
              });
              intervalList.push(
                setInterval(() => {
                  AudioSystem.playAudio(audioKey, actionData.soundUrl, () => {
                    return true;
                  });
                }, actionData.actionTime)
              );
            }, playSoundTime)
          );
        }
      }
      return () => {
        timerList.forEach((timer) => {
          clearTimeout(timer);
        });
        intervalList.forEach((interval) => {
          clearInterval(interval);
        });
      };
    }
  }, [curAnimation]);

  return null;
}

export function CreateMesh({
  useGame,
  gltf,
  delayActionList,
}: {
  useGame: any;
  gltf: GLTF;
  delayActionList: AvatarAction[];
}) {
  const ref = useRef<THREE.Group>(null);
  const [animationCount, setAnimationCount] = useState<number>(0);
  const animationsRef = useRef<THREE.AnimationClip[]>([]);
  const addAnimation = (clips: THREE.AnimationClip[]) => {
    for (let i = 0; i < clips.length; i++) {
      switch (clips[i].name) {
        case 'Action_00':
          clips[i].name = 'idle';
          break;
        case 'Action_01':
          clips[i].name = 'run';
          break;
        case 'Action_03':
          clips[i].name = 'walk';
          break;
        case 'Action_33':
          clips[i].name = 'weary_idle';
          break;
        case 'Action_34':
          clips[i].name = 'weary_walk';
          break;
        default:
          break;
      }
    }
    animationsRef.current.push(...clips);
    setAnimationCount(animationsRef.current.length);
  };

  const initializeAnimationSet = useGame((state: UseGameState) => state.initializeAnimationSet);

  useEffect(() => {
    initializeAnimationSet({
      idle: 'idle',
      walk: 'walk',
      run: 'run',
      jump: 'jump_01',
      jumpIdle: 'jump_02',
      jumpLand: 'jump_03',
      fall: 'fall',
    });
    if (ref.current) {
      const group = ref.current;
      gltf.scene.traverse((child) => {
        if (child.type.includes('Mesh')) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });
      group.add(gltf.scene);
      animationsRef.current.length = 0;
      const jumpClips: THREE.AnimationClip[] = [];
      //临时加入默认动作
      for (let i = 0; i < gltf.animations.length; i++) {
        if (gltf.animations[i].name === 'Action_00') {
          const clip = gltf.animations[i].clone();
          clip.name = 'Default';
          gltf.animations.push(clip);
        }

        //切割跳跃动作
        if (gltf.animations[i].name === 'Action_04') {
          gltf.animations[i].name = 'jump';
          const maxDuration = Math.floor(gltf.animations[i].duration * 10) / 10;
          const ranges = [
            { start: 0, end: Math.min(1.5, maxDuration), name: 'jump_01' },
            { start: 0.2, end: Math.min(1.5, maxDuration), name: 'jump_02' },
            { start: 1.05, end: Math.min(1.5, maxDuration), name: 'jump_03' },
            { start: 0.8, end: 1.05, name: 'fall' },
          ];
          const clip = gltf.animations[i].clone();
          const tracks = clip.tracks; // 动画的所有轨迹

          ranges.forEach((range) => {
            const { start, end } = range;
            const newTracks: THREE.VectorKeyframeTrack[] = [];
            tracks.map((track) => {
              const startIndex = track.times.findIndex((time) => time >= start);
              const endIndex = track.times.findIndex((time) => time > end);

              // 如果没有关键帧，跳过
              if (startIndex === -1 || endIndex === -1 || startIndex === endIndex) {
                // console.warn(`No valid keyframes found for track ${track.name} in range ${start} to ${end}`);
                return;
              }

              // 克隆轨迹并截取时间和值
              const newTrack = track.clone();
              newTrack.times = track.times.slice(startIndex, endIndex).map((time) => time - start);
              const valueSize = track.getValueSize(); // 每个时间点的值大小
              newTrack.values = track.values.slice(startIndex * valueSize, endIndex * valueSize);

              // 确保 `times` 和 `values` 不为空
              if (newTrack.times.length === 0 || newTrack.values.length === 0) {
                console.warn(`Empty track: ${track.name} after slicing. Skipping.`);
                return;
              }
              newTracks.push(newTrack);
            });

            const newClip = new THREE.AnimationClip(range.name, end - start, newTracks);
            jumpClips.push(newClip);
          });
        }
      }
      // 跳跃动作加入数组
      gltf.animations.push(...jumpClips);
      addAnimation(gltf.animations);
      delayActionList.forEach((action) => {
        action.load((clip: THREE.AnimationClip) => {
          addAnimation([clip]);
        });
      });
    }
  }, [gltf, delayActionList]);

  return (
    <group position={[0, 0, 0]} ref={ref}>
      {animationCount > 0 && (
        <PlayerAnimation
          useGame={useGame}
          animationsRef={animationsRef.current}
          scene={gltf.scene}
        />
      )}
    </group>
  );
}

export default function PlayerMesh({
  useGame,
  avatarData,
  usePet,
  meshScale = 1,
  onLoader,
  handFishData = {
    fishId: 0,
  },
  activityData = {
    tick: '',
    pizzaCount: -1,
  },
  itemDetail = {
    itemId: 0,
    curDurability: 0,
    isMe: false,
    showEffect: false,
    serverId: '',
    useGame: createUseGame(),
  },
}: {
  useGame: any;
  avatarData: AvatarData;
  usePet: string;
  onLoader?: (gltf: GLTF) => void;
  meshScale?: number;
  handFishData?: HandFishData;
  activityData?: ActivityData;
  itemDetail?: HandItemDetail;
}) {
  const useGameIndex = useGame((state: UseGameState) => state.getIndex());
  const [avatarGLTF, setAvatarGLTF] = useState<GLTF | null>(null);
  const [handObject, setHandObject] = useState<THREE.Object3D | null>(null);
  const [spineObject, setSpineObject] = useState<THREE.Object3D | null>(null);
  const [pizzaBag, setPizzaBag] = useState<boolean>(false);
  const curAnimation = useGame((state: UseGameState) => state.curAnimation);
  const [faceController, setFaceController] = useState<AvatarFace | null>(null);
  const delayActionListRef = useRef<AvatarAction[]>([]);
  let lastTime = Date.now();

  useEffect(() => {
    if (faceController) {
      switch (curAnimation) {
        case 'Action_05':
        case 'Action_06':
        case 'Action_07':
        case 'Action_05|Action_06|Action_07':
        case 'Action_12':
          faceController.setActionName('speak');
          break;
        case 'weary_idle':
        case 'weary_walk':
        case 'Action_35':
        case 'Action_36':
        case 'Action_37':
        case 'Action_38':
          faceController.setActionName('weary');
          break;
        default:
          faceController.setActionName('idle');
          break;
      }
    }
  }, [faceController, curAnimation]);

  useFrame(() => {
    const now = Date.now();
    const delta = now - lastTime;
    lastTime = now;
    if (faceController) {
      faceController.update(delta / 1000);
    }

    //todo 待优化
    setPizzaBag(activityData.pizzaCount >= 0);
  });

  useEffect(() => {
    setAvatarGLTF(null);
    setHandObject(null);
    delayActionListRef.current = [];
    let cancel = false;
    if (usePet.length > 0) {
      AvatarUtil.getPetGLB(usePet, (petGltf) => {
        if (cancel) return;
        for (let i = 0; i < petGltf.animations.length; i++) {
          switch (petGltf.animations[i].name) {
            case 'Action_00':
              petGltf.animations[i].name = 'idle';
              break;
            case 'Action_01':
              petGltf.animations[i].name = 'run';
              break;
            case 'Action_03':
              petGltf.animations[i].name = 'walk';
              break;
            case 'Action_04':
              petGltf.animations[i].name = 'jump';
              break;
            default:
              break;
          }
        }
        petGltf.scene.scale.set(meshScale, meshScale, meshScale);
        setAvatarGLTF(petGltf as any);
        onLoader && onLoader(petGltf as any);
      });
    } else {
      const tempAvatarData = new AvatarData();
      tempAvatarData.hatId = avatarData.hatId;
      tempAvatarData.shirtColor = avatarData.shirtColor;
      tempAvatarData.shirtTextureId = avatarData.shirtTextureId;
      tempAvatarData.shirtId = avatarData.shirtId;
      tempAvatarData.shoesId = avatarData.shoesId;
      tempAvatarData.pantsId = avatarData.pantsId;
      tempAvatarData.glovesId = avatarData.glovesId;
      if (pizzaBag)
        tempAvatarData.hatId = PizzaPointConfig.getInstance().getPizzaBagUrl(activityData.tick);
      AvatarUtil.getAvatarGLB(tempAvatarData, (gltf) => {
        if (cancel) return;
        // Bip001 R Hand
        //获取右手掌骨骼
        const bone = gltf.scene.getObjectByName('Bip001_R_Hand');
        if (bone) {
          setHandObject(bone);
        }
        // Bip001 Spine1
        //获取胸腔骨骼
        const bone1 = gltf.scene.getObjectByName('Bip001_Spine1');
        if (bone1) {
          setSpineObject(bone1);
        }
        AvatarActionConfig.getInstance().getActionList((list, delayList) => {
          delayActionListRef.current = delayList;
          const totalAnimations: THREE.AnimationClip[] = [];
          let loadNumber = 0;
          if (list.length > 0) {
            for (let i = 0; i < list.length; i++) {
              const action = list[i];
              if (action) {
                action.load((clip: THREE.AnimationClip) => {
                  loadNumber++;
                  totalAnimations.push(clip);
                  if (loadNumber == list.length) {
                    gltf.animations.push(...totalAnimations);
                    gltf.scene.scale.set(meshScale, meshScale, meshScale);
                    setAvatarGLTF(gltf as any);
                    onLoader && onLoader(gltf as any);
                  }
                });
              }
            }
          }
        });
      });
    }
    return () => {
      cancel = true;
    };
  }, [usePet, avatarData, pizzaBag, meshScale]);

  useEffect(() => {
    if (avatarGLTF) {
      const faceMesh = avatarGLTF.scene.getObjectByName('Face') as THREE.SkinnedMesh;
      if (faceMesh) {
        switch (usePet) {
          case '74f1c602036fc449a2323665ac88e922082e25149863f0fb0c97f83dec04b386i0':
          case '639f1da8f43ad3d2c9c7775fd2055f6c4fc20f57277d4afa311afbfc42e20dbfi0':
            setFaceController(AvatarUtil.createPetFace(faceMesh));
            break;
          default:
            if (avatarData.faceId) {
              setFaceController(AvatarUtil.createNpcFace(faceMesh, avatarData.faceId));
            } else {
              setFaceController(AvatarUtil.createAvatarFace(faceMesh));
            }
            break;
        }
      } else {
        // console.error('cant find faceMesh')
      }
    }
  }, [avatarGLTF, usePet]);

  const [fishGlb, setFishGlb] = useState<GLTF | null>(null);
  const [showFish, setShowFish] = useState(false);
  useEffect(() => {
    if (handFishData.fishId == 0) {
      setFishGlb(null);
      return;
    }
    FishConfig.getInstance().getData(handFishData.fishId, (fishData) => {
      LoaderUtil.loadGlb(fishData.glb_url, (glb) => {
        glb.scene.scale.set(fishData.fish_scale, fishData.fish_scale, fishData.fish_scale);
        glb.scene.position.set(0, 0.75 + fishData.fish_yOffset, 0.3);
        glb.scene.quaternion.setFromEuler(new THREE.Euler(0, Math.PI / 2, 0));
        setFishGlb(glb);
      });
    });
  }, [handFishData.fishId]);

  useEffect(() => {
    if (curAnimation === 'Action_19') {
      const timer = setTimeout(() => {
        setShowFish(true);
      }, 1000);

      return () => {
        clearTimeout(timer);
        setShowFish(false);
      };
    }
  }, [curAnimation]);

  useEffect(() => {
    if (avatarGLTF && fishGlb && showFish) {
      avatarGLTF.scene.add(fishGlb.scene);
      const mixer = new THREE.AnimationMixer(fishGlb.scene);
      const clock = new THREE.Clock();
      const action = mixer.clipAction(fishGlb.animations[1]);
      action.play();
      let stopLoop = false;

      function _loop() {
        if (stopLoop) {
          return;
        }
        mixer.update(clock.getDelta());
        requestAnimationFrame(_loop);
      }

      _loop();
      return () => {
        stopLoop = true;
        avatarGLTF.scene.remove(fishGlb.scene);
      };
    }
  }, [showFish, avatarGLTF, fishGlb]);

  return (
    <>
      <AudioSystemComponent _key={'playerMesh_' + useGameIndex} />
      {avatarGLTF && (
        <CreateMesh
          useGame={useGame}
          gltf={avatarGLTF}
          delayActionList={delayActionListRef.current}
        />
      )}
      {avatarGLTF && handObject && (
        <HandItem rightHand={handObject} itemDetail={itemDetail} useGame={useGame} />
      )}
      {spineObject && <PizzaBag spineBone={spineObject} activityData={activityData} />}
    </>
  );
}
