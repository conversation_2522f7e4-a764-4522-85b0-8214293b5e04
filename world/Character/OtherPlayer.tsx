import * as THREE from 'three';
import { Quaternion, Vector3 } from 'three';
import React, { useMemo, useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import AvatarData from '../../AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import PlayerMesh from './PlayerMesh';
import createUseGame, { UseGameState } from '@/src/stores/useGame';
import { HandItemDetail } from '@/world/HandItem/HandItem';
import SpeakUtil, { SpeakUtilData, VTTSubtitle } from '@/world/Util/SpeakUtil';
import TopAnswerUI, { AnswerTask } from '@/world/SceneUI/TopAnswerUI';
import { POPOVER_HEIGHT } from '@/constant';
import AudioSystemComponent from '@/world/Global/GlobalAudioSystem';
import TopNameUI from '@/world/SceneUI/TopNameUI';
import { GLTF } from 'three-stdlib';

export class OtherPlayerData {
  uuid: string;
  id: string;
  yawOffset = 0;
  name = '';
  position: THREE.Vector3;
  quaternion: THREE.Quaternion;
  avatarData: AvatarData;
  timestamp: number;
  itemId: number;
  pizzaCount: number;
  pizzaTick: string;
  useGame: any;
  isChange = false;
  curAnimation: string | undefined;
  lockAnimation: string | null;
  usePet = '';
  meshScale = 1;
  answerTask: AnswerTask | null = null;
  isVisible = true;
  speakUtil: SpeakUtilData;
  fishId = 0;

  constructor(id: string, avatarData: AvatarData, useGame: any) {
    this.uuid = THREE.MathUtils.generateUUID();
    this.id = id;
    this.itemId = 0;
    this.pizzaCount = -1;
    this.avatarData = avatarData;
    this.position = new THREE.Vector3();
    this.quaternion = new THREE.Quaternion();
    this.timestamp = Date.now();
    this.useGame = useGame;
    this.lockAnimation = null;
    this.pizzaTick = '';

    this.speakUtil = new SpeakUtilData();
  }

  loadVTT(vtt_data: VTTSubtitle[], mp3_url: string) {
    this.speakUtil.loadVTT(vtt_data, mp3_url);
  }

  getAudioKey() {
    return 'otherPlayer_' + this.id;
  }

  createAnswer() {
    const task = new AnswerTask(this.getAudioKey());
    this.answerTask = task;
    return task;
  }

  stopAnswer() {
    if (this.answerTask) {
      this.answerTask.stop();
    }
  }

  clearAnswer() {
    if (this.answerTask) {
      this.stopAnswer();
      this.answerTask = null;
    }
  }

  stopSpeak() {
    this.speakUtil.stopSpeak();
  }

  wordSpeak(text: string, startCallback: () => void, endCallback: () => void) {
    this.speakUtil.wordSpeak(text, startCallback, endCallback);
  }

  faceToPosition(position: THREE.Vector3) {
    position.y = this.position.y;
    const tempObject = new THREE.Object3D();
    tempObject.position.copy(this.position);
    tempObject.lookAt(position);
    this.quaternion.copy(tempObject.quaternion);
    this.isChange = true;
  }

  play(action: string) {
    this.curAnimation = action;
    this.isChange = true;
  }

  lock(action: string) {
    this.curAnimation = action;
    this.lockAnimation = action;
    this.isChange = true;
  }

  getReact(updateHeight: () => number, children?: React.ReactNode) {
    return <OtherPlayer data={this}>{children}</OtherPlayer>;
  }
}

function OtherPlayer({ data, children }: { data: OtherPlayerData; children: React.ReactNode }) {
  const ref = useRef<THREE.Group>(null);
  const offsetRef = useRef<THREE.Group>(null);
  const targetPositionRef = useRef(new Vector3()); // 使用 useRef 代替 state
  const targetQuaternionRef = useRef(new Quaternion());
  const rotationFactor = 0.1; // 距离系数，尝试调整这个值
  const smoothFactor = 0.25; // 插值系数，尝试调整这个值
  const useGame = data.useGame;
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const setLockAnimation = useGame((state: UseGameState) => state.setLockAnimation);
  const [answerTask, setAnswerTask] = useState<AnswerTask | null>(null);
  const [height, setHeight] = useState<number>(0);
  const [name, setName] = useState<string>('');
  const [avatarData, setAvatarData] = useState<AvatarData | null>(null);
  const [usePetInscriptionId, setUsePetInscriptionId] = useState<string>('');
  const fishActionData = useMemo(() => {
    return {
      fishId: 0,
      targetPos: new THREE.Vector3(),
      facePos: new THREE.Vector3(),
    };
  }, []);
  const itemDetail = useMemo(() => {
    return {
      isMe: false,
      itemId: 0,
      curDurability: 0,
      showEffect: false,
      serverId: '',
      useGame: createUseGame(),
    } as HandItemDetail;
  }, []);
  const activityData = useMemo(() => {
    return {
      tick: data.pizzaTick,
      pizzaCount: data.pizzaCount,
    };
  }, []);

  useFrame(() => {
    if (data.answerTask !== answerTask) {
      setAnswerTask(null);
      setTimeout(() => {
        setAnswerTask(data.answerTask);
      }, 1);
    }
    if (data.isChange) {
      fishActionData.fishId = data.fishId;
      targetPositionRef.current.set(data.position.x, data.position.y, data.position.z);
      targetQuaternionRef.current.set(
        data.quaternion.x,
        data.quaternion.y,
        data.quaternion.z,
        data.quaternion.w
      );
      setLockAnimation(data.lockAnimation);
      setCurAnimation(data.curAnimation);
      itemDetail.itemId = data.itemId;
      itemDetail.serverId = data.itemId > 0 ? '1' : '';
      itemDetail.curDurability = data.itemId > 0 ? 1 : 0;
      data.isChange = false;

      setName(data.name);
      setAnswerTask(answerTask);

      if (offsetRef.current) {
        offsetRef.current.quaternion.copy(
          new Quaternion().setFromEuler(new THREE.Euler(0, (data.yawOffset * Math.PI) / 180, 0))
        );
      }
      if (avatarData !== data.avatarData) {
        setAvatarData(data.avatarData);
      }
      if (usePetInscriptionId !== data.usePet) {
        setUsePetInscriptionId(data.usePet);
      }
    }

    if (offsetRef.current) {
      offsetRef.current.visible = data.isVisible;
    }

    if (ref.current) {
      const currentPos = ref.current.position;
      const currentQuat = ref.current.quaternion;
      if (currentPos.distanceTo(targetPositionRef.current) > 3) {
        currentPos.copy(targetPositionRef.current);
        currentQuat.copy(targetQuaternionRef.current);
      } else {
        // 插值平滑位置
        currentPos.lerp(targetPositionRef.current, smoothFactor);

        // 使用 copy 方法复制目标四元数
        currentQuat.slerp(targetQuaternionRef.current, rotationFactor);
      }
    }

    activityData.pizzaCount = data.pizzaCount;
    activityData.tick = data.pizzaTick;
  });

  return (
    <group ref={ref}>
      <AudioSystemComponent _key={data.getAudioKey()} />
      {answerTask && height > 0 && <TopAnswerUI height={height} task={answerTask} />}
      <SpeakUtil spaceUtilData={data.speakUtil} height={height} />
      {name.length > 0 && <TopNameUI height={height} name={name} />}
      <group ref={offsetRef}>
        {avatarData && (
          <PlayerMesh
            useGame={useGame}
            avatarData={avatarData}
            usePet={usePetInscriptionId}
            itemDetail={itemDetail}
            meshScale={data.meshScale}
            handFishData={fishActionData}
            activityData={activityData}
            onLoader={(gltf: GLTF) => {
              const box = new THREE.Box3().setFromObject(gltf.scene);
              // box 的 min 和 max 属性代表边界框的最小和最大坐标
              setHeight(Math.max(box.max.y - box.min.y + POPOVER_HEIGHT, 0));
            }}
          />
        )}
      </group>
      {children}
    </group>
  );
}

export default OtherPlayer;
