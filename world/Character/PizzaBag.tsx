import { useEffect, useMemo, useRef, useState } from 'react';
import { ActivityData } from '@/world/Character/PlayerMesh';
import * as THREE from 'three';
import { useFrame, useThree } from '@react-three/fiber';
import { GLTF } from 'three-stdlib';
import { LoaderUtil } from '@/world/Util/LoaderUtil';
import { PizzaPointConfig } from '@/world/Config/PizzaPointConfig';

export default function PizzaBag({
  activityData,
  spineBone,
}: {
  activityData: ActivityData;
  spineBone: THREE.Object3D;
}) {
  const { scene } = useThree();
  const [pizzaCount, setPizzaCount] = useState<number>(0);
  const [gltf, setGLTF] = useState<GLTF | null>(null);
  const [bagRoot, setBagRoot] = useState<THREE.Object3D | null>();
  const currentCount = useRef<number>(0);
  const spineBonePos = useMemo(() => new THREE.Vector3(), []);
  const spineBoneRot = useMemo(() => new THREE.Quaternion(), []);

  const addPizza = (targetCount: number) => {
    if (gltf && bagRoot) {
      if (currentCount.current < targetCount) {
        const pizzaMesh = gltf.scene.clone();
        pizzaMesh.position.y = currentCount.current * 0.05 - 0.13;
        pizzaMesh.position.x = -0.5;
        bagRoot.add(pizzaMesh);
        currentCount.current++;
        addPizza(targetCount);
      }
    }
  };

  useEffect(() => {
    LoaderUtil.loadGlb(PizzaPointConfig.getInstance().getPizzaBoxUrl(activityData.tick), (glb) => {
      glb.scene.traverse((child) => {
        if (child.name.includes('Effect')) {
          child.visible = false;
        }
      });
      setGLTF(glb);
    });

    const root = new THREE.Object3D();
    scene.add(root);
    setBagRoot(root);
    return () => {
      scene.remove(root);
      setBagRoot(null);
    };
  }, [activityData.tick, scene]);

  useEffect(() => {
    if (gltf && pizzaCount > 0) {
      addPizza(pizzaCount);
    } else {
      if (bagRoot) {
        bagRoot.clear();
        currentCount.current = 0;
      }
    }
  }, [pizzaCount, gltf]);

  useFrame(() => {
    setPizzaCount(activityData.pizzaCount);
    if (bagRoot) {
      spineBone.getWorldPosition(spineBonePos);
      spineBone.getWorldQuaternion(spineBoneRot);
      bagRoot.position.copy(spineBonePos);
      bagRoot.quaternion.copy(spineBoneRot);
    }
  });

  return null;
}
