import * as THREE from 'three';
import React, { useEffect, useState } from 'react';
import NFT from './NFT';
// import {toNumber} from "lodash";
import { toNumber } from 'es-toolkit/compat';
import AvatarDataNFT from '../../Data/AvatarDataNFT';
import { useThree } from '@react-three/fiber';
import GlobalSpaceEvent, { GlobalDataKey } from '../../Global/GlobalSpaceEvent';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import gsap from 'gsap';

interface IProps {
  scene: THREE.Group;
}

function SetCamera() {
  const { camera, size } = useThree();

  useEffect(() => {
    if (!camera) {
      return;
    }

    GlobalSpaceEvent.ListenKeyDataChange<OrbitControls>(
      GlobalDataKey.EditorControls,
      (controls) => {
        if (controls) {
          controls.enableZoom = true;
          controls.rotateSpeed = 1;
          gsap.to(controls.target, {
            x: 0,
            y: 1,
            z: 0,
            duration: 0.5, // 1秒动画时间
            ease: 'power1.out', // 动画曲线
            onUpdate: () => {
              controls.update();
            },
          });
          gsap.to(camera.position, {
            x: 2,
            y: 1.5,
            z: 2,
            duration: 0.5, // 1秒动画时间
            ease: 'power1.out', // 动画曲线
            onUpdate: () => {
              controls.update();
            },
          });
        }
      },
      true
    );
  }, [camera]);
  return null;
}

export default function NFTGroup({ scene }: IProps) {
  const [lookingIndex, setLookingIndex] = useState<number>(0);
  const [nftList, setNftList] = useState<THREE.Mesh[]>([]);
  const [nftDataMap, setNftDataMap] = useState<Map<number, AvatarDataNFT>>(new Map());

  useEffect(() => {
    const lookingIndexKey = GlobalSpaceEvent.ListenKeyDataChange<number>(
      GlobalDataKey.LookingNftIndex,
      (value) => {
        setLookingIndex(value);
      }
    );

    const nftDataKey = GlobalSpaceEvent.ListenKeyDataChange<AvatarDataNFT[]>(
      GlobalDataKey.NftData,
      (data) => {
        const dataMap = new Map<number, AvatarDataNFT>();

        for (let index = 0; index < data.length; index++) {
          const element = data[index];
          dataMap.set(element.position, element);
        }
        setNftDataMap(dataMap);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.LookingNftIndex, lookingIndexKey);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.NftData, nftDataKey);
    };
  }, []);

  useEffect(() => {
    const list: THREE.Mesh[] = [];
    scene.traverse((child) => {
      const mesh = child as THREE.Mesh;
      if (mesh && mesh.name.includes('NFT')) {
        list.push(mesh);
      }
    });
    list.sort((a, b) => {
      //取字符串后三位
      let name1 = toNumber(a.name.slice(-3));
      let name2 = toNumber(b.name.slice(-3));
      return name1 - name2;
    });
    setNftList(list);
  }, [scene]);

  return (
    <group name="nftGroup">
      {lookingIndex == 0 && <SetCamera />}
      {nftList.map((item, index) => (
        <NFT
          mesh={item}
          position={index + 1}
          nftData={nftDataMap.get(index + 1)}
          lookingIndex={lookingIndex}
          key={index}
        />
      ))}
    </group>
  );
}
