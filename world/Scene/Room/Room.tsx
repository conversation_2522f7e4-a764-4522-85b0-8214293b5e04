import React, { useEffect, useState } from 'react';
import { GLTF } from 'three-stdlib';
import NFTGroup from './NFTGroup';
import GlobalSpaceEvent, { GlobalDataKey } from '../../Global/GlobalSpaceEvent';
import { SCENE_TYPE } from '@/constant/type';
import FTGroup from './FTGroup';
import SceneLoader from '../SceneLoader';
import { LoaderUtil } from '@/world/Util/LoaderUtil';

export default function Model() {
  const [room, setRoom] = React.useState<GLTF | null>(null);
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.None);
  useEffect(() => {
    const sceneTypeKey = GlobalSpaceEvent.ListenKeyDataChange(
      GlobalDataKey.SceneType,
      (_sceneType: SCENE_TYPE) => {
        setSceneType(_sceneType);
      }
    );

    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, sceneTypeKey);
    };
  }, []);

  useEffect(() => {
    LoaderUtil.loadGlb('./space/glb/Room_00.glb', (gltf) => {
      setRoom(gltf as any);
    });
  }, []);

  const loaded = (haveWall: boolean) => {
    if (haveWall) {
      setTimeout(() => {
        GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false);
      }, 1000);
      //提前静默下载社区glb
      LoaderUtil.loadGlb('./space/glb/Community_00.glb', () => undefined);
    }
  };

  return (
    <group dispose={null} userData={{ camCollisionListener: true }}>
      {room && sceneType === SCENE_TYPE.Room && <NFTGroup scene={room.scene} />}
      {sceneType === SCENE_TYPE.Room && <FTGroup />}
      {room && (
        <SceneLoader
          loaded={loaded}
          gltf={room}
          noShadowList={[
            'house_wall_01',
            'house_wall_04',
            '_house_top',
            'Photo_02',
            'indoor_plant001',
            'house_TVgame',
            'house_TVgame_A',
            'game',
          ]}
          hideList={[
            'NFT_001',
            'NFT_002',
            'NFT_003',
            'NFT_004',
            'NFT_005',
            'NFT_006',
            'NFT_007',
            'NFT_008',
            'NFT_009',
            'NFT_010',
            'NFT_011',
            'NFT_012',
            'NFT_013',
            'NFT_014',
            'NFT_015',
            'NFT_016',
            'NFT_017',
            'NFT_018',
            'NFT_019',
            'NFT_020',
            'stop',
          ]}
        />
      )}
    </group>
  );
}
