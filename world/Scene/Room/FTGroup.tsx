import React, { useEffect, useState } from 'react';
import GlobalSpaceEvent, { GlobalDataKey } from '../../Global/GlobalSpaceEvent';
import AvatarDataFT from '../../Data/AvatarDataFT';
import FT from './FT';

export default function FTGroup() {
  const [isReady, setIsReady] = useState<boolean>(false);
  const [ftDataList, setFtDataList] = useState<AvatarDataFT[]>([]);

  useEffect(() => {
    let timerKey: NodeJS.Timeout;
    const ftKey = GlobalSpaceEvent.ListenKeyDataChange<AvatarDataFT[]>(
      GlobalDataKey.FtData,
      (data) => {
        setFtDataList(data);
        setIsReady(false);

        clearTimeout(timerKey);
        timerKey = setTimeout(() => {
          setIsReady(true);
        }, 500);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.FtData, ftKey);
      clearTimeout(timerKey);
    };
  }, []);

  return (
    <group name="ftGroup">
      {isReady &&
        ftDataList.map((item, index) => (
          <FT key={'FT_' + index} position={index + 1} ftData={item} />
        ))}
    </group>
  );
}
