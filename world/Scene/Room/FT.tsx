import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import AvatarDataFT from '../../Data/AvatarDataFT';
import { TOKEN_TYPE_ENUM } from '@/constant/type';
import { GLTF } from 'three-stdlib';
import { RigidBody } from '@react-three/rapier';
import GlobalSpaceEvent, { GlobalDataKey } from '../../Global/GlobalSpaceEvent';
import GlobalSpace from '../../Global/GlobalSpace';
import { useThree } from '@react-three/fiber';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

const getScreenPosition = (
  point: THREE.Vector3,
  camera: THREE.Camera,
  size: {
    width: number;
    height: number;
  }
) => {
  // 将三维坐标转换为屏幕坐标
  const projected = point.clone().project(camera);
  const x = (0.5 + projected.x / 2) * size.width; // 屏幕的 X 坐标
  const y = (0.5 - projected.y / 2) * size.height; // 屏幕的 Y 坐标
  return new THREE.Vector2(x, y);
};

interface IProps2 {
  mesh: THREE.Mesh;
  data: AvatarDataFT;
  position: THREE.Vector3;
}

function CreateMesh({ mesh, data, position }: IProps2) {
  const meshRef = useRef<THREE.Mesh>(null);
  const { camera, size } = useThree();

  useEffect(() => {
    if (meshRef.current) {
      const oldMesh = meshRef.current;
      const material = (meshRef.current.material as THREE.MeshStandardMaterial).clone();
      oldMesh.material = material;
      GlobalSpace.addHoverObject(oldMesh);
      oldMesh.userData.hoverCallback = () => {
        const obj = oldMesh;
        if (data && obj) {
          const mouse = getScreenPosition(
            obj.getWorldPosition(new THREE.Vector3(0, 0, 0)),
            camera,
            size
          );
          material.color = new THREE.Color(0xff8400);
          GlobalSpaceEvent.SetDataValue(GlobalDataKey.FtHoverObj, {
            ft: data,
            mouse: mouse,
          });
        }
      };
      oldMesh.userData.unHoverCallback = () => {
        if (data) {
          material.color = new THREE.Color(0xffffff);
          GlobalSpaceEvent.SetDataValue(GlobalDataKey.FtHoverObj, {
            ft: null,
            mouse: new THREE.Vector2(0, 0),
          });
        }
      };
      return () => {
        GlobalSpace.removeHoverObject(oldMesh);
      };
    }
  }, []);

  return (
    <>
      <RigidBody type={'dynamic'} colliders="cuboid" restitution={0.1} friction={0.4} ccd>
        <mesh
          ref={meshRef}
          name={mesh.name}
          position={position}
          scale={mesh.scale}
          rotation={new THREE.Euler((75 * Math.PI) / 180, 0, 0)}
          material={mesh.material}
          geometry={mesh.geometry}
          castShadow
          receiveShadow
        ></mesh>
      </RigidBody>
    </>
  );
}

interface IProps {
  ftData: AvatarDataFT;
  position: number;
}

export default function FT({ ftData, position }: IProps) {
  const [gltfBrc, setGltfBrc20] = useState<GLTF | null>(null);
  const [gltfCat, setGltfCat20] = useState<GLTF | null>(null);
  const [gltfRunes, setGltfRunes20] = useState<GLTF | null>(null);

  const [isReady, setIsReady] = useState<boolean>(false);
  const [mesh, setMesh] = useState<THREE.Mesh | null>(null);
  const [coinPos, setCoinPos] = useState<THREE.Vector3>(new THREE.Vector3(0, 0, 0));

  useEffect(() => {
    const loader = new GLTFLoader();
    loader.load(getCdnLink('./space/glb/Brc20.glb'), (gltf) => {
      setGltfBrc20(gltf as any);
    });
    loader.load(getCdnLink('./space/glb/Cat20.glb'), (gltf) => {
      setGltfCat20(gltf as any);
    });
    loader.load(getCdnLink('./space/glb/Runes.glb'), (gltf) => {
      setGltfRunes20(gltf as any);
    });
  }, []);

  useEffect(() => {
    setIsReady(false);
    if (!gltfBrc) return;
    if (!gltfCat) return;
    if (!gltfRunes) return;

    let glb: GLTF | null = null;
    let randomPos = new THREE.Vector3(0, 0, 0);
    switch (ftData.type) {
      case TOKEN_TYPE_ENUM.Brc20:
        randomPos.set(-2.5 + (Math.random() - 0.5) / 4, 2, 0.5 + (Math.random() - 0.5) / 4);
        glb = gltfBrc;
        break;
      case TOKEN_TYPE_ENUM.Cat20:
        randomPos.set(0.5 + (Math.random() - 0.5) / 4, 2, -2 + (Math.random() - 0.5) / 4);
        glb = gltfCat;
        break;
      case TOKEN_TYPE_ENUM.Runes:
        randomPos.set(-2 + (Math.random() - 0.5) / 4, 2, -1 + (Math.random() - 0.5) / 4);
        glb = gltfRunes;
        break;
      default:
    }
    setCoinPos(randomPos);
    if (glb) {
      let mesh: THREE.Mesh | null = null;
      glb.scene.traverse((obj) => {
        if (obj.type === 'Mesh') {
          mesh = obj as THREE.Mesh;
        }
      });
      if (mesh) {
        setMesh(mesh);
      }
    }

    const timerKey = setTimeout(() => {
      setIsReady(true);
    }, 50 * position);
    return () => {
      clearTimeout(timerKey);
    };
  }, [ftData, position, gltfBrc, gltfCat, gltfRunes]);

  return (
    <group>{isReady && mesh && <CreateMesh mesh={mesh} data={ftData} position={coinPos} />}</group>
  );
}
