/*
Enhanced CanvasUI with HTML5 support using CanvasTexture and Sprite
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { useControls } from 'leva';
import SceneRank from '@/world/Scene/Community/SccenRank';

function TransformControl({
  debug = false,
  pos,
  rot,
  scale,
  children,
}: {
  pos: number[];
  rot: number[];
  scale: number;
  debug?: boolean;
  children?: React.ReactNode;
}) {
  const groupRef = useRef<THREE.Group>(null);
  let rankTransformDebug = null;
  if (debug) {
    // Character Controls
    rankTransformDebug = useControls('Rank Transform Controls ', {
      posX: {
        value: pos[0],
        min: -30,
        max: 30,
        step: 0.01,
      },
      posY: {
        value: pos[1],
        min: -30,
        max: 30,
        step: 0.01,
      },
      posZ: {
        value: pos[2],
        min: -30,
        max: 30,
        step: 0.01,
      },
      rotX: {
        value: rot[0],
        min: -180,
        max: 180,
        step: 0.1,
      },
      rotY: {
        value: rot[1],
        min: -180,
        max: 180,
        step: 0.1,
      },
      rotZ: {
        value: rot[2],
        min: -180,
        max: 180,
        step: 0.1,
      },
      scale: {
        value: scale,
        min: 0,
        max: 2,
        step: 0.01,
      },
    });
    // Apply debug values
    pos = [rankTransformDebug.posX, rankTransformDebug.posY, rankTransformDebug.posZ];
    rot = [rankTransformDebug.rotX, rankTransformDebug.rotY, rankTransformDebug.rotZ];
    scale = rankTransformDebug.scale;
  }

  useEffect(() => {
    if (groupRef.current) {
      groupRef.current.position.copy(new THREE.Vector3(...pos));
      groupRef.current.scale.set(scale, scale, scale);
      groupRef.current.quaternion.setFromEuler(
        new THREE.Euler(
          (rot[0] * Math.PI) / 180,
          (rot[1] * Math.PI) / 180,
          (rot[2] * Math.PI) / 180
        )
      );
    }
  }, [groupRef.current, pos, rot, scale]);

  return <group ref={groupRef}>{children}</group>;
}

export default function CanvasUI() {
  return (
    <>
      <SceneRank
        rankData={{
          type: 'community',
          posX: -5.28,
          posY: 7.47,
          posZ: 3.83,
          rotX: 23.4,
          rotY: -51.7,
          rotZ: 18.7,
          scale: 0.38,
        }}
      />
      <SceneRank
        rankData={{
          type: 'fish',
          posX: 3.84,
          posY: 7.47,
          posZ: 5.3,
          rotX: 17.7,
          rotY: 34.5,
          rotZ: -10.3,
          scale: 0.38,
        }}
      />
      <SceneRank
        rankData={{
          type: 'tree',
          posX: 5.26,
          posY: 7.47,
          posZ: -3.81,
          rotX: -26,
          rotY: 129,
          rotZ: 21,
          scale: 0.38,
        }}
      />
      <SceneRank
        rankData={{
          type: 'stone',
          posX: -3.81,
          posY: 7.48,
          posZ: -5.24,
          rotX: -18,
          rotY: -145.6,
          rotZ: -10.5,
          scale: 0.38,
        }}
      />
    </>
  );
}
