/*
Enhanced CanvasUI with HTML5 support using CanvasTexture and Sprite
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { GetMyPlayer } from '@/world/Character/MyPlayer';
import { PizzaPointConfig, PizzaPointData } from '@/world/Config/PizzaPointConfig';
import { LoaderUtil } from '@/world/Util/LoaderUtil';
import GlobalSpace, { GAME_OP_TYPE } from '@/world/Global/GlobalSpace';
import { getPizzaActivity } from '@/world/Activity/PizzaActivity';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { MapConfig } from '@/world/Config/MapConfig';

export default function ScenePizza({ pointData }: { pointData: PizzaPointData }) {
  const pizzaActivity = getPizzaActivity();
  const myPlayer = GetMyPlayer();
  const groupRef = useRef<THREE.Group>(null);
  const [nearItem, setNearItem] = useState<boolean>(false);
  const [isPickedUp, setIsPickedUp] = useState<boolean>(false);

  useEffect(() => {
    if (!isPickedUp) {
      LoaderUtil.loadGlb(PizzaPointConfig.getInstance().getPizzaBoxUrl(), (gltf) => {
        if (groupRef.current) {
          const curMapData = MapConfig.getInstance().getCurMapData();
          if (curMapData) {
            groupRef.current.position.copy(
              new THREE.Vector3(
                pointData.position[0] - curMapData.offset[0],
                pointData.position[1] - curMapData.offset[1],
                pointData.position[2] - curMapData.offset[2]
              )
            );
            groupRef.current.quaternion.setFromEuler(
              new THREE.Euler(
                (pointData.rotation[0] * Math.PI) / 180,
                (pointData.rotation[1] * Math.PI) / 180,
                (pointData.rotation[2] * Math.PI) / 180
              )
            );
            gltf.scene.scale.set(pointData.glbScale, pointData.glbScale, pointData.glbScale);

            groupRef.current.add(gltf.scene);
          }
        }
      });
    }

    return () => {
      if (groupRef.current) {
        groupRef.current.clear();
      }
    };
  }, [isPickedUp]);

  useEffect(() => {
    if (!isPickedUp) {
      const opList: string[] = [];
      if (nearItem) {
        opList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              setIsPickedUp(true);
              pizzaActivity.pickUpPizza(pointData.id);
            },
            0,
            'Pick Up',
            getCdnLink('./icon/option/pickUp.svg')
          )
        );
      }
      return () => {
        opList.forEach((op) => {
          GlobalSpace.removeGameOp(op);
        });
      };
    }
  }, [isPickedUp, nearItem]);

  useEffect(() => {
    if (pointData) {
      const pointKey = 'pizza_' + pointData.id;
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(pointData.position[0], pointData.position[1], pointData.position[2]),
        (distance) => {
          setNearItem(distance < pointData.distance);
        }
      );
      setIsPickedUp(false);
    }
  }, [pointData]);

  return <group ref={groupRef}></group>;
}
