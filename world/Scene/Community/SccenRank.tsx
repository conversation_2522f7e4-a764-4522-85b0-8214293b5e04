/*
Enhanced CanvasUI with HTML5 support using CanvasTexture and Sprite
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import domtoimage from 'dom-to-image-more';

async function captureElement(element: HTMLDivElement): Promise<string> {
  try {
    return await domtoimage.toPng(element, {
      quality: 0.8,
      useCORS: true,
    });
  } catch (error) {
    console.error('Capture failed:', error instanceof Error ? error.message : error);
  }
  return '';
}

export type SceneRankData = {
  type: string;
  posX: number;
  posY: number;
  posZ: number;
  rotX: number;
  rotY: number;
  rotZ: number;
  scale: number;
};

export default function SceneRank({ rankData }: { rankData: SceneRankData }) {
  const myPlayer = GetMyPlayer();
  const rootRef = useRef<THREE.Group>(null);
  const [rankPlane, setRankPlane] = React.useState<THREE.Mesh | null>(null);
  const [uiTexture, setUiTexture] = React.useState<THREE.Texture | null>(null);

  useEffect(() => {
    if (!rootRef.current) return;

    const planeGeometry = new THREE.PlaneGeometry(16, 9); // 平面几何体
    const planeMaterial = new THREE.MeshBasicMaterial({
      map: uiTexture,
      // transparent: true,
      side: THREE.DoubleSide, // 双面可见
    });
    const uiPlane = new THREE.Mesh(planeGeometry, planeMaterial);
    uiPlane.position.set(rankData.posX, rankData.posY, rankData.posZ); // 位置
    uiPlane.quaternion.setFromEuler(
      new THREE.Euler(
        (rankData.rotX * Math.PI) / 180,
        (rankData.rotY * Math.PI) / 180,
        (rankData.rotZ * Math.PI) / 180
      )
    ); // 位置
    uiPlane.scale.set(rankData.scale, rankData.scale, rankData.scale);
    rootRef.current.add(uiPlane);
    setRankPlane(uiPlane);
  }, [rootRef.current]);

  useEffect(() => {
    if (uiTexture && rankPlane) {
      rankPlane.material = new THREE.MeshBasicMaterial({
        map: uiTexture,
        // transparent: true,
        side: THREE.DoubleSide, // 双面可见
      });
      //下载图片
      //@ts-ignore
      // uiTexture.image.toBlob((blob) => {
      //   if (blob) {
      //     const url = URL.createObjectURL(blob);
      //     const a = document.createElement('a');
      //     a.href = url;
      //     a.download = rankData.type + '_ui.png';
      //     a.click();
      //     URL.revokeObjectURL(url);
      //   }
      // })
    } else if (rankPlane) {
      rankPlane.material = new THREE.MeshBasicMaterial({
        map: uiTexture,
        color: 0x000000,
        // transparent: true,
        side: THREE.DoubleSide, // 双面可见
      });
    }
  }, [uiTexture, rankPlane]);

  useEffect(() => {
    const update = (element: HTMLDivElement) => {
      try {
        captureElement(element)
          .then((imgUrl) => {
            if (imgUrl.length > 0) {
              const loader = new THREE.TextureLoader();
              loader.load(imgUrl, (texture) => {
                texture.colorSpace = THREE.SRGBColorSpace;
                setUiTexture(texture);
              });
            }
          })
          .catch((err) => {
            console.error('dom-to-image-more error:', err);
          });
      } catch (err) {
        console.error('dom-to-image-more error:', err);
      }
    };
    switch (rankData.type) {
      case 'community':
        myPlayer.setAppApi(AppGameApiKey.updateCommunityRank, update);
        return () => {
          myPlayer.setAppApi(AppGameApiKey.updateCommunityRank, () => {});
        };
      case 'fish':
        myPlayer.setAppApi(AppGameApiKey.updateFishRank, update);
        return () => {
          myPlayer.setAppApi(AppGameApiKey.updateFishRank, () => {});
        };
      case 'tree':
        myPlayer.setAppApi(AppGameApiKey.updateTreeRank, update);
        return () => {
          myPlayer.setAppApi(AppGameApiKey.updateTreeRank, () => {});
        };
      case 'stone':
        myPlayer.setAppApi(AppGameApiKey.updateStoneRank, update);
        return () => {
          myPlayer.setAppApi(AppGameApiKey.updateStoneRank, () => {});
        };
    }
  }, []);
  return <group ref={rootRef}></group>;
}
