/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { GLTF } from 'three-stdlib';

export default function Island_02({ gltf }: { gltf: GLTF }) {
  const group = useRef<THREE.Group>();

  useEffect(() => {
    if (group.current) {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          mesh.castShadow = true;
          mesh.receiveShadow = true;
        }
      });
      group.current.add(gltf.scene);
    }
  }, []);

  return <group ref={group} dispose={null} userData={{ camCollisionListener: true }}></group>;
}
