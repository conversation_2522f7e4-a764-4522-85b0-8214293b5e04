/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef } from 'react';
import { GLTF } from 'three-stdlib';
import Island_01 from './Island_01';
import { RigidBody } from '@react-three/rapier';
import GlobalSpaceEvent, { GlobalDataKey } from '../../Global/GlobalSpaceEvent';
import GlobalSpace, { GAME_OP_TYPE } from '../../Global/GlobalSpace';
import { useFrame } from '@react-three/fiber';
import SceneLoader from '../SceneLoader';
import { LoaderUtil } from '@/world/Util/LoaderUtil';

export default function Island({ onClaimPotato }: { onClaimPotato?: () => void }) {
  const group = useRef<THREE.Group>(null);
  const cloudGroupRef = useRef<THREE.Group>(null);

  const [showIslandPotato, setShowIslandPotato] = React.useState<boolean>(false);
  const [island_00, setIsland_00] = React.useState<GLTF | null>(null);
  const [petMesh, setPetMesh] = React.useState<THREE.Mesh | null>(null);
  const [cloudMesh, setCloudMesh] = React.useState<THREE.Mesh | null>(null);

  const [island_01, setIsland_01] = React.useState<GLTF | null>(null);

  useEffect(() => {
    GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.ShowIslandPotato,
      (showIslandPotato) => {
        setShowIslandPotato(showIslandPotato);
      }
    );
    LoaderUtil.loadGlb('./space/glb/Island_00.glb', (gltf) => {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          if (mesh.name === 'pet') {
            setPetMesh(mesh);
          }
          if (mesh.name === 'cloud') {
            setCloudMesh(mesh);
          }
        }
      });
      setIsland_00(gltf as any);
    });
  }, []);

  useEffect(() => {
    if (island_00) {
      LoaderUtil.loadGlb('./space/glb/Island_01.glb', (gltf) => {
        setIsland_01(gltf as any);
      });
    }
  }, [island_00]);

  useEffect(() => {
    const potatoPos = new THREE.Vector3(1.5, 0, 3);
    let callbackKey = '';
    const cancelKey = GlobalSpace.whatCharacterPosition((position) => {
      if (!showIslandPotato) {
        GlobalSpace.cancelCharacterPositionCallback(cancelKey);
        return;
      }

      if (position.distanceTo(potatoPos) < 2) {
        if (callbackKey.length == 0) {
          callbackKey = GlobalSpace.addGameOp(GAME_OP_TYPE.PotatoOp, () => {
            if (onClaimPotato) {
              onClaimPotato();
              GlobalSpace.removeGameOp(callbackKey);
              callbackKey = '';
            }
          });
        }
      } else {
        if (callbackKey.length > 0) {
          GlobalSpace.removeGameOp(callbackKey);
          callbackKey = '';
        }
      }
    });
    return () => {
      if (callbackKey.length > 0) {
        GlobalSpace.removeGameOp(callbackKey);
        callbackKey = '';
      }
      GlobalSpace.cancelCharacterPositionCallback(cancelKey);
    };
  }, [showIslandPotato]);

  useFrame(() => {
    if (cloudGroupRef.current) {
      //绕Y轴旋转
      cloudGroupRef.current.rotation.y += 0.0002;
    }
  });

  const loaded = (haveWall: boolean) => {
    if (haveWall) {
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.SceneLoading, false);
    }
  };

  return (
    <group ref={group} dispose={null} userData={{ camCollisionListener: true }}>
      {island_01 && <Island_01 gltf={island_01} />}
      <group name="Scene" userData={{ camCollisionListener: true }}>
        {island_00 && (
          <SceneLoader
            loaded={loaded}
            gltf={island_00}
            noShadowList={['sky', 'door', 'door001']}
            hideList={['cloud', 'pet', 'stop']}
          />
        )}
        {showIslandPotato && petMesh && (
          <RigidBody type="fixed" colliders="trimesh" ccd>
            <mesh
              name="pet"
              castShadow
              receiveShadow
              geometry={petMesh.geometry}
              material={petMesh.material}
              position={[petMesh.position.x, petMesh.position.y, petMesh.position.z]}
              scale={petMesh.scale}
            />
          </RigidBody>
        )}
        {cloudMesh && (
          <group
            ref={cloudGroupRef}
            position={[cloudMesh.position.x, cloudMesh.position.y, cloudMesh.position.z]}
          >
            <mesh
              name="cloud"
              // castShadow
              // receiveShadow
              geometry={cloudMesh.geometry}
              material={cloudMesh.material}
              rotation={[Math.PI / 2, 0, 0]}
              scale={[0.004, 0.004, 0.007]}
            />
          </group>
        )}
      </group>
    </group>
  );
}
