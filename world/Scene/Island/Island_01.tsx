/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useState } from 'react';
import { GLTF } from 'three-stdlib';
import Island_Water from './Island_Water';
import SceneLoader from '../SceneLoader';

export default function Island_01({ gltf }: { gltf: GLTF }) {
  const [waterMesh, setWaterMesh] = useState<THREE.Mesh | null>(null);
  useEffect(() => {
    gltf.scene.traverse((object) => {
      const mesh = object as THREE.Mesh;
      if (mesh) {
        if (mesh.name === 'my_sea_water') {
          setWaterMesh(mesh);
        }
      }
    });
  }, []);

  return (
    <>
      <SceneLoader
        loaded={(haveWall) => {}}
        gltf={gltf}
        noShadowList={[]}
        hideList={['my_sea_water']}
      />
      {waterMesh && <Island_Water mesh={waterMesh} />}
    </>
  );
}
