import { GLTF } from 'three-stdlib';

import * as THREE from 'three';
import { Suspense, useEffect, useRef, useState } from 'react';
import { useAnimations } from '@react-three/drei';
import { RigidBody } from '@react-three/rapier';

function IdleAnimation(props: { animations: THREE.AnimationClip[]; children: React.ReactNode }) {
  const group = useRef(null);

  /**
   * Character animations setup
   */
  const { actions } = useAnimations(props.animations, group);

  useEffect(() => {
    for (const argumentsKey in actions) {
      const action = actions[argumentsKey];
      if (action) {
        action.reset().fadeIn(0).setLoop(THREE.LoopRepeat, Infinity).play();
      }
    }
  }, [actions]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export default function SceneLoader({
  loaded,
  gltf,
  hideList,
  noShadowList,
}: {
  loaded: (haveWall: boolean) => void;
  gltf: GLTF;
  hideList: string[];
  noShadowList: string[];
}) {
  const group = useRef<THREE.Group>(null);
  const [airWall, setAirWall] = useState<THREE.Mesh | null>(null);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  useEffect(() => {
    if (group.current) {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          if (mesh.name === 'stop') {
            const material = mesh.material as THREE.MeshStandardMaterial;
            material.map = null;
            setAirWall(mesh);
            mesh.userData.camExcludeCollision = true;
          }
          if (noShadowList.includes(mesh.name)) {
            mesh.castShadow = false;
            mesh.receiveShadow = false;
          } else {
            mesh.castShadow = true;
            mesh.receiveShadow = true;
          }
        }
      });
      group.current.add(gltf.scene);
      setAnimations(gltf.animations);
      loaded(false);
    }
  }, []);

  useEffect(() => {
    gltf.scene.traverse((object) => {
      const mesh = object as THREE.Mesh;
      if (mesh) {
        if (hideList.includes(mesh.name)) {
          mesh.visible = false;
        }
      }
    });
    return () => {
      gltf.scene.traverse((object) => {
        const mesh = object as THREE.Mesh;
        if (mesh) {
          if (hideList.includes(mesh.name)) {
            mesh.visible = true;
          }
        }
      });
    };
  }, [hideList]);

  useEffect(() => {
    if (airWall) {
      const timeout = setTimeout(() => {
        loaded(true);
      }, 3000);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [airWall]);

  return (
    <IdleAnimation animations={animations}>
      <group ref={group} userData={{ camCollisionListener: true }}>
        {airWall && (
          <RigidBody type="fixed" colliders="trimesh" ccd>
            <mesh
              geometry={airWall.geometry}
              material={airWall.material}
              castShadow={false}
              receiveShadow={false}
              position={[airWall.position.x, airWall.position.y, airWall.position.z]}
              rotation={[airWall.rotation.x, airWall.rotation.y, airWall.rotation.z]}
              scale={airWall.scale}
              userData={{ camExcludeCollision: true }}
            />
          </RigidBody>
        )}
      </group>
    </IdleAnimation>
  );
}
