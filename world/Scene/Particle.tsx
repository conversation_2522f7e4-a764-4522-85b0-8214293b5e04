/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import { useEffect, useState } from 'react';
import { ParticleConfig, ParticleData } from '@/world/Config/ParticleConfig';
import ParticleObject from '@/world/Particles/ParticleObject';

export default function Particle({ particleId }: { particleId: number }) {
  const [npcData, setNpcData] = useState<ParticleData | null>(null);

  useEffect(() => {
    ParticleConfig.getInstance().getData(particleId, (data) => {
      setNpcData(data);
    });
  }, []);

  return (
    <>
      {npcData && (
        <group
          position={
            new THREE.Vector3(npcData.position[0], npcData.position[1], npcData.position[2])
          }
          quaternion={new THREE.Quaternion().setFromEuler(new THREE.Euler(0, npcData.yawY, 0))}
        >
          <ParticleObject url={npcData.particle_url} scale={npcData.scale} />
        </group>
      )}
    </>
  );
}
