/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { useEffect, useRef, useState } from 'react';
import { NpcConfig, NpcData, NpcType } from '@/world/Config/NpcConfig';
import { OtherPlayerData } from '@/world/Character/OtherPlayer';
import AvatarData from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import createUseGame from '@/src/stores/useGame';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import GlobalSpace, { GAME_OP_TYPE } from '@/world/Global/GlobalSpace';
import { forEach } from 'es-toolkit/compat';
import { NpcChatConfig, NpcChatData } from '@/world/Config/NpcChatConfig';
import {
  NpcChatOptionConfig,
  NpcChatOptionData,
  NpcChatOptionType,
} from '@/world/Config/NpcChatOptionConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { POPOVER_HEIGHT } from '@/constant';
import { getPizzaActivity } from '@/world/Activity/PizzaActivity';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';

const PIZZA_NPC_ID = 2003;

export default function Npc({ npcId }: { npcId: number }) {
  const { userBasicInfo } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [npcData, setNpcData] = useState<NpcData | null>(null);
  const [npcChatData, setNpcChatData] = useState<NpcChatData | null>(null);
  const [npcChatOptionList, setNpcChatOptionList] = useState<NpcChatOptionData[]>([]);
  const [otherPlayerData, setOtherPlayerData] = useState<OtherPlayerData | null>(null);
  const [showChat, setShowChat] = useState(true);
  const [isPizzaTime, setIsPizzaTime] = useState(false);
  const [nearNpc, setNearNpc] = useState(false);
  const [haveTwitter, setHaveTwitter] = useState<boolean>(false);
  const groupRef = useRef<THREE.Group>(null);
  const myPlayer = GetMyPlayer();

  const [dailyBuyTotalCount, setDailyBuyTotalCount] = useState(0);
  const [dailyCurrentBuyCount, setDailyCurrentBuyCount] = useState(0);

  const [workbenchTotalCount, setWorkbenchTotalCount] = useState(0);
  const [workbenchCurrentCount, setWorkbenchCurrentCount] = useState(0);

  const updateHeight = () => {
    if (groupRef.current) {
      const box = new THREE.Box3().setFromObject(groupRef.current);

      return box.max.y - box.min.y + POPOVER_HEIGHT;
    }
    return 0;
  };

  const resetNpcChatData = () => {
    if (npcData) {
      switch (npcData.type) {
        case NpcType.NormalNpc:
          NpcChatConfig.getInstance().getData(npcData.chatId1, (data) => {
            setNpcChatData(data);
          });
          break;
        case NpcType.TwitterNpc:
          if (haveTwitter) {
            NpcChatConfig.getInstance().getData(npcData.chatId2, (data) => {
              setNpcChatData(data);
            });
          } else {
            NpcChatConfig.getInstance().getData(npcData.chatId1, (data) => {
              setNpcChatData(data);
            });
          }
          break;
      }
    }
  };

  useEffect(() => {
    const pointKey = 'npc_' + npcId;
    myPlayer.unregisterMapPoint(pointKey);
    if (npcId === PIZZA_NPC_ID && !isPizzaTime) {
      setNpcData(null);
      return;
    }
    NpcConfig.getInstance().getData(npcId, (data) => {
      setNpcData(data);
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(data.position[0], data.position[1], data.position[2]),
        (distance) => {
          setNearNpc(distance < data.distance);
        }
      );
    });
  }, [isPizzaTime, npcId]);

  useEffect(() => {
    if (npcData === null) {
      setOtherPlayerData(null);
      return;
    }
    const uuid = THREE.MathUtils.generateUUID();

    const avatarData = new AvatarData();
    avatarData.shirtTextureId = npcData.avatarData.shirtTextureId;
    avatarData.shirtId = npcData.avatarData.shirtId;
    avatarData.pantsId = npcData.avatarData.pantsId;
    avatarData.shoesId = npcData.avatarData.shoesId;
    avatarData.hatId = npcData.avatarData.hatId;
    avatarData.shirtColor = npcData.avatarData.shirtColor;
    avatarData.faceId = npcData.faceUrl.length > 0 ? npcData.faceUrl : null;
    avatarData.glovesId = npcData.avatarData.glovesId;
    const quaternion = new THREE.Quaternion().setFromEuler(
      new THREE.Euler(0, (npcData.yawY * Math.PI) / 180, 0)
    );
    const useGame = createUseGame();
    const playerData = new OtherPlayerData(uuid, avatarData, useGame);
    playerData.usePet = npcData.glbUrl;
    playerData.meshScale = npcData.glbScale;
    playerData.yawOffset = npcData.yawOffset;
    playerData.position.set(npcData.position[0], npcData.position[1], npcData.position[2]);
    playerData.quaternion = quaternion;
    playerData.curAnimation = npcData.default_action;
    playerData.lockAnimation = npcData.default_action;
    playerData.itemId = npcData.handItemId;
    playerData.name = npcData.name;
    playerData.isChange = true;
    setOtherPlayerData(playerData);
  }, [npcData]);

  useEffect(() => {
    if (nearNpc) {
      resetNpcChatData();
    } else {
      setNpcChatData(null);
    }
  }, [npcData, haveTwitter, nearNpc]);

  useEffect(() => {
    if (npcChatData) {
      NpcChatOptionConfig.getInstance().getDataList(npcChatData.optionList, (optionList) => {
        setNpcChatOptionList(optionList);
      });
    } else {
      setNpcChatOptionList([]);
    }
  }, [npcChatData]);

  useEffect(() => {
    if (otherPlayerData && npcData) {
      const npcChat = () => {
        if (npcChatData) {
          otherPlayerData.faceToPosition(myPlayer.position);
          if (npcChatData.replayAction === 1) {
            otherPlayerData.lock(npcChatData.chatAction);
          } else {
            otherPlayerData.play(npcChatData.chatAction);
          }
          const task = otherPlayerData.createAnswer();
          setTimeout(() => {
            const content = NpcChatConfig.getInstance().getWord(npcChatData);
            task.start(
              {
                content: content,
                soundUrl: npcChatData.chatUrl.length > 3 ? getCdnLink(npcChatData.chatUrl) : '',
                citationFiles: [],
              },
              () => {
                //  otherPlayerData.clearAnswer()
              }
            );
          }, 1);
        }
      };
      if (nearNpc) {
        npcChat();
      } else {
        otherPlayerData.lock(npcData.default_action);
        if (npcData.lockYaw) {
          otherPlayerData.quaternion = new THREE.Quaternion().setFromEuler(
            new THREE.Euler(0, (npcData.yawY * Math.PI) / 180, 0)
          );
          otherPlayerData.isChange = true;
        }
        otherPlayerData.clearAnswer();
      }
    }
  }, [nearNpc, npcChatData]);

  useEffect(() => {
    if (myPlayer.UsePetInscriptionId.length > 0) {
      return;
    }
    if (npcData === null) return;

    if (!nearNpc) {
      return;
    }

    if (showChat && npcChatOptionList.length > 0) {
      const lockButton = (delay = 2000) => {
        setShowChat(false);
        setTimeout(() => {
          setShowChat(true);
        }, delay);
      };
      const opKeyList: string[] = [];

      npcChatOptionList.forEach((option) => {
        let content = option.text;
        const params = option.params;
        switch (option.type) {
          case NpcChatOptionType.BuyEnergy:
            content += ' (' + dailyCurrentBuyCount + '/' + dailyBuyTotalCount + ')';
            break;
          // case NpcChatOptionType.OpenSynthesis:
          //   content += ' (' + workbenchCurrentCount + '/' + workbenchTotalCount + ')';
          //   break;
          case NpcChatOptionType.JoinActivity:
            const pizzaActivity = getPizzaActivity();
            const activityData = pizzaActivity.getActivityData();
            const now = Date.now();
            if (activityData.signUpTime - now > 0) {
              content +=
                '(Starts in ' + Math.floor((activityData.startTime - now) / 1000 / 60) + ' min)';
            } else if (activityData.endTime - now > 0) {
              if (activityData.pizzaData.length > 0) {
                content += '(Already registered)';
              } else {
                content += '(Not registered)';
              }
            }
            break;
        }
        opKeyList.push(
          GlobalSpace.addGameOp(
            GAME_OP_TYPE.CustomOp,
            () => {
              let needReset = true;
              switch (option.type) {
                case NpcChatOptionType.JumpChat:
                  const nextChatId = Number(params[0]);
                  setNpcChatData(null);
                  setNpcChatOptionList([]);
                  NpcChatConfig.getInstance().getData(nextChatId, (data) => {
                    setNpcChatData(data);
                  });
                  needReset = false;
                  break;
                case NpcChatOptionType.Twitter:
                  myPlayer.callAppApi(AppGameApiKey.authTwitter);
                  break;
                case NpcChatOptionType.ReceiveTool:
                  myPlayer.callAppApi(AppGameApiKey.receiveTool);
                  break;
                case NpcChatOptionType.BuyEnergy:
                  const buyCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.buyEnergy, buyCommunity);
                  break;
                case NpcChatOptionType.ActivityRule:
                  const activityType = Number(params[0]);
                  myPlayer.callAppApi(AppGameApiKey.activityRule, activityType);
                  break;
                case NpcChatOptionType.SubmitResources:
                  const submitCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.submitResources, submitCommunity);
                  break;
                case NpcChatOptionType.ShareTwitter:
                  const tweetsId = params[0];
                  myPlayer.callAppApi(AppGameApiKey.shareTweets, tweetsId);
                  break;
                case NpcChatOptionType.Donation:
                  const donationCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.donateTokens, donationCommunity);
                  break;
                case NpcChatOptionType.ClaimDrop:
                  const claimCommunity = params[0];
                  myPlayer.callAppApi(AppGameApiKey.claimDrop, claimCommunity);
                  break;
                case NpcChatOptionType.OpenSynthesis:
                  myPlayer.callAppApi(AppGameApiKey.openSynthesis, 'tool', () => {
                    if (otherPlayerData) otherPlayerData.play('Action_28');
                  });
                  break;
                // TODO: 添加宠物窝制造的对话选项回调
                // case NpcChatOptionType.OpenSynthesis:
                //   myPlayer.callAppApi(AppGameApiKey.openSynthesis, 'petBed', () => {
                //     if (otherPlayerData) otherPlayerData.play('Action_28');
                //   });
                //   break;
                case NpcChatOptionType.JoinActivity:
                  const pizzaActivity = getPizzaActivity();
                  pizzaActivity.joinActivity();
                  break;
                case NpcChatOptionType.JumpLink:
                  const linkUrl = params[0];
                  if (linkUrl.length > 0) {
                    window.open(linkUrl);
                  }
                  break;
              }
              if (needReset) {
                resetNpcChatData();
              }
              lockButton(option.clickDelay);
            },
            option.id,
            content,
            getCdnLink(option.iconUrl)
          )
        );
      });

      return () => {
        forEach(opKeyList, (opKey) => {
          GlobalSpace.removeGameOp(opKey);
        });
      };
    }
  }, [showChat, nearNpc, npcData, npcChatOptionList, dailyCurrentBuyCount, dailyBuyTotalCount]);

  useEffect(() => {
    if (userBasicInfo) {
      setDailyBuyTotalCount(userBasicInfo.toolConfig?.dailyBuyTotalCount || 0);
      setDailyCurrentBuyCount(userBasicInfo.toolConfig?.dailyCurrentBuyCount || 0);
      if (userBasicInfo.workbenchConfig) {
        setWorkbenchTotalCount(userBasicInfo.workbenchConfig.workbenchTotalCount || 0);
        setWorkbenchCurrentCount(userBasicInfo.workbenchConfig.workbenchCurrentCount || 0);
      }
      setHaveTwitter(userBasicInfo.twitterFlag || false);
    } else {
      setHaveTwitter(false);
    }
  }, [userBasicInfo]);

  useEffect(() => {
    if (npcId === PIZZA_NPC_ID) {
      const interval = setInterval(() => {
        const now = Date.now();
        const pizzaActivity = getPizzaActivity();
        const activityData = pizzaActivity.getActivityData();
        //报名中
        setIsPizzaTime(activityData.signUpTime < now && now <= activityData.startTime);
      }, 1000);
      return () => {
        clearInterval(interval);
      };
    }
  }, [npcId]);

  return <group ref={groupRef}>{otherPlayerData && otherPlayerData.getReact(updateHeight)}</group>;
}
