import React, { useRef } from 'react';
import * as THREE from 'three';
import { useControls } from 'leva';

export default function Fog({
  debug = false,
  foxColor = '#799ed0',
  nearDistance = 0,
  farDistance = 130,
}: {
  debug?: boolean;
  foxColor?: string;
  nearDistance?: number;
  farDistance?: number;
}) {
  const fogRef = useRef<THREE.Fog>(null);
  /**
   * Debug settings
   */
  let fogDebug = null;
  if (debug) {
    // Character Controls
    fogDebug = useControls('Fog Controls', {
      foxColor: {
        value: foxColor,
      },
      nearDistance: {
        value: nearDistance,
        min: 0,
        max: 1000,
        step: 1,
      },
      farDistance: {
        value: farDistance,
        min: 0,
        max: 1000,
        step: 1,
      },
    });
    // Apply debug values
    foxColor = fogDebug.foxColor;
    nearDistance = fogDebug.nearDistance;
    farDistance = fogDebug.farDistance;
  }

  return (
    <>
      <fog ref={fogRef} attach="fog" args={[foxColor, nearDistance, farDistance]} />
      {/* 颜色，近距离，远距离 */}
    </>
  );
}
