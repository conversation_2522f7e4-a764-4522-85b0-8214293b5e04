/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
*/

import * as THREE from 'three';
import React, { Suspense, useEffect, useRef, useState } from 'react';
import { TreeConfig, TreeData } from '@/world/Config/TreeConfig';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import { useFrame, useThree } from '@react-three/fiber';
import GlobalSpace, { GAME_OP_TYPE } from '@/world/Global/GlobalSpace';
import { GLTF } from 'three-stdlib';
import { AnimationSet, UseGameState } from '@/src/stores/useGame';
import { useAnimations } from '@react-three/drei';
import { RigidBody } from '@react-three/rapier';
import { getParticleSystem } from '@/world/Particles/ParticleSystem';
import AudioSystemComponent, { AudioSystem } from '@/world/Global/GlobalAudioSystem';
import { LoaderUtil } from '@/world/Util/LoaderUtil';
import { ItemType } from '@/world/Config/ItemConfig';
import { StoneConfig } from '@/world/Config/StoneConfig';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';

enum CoinStatus {
  Normal,
  Idle,
  Drop,
}

function Animation(props: {
  useGame: any;
  animationSet: AnimationSet;
  animations: THREE.AnimationClip[];
  children: React.ReactNode;
}) {
  // Change the character src to yours
  const group = useRef<THREE.Group>(null);

  /**
   * Character animations setup
   */
  const curAnimation = props.useGame((state: UseGameState) => state.curAnimation);
  const { actions } = useAnimations(props.animations, group);
  const resetAnimation = props.useGame((state: UseGameState) => state.reset);
  const initializeAnimationSet = props.useGame(
    (state: UseGameState) => state.initializeAnimationSet
  );

  useEffect(() => {
    // Initialize animation set
    initializeAnimationSet(props.animationSet);
  }, []);

  useEffect(() => {
    if (props.animations.length === 0 || curAnimation === null || curAnimation === undefined) {
      return;
    }
    //按|分割
    const actionList: string[] = curAnimation.split('|');
    //随机一个
    const actionKey = actionList[Math.floor(Math.random() * actionList.length)];

    const finishCall = () => {
      resetAnimation();
    };
    // Play animation
    const action: THREE.AnimationAction | null = actions[actionKey || ''];
    if (action) {
      // For jump and jump land animation, only play once and clamp when finish
      if (
        curAnimation !== props.animationSet.idle &&
        curAnimation !== props.animationSet.run &&
        curAnimation !== props.animationSet.walk
      ) {
        action.reset().fadeIn(0.2).setLoop(THREE.LoopOnce, 0).play();
        action.clampWhenFinished = true;
      } else {
        if (curAnimation === props.animationSet.jump) {
          action.reset().fadeIn(0.4).play();
        } else {
          action.reset().fadeIn(0.2).play();
        }
      }

      // When any action is clamp and finished reset animation
      (action as any)._mixer.addEventListener('finished', () => finishCall());
    }
    return () => {
      if (action) {
        // Fade out previous action
        action.fadeOut(0.2);

        // Clean up mixer listener, and empty the _listeners array
        (action as any)._mixer.removeEventListener('finished', () => finishCall());
        (action as any)._mixer._listeners = [];
      }
    };
  }, [curAnimation, props.animations]);

  return (
    <Suspense fallback={null}>
      <group ref={group} dispose={null}>
        {/* Replace character model here */}
        {props.children}
      </group>
    </Suspense>
  );
}

export function CreateMesh({ useGame, gltf }: { useGame: any; gltf: GLTF }) {
  const ref = useRef<THREE.Group>(null);
  const [animations, setAnimations] = useState<THREE.AnimationClip[]>([]);
  const [airWall, setAirWall] = useState<THREE.Mesh | null>(null);
  const animationSet = {
    idle: 'idle',
    action1: 'shake',
    action2: 'fall',
  };

  useEffect(() => {
    if (ref.current) {
      const group = ref.current;
      gltf.scene.traverse((child) => {
        if (child.type.includes('Mesh')) {
          if (child.name.includes('stop')) {
            setAirWall(child as THREE.Mesh);
          } else {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        }
      });
      group.add(gltf.scene);
      // 跳跃动作加入数组
      setAnimations(gltf.animations);
    }
  }, []);

  return (
    <Animation useGame={useGame} animationSet={animationSet} animations={animations}>
      {airWall && (
        <RigidBody type="fixed" colliders="trimesh" ccd>
          <mesh
            geometry={airWall.geometry}
            material={airWall.material}
            castShadow={false}
            receiveShadow={false}
            position={[airWall.position.x, airWall.position.y, airWall.position.z]}
            rotation={[airWall.rotation.x, airWall.rotation.y, airWall.rotation.z]}
            scale={airWall.scale}
            userData={{ camExcludeCollision: true }}
          />
        </RigidBody>
      )}
      <group position={[0, 0, 0]} ref={ref} />
    </Animation>
  );
}

function TreeCoin({ initPos, status }: { initPos: THREE.Vector3; status: CoinStatus }) {
  const rigidType = status === CoinStatus.Drop ? 'dynamic' : 'fixed';
  const meshRef = useRef<THREE.Mesh>(null);
  const [mesh, setMesh] = useState<THREE.Mesh | null>(null);

  useEffect(() => {
    LoaderUtil.loadGlb('./assets/Coin/potato_coin.glb', (gltf) => {
      setMesh(gltf.scene.children[0] as THREE.Mesh);
    });
  }, []);

  useFrame((state) => {
    if (meshRef.current) {
      if (status === CoinStatus.Drop) {
        meshRef.current.rotation.y = 0;
      } else {
        meshRef.current.rotation.y += 0.01;
      }
    }
  });
  return (
    <>
      {mesh && (
        <RigidBody type={rigidType} colliders="cuboid" restitution={0.8} friction={0.4} ccd>
          <mesh
            ref={meshRef}
            name={mesh.name}
            position={initPos}
            scale={mesh.scale}
            rotation={new THREE.Euler(0, 0, 0)}
            material={mesh.material}
            geometry={mesh.geometry}
            castShadow
            receiveShadow></mesh>
        </RigidBody>
      )}
    </>
  );
}

export default function SceneTree({ treeId }: { treeId: number }) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const particleSystem = getParticleSystem();
  const debugHitTree = localStorage.getItem('debugHitTree') === 'true';
  const { scene } = useThree();
  const myPlayer = GetMyPlayer();
  const useGame = myPlayer.getUseGame();
  const setCurAnimation = useGame((state: UseGameState) => state.setCurAnimation);
  const treeObject = TreeConfig.getInstance().getObject(treeId);
  const [treeData, setTreeData] = useState<TreeData | null>(null);
  const [showHitButton, setShowHitButton] = useState<boolean>(false);
  const [comboFail, setComboFail] = useState<boolean>(false);
  const [isDead, setIsDead] = useState<boolean>(true);
  const [gltf, setGltf] = useState<GLTF | null>(null);
  const [treeMesh, setTreeMesh] = useState<THREE.Object3D | null>(null);
  const [smokeObject, setSmokeObject] = useState<THREE.Object3D | null>(null);

  const lightObjectRef = useRef<THREE.Object3D | null>(null);
  const [showLight, setShowLight] = useState<boolean>(false);
  const [isLogin, setIsLogin] = useState<boolean>(myPlayer.btcAddress.length > 0);
  // const [coinStatus, setCoinStatus] = useState<CoinStatus>(CoinStatus.Normal);
  // const [coinPos, setCoinPos] = useState<THREE.Vector3 | null>(null);
  const setTreeCurAnimation = treeObject.useGame((state: UseGameState) => state.setCurAnimation);
  const curTreeAnimation = treeObject.useGame((state: UseGameState) => state.curAnimation);
  const groupRef = useRef<THREE.Group>(null);
  useEffect(() => {
    TreeConfig.getInstance().getData(treeId, (data) => {
      setTreeData(data);
      if (treeObject.status == 'alive') {
        setIsDead(false);
      }
    });
  }, []);

  useEffect(() => {
    if (treeMesh) {
      //为登录或者未死亡显示树木
      treeMesh.visible = !isLogin || !isDead;
    }
  }, [isDead, treeMesh, isLogin]);

  useEffect(() => {
    if (groupRef.current && treeData) {
      const group = groupRef.current;
      const loader = new GLTFLoader();
      loader.load(getCdnLink(treeData.glb_url), (_gltf) => {
        setGltf(_gltf as any);

        _gltf.scene.traverse((child) => {
          if (child.name.includes('mesh')) {
            setTreeMesh(child);
          }
          if (child.name.includes('_E')) {
            setSmokeObject(child);
          }
          if (child.name.includes('_light')) {
            child.visible = false;
            lightObjectRef.current = child;
          }
        });

        for (let i = 0; i < _gltf.animations.length; i++) {
          const clip = _gltf.animations[i];
          if (clip.name.includes('action01')) {
            clip.name = 'idle';
          }
          if (clip.name.includes('action02')) {
            clip.name = 'shake';
          }
          if (clip.name.includes('action03')) {
            clip.name = 'fall';
          }
        }
        setTreeCurAnimation('idle');
        group.position.set(treeData.position[0], treeData.position[1], treeData.position[2]);
        group.quaternion.setFromEuler(new THREE.Euler(0, treeData.yawY || 0, 0));
        //
        // const coinObject = new THREE.Object3D();
        // coinObject.position.set(
        //   treeData.coin_position[0],
        //   treeData.coin_position[1],
        //   treeData.coin_position[2]
        // );
        // group.add(coinObject);a
        // const coinWorldPos = new THREE.Vector3();
        // coinObject.getWorldPosition(coinWorldPos);
        // setCoinPos(coinWorldPos);
      });
    }
  }, [treeData]);

  useEffect(() => {
    if (lightObjectRef.current) {
      lightObjectRef.current.castShadow = false;
      lightObjectRef.current.receiveShadow = false;
      lightObjectRef.current.visible =
        (curTreeAnimation === 'idle' || curTreeAnimation === 'shake') && showLight && !isDead;
    }
  }, [treeId, curTreeAnimation, showLight, isDead, lightObjectRef.current]);

  useEffect(() => {
    if (myPlayer.UsePetInscriptionId.length > 0) {
      return;
    }

    if (!isDead && showHitButton && !comboFail && treeData && treeObject.hp > 0) {
      const opKey = GlobalSpace.addGameOp(
        GAME_OP_TYPE.HitTree,
        () => {
          myPlayer.hitTree(
            (isCombo, damage: number) => {
              const delay = isCombo ? 500 : 750;
              setTimeout(() => {
                AudioSystem.playAudio(
                  'scene_tree_' + treeData.id,
                  TreeConfig.getInstance().getHitSound(treeData),
                  () => {
                    return true;
                  }
                );
              }, delay);
              treeObject.combo += 1;
              setCurAnimation('Action_08');
              if (isCombo) {
                setTimeout(() => {
                  setCurAnimation('Action_09');
                }, 1);
              }
              if (treeObject.hp < damage) {
                setComboFail(true);
              }
            },
            (damage, position, quaternion) => {
              if (treeObject.hp <= 0) {
                //放置重复请求
                return;
              }
              treeObject.hp -= damage;
              particleSystem.addParticle(
                position,
                quaternion,
                './particles/Effect_wood_0.json',
                treeData.hit_effect_scale,
                treeData.hit_effect_during
              );
              particleSystem.addParticle(
                position,
                quaternion,
                './particles/Effect_wood_1.json',
                treeData.hit_effect_scale,
                treeData.hit_effect_during
              );
              if (treeObject.combo > 1) {
                myPlayer.callAppApi(AppGameApiKey.showCombo, treeObject.combo);
              }
              if (treeObject.hp > 0) {
                setTreeCurAnimation('shake');
              } else {
                setTreeCurAnimation('fall');
                const userItemId = myPlayer.axeParams?.userItemId || '';
                setTimeout(() => {
                  treeObject.status = 'dead';
                  myPlayer.callAppApi(
                    AppGameApiKey.cutTree,
                    treeObject.tag,
                    treeObject.id,
                    userItemId
                  );
                  // TreeConfig.getInstance().cutTree(treeObject.tag);
                }, treeData.tree_disappear_time);
                setTimeout(() => {
                  if (smokeObject) {
                    const worldPos = smokeObject.getWorldPosition(new THREE.Vector3());
                    particleSystem.addParticle(
                      worldPos,
                      smokeObject.quaternion,
                      './particles/Effect_smoke.json',
                      treeData.fall_effect_scale,
                      treeData.fall_effect_during
                    );
                  }
                }, treeData.fall_effect_delay);
                AudioSystem.playAudio(
                  'scene_tree_' + treeData.id,
                  TreeConfig.getInstance().getFallSound(treeData),
                  () => {
                    return true;
                  }
                );
              }
            },
            () => {
              setComboFail(false);
              treeObject.combo = 0;
            },
            () => {
              setComboFail(true);
            },
            scene,
            treeData
          );
          if (debugHitTree) {
            // 显示树半径
            const geometry = new THREE.SphereGeometry(treeData.radius);
            const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 }); // 红色高亮
            const sphere = new THREE.Mesh(geometry, material);
            sphere.position.set(
              treeData.position[0],
              treeData.position[1] + 0.5,
              treeData.position[2]
            );
            scene.add(sphere);

            setTimeout(() => {
              scene.remove(sphere);
            }, 2000);
          }
        },
        treeData.id
      );

      return () => {
        GlobalSpace.removeGameOp(opKey);
      };
    }
  }, [showHitButton, comboFail, isDead]);

  useEffect(() => {
    setIsLogin(btcAddress.length > 0);
  }, [btcAddress]);

  useEffect(() => {
    if (treeData) {
      const pointKey = 'tree_' + treeId;
      myPlayer.registerMapPoint(
        pointKey,
        new THREE.Vector3(treeData.position[0], treeData.position[1], treeData.position[2]),
        (distance) => {
          if (myPlayer.itemData && myPlayer.itemData.type === ItemType.Axe) {
            if (distance > treeData.range[0] && distance < treeData.range[3]) {
              //计算角度
              const angle = Math.atan2(
                myPlayer.position.z - treeData.position[2],
                myPlayer.position.x - treeData.position[0]
              );
              if (
                StoneConfig.getInstance().checkInSector(
                  treeData.sector_range_start,
                  treeData.sector_range_length,
                  angle
                )
              ) {
                setShowHitButton(true);
              } else {
                setShowHitButton(false);
              }
            } else {
              setShowHitButton(false);
            }
          } else {
            setShowHitButton(false);
          }
        }
      );
    }
  }, [treeData]);

  useEffect(() => {
    if (treeObject && treeData) {
      const interval = setInterval(() => {
        if (isDead && treeObject.status === 'alive') {
          setIsDead(false);
          treeObject.hp = treeData.max_hp;
        }
        if (!isDead && treeObject.status !== 'alive') {
          setIsDead(true);
          treeObject.hp = 0;
        }
        setShowLight(treeObject.haveCoin);
      }, 1000);
      return () => {
        clearInterval(interval);
      };
    }
  }, [treeData, isDead, treeObject]);

  return (
    <>
      <group ref={groupRef}>
        {gltf && <CreateMesh useGame={treeObject.useGame} gltf={gltf} />}
        <AudioSystemComponent _key={'scene_tree_' + treeId} />
      </group>
      {/*{coinPos && CoinStatus.Normal !== coinStatus && (*/}
      {/*  <TreeCoin initPos={coinPos} status={coinStatus} />*/}
      {/*)}*/}
    </>
  );
}
