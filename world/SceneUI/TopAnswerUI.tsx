import { Html } from '@react-three/drei';
import React, { useEffect } from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';

import styled from 'styled-components';
import { IAssistantAnswer } from '@/constant/type';
import MarkdownRender from '@/components/MarkdownRender';
import { AudioSystem } from '@/world/Global/GlobalAudioSystem';

const UIBox = styled.div`
  .bubble-container {
    position: relative;
    display: inline-block;
    bottom: 0;

    .window {
      left: 4px; /* 往右便宜4px, 平衡滑动条多出的8px */
      max-width: 900px; /* 固定宽度 */
      //border: 1px solid #ccc; /* 窗口边框 */
      //border-radius: 5px; /* 圆角 */
      //overflow: hidden; /* 隐藏溢出内容 */
      //box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* 阴影效果 */
    }

    .content {
      max-height: 400px; /* 最大高度，8行文本 */
      overflow-y: auto; /* 允许垂直滚动 */
      font-family: Arial, sans-serif; /* 字体 */
      /* 滚动条样式 */

      ::-webkit-scrollbar {
        width: 8px; /* 滚动条宽度 */
      }

      ::-webkit-scrollbar-track {
        background: transparent; /* 滚动条轨道背景 */
      }

      ::-webkit-scrollbar-thumb {
        background-color: #c69f7e; /* 滚动条颜色 */
        border-radius: 10px; /* 圆角 */
      }
    }

    .divider {
      height: 5px; /* 分割线高度 */
      background-color: #cabfab; /* 分割线颜色 */
      margin: 15px 6px 10px 6px; /* 分割线上下外边距 */
    }

    .links {
      padding: 10px; /* 内边距 */
      color: #686663; /* 分割线以下部分颜色 */
    }

    .links span {
      font-size: 20px; /* 链接字体大小 */
    }

    .links a {
      display: inline-block; /* 使链接横向排列 */
      color: #686663; /* 链接颜色 */
      text-decoration: none; /* 去掉下划线 */
      font-size: 20px; /* 链接字体大小 */
      margin: 5px; /* 链接外边距 */
      white-space: nowrap; /* 防止链接文本换行 */
      background-color: #ecdbc6; /* 背景颜色 */
      border-radius: 10px; /* 圆角处理 */
      padding: 10px 20px; /* 内边距 */
    }

    .links a:hover {
      text-decoration: underline; /* 悬停时添加下划线 */
    }

    .text {
      font-size: 35px;
      word-wrap: break-word; /* 允许长单词换行 */
      overflow-wrap: break-word; /* 兼容性 */
      color: #140f08; /* 正文文本颜色 */
    }
  }

  //

  .bubble-container-bg {
    left: -60px;
    top: -50px;
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    border-top: 50px solid transparent; /* 为九宫格留出边框部分 */
    border-left: 60px solid transparent; /* 为九宫格留出边框部分 */
    border-bottom: 70px solid transparent; /* 为九宫格留出边框部分 */
    border-right: 60px solid transparent; /* 为九宫格留出边框部分 */
    border-image: url('/image/topChat/chat_bg.png'); /* 九宫格拉伸 */
    border-image-slice: 125 150 175 150 fill;
    z-index: -1;
    /* 其他属性保持不变 */
    border-image-repeat: no-repeat; /* 使用 round 代替 stretch */
    transform: translateZ(0); /* 强制硬件加速 */
    backface-visibility: hidden; /* 防止渲染问题 */
  }

  .image-slider {
    position: relative;
    padding: 10px 80px 10px 80px;

    .image-slider-img {
      width: 150px; /* 根据图片大小调整 */
      height: 30px; /* 根据图片大小调整 */
      background-size: 100% 100%;
    }
  }
`;

export class AnswerTask {
  curWord: string;
  startTime: number;
  stopTime: number;
  content: string;
  audioKey: string;
  citationList: {
    title: string;
    url: string;
  }[];
  callback: () => void;

  constructor(audioKey: string) {
    this.audioKey = audioKey;
    this.startTime = 0;
    this.content = '';
    this.curWord = '';
    this.citationList = [];
    this.stopTime = 0;
    this.callback = () => {};
  }

  start(data: IAssistantAnswer, callback: () => void) {
    const getUrlText = (text: string) => {
      if (text && text.length < 30) {
        return text;
      }
      return text.substring(0, 20) + '...' + text.substring(text.length - 8, text.length);
    };

    this.callback = callback;
    this.content = data.content;
    for (let i = 0; i < data.citationFiles.length; i++) {
      if (data.citationFiles[i].webLink && data.citationFiles[i].webLink.length > 0) {
        this.citationList.push({
          title: getUrlText(data.citationFiles[i].title),
          url: data.citationFiles[i].webLink,
        });
      } else {
        this.citationList.push({
          title: getUrlText(data.citationFiles[i].title),
          url: data.citationFiles[i].fileUrl,
        });
      }
    }

    const startWord = () => {
      this.startTime = Date.now();
      this.stopTime = this.startTime + this.content.length * 30;
    };

    if (data.soundUrl.length > 0) {
      AudioSystem.playAudio(this.audioKey, data.soundUrl, () => {
        startWord();
        return true;
      });
    } else {
      startWord();
    }
  }

  stop() {
    this.stopTime = Date.now();
    this.callback();
    this.callback = () => {};
  }
}

export default function TopAnswerUI({ height, task }: { height: number; task: AnswerTask }) {
  const ref = React.useRef<THREE.Group>(null);
  const sliderRef = React.useRef<HTMLImageElement>(null);
  const contentRef = React.useRef<HTMLImageElement>(null);
  const [word, setWord] = React.useState<string>('');
  const [mouseOver, setMouseOver] = React.useState<boolean>(false);
  const [urlList, setUrlList] = React.useState<{ title: string; url: string }[]>([]);
  const [contentHeight, setContentHeight] = React.useState<number>(0);

  // 在每一帧更新时，让元素朝向摄像机
  useFrame(({ camera }) => {
    if (ref.current) {
      // 只计算水平方向的朝向
      const cameraWorldPos = new THREE.Vector3();
      camera.getWorldPosition(cameraWorldPos);
      const objWorldPos = new THREE.Vector3();
      ref.current.getWorldPosition(objWorldPos);
      cameraWorldPos.y = objWorldPos.y;
      ref.current.lookAt(cameraWorldPos);
    }
    if (sliderRef.current) {
      //秒
      const second = Math.floor((Date.now() * 3) / 1000);
      //0.5秒替换一次index
      const index = (second % 3) + 1;
      const divList = sliderRef.current.getElementsByTagName('div');
      for (let i = 0; i < divList.length; i++) {
        divList[i].style.display = index == i + 1 ? 'block' : 'none';
      }
    }
    //打字形式出现文字
    if (task.startTime > 0) {
      const now = Date.now();
      if (task.stopTime > now) {
        const wordLength = task.content.length;
        const curLength = Math.floor((now - task.startTime) / 15);
        if (curLength >= wordLength) {
          task.curWord = task.content;
          setWord(task.curWord);
        } else {
          task.curWord = task.content.substring(0, Math.floor(curLength));
          setWord(task.curWord);
        }
      } else {
        task.curWord = task.content;
        setUrlList(task.citationList);
        setWord(task.curWord);
        task.startTime = 0;
        task.callback();
      }
    }

    if (contentRef.current) {
      if (contentHeight != contentRef.current.scrollHeight) {
        setContentHeight(contentRef.current.scrollHeight);
      }
    }
  });

  useEffect(() => {
    if (!mouseOver && contentRef.current) {
      contentRef.current.scrollTop = contentHeight;
    }
  }, [contentHeight]);

  return (
    <group ref={ref} position={[0, height, 0]}>
      <Html
        distanceFactor={1} // 让它始终保持固定的大小，不随摄像机远近变化
        transform // 保证HTML内容的大小不随距离变化
        // occlude  // 确保 HTML 元素不会被 3D 场景中的物体遮挡
        // pointerEvents="none" // 禁用鼠标事件
        position={[0, 0, 0]}
        style={{
          transformOrigin: 'bottom center',
          transform: 'translate(0,-50%)',
          transition: 'all 0.3s ease',
          left: '50%',
        }}
        center={false}
      >
        <UIBox>
          <div
            className="bubble-container"
            onMouseOver={() => {
              setMouseOver(true);
            }}
            onMouseLeave={() => {
              setMouseOver(false);
            }}
          >
            <div className="bubble-container-bg" />
            {word !== '' && (
              <div className="window">
                <div className="content" ref={contentRef}>
                  {/* <div className="text">{word}</div> */}
                  <div className="text">
                    <MarkdownRender context={word} />
                  </div>
                  {urlList.length > 0 && (
                    <>
                      <div className="divider"></div>
                      <div className="links">
                        <span>Condition：</span>
                        {urlList.map((item, index) => (
                          <a className="bubble-link" target="_blank" href={item.url} key={index}>
                            {item.title}
                          </a>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
            {word === '' && (
              <div className="image-slider">
                <div className="image-slider-img" ref={sliderRef}>
                  <div
                    className="image-slider-img"
                    style={{ backgroundImage: 'url(/image/topChat/chat1.png)' }}
                  />
                  <div
                    className="image-slider-img"
                    style={{ backgroundImage: 'url(/image/topChat/chat2.png)' }}
                  />
                  <div
                    className="image-slider-img"
                    style={{ backgroundImage: 'url(/image/topChat/chat3.png)' }}
                  />
                </div>
              </div>
            )}
          </div>
        </UIBox>
      </Html>
    </group>
  );
}
