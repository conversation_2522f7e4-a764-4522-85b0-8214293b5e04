:root {
  --animate-duration: 1s;
  --combo-z-index: 1000;
}
@font-face {
  font-family: 'JetBrains Mono';
  src: url('/fonts/JetBrainsMono-VariableFont_wght.ttf');
}
@font-face {
  font-family: 'Baloo 2';
  src: url('/fonts/Baloo2-VariableFont_wght.ttf');
}
@font-face {
  font-family: 'Bevan';
  src: url('/fonts/Bevan-Regular.ttf');
}
@font-face {
  font-family: 'Shrikhand';
  src: url('/fonts/Shrikhand-Regular.ttf');
}
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-VariableFont_opsz,wght.ttf');
}

html,
body,
#__next {
  width: 100dvw;
  height: 100dvh;
  overflow: hidden !important;
  padding: 0;
  margin: 0;
  font-family: 'JetBrains Mono';
  --cap-color: #212121;
  --cap-background: #fdfdfd;
  --cap-border-color: #dddddd8f;
  --cap-widget-padding: 14px;
  --cap-gap: 30px;
  font-size: calc(100vmax / 1920 * 16);
  *::-webkit-scrollbar {
    width: 0.5rem;
    height: 0.5rem;
    background: #00000010;
    border-radius: 0.5rem;
  }

  *::-webkit-scrollbar-thumb {
    background: #53515d;
    border-radius: 0.5rem;
  }
}
@keyframes loading-ano {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

#__next {
  width: 100%;
  height: 100%;
}

.rc-tooltip-inner {
  background: transparent;
  padding: 0;
  box-shadow: none;
}

/* @media screen and (orientation: portrait) {
  body {
    position: relative;
    overflow: hidden;
    width: 100dvh;
    height: 100dvw;
    top: calc((100dvh - 100dvw) / 2);
    left: calc((100dvw - 100dvh) / 2);
    transform: rotate(90deg);
    transform-origin: 50% 50%;
  }
}
@media screen and (orientation: landscape) {
  body {
    width: 100dvw;
    height: 100dvh;
    top: 0;
    left: 0;
    transform: none;
    transform-origin: 50% 50%;
  }
} */
