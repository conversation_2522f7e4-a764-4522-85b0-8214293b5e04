.scrollListContainer {
  position: relative;
  /* width: 100%; */
  height: 100%;
  width: 340px;
}

.scrollList {
  height: 100%;
  overflow-y: auto;
  /* padding: 0px 10px 0px 0px; */
}

.scrollList::-webkit-scrollbar {
  width: 6px;
}

.noScrollbar::-webkit-scrollbar {
  display: none;
}

.scrollList::-webkit-scrollbar-thumb {
  background: #c69f7e;
  border-radius: 3px;
}

.noScrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.item {
  padding: 16px;
  background-color: #faf3e5;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.item.selected {
  background-color: #faf3e5;
}

.item-text {
  color: white;
  margin: 0;
}

.topGradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(to bottom, #fef1df, transparent);
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.bottomGradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, #fef1df, transparent);
  pointer-events: none;
  transition: opacity 0.3s ease;
}
