import { useEffect } from 'react';
import { useAppSelector } from './useStore';
import { playerEnergyZustandStore } from '@/contexts/playerEnergyContext/store';

export function useLogoutAction() {
  const btcAddress = useAppSelector((state) => state.AppReducer.btcAddress);

  useEffect(() => {
    if (!btcAddress) {
      playerEnergyZustandStore.getState().resetPlayerInfo();
    }
  }, [btcAddress]);
}
