import { debounce } from 'lodash';
import { useEffect } from 'react';
import { useAppDispatch } from './useStore';
import isMobile from 'ismobilejs';
import { setIsMobile } from '@/store/app';

function useResizeUpdateMobile() {
  const dispatch = useAppDispatch();
  useEffect(() => {
    const update = () => {
      const mobileInfo = isMobile();

      dispatch(setIsMobile(mobileInfo.any));
    };

    const handleResize = debounce(() => {
      update();
    }, 500);

    const resizeObserver = new ResizeObserver(handleResize);
    if (document.documentElement) {
      resizeObserver.observe(document.documentElement);
    }
    return () => {
      resizeObserver.disconnect();
      handleResize.cancel();
    };
  }, []);
}

export default useResizeUpdateMobile;
