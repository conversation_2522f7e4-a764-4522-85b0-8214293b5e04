import { useEffect, useRef } from 'react';

// 20 different colors for player identification
export const PLAYER_COLORS = [
  '#C0C0C0', // 灰色
  // "#FF2828", // 红色
  // "#FF3A1C", // 橙红色
  // "#FF6A00", // 橙色
  // "#FFAE00", // 金黄色
  // "#FFFF27", // 黄色
  // "#8CFF00", // 黄绿色
  // "#00E545", // 绿色
  // "#00AA4D", // 深绿色
  // "#008000", // 森林绿
  // "#00FFE1", // 青绿色
  // "#00A692", // 蓝绿色
  // "#36E1FF", // 天蓝色
  // "#00B4F5", // 蓝色
  // "#0071F3", // 深蓝色
  // "#7D73FF", // 浅紫色
  // "#1100FF", // 靛蓝色
  // "#7B00FF", // 紫色
  // "#FF1CE1", // 粉紫色
  // "#FF1C8A", // 粉红色
  // "#FF7093", // 玫瑰色
];

export const GM_BTC_ADDRESS = '**************************************************************';

// ====== 特殊账号配置 ======
const SPECIAL_ACCOUNTS: Record<string, { label: string; color: string }> = {
  [GM_BTC_ADDRESS]: {
    label: 'GM',
    color: '#bc2b9d',
  },
};

export function usePlayerColors(useSequentialColors = false) {
  const playerColorMapRef = useRef<Map<string, string>>(new Map()); // 保存玩家ID到颜色的映射
  const nextColorIndexRef = useRef<number>(0); // 下一个要分配的颜色索引
  const shuffledColorsRef = useRef<string[]>([]); // 保存打乱的颜色数组

  // 重新打乱颜色数组的函数
  const reshuffleColors = () => {
    shuffledColorsRef.current = [...PLAYER_COLORS].sort(() => Math.random() - 0.5);
    // 清空颜色映射，这样所有玩家都会重新分配颜色
    playerColorMapRef.current.clear();
    nextColorIndexRef.current = 0;
  };

  // 初始化时打乱一次颜色
  useEffect(() => {
    reshuffleColors();
  }, []);

  // 清除颜色映射的方法（可以在退出房间时调用）
  const clearColorMap = () => {
    playerColorMapRef.current.clear();
    nextColorIndexRef.current = 0;
    reshuffleColors();
  };

  /**
   * 基于哈希值的颜色分配
   * @description 根据玩家ID获取玩家颜色
   * 同一个playerId总是映射到同一个颜色，即使刷新页面或重新进入房间
   * 不同的playerId通常会映射到不同的颜色
   * 当玩家超过20个时，颜色会重复使用，但不一定是按顺序重复
   * @param playerId
   * @returns
   */
  const getPlayerColorByHash = (playerId: string) => {
    // 将playerId中每个字符的ASCII码相加得到一个总和
    const hash = Array.from(playerId).reduce((acc, char) => acc + char.charCodeAt(0), 0);
    // 用总和对颜色数组长度取模，得到一个0到19之间的索引
    return PLAYER_COLORS[hash % PLAYER_COLORS.length];
  };

  /**
   * 按顺序颜色分配
   * @description 顺序颜色分配
   * @param playerId
   * @returns
   */
  const getPlayerColorSequential = (playerId: string) => {
    // 如果该玩家已经有分配的颜色，直接返回
    if (playerColorMapRef.current.has(playerId)) {
      return playerColorMapRef.current.get(playerId)!;
    }

    // 否则分配一个新颜色
    const colorIndex = nextColorIndexRef.current % PLAYER_COLORS.length;
    const color = PLAYER_COLORS[colorIndex];

    // 保存分配结果
    playerColorMapRef.current.set(playerId, color);
    nextColorIndexRef.current += 1;

    return color;
  };

  /**
   * 随机颜色分配
   * @description 使用预先打乱的颜色数组，确保颜色分配既随机又一致
   * @param playerId
   * @returns
   */
  const getPlayerColorRandom = (playerId: string) => {
    // 如果该玩家已经有分配的颜色，直接返回
    if (playerColorMapRef.current.has(playerId)) {
      return playerColorMapRef.current.get(playerId)!;
    }

    // 否则从打乱的颜色数组中分配一个新颜色
    const colorIndex = nextColorIndexRef.current % shuffledColorsRef.current.length;
    const color = shuffledColorsRef.current[colorIndex];

    // 保存分配结果
    playerColorMapRef.current.set(playerId, color);
    nextColorIndexRef.current += 1;

    return color;
  };

  // 获取特殊账号属性
  const getSpecialAccountProps = (playerId: string) => {
    return SPECIAL_ACCOUNTS[playerId] || null;
  };

  // 根据当前选择的方案获取颜色
  const getPlayerColor = (playerId: string, currentPlayerId?: string) => {
    // 检查是否是特殊账号
    const specialAccount = getSpecialAccountProps(playerId);
    if (specialAccount) {
      return specialAccount.color;
    }

    // 检查是否是当前玩家
    if (playerId === currentPlayerId) {
      return '#00FF26'; // 当前玩家固定黄色
    }

    // 否则根据选择的模式获取颜色
    if (useSequentialColors) {
      return getPlayerColorSequential(playerId);
    } else {
      return getPlayerColorRandom(playerId);
    }
  };

  // 根据当前选择的方案获取颜色
  const getPlayerTextColor = (playerId: string, currentPlayerId?: string) => {
    // 检查是否是特殊账号
    const specialAccount = getSpecialAccountProps(playerId);
    if (specialAccount) {
      return specialAccount.color;
    }

    // 检查是否是当前玩家
    if (playerId === currentPlayerId) {
      return '#00FF26'; // 当前玩家固定黄色
    }

    // 否则根据选择的模式获取颜色
    if (useSequentialColors) {
      return getPlayerColorSequential(playerId);
    } else {
      return '#FFFFFF';
    }
  };

  return {
    getPlayerColor,
    getPlayerTextColor,
    getSpecialAccountProps,
    clearColorMap,
    reshuffleColors,
    PLAYER_COLORS,
  };
}
