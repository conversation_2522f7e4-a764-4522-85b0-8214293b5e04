import { useState, useEffect } from 'react';

export function useFullscreen() {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const checkFullscreen = () => {
    const fullscreenElement = document.fullscreenElement;
    setIsFullscreen(!!fullscreenElement);
  };

  const enterFullscreen = () => {
    const el = document.documentElement;
    if (el.requestFullscreen) {
      el.requestFullscreen();
    }
  };

  const exitFullscreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  };

  const toggleFullscreen = () => {
    if (isFullscreen) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };

  useEffect(() => {
    const events = ['fullscreenchange'];

    const handler = () => checkFullscreen();

    events.forEach((event) => {
      document.addEventListener(event, handler);
    });

    checkFullscreen();

    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handler);
      });
    };
  }, []);

  return { isFullscreen, enterFullscreen, exitFullscreen, toggleFullscreen };
}
