import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getUserDropItemList } from '@/server';
import { IAppState } from '@/constant/type';
import { setRockList, setTreeList } from '@/store/app';
import toast from 'react-hot-toast';
import { ItemDropConfig } from '@/world/Config/ItemDropConfig';

export const useResourceList = (options = { autoFetch: true }) => {
  const dispatch = useDispatch();
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  // 获取拾取物列表的核心函数
  const fetchDropList = async (retryTimes = 3) => {
    const errorRetry = (msg: string) => {
      if (retryTimes > 0) {
        toast.error(msg);
        setTimeout(() => {
          fetchTreeList(retryTimes - 1).then();
        }, 3000);
      } else {
        toast.error('Request failed, Please check your network and try again.');
      }
    };
    try {
      const response = await getUserDropItemList();
      const { code, msg, data } = response.data;
      if (code === 1) {
        ItemDropConfig.getInstance().updateData(data);
      } else {
        if (105 === code) {
          return;
        }
        errorRetry(msg || 'Failed to get drop list');
      }
    } catch (err) {
      errorRetry('Failed to get drop list(catch)');
    } finally {
    }
  };
  // 获取树木列表的核心函数
  const fetchTreeList = async (retryTimes = 3) => {
    // const errorRetry = (msg: string) => {
    //   if (retryTimes > 0) {
    //     toast.error(msg)
    //     setTimeout(() => {
    //       fetchTreeList(retryTimes - 1).then()
    //     }, 3000)
    //   } else {
    //     toast.error("Request failed, Please check your network and try again.")
    //   }
    // }
    // try {
    //   const res = await getTreeList();
    //   if (res.data && res.data.code === 1) {
    //     const {trees, easterEgg, leftTime} = res.data.data;
    //     dispatch(setTreeList(trees));
    //     dispatch(setTreeLeftTime(leftTime || undefined));
    //     TreeConfig.getInstance().updateTreeData(btcAddress, easterEgg, trees);
    //   } else {
    //     if (105 === res.data.code) {
    //       return
    //     }
    //     errorRetry(res.data?.message || "Failed to get tree list")
    //   }
    // } catch (err) {
    //   errorRetry("Failed to get tree list(catch)");
    // } finally {
    // }
  };

  // 获取矿石列表
  const fetchRockList = async (retryTimes = 3) => {
    // const errorRetry = (msg: string) => {
    //   if (retryTimes > 0) {
    //     toast.error(msg)
    //     setTimeout(() => {
    //       fetchTreeList(retryTimes - 1).then()
    //     }, 3000)
    //   } else {
    //     toast.error("Request failed, Please check your network and try again.")
    //   }
    // }
    // try {
    //   const res = await getRockList();
    //   if (res.data && res.data.code === 1) {
    //     const {rocks, leftTime} = res.data.data;
    //     dispatch(setRockList(rocks));
    //     dispatch(setRockLeftTime(leftTime || undefined));
    //     StoneConfig.getInstance().updateTreeData(rocks);
    //   } else {
    //     if (105 === res.data.code) {
    //       return
    //     }
    //     errorRetry(res.data.msg || "Failed to get rock list");
    //   }
    // } catch (err) {
    //   errorRetry("Failed to get rock list(catch)");
    // }
  };

  // 自动获取（如果启用）
  useEffect(() => {
    if (options.autoFetch) {
      if (btcAddress) {
        fetchTreeList().then();
        fetchRockList().then();
        fetchDropList().then();
        let lock = false;
        const timer = setInterval(async () => {
          if (lock) {
            return;
          }
          const now = Date.now();
          const refreshTime = ItemDropConfig.getInstance().getLastRefreshTime();
          if (refreshTime && now > refreshTime) {
            lock = true;
            await fetchDropList().then();
            lock = false;
          }
        }, 1000);
        return () => {
          clearInterval(timer);
        };
      } else if (!btcAddress) {
        dispatch(setTreeList([]));
        dispatch(setRockList([]));
      }
    }
  }, [btcAddress, options.autoFetch]);

  return {
    refreshDropList: fetchDropList,
    refreshTreeList: fetchTreeList, // 提供手动刷新函数
    refreshRockList: fetchRockList,
  };
};
