import { useCallback, useEffect, useRef, useState } from 'react';
import {
  getBatchNewRankingList,
  getNewRankingList,
  getPublicRankingList,
  getRankingScoreList,
  getRockRankingList,
} from '@/server';
import toast from 'react-hot-toast';

// 定义排行榜数据类型
export interface RankItem {
  rankInfo?: {
    score: number;
    userId: string;
    address: string;
  }[];
  selfRankInfo?: {
    rank: number;
    score: number;
  };
}

interface UseRankOptions {
  pollingInterval?: number; // 轮询间隔，单位毫秒，默认60秒
  initialFetch?: boolean; // 是否在挂载时立即获取数据
  topN?: number; // 排行榜积分列表数量
  manualPolling?: boolean; // 是否手动触发轮询，默认为false（自动轮询）
}

interface RankListByType {
  rank: number;
  rankInfo: {
    score: number;
    rank: number;
    address: string;
  }[];
  score: number;
}

const POLLING_INTERVAL = 3 * 60 * 1000; // 默认3分钟轮询一次

export const useRank = (options: UseRankOptions = {}) => {
  const {
    pollingInterval = POLLING_INTERVAL, // 默认3分钟轮询一次
    initialFetch = true, // 默认挂载时获取数据
    topN = 100, // 默认排行榜积分列表数量
    manualPolling = false, // 默认自动轮询
  } = options;

  const [rankList, setRankList] = useState<RankItem | null>(null);
  const [rockRankList, setRockRankList] = useState<RankItem | null>(null);
  const [fishRankList, setFishRankList] = useState<RankItem | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null); // 用于存储定时器ID
  const [rankListByType, setRankListByType] = useState<RankListByType | null>(null);
  const [noLoginRankData, setNoLoginRankData] = useState<RankListByType | null>(null);

  // 无需登陆查询排行榜
  const getNoLoginRankingList = async (activityTag: string) => {
    try {
      const validTags = ['3101', '3102', '3103'];
      const response = await getPublicRankingList(
        activityTag,
        validTags.includes(activityTag) ? 40 : 25
      );
      const { code, msg, data } = response.data;
      if (code === 1) {
        setNoLoginRankData(data);
        return data;
      } else {
        throw new Error(msg || '获取排行榜失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error : new Error('获取排行榜失败'));
    }
  };

  // 新接口：查询排行榜
  const getRankingListByType = async (type: string) => {
    try {
      setLoading(true);
      const validTags = ['3101', '3102', '3103'];
      const response = await getNewRankingList({
        tag: type,
        topN: validTags.includes(type) ? 40 : 20,
      });
      const { code, msg, data } = response.data;
      if (code === 1) {
        setRankListByType(data);
        return data;
      } else {
        throw new Error(msg || '获取排行榜失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error : new Error('获取排行榜失败'));
    } finally {
      setLoading(false);
    }
  };

  /**
   * @deprecated
   */
  const getRankingListByType2 = async (type: string) => {
    try {
      const validTags = ['3101', '3102', '3103'];
      const response = await getNewRankingList({
        tag: type,
        topN: validTags.includes(type) ? 40 : 20,
      });
      const { code, msg, data } = response.data;
      if (code === 1) {
        return data;
      } else {
        throw new Error(msg || '获取排行榜失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error : new Error('获取排行榜失败'));
    }
  };

  // 查询排行榜独立使用
  const getBatchRankingListByType = async (tag: string) => {
    try {
      const validTags = ['3101', '3102', '3103'];
      const response = await getBatchNewRankingList({
        tag: tag,
        topN: validTags.includes(tag) ? 40 : 20,
      });
      const { code, msg, data } = response.data;
      if (code === 1) {
        return data;
      } else {
        throw new Error(msg || '获取排行榜失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error : new Error('获取排行榜失败'));
    }
  };

  const getRankingList = async () => {
    try {
      const response = await getRankingScoreList(topN);
      if (response.data && response.data.code === 1) {
        // setRankList(response.data.data || null);
        const { rankInfo, rank, score } = response.data.data;
        const selfRankInParam = {
          score: score < 0 ? 0 : score,
          rank: rank < 0 ? '-' : rank,
        };
        const params = {
          ...rankList,
          rankInfo: rankInfo,
          selfRankInfo: selfRankInParam,
        };
        setRankList(params);
      } else {
        throw new Error(response.data?.message || '获取排行榜失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('获取排行榜失败'));
    } finally {
      setLoading(false);
    }
  };
  const getRockRankingListFunc = async () => {
    try {
      const response = await getRockRankingList(topN, 'Pickaxe');
      if (response.data && response.data.code === 1) {
        const { rankInfo, rank, score } = response.data.data;
        const selfRankInParam = {
          score: score < 0 ? 0 : score,
          rank: rank < 0 ? '-' : rank,
        };
        const params = {
          ...rankList,
          rankInfo: rankInfo,
          selfRankInfo: selfRankInParam,
        };
        setRockRankList(params);
      } else {
        throw new Error(response.data?.message || '获取排行榜失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('获取排行榜失败'));
    } finally {
      setLoading(false);
    }
  };
  const getFishRankingList = async () => {
    try {
      const response = await getRockRankingList(topN, 'FishingPole');
      if (response.data && response.data.code === 1) {
        // setRankList(response.data.data || null);
        const { rankInfo, rank, score } = response.data.data;
        const selfRankInParam = {
          score: score < 0 ? 0 : score,
          rank: rank < 0 ? '-' : rank,
        };
        const params = {
          ...rankList,
          rankInfo: rankInfo,
          selfRankInfo: selfRankInParam,
        };
        setFishRankList(params);
      } else {
        throw new Error(response.data?.message || '获取排行榜失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('获取排行榜失败'));
    } finally {
      setLoading(false);
    }
  };

  // 获取矿石排行榜列表
  const onGetPickaxeRankList = async () => {
    try {
      const res = await getRockRankingList(100, 'Pickaxe');
      if (res.data && res.data.code === 1) {
        toast.success('Get Rock Ranking List Success');
      } else {
        toast.error(res.data.msg);
      }
    } catch (error) {
      console.log(error);
    }
  };

  // 获取排行榜数据
  const fetchRankList = useCallback(async () => {
    setLoading(true);
    setError(null);
    await getRankingList();
    await getRockRankingListFunc();
    await getFishRankingList();
  }, []);

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  }, []);

  // 开始轮询
  const startPolling = useCallback(() => {
    // 先停止可能存在的轮询
    stopPolling();

    // 立即获取一次数据
    fetchRankList();

    // 如果不是手动轮询模式，则设置定时器
    if (!manualPolling) {
      intervalIdRef.current = setInterval(fetchRankList, pollingInterval);
    }

    // 返回清理函数
    return stopPolling;
  }, [fetchRankList, pollingInterval, manualPolling, stopPolling]);

  const triggerPolling = useCallback(async () => {
    // 无论是否正在加载，都尝试获取数据
    try {
      setLoading(true);
      const response = await getRankingScoreList(topN);

      if (response.data && response.data.code === 1) {
        setRankList(response.data.data || []);
      } else {
        throw new Error(response.data?.message || '');
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('error'));
    } finally {
      setLoading(false);
    }

    // 如果是自动轮询模式，则重置定时器
    if (!manualPolling && intervalIdRef.current) {
      stopPolling();
      intervalIdRef.current = setInterval(fetchRankList, pollingInterval);
    }
  }, [topN, manualPolling, pollingInterval, stopPolling, fetchRankList]);

  // 组件挂载时开始轮询（如果启用）
  useEffect(() => {
    if (initialFetch) {
      const cleanup = startPolling();
      return cleanup;
    }

    // 组件卸载时清理
    return stopPolling;
  }, [initialFetch, startPolling, stopPolling]);

  return {
    rankList,
    loading,
    error,
    rockRankList,
    fetchRankList, // 手动获取数据的方法
    startPolling, // 手动开始轮询的方法
    stopPolling, // 手动停止轮询的方法
    triggerPolling, // 手动触发一次轮询的方法
    getRankingList,
    getRockRankingListFunc,
    getFishRankingList,
    onGetPickaxeRankList,
    fishRankList,
    getRankingListByType,
    rankListByType,
    getNoLoginRankingList,
    noLoginRankData,
    getRankingListByType2,
    getBatchRankingListByType,
  };
};
