import { useEffect } from 'react';
import { getTtsWhiteList } from '../server';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '../constant/type';
import { setIsTtsWhiteList } from '../store/app';

export default function useTtsWhiteList() {
  const dispatch = useDispatch();
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  useEffect(() => {
    if (!btcAddress) {
      dispatch(setIsTtsWhiteList(false));
      return;
    }
    if (window.localStorage.getItem('isTtsWhiteList')) {
      dispatch(setIsTtsWhiteList(true));
      return;
    }
    getTtsWhiteList().then((res) => {
      dispatch(setIsTtsWhiteList(res.data.data));
    });
  }, [btcAddress]);
}
