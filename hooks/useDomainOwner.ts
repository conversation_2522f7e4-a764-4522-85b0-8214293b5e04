import { useEffect } from 'react';
import { DOMAIN_JUMP } from '../constant';
import { checkDomainHolder } from '../server';
import { useDispatch, useSelector } from 'react-redux';
import { setDomainOwner } from '../store/app';
import { IAppState } from '../constant/type';

export default function useDomainOwner() {
  const dispatch = useDispatch();
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  useEffect(() => {
    if (!btcAddress) {
      return;
    }
    if (
      window.location.search.includes('domainOwner=true') ||
      !!localStorage.getItem('domainOwner')
    ) {
      dispatch(setDomainOwner(true));
      return;
    }
    const domain = window.location.hostname;
    if (domain.includes(DOMAIN_JUMP)) {
      let match = domain.match(/^([^.]+)\./);
      const name = match ? match[1].replace('https://', '') : null; // 如果匹配成功，返回第一个捕获组
      if (!name) {
        return;
      }
      checkDomainHolder().then((res) => {
        dispatch(setDomainOwner(res.data.data));
      });

      /* // 获取当前域名绑定的录制的视频数据
       getTtsInfoBind().then(res => {
         if (res.data.code === 1) {
           dispatch(setTtsBuildData(res.data.data))
         }
       })*/
    }
  }, [btcAddress]);
}
