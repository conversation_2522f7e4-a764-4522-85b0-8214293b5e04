import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getUserBasicInfo } from '@/server';
import { IAppState } from '@/constant/type';
import {
  setAxeParams,
  setEasterEggInfo,
  setUserBasicInfo,
  setWhackAMoleEasterEgg,
} from '@/store/app';
import { decodeBase64ToJSON } from '@/utils/base64';
import { useTaskContext } from 'contexts/TaskContext';
import toast from 'react-hot-toast';
import { usePlayerEnergyDispatch } from '@/contexts/playerEnergyContext';
import { useEnergyConsume } from '@/contexts/energyConsumeContext';

export const useUserBasicInfo = () => {
  const dispatch = useDispatch();
  const { syncTaskProgress, incentivesConfig, getIncentivesConfig } = useTaskContext();
  const { btcAddress, userBasicInfo, axeParams } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const playerEnergyDispatch = usePlayerEnergyDispatch();

  const { updateEnergyConsume } = useEnergyConsume();

  const fetchUserBasicInfo = useCallback(async () => {
    await getIncentivesConfig();
    try {
      const res = await getUserBasicInfo();
      const { code, data, msg } = res.data;
      if (code === 1) {
        // 钓鱼彩蛋
        const fishEasterEgg = data.fishEasterEgg;
        if (fishEasterEgg) {
          const base64FishEasterEgg = decodeBase64ToJSON(fishEasterEgg);
          dispatch(setEasterEggInfo(base64FishEasterEgg));
        } else {
          dispatch(setEasterEggInfo(undefined));
        }

        const basicInfoData = data || {};

        // dispatch(updatePlayerInfo(basicInfoData.playerInfo));
        // updatePlayerInfo(basicInfoData.playerInfo);

        playerEnergyDispatch({
          type: 'UPDATE',
          payload: basicInfoData.playerInfo,
        });

        updateEnergyConsume(basicInfoData.energyConsumeConfigs);
        dispatch(setUserBasicInfo(basicInfoData as any));
        dispatch(
          setAxeParams({
            ...axeParams,
            ...basicInfoData.userItemInfo,
          })
        );

        // 狗头彩蛋
        const dogEasterEgg = basicInfoData.dogEasterEgg as string;
        if (dogEasterEgg.length > 0) {
          const base64DogEasterEgg = decodeBase64ToJSON(dogEasterEgg);
          // dispatch(setDogEasterEgg(base64DogEasterEgg));
          dispatch(setWhackAMoleEasterEgg(base64DogEasterEgg));
        } else {
          // dispatch(setDogEasterEgg(null));
          dispatch(setWhackAMoleEasterEgg(null));
        }
        return basicInfoData;
      } else {
        toast.error(res.data?.msg || res.data?.message);
      }
    } catch (err) {
      return {};
    } finally {
    }
  }, [btcAddress]);

  useEffect(() => {
    if (userBasicInfo?.userTaskProgresses && incentivesConfig.length) {
      // userTaskProgresses: 任务进度信息
      if (userBasicInfo.userTaskProgresses) {
        // 同步服务器任务数据到本地追踪的任务
        syncTaskProgress(userBasicInfo.userTaskProgresses, incentivesConfig);
      }
    }
  }, [userBasicInfo, incentivesConfig]);

  return {
    refreshUserBasicInfo: fetchUserBasicInfo, // 提供手动刷新函数
  };
};
