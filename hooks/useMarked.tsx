import { marked } from 'marked';
import { useCallback } from 'react';
import customHeadingId from 'marked-custom-heading-id';

// 处理特殊的 Markdown 格式
const processMarkdown = (text: string): string => {
  // 处理下划线式标题 (如 "==" 和 "--" 下划线)
  const lines = text.split('\n');
  const processedLines = [];

  for (let i = 0; i < lines.length; i++) {
    processedLines.push(lines[i]);

    // 检查下一行是否为标题下划线格式
    if (i < lines.length - 1) {
      const nextLine = lines[i + 1];
      // 检查是否为 "===" 格式的一级标题
      if (/^=+$/.test(nextLine.trim())) {
        // 替换当前行为 # 格式标题
        processedLines[processedLines.length - 1] = `# ${lines[i]}`;
        i++; // 跳过下划线行
      }
      // 检查是否为 "---" 格式的二级标题
      else if (/^-+$/.test(nextLine.trim())) {
        // 替换当前行为 ## 格式标题
        processedLines[processedLines.length - 1] = `## ${lines[i]}`;
        i++; // 跳过下划线行
      }
    }
  }

  return processedLines.join('\n');
};

export default function useMarked() {
  marked.use({
    async: true,
    pedantic: false,
    gfm: true,
    breaks: false,
    hooks: null,
    tokenizer: null,
    walkTokens: null,
    silent: false,
  });

  const render = new marked.Renderer();
  marked.use({ renderer: render });
  marked.use(customHeadingId());

  const markdownToHtml = useCallback(async (context: string): Promise<string> => {
    try {
      const processedContext = processMarkdown(context);
      const html = await marked.parse(processedContext);
      return html;
    } catch (error) {
      return '';
    }
  }, []);

  return {
    markdownToHtml,
  };
}
