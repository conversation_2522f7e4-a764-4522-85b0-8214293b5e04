import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IBagInventoryItem, IGameState } from '../constant/type';
import { useEffect, useState } from 'react';
import { getBagInventoryList, getMaterialList, getSyntheticsList } from '../server';
import {
  setBagInventoryList,
  setEquipmendResult,
  setMaterialList,
  setSyntheticsList,
} from '@/store/game';
import { ItemConfig } from '@/world/Config/ItemConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { getDurabilityTips } from '@/utils/durabilityTips';

let tempShortcut = 100;

export default function useBagInventory(initGet?: boolean, forceRefresh?: boolean) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const { bagInventoryList, syntheticsList, equipmendResult, materialList } = useSelector(
    (state: { GameReducer: IGameState }) => state.GameReducer
  );

  const [isPending, setIsPending] = useState<boolean>(false);

  // 添加数据加载标记
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  const dispatch = useDispatch();

  // 一次性获取所有数据
  const addNewItem = async (userItemInfo: IBagInventoryItem) => {
    let shortcut = userItemInfo.shortcut as string;
    if (Number(shortcut) > 5 || Number(shortcut) <= 0) {
      tempShortcut++;
      shortcut = String(tempShortcut);
    }
    const newList: IBagInventoryItem[] = [];
    bagInventoryList.forEach((item) => {
      if (item.shortcut !== shortcut) {
        newList.push(item);
      }
    });
    const newItemInfo = {
      ...userItemInfo,
      shortcut,
      isNew: true,
    } as IBagInventoryItem;

    dispatch(setBagInventoryList([...newList, newItemInfo]));
  };

  // 一次性获取所有数据
  const updateDurability = async (userItemId: string, currentDurability: number) => {
    const newList = bagInventoryList.map((item) => {
      if (item.userItemId === userItemId) {
        return {
          ...item,
          currentDurability,
        };
      }
      return item;
    });
    dispatch(setBagInventoryList(newList));
    // 添加耐久度提示
    if (currentDurability !== undefined) {
      getDurabilityTips(currentDurability);
    }
  };

  // 一次性获取所有数据
  const fetchAllData = async () => {
    setIsPending(true);

    try {
      // 并行请求两个接口
      await Promise.all([getBagInventoryListDta(), getMaterialListData()]);

      // 标记数据已加载
      setDataLoaded(true);
    } catch (error) {
      console.error(error);
    } finally {
      setIsPending(false);
    }
  };

  // 获取材料列表
  const getMaterialListData = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    setIsPending(true);

    return getMaterialList()
      .then((res) => {
        const materialList = res.data.data as IBagInventoryItem[];
        if (materialList && materialList.length > 0) {
          let totalCount = materialList.length;
          const newList: IBagInventoryItem[] = [];
          for (let i = 0; i < materialList.length; i++) {
            const item = materialList[i];
            ItemConfig.getInstance().getData(Number(item.tag), (data) => {
              totalCount--;
              const name = data?.name || item.tag;
              const description = data?.description || '';
              const icon = getCdnLink(data?.icon_url || '');
              const newItem = {
                ...item,
                name,
                description,
                icon,
              };
              newList.push(newItem);
              if (totalCount === 0) {
                dispatch(setMaterialList(newList));
              }
            });
          }
        } else {
          dispatch(setMaterialList([]));
        }
        return res;
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 获取可合成的列表
  const getSyntheticsListData = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    // 如果已经有数据且不强制刷新，则跳过
    // if (syntheticsList.length > 0 && !forceRefresh) {
    //   return Promise.resolve();
    // }

    setIsPending(true);

    return getSyntheticsList()
      .then((res) => {
        dispatch(setSyntheticsList(res.data.data || []));
        return res;
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 获取背包物品和已经装备物品
  const getBagInventoryListDta = () => {
    if (!btcAddress) {
      return Promise.resolve();
    }

    setIsPending(true);

    return getBagInventoryList()
      .then((res) => {
        if (res.data.code === 1) {
          // dispatch(setBagInventoryList(packResult));
          // dispatch(setEquipmendResult(equipmedResult));
          const { packResult, equipmedResult } = res.data.data;
          const bagInventoryList = packResult as IBagInventoryItem[];
          if (bagInventoryList) {
            let totalCount = bagInventoryList.length;
            const newList: IBagInventoryItem[] = [];
            for (let i = 0; i < bagInventoryList.length; i++) {
              const item = bagInventoryList[i];

              ItemConfig.getInstance().getData(Number(item.tag), (data) => {
                totalCount--;
                const name = data?.name || item.tag;
                const description = data?.description || '';
                const icon = getCdnLink(data?.icon_url || '');
                //判断是否是发光武器
                const isGlowWeapon = !!data?.effect_url;
                const quality = data?.quality || 0;
                const newItem = {
                  ...item,
                  name,
                  description,
                  icon,
                  isGlowWeapon,
                  quality,
                };
                newList.push(newItem);
                if (totalCount === 0) {
                  dispatch(setBagInventoryList(newList));
                }
              });
            }
          } else {
            dispatch(setBagInventoryList([]));
          }
          dispatch(setEquipmendResult(equipmedResult || []));
        }
        return res;
      })
      .finally(() => {
        setIsPending(false);
      });
  };

  // 初始化时或强制刷新时加载数据
  useEffect(() => {
    if ((initGet && btcAddress && !dataLoaded) || forceRefresh) {
      fetchAllData();
    }
  }, [btcAddress, initGet, forceRefresh]);

  return {
    bagInventoryList,
    syntheticsList,
    equipmendResult,
    materialList,
    addNewItem,
    updateDurability,
    getSyntheticsListData,
    getBagInventoryListDta,
    fetchAllData,
    isPending,
    dataLoaded,
    getMaterialListData,
  };
}
