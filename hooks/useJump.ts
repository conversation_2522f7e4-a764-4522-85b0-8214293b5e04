import { useEffect } from 'react';
import { getDomainOwner } from '../server';
import { DOMAIN_JUMP } from '../constant';
import { useSelector } from 'react-redux';
import { IAppState } from '../constant/type';
import { useRouter } from 'next/router';

export default function useJump() {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const router = useRouter();
  useEffect(() => {
    const domain = window.location.hostname;
    if (
      !btcAddress ||
      (window.location.pathname !== '/avatar' && window.location.pathname !== '/') ||
      !domain.includes(DOMAIN_JUMP)
    ) {
      return;
    }
    let match = domain.split('.');
    const name = match ? match[0] : null;
    if (!name) {
      return;
    }

    getDomainOwner(name).then((res) => {
      if (res.data.data.address === btcAddress && window.location.pathname === '/avatar') {
        router.replace('/');
      } else if (res.data.data.address !== btcAddress && window.location.pathname === '/') {
        router.replace('/avatar');
      }
    });
  }, [btcAddress]);
}
