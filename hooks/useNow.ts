import { useEffect, useState } from 'react';

export default function useNow() {
  // 初始化状态为当前时间的秒数时间戳 使用位运算 ~~ Math.floor
  const [now, setNow] = useState(() => ~~(Date.now() / 1000));
  // 每秒更新一次时间
  useEffect(() => {
    // 定时器，每秒更新一次时间戳
    const timeout = setTimeout(() => {
      setNow(~~(Date.now() / 1000));
    }, 1000);
    // 返回一个清理函数，在组件卸载时清除定时器
    return () => clearTimeout(timeout);
  }, [now]);
  // 返回当前时间戳
  return now;
}
