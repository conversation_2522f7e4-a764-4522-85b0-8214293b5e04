@echo off
REM 设置控制台编码为UTF-8，支持中文和emoji显示
chcp 65001 >nul
setlocal enabledelayedexpansion
REM 设置Python环境变量，确保UTF-8输出
set PYTHONIOENCODING=utf-8

REM JSON 文件上传工具 - Windows 版本 (MD5增量上传)
REM 使用方法:
REM   upload.bat          # 正常上传模式（只上传变化的文件）
REM   upload.bat --init   # 初始化MD5记录模式
REM   upload.bat -i       # 初始化MD5记录模式（简写）

echo ====================================================
echo 📦 JSON 文件上传工具 (MD5增量上传)
echo ====================================================
echo 🔄 只上传MD5发生变化的JSON文件
echo.

REM 检查 Python 环境
echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    set PIP_CMD=pip
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do echo ✅ 找到 Python: %%i
) else (
    python3 --version >nul 2>&1
    if !errorlevel! equ 0 (
        set PYTHON_CMD=python3
        set PIP_CMD=pip3
        for /f "tokens=*" %%i in ('python3 --version 2^>^&1') do echo ✅ 找到 Python3: %%i
    ) else (
        echo ❌ 未找到 Python，请先安装 Python 3.6+
        echo 💡 下载地址: https://www.python.org/downloads/
        pause
        exit /b 1
    )
)

echo.

REM 检查或创建虚拟环境
set VENV_DIR=obs_upload_env
echo 📦 检查虚拟环境...
if not exist "%VENV_DIR%" (
    echo 🔧 创建虚拟环境...
    %PYTHON_CMD% -m venv %VENV_DIR%
    if !errorlevel! neq 0 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
) else (
    echo ✅ 虚拟环境已存在
)

REM 激活虚拟环境
echo 🔌 激活虚拟环境...
call %VENV_DIR%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 激活虚拟环境失败
    pause
    exit /b 1
)

REM 检查依赖
echo 📦 检查依赖...
python -c "import obs" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ OBS SDK 已安装
) else (
    echo ⚠️  未找到 OBS SDK，正在安装...
    pip install esdk-obs-python
    if !errorlevel! equ 0 (
        echo ✅ 依赖安装成功
    ) else (
        echo ❌ 依赖安装失败
        echo 💡 提示：如果遇到权限问题，请尝试以管理员身份运行
        pause
        exit /b 1
    )
)

echo.

REM 检查命令行参数
set INIT_MODE=false
if "%1"=="--init" set INIT_MODE=true
if "%1"=="-i" set INIT_MODE=true

if "%INIT_MODE%"=="true" (
    echo 🔄 MD5记录初始化模式
) else (
    echo 🚀 正常上传模式（只上传变化的文件）
)
echo.

REM 如果是初始化模式，直接运行初始化
if "%INIT_MODE%"=="true" (
    echo 📝 初始化MD5记录...
    python upload_json_files_proxy.py --init
) else (
    REM 正常上传模式，检查 Vite 服务器
    echo 🔗 运行代理模式...
    echo 📝 检查 Vite 开发服务器是否运行...

    REM 使用 PowerShell 检查 HTTP 状态码
    powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://**************:3000' -UseBasicParsing -TimeoutSec 5; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

    if !errorlevel! equ 0 (
        echo ✅ Vite 服务器正在运行
        python upload_json_files_proxy.py
    ) else (
        echo ❌ Vite 服务器未运行
        echo 💡 请先启动开发服务器: npm run dev
    )
)

echo.
echo ✅ 脚本执行完成

REM 退出虚拟环境
call %VENV_DIR%\Scripts\deactivate.bat

echo.
echo 按任意键退出...
pause >nul
