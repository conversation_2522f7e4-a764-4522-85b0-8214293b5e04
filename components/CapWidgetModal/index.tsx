import React, { useEffect, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import styled from 'styled-components';
import CapWidget from '../CapWidget';

const ModalContainer = styled(motion.div)`
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ModalContent = styled(motion.div)`
  background-color: #feefc4;
  width: 350px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border: 8px solid #e8901c;
  border-radius: 42px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 104%;
    height: 113%;
    border: 6px solid #14110a;
    border-radius: 44px;
    transform: translate(-50%, -50%);
    z-index: -1;
  }
`;

const ModalHeader = styled.div`
  margin-bottom: 16px;
  text-align: center;
`;

const ModalTitle = styled.h3`
  font-size: 20px;
  font-weight: 900;
  color: #14110a;
  margin: 0;
`;

const TurnstileContainer = styled.div`
  display: flex;
  justify-content: center;
  margin: 16px 0;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 16px;
`;

const Button = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.cancel {
    background-color: #c1af9c;
    color: #fff;
    margin-right: 12px;
    cursor: pointer;
    width: 140px;
    border-radius: 12px;
    font-weight: 700;
    &:hover {
      background-color: #e0e0e0;
      color: #14110a;
    }
  }

  &.confirm {
    background-color: #fc7922;
    color: white;
    cursor: pointer;
    width: 140px;
    font-weight: 700;
    border-bottom: 4px solid #b5581a;
    box-shadow: 0 2px 0 #b5581a;
    border-radius: 12px;
    &:disabled {
      background-color: #f0a370;
      cursor: not-allowed;
      border-bottom: 4px solid #b5581a;
      box-shadow: 0 2px 0 #b5581a;
    }
  }
`;

const ModalClose = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
`;

const ModalDescription = styled.div`
  margin-bottom: 16px;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: #686663;
`;

interface CloudFlareModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerify: (token: string) => void;
  title?: string;
  confirmText?: string;
  cancelText?: string;
  autoCloseTimeout?: number; // 自动关闭超时时间（毫秒）
  description?: string;
  isFooter?: boolean;
  isKey?: string;
}

const CloudFlareModal: React.FC<CloudFlareModalProps> = ({
  isOpen,
  onClose,
  onVerify,
  title,
  confirmText,
  cancelText,
  autoCloseTimeout = 2 * 60 * 1000, // 默认 2 分钟
  description,
  isFooter = true,
  isKey,
}) => {
  const [token, setToken] = React.useState<string>('');

  const modalOpenTimeRef = useRef<number>(0);

  // 1. 模态框打开/关闭时的副作用
  useEffect(() => {
    if (isOpen) {
      // 模态框打开时记录时间
      modalOpenTimeRef.current = Date.now();
      // 清空之前的状态
      setToken('');
    } else {
      // 模态框关闭时清理
      setToken('');
    }
  }, [isOpen]);

  // 3. 自动关闭超时处理
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (isOpen && autoCloseTimeout > 0) {
      timeoutId = setTimeout(() => {
        // 延迟一秒后关闭，让用户看到提示
        setTimeout(() => {
          onClose();
        }, 1000);
      }, autoCloseTimeout);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isOpen, onClose, autoCloseTimeout]);

  const handleVerify = (newToken: string) => {
    setToken(newToken);
    if (newToken) {
      onVerify(newToken);
      onClose();
    }
  };

  const handleConfirm = () => {
    if (token) {
      onVerify(token);
      onClose();
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 30,
      },
    },
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <ModalContainer
          key="modal-backdrop"
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          // onClick={onClose}
        >
          <ModalContent
            key="modal-content"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <ModalHeader>{title && <ModalTitle>{title}</ModalTitle>}</ModalHeader>
            <ModalClose onClick={onClose}>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M5.39363 5.39168C5.72836 5.05694 6.27108 5.05694 6.60581 5.39168L11.9997 10.7856L17.3936 5.39168C17.7284 5.05694 18.2711 5.05694 18.6058 5.39168C18.9405 5.72641 18.9405 6.26912 18.6058 6.60386L13.2119 11.9978L18.6058 17.3917C18.9405 17.7264 18.9405 18.2691 18.6058 18.6039C18.2711 18.9386 17.7284 18.9386 17.3936 18.6039L11.9997 13.21L6.60581 18.6039C6.27108 18.9386 5.72836 18.9386 5.39363 18.6039C5.05889 18.2691 5.05889 17.7264 5.39363 17.3917L10.7875 11.9978L5.39363 6.60386C5.05889 6.26912 5.05889 5.72641 5.39363 5.39168Z"
                  fill="#767676"
                />
              </svg>
            </ModalClose>
            {description && <ModalDescription>{description}</ModalDescription>}

            <TurnstileContainer>
              <CapWidget onSolve={handleVerify} isKey={isKey} />
            </TurnstileContainer>

            {isFooter && (
              <ButtonContainer>
                <Button className="cancel" onClick={onClose}>
                  {cancelText}
                </Button>
                <Button className="confirm" onClick={handleConfirm} disabled={!token}>
                  {confirmText}
                </Button>
              </ButtonContainer>
            )}
          </ModalContent>
        </ModalContainer>
      )}
    </AnimatePresence>
  );
};

export default CloudFlareModal;
