'use client';
import { memo } from 'react';
import styled from 'styled-components';

interface ProgressProps {
  progress: number;
}

const ProgressContainer = styled.div`
  width: 80%;
  max-width: 25rem;
  height: 0.5rem;
  border: 0.0625rem solid #000000;
  box-sizing: border-box;
  margin-top: 1.25rem;
  position: relative;
`;

const ProgressText = styled.span`
  position: absolute;
  bottom: 0;
  right: 0;
  transform: translate(50%, 100%);
  color: #000;
  font-family: 'JetBrains Mono';
  font-size: 1.25rem;
`;

function Progress({ progress }: ProgressProps) {
  return (
    <ProgressContainer>
      <div
        style={{
          width: `${progress}%`,
          height: '100%',
          background: '#000000',
          position: 'relative',
        }}>
        <ProgressText>{progress}%</ProgressText>
      </div>
    </ProgressContainer>
  );
}

export default memo(Progress);
