import { ShakeXAnimation } from '@/animates';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

const FailedContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  width: 30.75rem;
  height: 20rem;
`;

interface FailedProps {
  isVisible: boolean;
  onComplete?: () => void;
  failedImage?: string;
}

const Failed = ({ isVisible, onComplete, failedImage = '/image/failed.png' }: FailedProps) => {
  const [isShowing, setIsShowing] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setIsShowing(true);
      // 2秒后隐藏
      const timer = setTimeout(() => {
        setIsShowing(false);
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  if (!isShowing) return null;

  return (
    <FailedContainer>
      <ShakeXAnimation>
        <Image
          src={failedImage}
          alt="失败"
          width={515}
          height={397}
          style={{
            width: '32.1875rem',
            height: '24.8125rem',
          }}
        />
      </ShakeXAnimation>
    </FailedContainer>
  );
};

export default Failed;
