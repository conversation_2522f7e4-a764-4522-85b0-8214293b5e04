import { RewardType } from '@/constant/type';

export interface AnimationConfigItem {
  victoryImageSrc: string;
  failureImageSrc: string;
  eventImageSrc: string;
  eventImageActiveSrc: string;
}

type AnimationConfig = Record<RewardType, AnimationConfigItem>;

export const AnimationSequenceConfig: AnimationConfig = {
  [RewardType.wangcai]: {
    eventImageActiveSrc: '/image/easterEgg/wangcai-active.png',
    eventImageSrc: '/image/easterEgg/wangcai.png',
    failureImageSrc: '/image/easterEgg/wangcai-failed.png',
    victoryImageSrc: '/image/easterEgg/wangcai-victory.png',
  },
  [RewardType.TheLonelyBit]: {
    eventImageActiveSrc: '/image/easterEgg/TheLonelyBit-active.png',
    eventImageSrc: '/image/easterEgg/TheLonelyBit.png',
    failureImageSrc: '/image/easterEgg/TheLonelyBit-failed.png',
    victoryImageSrc: '/image/easterEgg/TheLonelyBit-victory.png',
  },
  [RewardType.potato]: {
    eventImageActiveSrc: '/image/easterEgg/potato-active.png',
    eventImageSrc: '/image/easterEgg/potato.png',
    failureImageSrc: '/image/easterEgg/potato-failed.png',
    victoryImageSrc: '/image/easterEgg/potato-victory.png',
  },
  [RewardType.sQUAQ___000]: {
    eventImageActiveSrc: '/image/easterEgg/sQUAQ___000-active.png',
    eventImageSrc: '/image/easterEgg/sQUAQ___000.png',
    failureImageSrc: '/image/easterEgg/sQUAQ___000-failed.png',
    victoryImageSrc: '/image/easterEgg/sQUAQ___000-victory.png',
  },
  [RewardType.sPIZZA___000]: {
    eventImageActiveSrc: '/image/easterEgg/sPIZZA___000-active.png',
    eventImageSrc: '/image/easterEgg/sPIZZA___000.png',
    failureImageSrc: '/image/easterEgg/sPIZZA___000-failed.png',
    victoryImageSrc: '/image/easterEgg/sPIZZA___000-victory.png',
  },
};
