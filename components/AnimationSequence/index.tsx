import { forwardRef, useImperative<PERSON>andle, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import ImageAnimation, { ImageAnimationRef } from './ImageAnimation';
import Victory from './Victory';
import Failed from './Failed';
import ReadyGo from './ReadyGo';
import { motion } from 'motion/react';
import { createParams, getLocalSession, rsaEncrypt } from '@/utils';
import { IAppState, RewardType } from '@/constant/type';
import { useDispatch, useSelector } from 'react-redux';
import { getEasterEggResult, getEggResult } from '@/server';
import toast from 'react-hot-toast';
import { setWhackAMoleEasterEgg } from '@/store/app';
import { AnimationSequenceConfig } from './config';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';

const Container = styled(motion.div)<{ $isPlaying: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

export interface AnimationConfig {
  tag: number;
  interval: number;
  pause: number;
  startTime: number;
  endTime: number;
}

export interface AnimationSequenceProps {
  animationConfigs: AnimationConfig[];
  rewardType?: RewardType;
  readyDuration?: number;
  goDuration?: number;
  onComplete?: (success: boolean) => void;
}

export interface AnimationSequenceRef {
  start: () => void;
  reset: () => void;
}

const AnimationSequence = forwardRef<AnimationSequenceRef, AnimationSequenceProps>(
  (
    {
      animationConfigs,
      readyDuration = 800,
      goDuration = 800,
      onComplete,
      rewardType = RewardType.wangcai,
    },
    ref
  ) => {
    const animationConfig = useMemo(() => {
      const list: AnimationConfig[] = [];
      if (animationConfigs && animationConfigs.length > 0) {
        let startTime = 0;
        let endTime = 0;
        for (let i = 0; i < animationConfigs.length; i++) {
          const item = animationConfigs[i];
          startTime = endTime + item.interval;
          endTime = startTime + item.pause;
          list.push({ ...item, startTime, endTime });
        }
      }
      return list;
    }, [animationConfigs]);
    const [showVictory, setShowVictory] = useState(false);
    const [showFailed, setShowFailed] = useState(false);
    const [showReadyGo, setShowReadyGo] = useState(false);
    const [isVisible, setIsVisible] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);
    const dispatch = useDispatch();
    const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

    const imageAnimationRef = useRef<ImageAnimationRef>(null);
    const timestampRef = useRef<number>(0);

    const handleAnimationComplete = async (
      success: boolean,
      content?: string,
      readyTimestamp?: number,
      firstAnimationStartTimestamp?: number
    ) => {
      try {
        const params = createParams(btcAddress, '/easter/whack-a-mole');
        const encrypted = rsaEncrypt(params);
        const headers = {
          sw: encrypted,
          address: btcAddress,
          session: getLocalSession(btcAddress).sessionId,
          timestamp: firstAnimationStartTimestamp,
        };

        // console.log("headers=========", headers);
        const res = await getEasterEggResult(
          {
            content,
          },
          headers
        );
        // console.log("res.data=========", res.data);
        const { code, msg } = res.data;
        if (code === 1) {
          setTimeout(() => {
            setShowVictory(true);
            imageAnimationRef.current?.reset();
            timestampRef.current = 0;
          }, 1000);
        } else {
          // toast.error(msg);
          setShowFailed(true);
          imageAnimationRef.current?.reset();
          timestampRef.current = 0;
          setIsPlaying(false);
        }
      } catch (error) {
        // console.log("error=========", error);
        setShowFailed(true);
        imageAnimationRef.current?.reset();
        timestampRef.current = 0;
        setIsPlaying(false);
      }
    };

    useImperativeHandle(ref, () => ({
      start: () => {
        setIsVisible(true);
        setShowReadyGo(true);
        setShowFailed(false);
      },
      reset: () => {
        setIsVisible(false);
        setShowVictory(false);
        setShowFailed(false);
        setShowReadyGo(false);
        setIsPlaying(false);
        imageAnimationRef.current?.reset();
      },
    }));

    const imageConfig = useMemo(() => {
      return AnimationSequenceConfig[rewardType];
    }, [rewardType]);

    if (!isVisible) return null;

    return (
      <>
        <Container $isPlaying={isPlaying}>
          <ImageAnimation
            ref={imageAnimationRef}
            onComplete={handleAnimationComplete}
            animationConfig={animationConfig}
            imageConfig={imageConfig}
          />
          <Victory
            isVisible={showVictory}
            onComplete={() => {
              setShowVictory(false);
              const myPlayer = GetMyPlayer();
              myPlayer.callAppApi(AppGameApiKey.showEasterEggRewards, {
                modalStyleType: 'pickaxes',
                subTitle: 'Tap & Stick Fun',
              });
              dispatch(setWhackAMoleEasterEgg(null));
              setIsVisible(false);
            }}
            victoryImage={imageConfig?.victoryImageSrc}
          />
          <Failed
            isVisible={showFailed}
            onComplete={() => {
              setShowFailed(false);
              setIsVisible(false);
              dispatch(setWhackAMoleEasterEgg(null));
            }}
            failedImage={imageConfig?.failureImageSrc}
          />
          {showReadyGo && (
            <ReadyGo
              onComplete={() => {
                setShowReadyGo(false);
                setIsPlaying(true);
                imageAnimationRef.current?.start();
              }}
              readyDuration={readyDuration}
              goDuration={goDuration}
            />
          )}
        </Container>
      </>
    );
  }
);

AnimationSequence.displayName = 'AnimationSequence';

export default AnimationSequence;
