import { ChangeSceneWindowView } from './style';

import React, { useEffect, useMemo, useState } from 'react';
import { SCENE_TYPE } from '../../constant/type';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
} from '../../world/Global/GlobalSpaceEvent';
import * as THREE from 'three';
import HomeLandImg from '../../public/image/scene/homeland.png';
import MyCabinImg from '../../public/image/scene/mycabin.png';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import { DoorConfig } from '@/world/Config/DoorConfig';
import { IS_MOBILE_ENV } from '@/constant';

export default function ChangeSceneWindow({
  screenTypeCallback,
}: {
  screenTypeCallback?: Function;
}) {
  const [sceneType, setSceneType] = useState<SCENE_TYPE>(SCENE_TYPE.Room);
  const [isGame, setIsGame] = useState(false);
  const myPlayer = GetMyPlayer();
  useEffect(() => {
    const unsubscribe1 = GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
      GlobalDataKey.SceneType,
      (sceneType: SCENE_TYPE) => {
        setSceneType(sceneType);
      }
    );
    const unsubscribe2 = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        setIsGame(status == SpaceStatus.Game);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, unsubscribe1);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, unsubscribe2);
    };
  }, []);
  useEffect(() => {
    screenTypeCallback && screenTypeCallback(sceneType);
  }, [sceneType, screenTypeCallback]);

  const showScene: null | { img: string; text: string } = useMemo(() => {
    switch (sceneType) {
      case SCENE_TYPE.Island:
        return {
          img: HomeLandImg.src,
          text: 'My Cabin',
        };
      case SCENE_TYPE.Room:
        return {
          img: MyCabinImg.src,
          text: 'Homeland',
        };
      case SCENE_TYPE.Community:
        return {
          img: MyCabinImg.src,
          text: 'Homeland',
        };
      default:
        return null;
    }
  }, [sceneType]);
  const switchScene = () => {
    let targetDoorId = 1001;
    if (sceneType === SCENE_TYPE.Island) {
      targetDoorId = 2001;
    } else if (sceneType === SCENE_TYPE.Community) {
      targetDoorId = 3001;
    }
    DoorConfig.getInstance().getData(targetDoorId, (doorData) => {
      myPlayer.callAppApi(AppGameApiKey.setLoaderType, doorData.loadingType);
      const targetPosition = new THREE.Vector3(
        doorData.targetPos[0],
        doorData.targetPos[1],
        doorData.targetPos[2]
      );
      const cameraPosition = new THREE.Vector3(
        doorData.targetCamPos[0],
        doorData.targetCamPos[1],
        doorData.targetCamPos[2]
      );
      const cameraDirection = cameraPosition.clone().sub(targetPosition).normalize();
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
        characterType: CharacterType.Player,
        position: targetPosition,
        sceneType: doorData.targetMapId,
        camDirection: cameraDirection,
      });
    });
  };
  return (
    <ChangeSceneWindowView>
      {isGame && showScene !== null && !IS_MOBILE_ENV && (
        <div className="currency-box" onClick={switchScene}>
          <img src={showScene.img} alt="" />
          <div>{showScene.text}</div>
        </div>
      )}
    </ChangeSceneWindowView>
  );
}
