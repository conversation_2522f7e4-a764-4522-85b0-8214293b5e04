import styled from 'styled-components';

export const ChangeSceneWindowView = styled.div`
  font-family: 'JetBrains Mono';
  display: flex;
  position: fixed;
  left: 2rem;
  bottom: 2rem;

  .currency-box {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    & > img {
      width: 12.375rem;
      height: 11.375rem;
    }
    & > div {
      width: 11rem;
      height: 3rem;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #140f0899;
      backdrop-filter: blur(0.5rem);
      font-family: 'JetBrains Mono';
      font-size: 1.75rem;
      font-weight: 500;
      line-height: 1.75rem;
      letter-spacing: -0.04em;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #ffffff;
      border-radius: 1rem;
      margin-top: -0.6875rem;
    }
    &:hover {
      & > div {
        background: #ff8316cc;
      }
    }
  }
`;
