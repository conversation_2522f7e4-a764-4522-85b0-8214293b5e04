'use client';
import { forwardRef, Ref, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import Image from 'next/image';
import styled from 'styled-components';
import Rank2 from '/public/image/rank2.png';
import Rank1 from '/public/image/rank1.png';
import Rank3 from '/public/image/rank3.png';
import Rank4 from '/public/image/rank4.png';
import Rank5 from '/public/image/rank5.png';
import { motion } from 'motion/react';
import { donationConfirmSend, preDonation } from '@/server';
import SignMessage from '@/utils/signMessage';
import toast from 'react-hot-toast';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

// Community rank image mapping
const COMMUNITY_RANK_IMAGES = {
  potato: Rank2.src,
  wangcai: Rank1.src,
  TheLonelyBit: Rank3.src,
  Pizza: Rank4.src,
  DomoDucks: Rank5.src,
};

// Community types type definition
type CommunityType = keyof typeof COMMUNITY_RANK_IMAGES;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
`;

const MenuContent = styled(motion.div)`
  display: flex;
  align-items: center;
  width: calc(100% - 3.75rem);
  justify-content: space-around;
  padding: 1.875rem 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.75rem;
  box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15);
`;

const TextContainer = styled.div`
  padding: 1.875rem 2.25rem;
  box-sizing: border-box;
`;

const Items = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.625rem;

  .dot {
    color: #542d00;
    font-size: 1.25rem;
  }

  .value {
    color: #fff;
    font-size: 1.25rem;
    font-weight: bold;
    border-top: 0.1875rem solid #8c8475;
    box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
    width: 6.25rem;
    height: 2.1875rem;
    background: #c2b8a2;
    border-radius: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;

    input {
      width: 80%;
      height: 80%;
      text-align: center;
      background: transparent;
      border: none;
      color: inherit;
      font-size: inherit;
      font-weight: inherit;
      outline: none;

      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type='number'] {
        -moz-appearance: textfield;
      }
    }
  }
`;

interface ModalProps {
  onClose: () => void;
}

interface DonationInfo {
  donationAmount: number;
  feeTickBalance: string;
  tickBalance: string;
  icon: string;
}

// 土豆社区燃料经费最低下限
const POTATO_MIN_FUEL_FUND = 0.00239999;

export interface DonateTokensRef {
  open: (data: DonationInfo, communityType: CommunityType) => void;
  close: () => void;
  reset: () => void;
}

const DonateTokens = forwardRef<DonateTokensRef, ModalProps>(
  ({ onClose }: ModalProps, ref: Ref<DonateTokensRef>) => {
    const { btcWallet } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
    const [isMounted, setIsMounted] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [donationAmount, setDonationAmount] = useState('');
    const tickBalanceRef = useRef('');
    const feeTickBalanceRef = useRef('');
    const [communityType, setCommunityType] = useState<CommunityType>('potato');
    const [confirmLoading, setConfirmLoading] = useState(false);

    useEffect(() => {
      setIsMounted(true);
      return () => setIsMounted(false);
    }, []);

    const communityText = `Please enter the donation amount.
After confirming the donation, your tokens will be added to the Pizzaswap community pool and used for future airdrops.`;

    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;

      // 允许空值或数字输入
      if (value === '') {
        setDonationAmount('');
        return;
      }

      // 基本数字格式验证（允许数字和一个小数点）
      if (/^\d*\.?\d*$/.test(value)) {
        // 处理小数点后的位数
        const parts = value.split('.');

        if (parts.length === 1) {
          // 没有小数点的情况
          setDonationAmount(value);
        } else if (parts.length === 2) {
          const integerPart = parts[0];
          let decimalPart = parts[1];

          // 限制小数位最多2位
          if (decimalPart.length > 2) {
            decimalPart = decimalPart.slice(0, 2);
          }

          // 移除结尾的0（如果是第二位小数是0）
          if (decimalPart.length === 2 && decimalPart[1] === '0') {
            decimalPart = decimalPart[0];
          }

          // 组合结果
          const result = integerPart + (decimalPart ? '.' + decimalPart : '');
          setDonationAmount(result);
        }
      }
    };

    useImperativeHandle(ref, () => ({
      open: (data: DonationInfo, communityType) => {
        tickBalanceRef.current = data.tickBalance;
        feeTickBalanceRef.current = data.feeTickBalance;

        let balance;

        if (data.donationAmount > 0 && data.tickBalance) {
          balance =
            Number(data.tickBalance) > Number(data.donationAmount)
              ? Number(data.donationAmount)
              : 0;
        }

        setCommunityType(communityType);

        // 先获取两位小数的字符串
        let fixedTickBalance = Number(balance || 0).toFixed(2);
        // 处理尾随零的问题
        if (fixedTickBalance.endsWith('0')) {
          if (fixedTickBalance.endsWith('.00')) {
            // 如果是整数（如1.00），移除小数点和所有零
            fixedTickBalance = fixedTickBalance.replace('.00', '');
          } else if (fixedTickBalance.endsWith('0')) {
            // 如果第二位小数是0（如1.10或1.20），移除最后一个0
            fixedTickBalance = fixedTickBalance.slice(0, -1);
          }
        }
        setDonationAmount(fixedTickBalance);
        setIsOpen(true);
        // 禁止玩家控制
        KeyPressUtil.setEnable(false);
      },
      reset: () => {
        setDonationAmount('');
        feeTickBalanceRef.current = '';
        tickBalanceRef.current = '';
      },
      close: () => {
        setIsOpen(false);
        setDonationAmount('');
        feeTickBalanceRef.current = '';
        tickBalanceRef.current = '';
      },
    }));

    const rankImg = useMemo(() => {
      return COMMUNITY_RANK_IMAGES[communityType] || '';
      return '';
    }, [communityType]);

    if (!isMounted) {
      return null;
    }

    const onConfirm = async (donationAmount: string) => {
      if (Number(donationAmount) > Number(tickBalanceRef.current)) {
        toast.error('Insufficient Pizzaswap balance');
        return;
      }

      if (Number(feeTickBalanceRef.current) < POTATO_MIN_FUEL_FUND) {
        toast.error('Insufficient Pizzaswap gas fee');
        return;
      }

      try {
        setConfirmLoading(true);
        const res = await preDonation(communityType, donationAmount);
        const { code, msg, data } = res.data;
        if (code === 1) {
          const { params, orderId, signMsgs } = data;
          if (signMsgs.length > 0) {
            // const sigs = [];
            //
            // // 循环处理所有签名消息
            // for (const msg of signMsgs) {
            //   // 对每条消息进行签名
            //   const signMsg = await SignMessage.signMessage(btcWallet, msg);
            //   sigs.push(signMsg);
            // }
            const sigs = await SignMessage.multiSignMessage(btcWallet, signMsgs);

            const donationParams = {
              ...params,
              sigs,
            };
            const donationRes = await donationConfirmSend(donationParams, {
              orderId,
            });
            const donationResData = donationRes.data;
            if (donationResData.code === 1) {
              toast.success('Donation Successfully');
              setIsOpen(false);
              setDonationAmount('');
              setConfirmLoading(false);
            } else {
              toast.error(donationResData.msg);
              setConfirmLoading(false);
            }
          }
        } else {
          toast.error(msg);
          setConfirmLoading(false);
        }
      } catch (error) {
        console.error(error);
        setConfirmLoading(false);
      }
    };

    return (
      //@ts-ignore
      <Dialog isOpen={isOpen} onClose={onClose}>
        <ModalContent
          confirmDisabled={donationAmount === '0' || donationAmount === '' || confirmLoading}
          confirmLoading={confirmLoading}
          confirmText="Donate"
          onConfirm={() => {
            onConfirm(donationAmount)
              .then(() => {})
              .finally(() => {
                // 开启玩家控制
                KeyPressUtil.setEnable(true);
              });
          }}
          onClose={() => {
            setIsOpen(false);
            setConfirmLoading(false);
            feeTickBalanceRef.current = '';
            tickBalanceRef.current = '';
            // 开启玩家控制
            KeyPressUtil.setEnable(true);
          }}
          onCancel={() => {
            setIsOpen(false);
            setConfirmLoading(false);
            feeTickBalanceRef.current = '';
            tickBalanceRef.current = '';
            // 开启玩家控制
            KeyPressUtil.setEnable(true);
          }}
          title={
            <Image
              src="/image/tokens.png"
              alt="donate"
              width={360}
              height={62}
              style={{ width: '22.5rem', height: '3.875rem' }}
            />
          }
          footerStyle={{
            padding: '0.625rem 2.625rem 1.875rem',
          }}>
          <ContentContainer>
            <TextContainer>{communityText}</TextContainer>
            <MenuContent
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}>
              <Items>
                <div className="icon">
                  <Image
                    src={rankImg}
                    alt="rank"
                    width={50}
                    height={50}
                    style={{ width: '3.125rem', height: '3.125rem' }}
                  />
                </div>
                <div className="dot">*</div>
                <div className="value">
                  <input
                    type="number"
                    value={donationAmount}
                    onChange={handleAmountChange}
                    placeholder="0"
                  />
                </div>
              </Items>
            </MenuContent>
          </ContentContainer>
        </ModalContent>
      </Dialog>
    );
  }
);
DonateTokens.displayName = 'DonateTokens';
export default DonateTokens;
