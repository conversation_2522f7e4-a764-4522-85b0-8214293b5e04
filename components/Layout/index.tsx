import React from 'react';
import Loading from '../Loading';
import Header from '../Header';
import { useRouter } from 'next/router';
import { createGlobalStyle } from 'styled-components';
import { IS_MOBILE_ENV } from '@/constant';

const headerNotFixedRouts = ['/assets', '/orders'];

const MobileRotateStyle = createGlobalStyle`
  @media screen and (orientation: portrait) {
    body {
      position: relative;
      overflow: hidden;
      width: 100dvh;
      height: 100dvw;
      top: calc((100dvh - 100dvw) / 2);
      left: calc((100dvw - 100dvh) / 2);
      transform: rotate(90deg);
      transform-origin: 50% 50%;
    }
  }
  @media screen and (orientation: landscape) {
    body {
      width: 100dvw;
      height: 100dvh;
      top: 0;
      left: 0;
      transform: none;
      transform-origin: 50% 50%;
    }
  }
`;

/**
 * 查看了改组件的所有引用，均未发现有传递 metaVideoId 和 metaImage,
 * 这两个属性没有使用，可以选择删除
 *
 * 这个组件的功能应该是，编写各个页面之间的通用布局样式，或者通用逻辑处理
 * 原先编写的方式未能够体现这个组件的功能
 * TODO: 现存的 页面都自己写了自己的 layout，可以说是重复代码
 * 每个页面的Header 如果一样 可以直接放到这个layout下
 */
export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();

  const isFixed = headerNotFixedRouts.every((item) => item !== router.pathname);

  return (
    <>
      <Header isFixed={isFixed} />
      {IS_MOBILE_ENV && <MobileRotateStyle />}
      {/* 原HEAD 配置移至 _document.tsx 中 */}
      {children}
      <Loading />
    </>
  );
}
