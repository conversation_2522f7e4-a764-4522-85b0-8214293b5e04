import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled from 'styled-components';
import {
  Body,
  Cell,
  Header,
  HeaderCell,
  HeaderRow,
  Row,
  Table,
} from '@table-library/react-table-library/table';
import { useTheme } from '@table-library/react-table-library/theme';
import Dialog from '@/commons/Dialog';
import { useRank } from '@/hooks/useRank';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { ConfigManager } from '@/world/Config/ConfigManager';
import { getCurrentActiveTab } from '@/utils/activities';

// 自定义主题样式
const customTheme = {
  Table: `
    --data-table-library_grid-template-columns: 6.25rem 1fr 11.25rem !important;
    
    height: 100%;
    margin: 0;
    padding: 0;
    
    .header {
      background-color: #111111;
    }
    
    .tbody {
      background-color: #111111;
    }
    &.scroll {
      overflow: visible !important;
      max-height: none !important;
    }
  `,
  HeaderRow: `
    color: #7DFF81;
    font-weight: 900;
    text-transform: uppercase;
    font-size: 1rem;
    text-align: left;
    height: 3.125rem;
    line-height: 3.125rem;
    margin: 0;
    padding: 0;
    background-color: #111111;
  `,
  Row: `
    height: 3.125rem;
    line-height: 3.125rem;
    background-color: #111111;
    margin: 0;
    padding: 0;
    
    &:nth-of-type(even) {
      background-color: #111111;
    }
    
    /* 移除Row的边框设置，交给Cell处理 */
    &.row-rank-1 {
      color: #ffda0b;
    }
    
    &.row-rank-2 {
      color: #ff9e17;
    }
    
    &.row-rank-3 {
      color: #ff5900;
    }
    
    &.row-rank-4, &.row-rank-5, &.row-rank-6, 
    &.row-rank-7, &.row-rank-8, &.row-rank-9, &.row-rank-10 {
      color: #48a7ff;
    }
    
    &.row-current-user {
      color: #48fff0;
      text-shadow: 0 0 0.5rem rgba(72, 255, 240, 0.6);
      position: relative;
    }
    
    &.empty-row {
      /* 保持空行样式 */
    }
  `,
  Cell: `
    padding: 0.5rem 1rem;
    height: 3.125rem;
    line-height: 2.0625rem;
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
    
    &:first-of-type {
      padding-left: 1rem;
    }
    
    /* 设置单元格的边框样式 */
    .row-rank-1 & {
      color: #ffda0b;
      border: 0.0625rem solid #ffd700;
      text-shadow: 0 0 0.5rem rgba(255, 218, 11, 0.6);
    }
    
    .row-rank-2 & {
      color: #ff9e17;
      border: 0.0625rem solid #ff9900;
      text-shadow: 0 0 0.5rem rgba(255, 153, 0, 0.6);
    }
    
    .row-rank-3 & {
      color: #ff5900;
      border: 0.0625rem solid #ff5500;
      text-shadow: 0 0 0.5rem rgba(255, 89, 0, 0.6);
    }
    
    .row-rank-4 &, .row-rank-5 &, .row-rank-6 &, 
    .row-rank-7 &, .row-rank-8 &, .row-rank-9 &, .row-rank-10 & {
      color: #48a7ff;
      border: 0.03125rem solid rgba(105, 105, 105, 0.6);
    }
    
    .row-current-user & {
      color: #48fff0;
      border: 0.0625rem solid #48fff0;
      text-shadow: 0 0 0.5rem rgba(72, 255, 240, 0.6);
    }
    
    .empty-row & {
      border: none;
    }    
  `,
  HeaderCell: `
    padding: 0.5rem 1rem 0.5rem 0.375rem;
    color: #7DFF81;
    font-weight: 900;
    font-size: 1.375rem;
    text-align: left;
    height: 3.125rem;
    line-height: 2.0625rem;
    margin: 0;
    border: 0.0625rem solid #111;
  `,
};

// 样式容器
const ResourceTableContainer = styled.div`
  width: 60rem;
  /* height: 33.75rem; */
  margin: 0 auto;
  /* background-color: #000000; */
  overflow: hidden;

  /* 添加3D视角适配的响应式设计 */
  @media (max-width: 75rem) {
    transform: scale(0.9);
  }
  @media (max-width: 56.25rem) {
    transform: scale(0.8);
  }

  /* 全局去除表格边距 */

  table {
    border-collapse: collapse !important;
    border-spacing: 0 !important;
  }

  /* 图像渲染优化 */
  image-rendering: -webkit-optimize-contrast;
  backface-visibility: hidden;
  transform: translateZ(0);

  /* 确保边框的渲染方式正确 */

  td {
    border-collapse: collapse;
  }

  /* 确保每行的边框颜色匹配 */

  .row-rank-1 td {
    border-color: #ffd700;
  }

  .row-rank-2 td {
    border-color: #ff9900;
  }

  .row-rank-3 td {
    border-color: #ff5500;
  }

  .rank-cell {
    text-align: center;
    font-weight: 900;
    font-size: 1.375rem;
  }

  .address-cell {
    font-weight: 900;
    font-size: 1.375rem;
  }

  .score-cell {
    font-weight: 900;
    font-size: 1.375rem;
  }

  .row-rank-4 td,
  .row-rank-5 td,
  .row-rank-6 td,
  .row-rank-7 td,
  .row-rank-8 td,
  .row-rank-9 td,
  .row-rank-10 td {
    border-color: rgba(105, 105, 105, 0.6);
  }

  .row-current-user td {
    border-color: #48fff0;
  }

  .current-user-rank {
    position: relative;
  }

  .current-user-rank::before {
    content: '';
    position: absolute;
    left: -0.625rem; /* 调整距离，使箭头位于表格左侧 */
    top: 50%;
    transform: translateY(-50%);
    width: 2.5rem; /* 箭头宽度 */
    height: 2.5rem; /* 箭头高度 */
    background-image: url('/image/user.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 10; /* 确保箭头在最上层 */
  }

  /* 确保相邻单元格的边框不重叠 */

  tr td {
    border-right-width: 0;
  }

  tr td:last-child {
    border-right-width: 0.0625rem;
  }

  /* 防止表格边框出现缝隙 */

  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
`;

// 资源类型
export type ResourceType = 'fish' | 'tree' | 'stone';

// 排行榜数据类型
interface ResourceItem {
  rank: number;
  score: number;
  address: string;
  id: string;
  isCurrentUser?: boolean;
}

// 组件属性
interface ResourceTableProps {
  resourceType: ResourceType;
  onDataUpdate?: (element: HTMLDivElement | null) => void; // 数据更新通知
  timerInterval?: number;
  autoPolling?: boolean;
}

// 引用类型
export interface ResourceTableRef {
  fetchData: () => Promise<void>;
}

export const ResourceTable = forwardRef<ResourceTableRef, ResourceTableProps>(
  ({ resourceType, onDataUpdate, timerInterval = 3, autoPolling = true }, ref) => {
    const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
    const htmlElementRef = useRef<HTMLDivElement | null>(null);
    const [resources, setResources] = useState<ResourceItem[]>([]);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const { getRankingListByType, getNoLoginRankingList } = useRank({
      initialFetch: false,
      manualPolling: false,
    });

    const rankRef = useRef<number>(0);
    const scoreRef = useRef<number>(0);

    //声明一个方法判断是否是当前用户
    const isCurrentUser = (address: string) => {
      return address === btcAddress;
    };

    // 获取数据方法
    const fetchData = async () => {
      ConfigManager.getInstance().getData(async (data) => {
        const menus = data.menus;
        const menu = menus.find((menu) => menu.type === resourceType);
        // 找出当前menu下活跃阶段，然后获取到对应的tab，tab里存在对应的tag
        if (menu) {
          const activeTab = getCurrentActiveTab(menu?.tabs);
          if (activeTab) {
            const tag = activeTab.type;

            try {
              // 提取共同处理逻辑为一个函数
              const processRankingData = (rankInfo: any[], isLoggedIn: boolean) => {
                if (!rankInfo) return;

                const rankings = rankInfo.map((item: any) => ({
                  address: item.address,
                  score: item.score,
                  rank: item.rank,
                  id: item.address,
                  isCurrentUser: isLoggedIn && isCurrentUser(item.address),
                }));

                setResources(rankings);

                if (onDataUpdate) {
                  setTimeout(() => {
                    onDataUpdate(htmlElementRef.current);
                  }, 1000);
                }
              };

              // 根据登录状态获取不同的排名数据
              const { rankInfo, rank, score } = btcAddress
                ? await getRankingListByType(tag)
                : await getNoLoginRankingList(tag);

              rankRef.current = rank;
              scoreRef.current = score;
              processRankingData(rankInfo, !!btcAddress);
            } catch (error) {
              console.error(`Failed to fetch ${resourceType} ranking data:`, error);
            }
          }
        }
      });
    };

    // 定时器管理
    const onStartTimer = () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      timerRef.current = setInterval(
        () => {
          const randomDelay = Math.random() * 6000;
          setTimeout(() => {
            fetchData().then();
          }, randomDelay);
        },
        timerInterval * 60 * 1000
      );
    };

    const onStopTimer = () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };

    // 初始数据加载和定时器设置
    useEffect(() => {
      // 初始化数据
      fetchData();

      if (autoPolling) {
        onStartTimer();
      }
      return () => {
        onStopTimer();
      };
    }, [autoPolling, resourceType, btcAddress]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      fetchData,
    }));

    const tableTheme = useTheme(customTheme);

    // 修改显示数据处理部分
    const displayData = useMemo(() => {
      if (resources.length) {
        // 复制排名数据，但修改前10名中的isCurrentUser标记
        const rankingsData = resources.map((item) => ({
          ...item,
          // 取消前10名中用户的isCurrentUser标记
          isCurrentUser: false,
        }));

        // 只取前10条数据
        const top10Data = rankingsData.slice(0, 10);

        // 计算前10行需要的空行数
        const emptyRowsCount = Math.max(0, 10 - top10Data.length);

        // 创建空行
        const emptyRows = Array(emptyRowsCount)
          .fill(null)
          .map((_, index) => ({
            id: `empty-${index}`,
            rank: -1,
            address: '',
            score: 0,
            isEmpty: true,
          }));

        // 查找用户在排名中的位置
        // const userItem = resources.find((item) => isCurrentUser(item.address));

        // 只有在用户已登录时才添加底部用户行
        if (btcAddress && rankRef.current && scoreRef.current) {
          // 创建显示在底部的当前用户，确保isCurrentUser为true
          const currentUser = {
            id: 'current-user',
            rank: rankRef.current < 0 ? '-' : rankRef.current,
            address: btcAddress,
            score: scoreRef.current < 0 ? '-' : scoreRef.current,
            isCurrentUser: true, // 只有底部显示用户保持true
          };

          // 无论用户是否在排行榜中，都添加底部用户行
          return {
            nodes: [...top10Data, ...emptyRows, currentUser],
          };
        } else {
          // 用户未登录，只返回排名数据和空行
          return {
            nodes: [...top10Data, ...emptyRows],
          };
        }
      }
      return {
        nodes: [],
      };
    }, [resources, btcAddress, rankRef.current, scoreRef.current]);

    const formatAddress = (address: string, left: number = 8, right: number = 8) => {
      return address.toUpperCase().slice(0, left) + '...' + address.toUpperCase().slice(-right);
    };

    return (
      <ResourceTableContainer ref={htmlElementRef}>
        <Table
          data={displayData}
          theme={tableTheme}
          layout={{
            custom: true,
            isScrollable: false,
          }}>
          {(tableList: any) => (
            <>
              <Header>
                <HeaderRow>
                  <HeaderCell>RANK</HeaderCell>
                  <HeaderCell>ADDRESS</HeaderCell>
                  <HeaderCell>SCORE</HeaderCell>
                </HeaderRow>
              </Header>

              <Body>
                {resources.length === 0 ? (
                  <Row className="empty-row">
                    <Cell
                      colSpan={3}
                      style={{
                        height: '30.625rem',
                        backgroundColor: '#111111',
                        border: 'none',
                        width: '60rem',
                      }}></Cell>
                  </Row>
                ) : (
                  tableList.map((item: any) => (
                    <Row
                      key={item.id}
                      item={item}
                      className={
                        item.isEmpty
                          ? 'empty-row'
                          : item.isCurrentUser
                            ? 'row-current-user'
                            : `row-rank-${item.rank}`
                      }>
                      <Cell
                        className={`rank-cell ${item.isCurrentUser ? 'current-user-rank' : ''}`}>
                        {item.isEmpty ? '' : item.rank}
                      </Cell>
                      <Cell className="address-cell">
                        {item.address && formatAddress(item.address, 10, 18)}
                      </Cell>
                      <Cell className="score-cell">
                        {item.isEmpty ? '' : item.score.toLocaleString()}
                      </Cell>
                    </Row>
                  ))
                )}
              </Body>
            </>
          )}
        </Table>
      </ResourceTableContainer>
    );
  }
);

ResourceTable.displayName = 'ResourceTable';

// Modal组件部分保持不变
interface ResourceTableModalProps {
  resourceType: ResourceType;
  onClose: () => void;
}

export interface ResourceTableModalRef {
  open: () => void;
  refresh: () => void;
}

const ResourceTableModal = React.forwardRef<ResourceTableModalRef, ResourceTableModalProps>(
  ({ resourceType, onClose }: ResourceTableModalProps, ref) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const tableContainerRef = useRef<HTMLDivElement>(null);
    const tableRef = useRef<ResourceTableRef>({
      fetchData: () => Promise.resolve(),
    });

    useImperativeHandle(ref, () => ({
      open: () => {
        setIsOpen(true);
      },
      refresh: () => {
        if (tableRef.current.fetchData) {
          tableRef.current.fetchData();
        }
      },
    }));

    const handleScreenshot = () => {
      if (tableContainerRef.current) {
      }
    };

    return (
      <Dialog
        isOpen={isOpen}
        onClose={() => {
          setIsOpen(false);
          onClose();
        }}>
        <div ref={tableContainerRef}>
          <ResourceTable
            ref={tableRef}
            resourceType={resourceType}
            timerInterval={1}
            autoPolling={false}
          />
        </div>
        {/* 工具栏 */}
        <div>
          <button onClick={handleScreenshot}>截图</button>
        </div>
      </Dialog>
    );
  }
);

ResourceTableModal.displayName = 'ResourceTableModal';

export { ResourceTableModal };
