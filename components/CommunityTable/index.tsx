import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Body, Cell, Row, Table } from '@table-library/react-table-library/table';
import { useTheme } from '@table-library/react-table-library/theme';
import Image from 'next/image';
import Dialog from '@/commons/Dialog';
import { useRank } from '@/hooks/useRank';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { ConfigManager } from '@/world/Config/ConfigManager';
import { CommunityTableContainer } from './CommunityTable.styles';
import { COMMUNITY_CONFIG, CommunityKey } from '../Ranking/components/communityConfig';

// 社区数据类型
interface Community {
  rank: number;
  score: number;
  address: string;
  id: string;
  avatarUrl: string;
}

export interface CommunityTableRef {
  fetchData: () => Promise<void>;
}

// 组件属性
interface CommunityTableProps {
  onDataUpdate?: (element: HTMLDivElement | null) => void; // 数据更新通知
  // 定时器时间 默认3分钟更新数据
  timerInterval?: number;
  autoPolling?: boolean; // 是否自动开启轮询
}

export const CommunityTable = forwardRef<CommunityTableRef, CommunityTableProps>(
  ({ onDataUpdate, timerInterval = 3, autoPolling = true }, ref) => {
    const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

    const [communities, setCommunities] = useState<Community[]>([]);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const { getRankingListByType, getNoLoginRankingList } = useRank({
      initialFetch: false,
      manualPolling: false,
    });
    const htmlElementRef = useRef<HTMLDivElement | null>(null);

    // 获取数据方法
    const fetchData = async () => {
      try {
        let type = '';
        ConfigManager.getInstance().getData((data) => {
          const menus = data.menus;
          const menu = menus.find((item) => item.name === 'menu-0');
          if (menu) {
            const tabs = menu.tabs;
            type = tabs[0].type;
          }
        });
        // 根据登录状态选择API方法
        const { rankInfo } = btcAddress
          ? await getRankingListByType(type)
          : await getNoLoginRankingList(type);

        if (!rankInfo) return;

        // 设置头像URL的辅助函数 - 使用COMMUNITY_CONFIG
        const getAvatarUrl = (address: string) => {
          const config = COMMUNITY_CONFIG[address as CommunityKey];
          return config?.avatar || '';
        };

        // 处理数据
        const list = rankInfo.map((item: any) => ({
          address: item.address,
          score: item.score,
          rank: item.rank,
          id: item.address,
          avatarUrl: getAvatarUrl(item.address) || '',
        }));

        setCommunities(list);

        // 通知外部数据已更新
        if (onDataUpdate) {
          setTimeout(() => {
            onDataUpdate(htmlElementRef.current);
          }, 1000);
        }
      } catch (error) {
        console.error('Failed to fetch community ranking data:', error);
      }
    };

    // 启动定时器
    const onStartTimer = () => {
      // 先清除可能存在的旧定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // 设置新定时器
      timerRef.current = setInterval(fetchData, timerInterval * 60 * 1000);
    };

    // 停止定时器
    const onStopTimer = () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };

    // 根据autoPolling属性控制定时器
    useEffect(() => {
      // 总是先获取一次数据
      fetchData();

      // 根据autoPolling决定是否启动定时器
      if (autoPolling) {
        onStartTimer();
      }

      // 清理函数
      return () => {
        onStopTimer();
      };
    }, [autoPolling, btcAddress]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      fetchData, // 手动更新数据
    }));

    // 创建数据节点
    const data = useMemo(
      () => ({
        nodes: communities,
      }),
      [communities]
    );

    // 表格主题
    const theme = useTheme({
      Table: `
      --data-table-library_grid-template-columns: 1fr 3fr 2fr;
    `,
      HeaderRow: `
      display: none;
    `,
      Row: `
      font-size: 1.25rem;
      
      &:hover {
        opacity: 0.9;
      }
    `,
      Cell: `
      padding: 1rem;
      text-align: center;
    `,
    });

    return (
      <CommunityTableContainer ref={htmlElementRef}>
        <Table data={data} theme={theme}>
          {(tableList: Community[]) => (
            <Body>
              {tableList.map((item) => (
                <Row key={item.address} item={item} data-rank={item.rank}>
                  <Cell className="rank-cell">
                    <span data-rank={item.rank}>{item.rank}</span>
                  </Cell>
                  <Cell className="name-cell">
                    <div className="avatar-container">
                      <div className="avatar-image" data-rank={item.rank}>
                        {item.avatarUrl && (
                          <Image
                            src={item.avatarUrl}
                            alt={item.address}
                            width={60}
                            height={60}
                            style={{
                              width: '3.75rem',
                              height: '3.75rem',
                            }}
                            className="avatar-image-img"
                            priority={item.rank <= 3}
                            crossOrigin="anonymous"
                            loading="eager"
                          />
                        )}
                      </div>
                      <span className="community-name" data-rank={item.rank}>
                        {item.address}
                      </span>
                    </div>
                  </Cell>
                  <Cell className="score-cell">
                    <span className="score-value" data-rank={item.rank}>
                      {item.score.toLocaleString()}
                    </span>
                  </Cell>
                </Row>
              ))}
            </Body>
          )}
        </Table>
      </CommunityTableContainer>
    );
  }
);
CommunityTable.displayName = 'CommunityTable';

interface CommunityTableModalProps {
  onClose: () => void;
}

export interface CommunityTableModalRef {
  open: () => void;
  refresh: () => void;
}

const CommunityTableModal = React.forwardRef<CommunityTableModalRef, CommunityTableModalProps>(
  ({ onClose }: CommunityTableModalProps, ref) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const tableRef = useRef<CommunityTableRef>({
      fetchData: () => Promise.resolve(),
    });

    useImperativeHandle(ref, () => ({
      open: () => {
        setIsOpen(true);
      },
      refresh: () => {
        // 如果有外部传入的刷新方法，调用它
        if (tableRef.current.fetchData) {
          tableRef.current.fetchData();
        }
      },
    }));

    return (
      //@ts-ignore
      <Dialog
        isOpen={isOpen}
        onClose={() => {
          setIsOpen(false);
          onClose();
        }}>
        <CommunityTable ref={tableRef} timerInterval={1} autoPolling={true} />
      </Dialog>
    );
  }
);

CommunityTableModal.displayName = 'CommunityTableModal';

export default CommunityTableModal;
