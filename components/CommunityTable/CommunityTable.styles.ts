import styled from 'styled-components';

// 排名主题色配置
export const RANK_THEME_COLORS = {
  1: '#ffda0b', // 第一名黄色
  2: '#ff9e17', // 第二名橙色
  3: '#ff5900', // 第三名红色
  4: '#48A7FF', // 第四名蓝色
  5: '#48A7FF', // 第五名蓝色
  default: '#cccccc', // 默认灰色
};

// 获取特定排名的主题色
export const getRankThemeColor = (rank: number): string => {
  return RANK_THEME_COLORS[rank as keyof typeof RANK_THEME_COLORS] || RANK_THEME_COLORS.default;
};

// 表格外层容器样式
export const CommunityTableContainer = styled.div`
  /* 16:9比例设置 */
  width: 60rem;
  margin: 0 auto;

  /* 添加3D视角适配的响应式设计 */
  @media (max-width: 75rem) {
    transform: scale(0.9);
  }
  @media (max-width: 56.25rem) {
    transform: scale(0.8);
  }

  /* 表格样式 */
  table {
    width: 100%;
    height: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: transparent;
  }

  td {
    padding: 0.75rem;
    font-size: 2rem;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    background-color: #111111;
    text-shadow: 0 0 0.3125rem rgba(255, 255, 255, 0.2);
  }

  /* 使用data-rank属性选择器替代硬编码类名 */
  tr[data-rank] td {
    /* 基础共享样式 */
    padding: 0.75rem 1rem;
  }

  /* 第一名样式 */
  tr[data-rank='1'] td {
    border: 0.125rem solid ${RANK_THEME_COLORS[1]};
    padding: 1.875rem 1rem;
    height: 8.75rem;
    /* 特定td的样式 */
    &:nth-child(2) {
      border: 0.03125rem solid ${RANK_THEME_COLORS[1]};
    }
  }

  /* 第二、三名样式 */
  tr[data-rank='2'] td,
  tr[data-rank='3'] td {
    border: 0.09375rem solid ${RANK_THEME_COLORS[2]};
    padding: 1.375rem 1rem;
    height: 7.5rem;
    &:nth-child(2) {
      border: 0.03125rem solid ${RANK_THEME_COLORS[2]};
    }
  }

  /* 第四、五名样式 */
  tr[data-rank='4'] td,
  tr[data-rank='5'] td {
    border: 0.0625rem solid #747474;
    padding: 0.875rem 1.875rem;
    height: 6.25rem;
    &:nth-child(2) {
      border: 0.03125rem solid #747474;
    }
  }

  /* 排名单元格样式 */
  .rank-cell span {
    font-weight: bold;
  }

  /* 使用data-rank动态设置排名颜色 */
  .rank-cell span[data-rank='1'] {
    color: ${RANK_THEME_COLORS[1]};
  }
  .rank-cell span[data-rank='2'] {
    color: ${RANK_THEME_COLORS[2]};
  }
  .rank-cell span[data-rank='3'] {
    color: ${RANK_THEME_COLORS[3]};
  }
  .rank-cell span[data-rank='4'],
  .rank-cell span[data-rank='5'] {
    color: #48a7ff !important;
  }

  /* 头像和名称容器 */
  .avatar-container {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 1rem;
  }

  /* 头像基本样式 */
  .avatar-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
    }
  }
  /* 头像发光效果去除（第四、五名） */
  .avatar-image[data-rank='4'],
  .avatar-image[data-rank='5'] {
    box-shadow: none !important;
  }
  .avatar-image[data-rank='4']::before,
  .avatar-image[data-rank='5']::before {
    display: none !important;
    box-shadow: none !important;
  }

  /* 根据排名设置头像样式 */
  .avatar-image[data-rank='1'] {
    width: 6.25rem;
    height: 7.5rem;
    &::before {
      width: 5rem;
      height: 5rem;
      box-shadow: 0 0 0.5rem 0.5rem rgba(255, 218, 11, 0.5);
    }
    img {
      width: 5rem !important;
      height: 5rem !important;
      border: 0.25rem solid ${RANK_THEME_COLORS[1]};
      border-radius: 50%;
    }
  }
  .avatar-image[data-rank='2'],
  .avatar-image[data-rank='3'] {
    width: 5.625rem;
    height: 5.625rem;
    &::before {
      width: 4.375rem;
      height: 4.375rem;
      box-shadow: 0 0 0.375rem 0.375rem rgba(255, 158, 23, 0.45);
    }
    img {
      width: 4.375rem !important;
      height: 4.375rem !important;
      border: 0.1875rem solid ${RANK_THEME_COLORS[2]};
      border-radius: 50%;
    }
  }
  .avatar-image[data-rank='3'] {
    &::before {
      box-shadow: 0 0 0.25rem 0.25rem rgba(255, 89, 0, 0.4);
    }
    img {
      border: 0.1875rem solid ${RANK_THEME_COLORS[3]};
    }
  }

  /* 社区名称样式 */
  .community-name {
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.0625rem;
    text-shadow: 0 0 0.5rem rgba(255, 255, 255, 0.3);
  }
  .community-name[data-rank='1'] {
    font-size: 2.5rem;
    color: ${RANK_THEME_COLORS[1]} !important;
  }
  .community-name[data-rank='2'] {
    font-size: 2.125rem;
    color: ${RANK_THEME_COLORS[2]} !important;
  }
  .community-name[data-rank='3'] {
    font-size: 1.75rem;
    color: ${RANK_THEME_COLORS[3]} !important;
  }
  .community-name[data-rank='4'],
  .community-name[data-rank='5'] {
    font-size: 1.5rem;
    color: #48a7ff !important;
  }

  /* 分数样式 */
  .score-value {
    font-weight: bold;
    text-shadow: 0 0 0.625rem rgba(255, 215, 0, 0.4);
  }
  .score-value[data-rank='1'] {
    font-size: 2.5rem;
    color: ${RANK_THEME_COLORS[1]};
  }
  .score-value[data-rank='2'] {
    font-size: 2.125rem;
    color: ${RANK_THEME_COLORS[2]};
  }
  .score-value[data-rank='3'] {
    font-size: 1.75rem;
    color: ${RANK_THEME_COLORS[3]};
  }
  .score-value[data-rank='4'],
  .score-value[data-rank='5'] {
    font-size: 1.5rem;
    color: #48a7ff !important;
  }

  /* 增强视觉层次感 */
  tr[data-rank='1'] {
    font-size: 1.2em;
    transform: translateZ(1.25rem) scale(1.08);
  }

  tr[data-rank='2'] {
    font-size: 1.1em;
    transform: translateZ(0.625rem) scale(1.04);
  }

  tr[data-rank='3'] {
    font-size: 1em;
    transform: translateZ(0.3125rem) scale(1.02);
  }

  tr[data-rank='4'],
  tr[data-rank='5'] {
    font-size: 0.95em;
    transform: translateZ(0.125rem) scale(1.01);
  }

  /* 添加这些CSS规则以提高html2canvas渲染质量 */
  image-rendering: -webkit-optimize-contrast;
  backface-visibility: hidden;
  transform: translateZ(0);
`;
