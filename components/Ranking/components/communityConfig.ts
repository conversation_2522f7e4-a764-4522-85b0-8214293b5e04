import rank1 from '/public/image/rank1.png';
import rank2 from '/public/image/rank2.png';
import rank3 from '/public/image/rank3.png';
import rank4 from '/public/image/rank4.png';
import rank5 from '/public/image/rank5.png';
import WangcaiIcon from '@/commons/WangcaiIcon';
import PotatoIcon from '@/commons/PotatoIcon';
import TheLonelyBitIcon from '@/commons/TheLonelyBitIcon';
import PizzaIcon from '@/commons/PizzaIcon';
import DomoDucksIcon from '@/commons/DomoDucksIcon';

export const COMMUNITY_CONFIG = {
  wangcai: {
    key: 'wangcai',
    name: '旺财',
    avatar: rank1.src,
    icon: WangcaiIcon,
    borderColor: '#ffcd04',
    bgColor: '#ffcd04',
    bgImage: '/image/1.png',
    npcId: 3003,
    className: 'community-wangcai',
  },
  potato: {
    key: 'potato',
    name: '<PERSON><PERSON><PERSON>',
    avatar: rank2.src,
    icon: PotatoIcon,
    borderColor: '#a3a9bc',
    bgColor: '#a3a9bc',
    bgImage: '/image/2.png',
    npcId: 3004,
    className: 'community-potato',
  },
  TheLonelyBit: {
    key: 'TheLonelyBit',
    name: 'TheLonelyBit',
    avatar: rank3.src,
    icon: TheLonelyBitIcon,
    borderColor: '#cf8952',
    bgColor: '#cf8952',
    bgImage: '/image/3.png',
    npcId: 3002,
    className: 'community-thelonelybit',
  },
  Pizza: {
    key: 'Pizza',
    name: 'Pizza',
    avatar: rank4.src,
    icon: PizzaIcon,
    borderColor: '#14110a',
    bgColor: '#14110a',
    bgImage: '/image/4.png',
    npcId: 3005,
    className: 'community-pizza',
  },
  DomoDucks: {
    key: 'DomoDucks',
    name: 'DomoDucks',
    avatar: rank5.src,
    icon: DomoDucksIcon,
    borderColor: '#14110a',
    bgColor: '#14110a',
    bgImage: '/image/5.png',
    npcId: 3006,
    className: 'community-domoducks',
  },
};

export type CommunityKey = keyof typeof COMMUNITY_CONFIG;

// 全局动画配置参数
export const animationConfig = {
  // 主要卡片动画
  card: {
    initialY: 80, // 初始Y位置偏移
    duration: 0.5, // 持续时间
    stiffness: 120, // 弹性系数 (spring效果)
    // 使用函数动态计算延迟，支持任意排名
    getDelay: (rank: number) => {
      // 排名越高延迟越长，最小0.1秒
      return Math.max(0.5 - (rank - 1) * 0.1, 0.1);
    },
  },
  // 图标动画
  icon: {
    duration: 0.3, // 持续时间
    // 使用函数动态计算延迟，支持任意排名
    getDelay: (rank: number) => {
      return Math.max(0.5 - (rank - 1) * 0.1, 0.1);
    },
  },
  // 信息动画
  info: {
    initialX: -20, // 初始X位置偏移
    duration: 0.3, // 持续时间
    // 使用函数动态计算延迟，支持任意排名
    getDelay: (rank: number) => {
      return Math.max(0.5 - (rank - 1) * 0.1, 0.1);
    },
  },
};

/**
 * 获取动画延迟
 * @param rank 排名（1-n）
 * @param type 'rank' | 'icon' | 'info'
 * @returns number
 */
export function getCommunityAnimationDelay(rank: number, type: 'rank' | 'icon' | 'info'): number {
  // 优先使用计算函数
  if (type === 'rank') {
    return animationConfig.card.getDelay(rank);
  } else if (type === 'icon') {
    return animationConfig.icon.getDelay(rank);
  } else if (type === 'info') {
    return animationConfig.info.getDelay(rank);
  }
  return 0;
}
