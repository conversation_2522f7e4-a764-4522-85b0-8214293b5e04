import { setMenuType } from '@/store/app';
import { motion } from 'motion/react';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import styled from 'styled-components';
import { RankingConfig } from '@/world/Config/RankingConfig';

const ModalMenu = styled.div`
  position: absolute;
  left: -10%;
  top: 0%;
  transform: translate(-60%, 0%);
  display: flex;
  flex-direction: column;
  perspective: 50rem;
  gap: 0.125rem;
`;

const MenuItem = styled(motion.div)<{ $isActive: boolean }>`
  content: '';
  display: block;
  width: 10rem;
  height: 4.5rem;
  position: relative;
  cursor: pointer;
  transform-style: preserve-3d;

  &.menu-0 .menu-0-bg {
    background: url('/image/menu-0.png') no-repeat center center;
    background-size: contain;
    z-index: 2;
    width: 100%;
    height: 100%;
  }

  &.menu-1 .menu-1-bg {
    background: url('/image/menu-1.png') no-repeat center center;
    background-size: contain;
    z-index: 2;
    width: 100%;
    height: 100%;
    position: relative;
  }

  &.menu-2 .menu-2-bg {
    background: url('/image/menu-2.png') no-repeat center center;
    background-size: contain;
    z-index: 2;
    width: 100%;
    height: 100%;
  }

  &.menu-3 .menu-3-bg {
    background: url('/image/menu-3.png') no-repeat center center;
    background-size: contain;
    z-index: 2;
    width: 100%;
    height: 100%;
  }

  &.menu-4 .menu-4-bg {
    background: url('/image/menu-4.png') no-repeat center center;
    background-size: inherit; // 继承父元素的背景大小
    z-index: 2;
    width: 100%;
    height: 100%;
    cursor: not-allowed;
  }

  &::before {
    content: '';
    display: block;
    width: calc(100% - 0.625rem);
    height: calc(100% - 0.625rem);
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    border-top-left-radius: 2.25rem;
    border-bottom-left-radius: 2.25rem;
    border-top-right-radius: 1.125rem;
    border-bottom-right-radius: 1.125rem;
    border-width: 0.25rem;
    border-style: solid;
    transition:
      background-color 0.3s,
      border-color 0.3s;
  }

  &.menu-0::before {
    background-color: ${(props) => (props.$isActive ? '#5e784c' : '#9a7658')};
    border-color: ${(props) => (props.$isActive ? '#fff' : '#bd9674')};
  }

  &.menu-1::before {
    background-color: ${(props) => (props.$isActive ? '#5e784c' : '#9a7658')};
    border-color: ${(props) => (props.$isActive ? '#fff' : '#bd9674')};
  }

  &.menu-2::before {
    background-color: ${(props) => (props.$isActive ? '#fc7922' : '#9a7658')};
    border-color: ${(props) => (props.$isActive ? '#fff' : '#bd9674')};
  }

  &.menu-3::before {
    background-color: ${(props) => (props.$isActive ? '#fc7922' : '#9a7658')};
    border-color: ${(props) => (props.$isActive ? '#fff' : '#bd9674')};
  }

  &.menu-4::before {
    background-color: ${(props) => (props.$isActive ? '#fc7922' : '#9a7658')};
    border-color: ${(props) => (props.$isActive ? '#fff' : '#bd9674')};
  }
`;

export type MenuType = 'menu-0' | 'menu-1' | 'menu-2' | 'menu-3' | 'menu-4' | string;

interface MenuProps {
  onMenuClick: (menu: MenuType) => void;
  currentMenu?: MenuType;
}

const Menu: React.FC<MenuProps> = ({ onMenuClick, currentMenu }) => {
  const [activeMenu, setActiveMenu] = useState<MenuType>(currentMenu || 'menu-0');
  const [menuItems, setMenuItems] = useState<Array<{ name: string; active: boolean }>>([]);
  const dispatch = useDispatch();

  // 当外部 currentMenu 变化时，更新内部 activeMenu
  useEffect(() => {
    if (currentMenu && currentMenu !== activeMenu) {
      setActiveMenu(currentMenu as MenuType);
    }
  }, [currentMenu]);

  useEffect(() => {
    // 加载配置数据
    RankingConfig.getInstance().getData((data) => {
      if (data.menus && data.menus.length > 0) {
        setMenuItems(data.menus);

        // 如果没有外部传入的 currentMenu，则从配置中查找活跃菜单
        if (!currentMenu) {
          const activeItem = data.menus.find((item) => item.active);
          if (activeItem && activeItem.name) {
            setActiveMenu(activeItem.name as MenuType);
          }
        }
      }
    });
  }, [currentMenu]);

  useEffect(() => {
    if (activeMenu) {
      onMenuClick(activeMenu);
      dispatch(setMenuType(activeMenu));
    }
  }, [activeMenu, onMenuClick, dispatch]);

  return (
    <ModalMenu>
      {/* 动态渲染菜单项 */}
      {menuItems.map((item, index) => (
        <MenuItem
          key={item.name + index}
          className={item.name || `menu-${index}`}
          $isActive={activeMenu === (item.name as MenuType)}
          onClick={() => {
            if (item.name !== 'menu-4') {
              // 保留 menu-4 的特殊处理
              setActiveMenu(item.name as MenuType);
            }
          }}
          whileTap={{ scale: 0.95 }}
          animate={{
            scale: activeMenu === (item.name as MenuType) ? 1.05 : 0.9,
            x: activeMenu === (item.name as MenuType) ? '0.3125rem' : '0rem',
            z: activeMenu === (item.name as MenuType) ? '1.25rem' : '-1.25rem',
            rotateX: activeMenu === (item.name as MenuType) ? 0 : -5,
            opacity: activeMenu === (item.name as MenuType) ? 1 : 0.8,
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 20,
          }}>
          <div className={`${item.name || `menu-${index}`}-bg`}></div>
        </MenuItem>
      ))}
    </ModalMenu>
  );
};

export default Menu;
