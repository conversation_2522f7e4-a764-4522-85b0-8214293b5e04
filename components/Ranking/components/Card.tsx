import React, { memo, UIEvent, useEffect, useMemo, useRef, useState } from 'react';
// import { isEqual } from "lodash";
import { isEqual } from 'es-toolkit';
import styles from '@root/styles/AnimatedList.module.css';
import AnimatedItem from '@/components/EditAvatarPage/RecordingModal/AssistantQuestion/QuestionHistoryModal/components/AnimatedItem';
import T2 from '/public/image/t2-1.png';
import T2_2 from '/public/image/t2-2.png';
import Yu from '/public/image/yu.png';

import Image from 'next/image';
import styled from 'styled-components';
import { toFormatAccount } from '@/utils';
import { IAppState } from '@/constant/type';
import { useSelector } from 'react-redux';

const ItemContainer = styled.div<{ background?: string }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -1.875rem;
    right: 0;
    bottom: 0;
    width: 6rem;
    height: 100%;
    /* background:  linear-gradient(135deg, #fca346, #ff7e29); 添加渐变效果 */
    background: ${(props) => {
      if (props.background) return props.background;

      return `linear-gradient(135deg,#fca346,#ff7e29)`;
    }};
    border-radius: 1.875rem; /* 使用较大的值确保形成椭圆 */
    box-shadow: 0.125rem 0 0.25rem 0.1875rem rgba(0, 0, 0, 0.1); /* 添加阴影增强立体感 */
  }
`;

// 添加内容容器，确保内容在椭圆上方显示
const ContentContainer = styled.div`
  position: relative;
  z-index: 1; /* 确保内容在伪元素上方 */
  display: flex;
  flex-grow: 1;
  width: 100%;
  height: 5rem;
  align-items: center;
  justify-content: space-around;
  /* gap: 10%; */
  padding-left: 1.875rem;
`;

// 为左侧椭圆添加数字或图标
const LevelIndicator = styled.div`
  position: absolute;
  left: 0%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  color: white;
  font-weight: bold;
  font-size: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.75rem;
  height: 3.75rem;
  text-align: center;
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);

  /* 确保图片和文字都能正确居中显示 */
  @keyframes thing {
    0% {
      background-position: 130%;
      opacity: 1;
    }

    to {
      background-position: -166%;
      opacity: 0;
    }
  }

  .inner {
    width: 100%;
    height: 100%;
    background: transparent;
    overflow: hidden;
    transition: inherit;
    border-radius: 50%;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(-65deg, #0000 40%, #fafafa 50%, #0000 70%);
      background-size: 200% 100%;
      background-repeat: no-repeat;
      animation: thing 3s ease infinite;
      width: 65%;
      height: 75%;
      left: 50%;
      top: 55%;
      transform: translate(-50%, -50%);
      /* 裁剪为六边形形状 */
      clip-path: polygon(
        50% 0%,
        /* 顶部中点 */ 100% 25%,
        /* 右上角 */ 100% 75%,
        /* 右下角 */ 50% 100%,
        /* 底部中点 */ 0% 75%,
        /* 左下角 */ 0% 25% /* 左上角 */
      );
    }

    .rank-icon {
      width: 100%;
      height: 100%;
      object-fit: contain; /* 确保图片适应容器而不变形 */
    }
  }
`;

const AddressContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const AddressText = styled.div`
  font-size: 1.125rem;
  font-weight: bold;
  color: #140f08;
`;

// 奖励数量
const RewardNumber = styled.div`
  font-size: 1.125rem;
  font-weight: bold;
  color: #fff;
  width: 5rem;
  height: 1.875rem;
  border-radius: 0.625rem;
  background: #c2b8a2;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  /* 更强烈的上边框立体效果 */
  box-shadow:
    inset 0rem 0.3125rem 0.25rem 0rem rgba(140, 132, 117, 0.7),
    0rem 0.5rem 0.25rem 0rem rgba(0, 0, 0, 0.1);

  /* 使用渐变背景增强立体感 */
  background-color: #c2b8a2;

  /* 去除所有边框 */
  border: none;
  outline: none;

  .jinbi {
    position: absolute;
    top: 50%;
    left: 0%;
    width: 2.5rem;
    height: 2.5rem;
    transform: translate(-50%, -50%);
  }

  .number {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    left: 0.3125rem;
    color: #fff;
    font-size: 1.25rem;
    font-family: 'JetBrains Mono';
  }
`;

const CurrentRankKingContainer = styled.div`
  position: absolute;
  left: 0;
  bottom: 0;
  border-radius: 1.125rem;
  width: 100%;
  height: 5rem;
  z-index: 100;
  background: #fc8924;
  box-shadow:
    0rem 0.25rem 0rem 0rem #d9c4a3,
    0.125rem 0rem 0rem 0rem #d9c4a3,
    0rem 0.375rem 0.75rem -0.125rem rgba(0, 0, 0, 0.15);
  border-bottom: 0.3125rem solid rgba(181, 100, 27, 0.5);
  overflow: hidden;
`;

const EmptyContainer = styled.div`
  width: 100%;
  height: calc(100% - 3.125rem);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 1rem;
  text-shadow: 0 0 0.03125rem #000;
  font-weight: 400;
  -webkit-text-stroke: 0.03125rem #000;
`;

interface AnimatedListProps {
  items?: { score: number; address: string }[];
  onItemSelect?: (item: string, index: number) => void;
  showGradients?: boolean;
  enableArrowNavigation?: boolean;
  className?: string;
  itemClassName?: string;
  displayScrollbar?: boolean;
  initialSelectedIndex?: number;
  topGradient?: string;
  bottomGradient?: string;
  padding?: string;
  showTopGradient?: boolean;
  showBottomGradient?: boolean;
  displayRank?: number;
  selfRankInfo?: {
    score: number;
    address: string;
    rank: number;
  } | null;
  activityStatus?: string;
}

/**
 * 新需求：
 * 前三名等级图标
 */

// 前三名等级图标
const RankIcon1 = '/image/rank-1.png';
const RankIcon2 = '/image/rank-2.png';
const RankIcon3 = '/image/rank-3.png';
const rank123 = {
  1: {
    src: RankIcon1,
    alt: 'rank-1',
  },
  2: {
    src: RankIcon2,
    alt: 'rank-2',
  },
  3: {
    src: RankIcon3,
    alt: 'rank-3',
  },
};

const RenderRankIcon = styled.div<{ src: string }>`
  background-image: url(${(props) => props.src});
  background-size: cover;
  background-position: center;
`;

// 等级渲染函数：前三名使用图标，其余使用数字
const renderRankLevel = (level: number) => {
  if (level >= 1 && level <= 3) {
    // 前三名使用图标
    const rankIconInfo = rank123[level as 1 | 2 | 3];
    return (
      <div className="inner">
        <RenderRankIcon className="rank-icon" src={rankIconInfo.src} />
      </div>
    );
  } else {
    // 其他名次使用数字
    return level;
  }
};

const CardListItem = memo(
  ({
    background,
    score,
    address,
    level,
  }: {
    background?: string;
    score: number;
    address: string;
    level: number;
  }) => {
    const { menuType } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

    const rewardImage = useMemo(() => {
      if (menuType === 'menu-2') {
        return T2_2.src;
      }
      if (menuType === 'menu-3') {
        return Yu.src;
      }
      return T2.src;
    }, [menuType]);

    return (
      <ItemContainer background={background}>
        <LevelIndicator>{renderRankLevel(level)}</LevelIndicator>

        <ContentContainer>
          <AddressContainer>
            <AddressText>{toFormatAccount(address, 5, 5)}</AddressText>
          </AddressContainer>
          <RewardNumber>
            <Image src={rewardImage} className="jinbi" alt="tree" width={30} height={30} />
            <span className="number">{score}</span>
          </RewardNumber>
        </ContentContainer>
      </ItemContainer>
    );
  }
);
CardListItem.displayName = 'CardListItem';

const AnimatedList: React.FC<AnimatedListProps> = ({
  items = [],
  onItemSelect,
  showGradients = true,
  enableArrowNavigation = true,
  className = '',
  displayScrollbar = true,
  initialSelectedIndex = -1,
  topGradient,
  bottomGradient,
  padding = '0.625rem',
  showTopGradient = true,
  showBottomGradient = true,
  selfRankInfo,
  activityStatus,
}) => {
  const listRef = useRef<HTMLDivElement>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(initialSelectedIndex);
  const [keyboardNav, setKeyboardNav] = useState<boolean>(false);
  const [topGradientOpacity, setTopGradientOpacity] = useState<number>(0);
  const [bottomGradientOpacity, setBottomGradientOpacity] = useState<number>(1);

  const handleScroll = (e: UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    setTopGradientOpacity(Math.min(scrollTop / 50, 1));
    const bottomDistance = scrollHeight - (scrollTop + clientHeight);
    setBottomGradientOpacity(scrollHeight <= clientHeight ? 0 : Math.min(bottomDistance / 50, 1));
  };

  // Keyboard navigation: arrow keys, tab, and enter selection
  useEffect(() => {
    if (!enableArrowNavigation) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown' || (e.key === 'Tab' && !e.shiftKey)) {
        e.preventDefault();
        setKeyboardNav(true);
        setSelectedIndex((prev) => Math.min(prev + 1, items.length - 1));
      } else if (e.key === 'ArrowUp' || (e.key === 'Tab' && e.shiftKey)) {
        e.preventDefault();
        setKeyboardNav(true);
        setSelectedIndex((prev) => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter') {
        if (selectedIndex >= 0 && selectedIndex < items.length) {
          e.preventDefault();
          if (onItemSelect) {
            onItemSelect(items[selectedIndex].address, selectedIndex);
          }
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [items, selectedIndex, onItemSelect, enableArrowNavigation]);

  // Scroll the selected item into view if needed
  useEffect(() => {
    if (!keyboardNav || selectedIndex < 0 || !listRef.current) return;
    const container = listRef.current;
    const selectedItem = container.querySelector(
      `[data-index="${selectedIndex}"]`
    ) as HTMLElement | null;
    if (selectedItem) {
      const extraMargin = 50;
      const containerScrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;
      const itemTop = selectedItem.offsetTop;
      const itemBottom = itemTop + selectedItem.offsetHeight;
      if (itemTop < containerScrollTop + extraMargin) {
        container.scrollTo({ top: itemTop - extraMargin, behavior: 'smooth' });
      } else if (itemBottom > containerScrollTop + containerHeight - extraMargin) {
        container.scrollTo({
          top: itemBottom - containerHeight + extraMargin,
          behavior: 'smooth',
        });
      }
    }
    setKeyboardNav(false);
  }, [selectedIndex, keyboardNav]);

  return (
    <div
      className={`${styles.scrollListContainer} ${className}`}
      style={{
        width: '100%',
      }}>
      <div
        ref={listRef}
        className={`${styles.scrollList} ${!displayScrollbar ? styles.noScrollbar : ''}`}
        onScroll={handleScroll}>
        {items.length ? (
          items.map((item, index) => (
            <AnimatedItem
              key={index}
              delay={0.1}
              index={index}
              onMouseEnter={() => setSelectedIndex(index)}
              onClick={() => {
                setSelectedIndex(index);
                if (onItemSelect) {
                  onItemSelect(item.address, index);
                }
              }}
              border={false}
              marginBottom={index === items.length - 1 ? '5rem' : '0.7rem'}
              padding={padding}>
              <CardListItem score={item.score} address={item.address} level={index + 1} />
            </AnimatedItem>
          ))
        ) : (
          <EmptyContainer>
            {activityStatus === 'upcoming' ? (
              'Event not started'
            ) : activityStatus === 'ongoing' && items.length === 0 ? (
              <>
                <div>No ranking data yet.</div>
                <div>Join the event now and get on the leaderboard!</div>
              </>
            ) : (
              'Event ended'
            )}
          </EmptyContainer>
        )}
      </div>
      {selfRankInfo && (
        <CurrentRankKingContainer>
          <CardListItem
            background="#fc5716"
            score={selfRankInfo?.score || 0}
            address={selfRankInfo?.address || ''}
            level={selfRankInfo?.rank || 0}
          />
        </CurrentRankKingContainer>
      )}
      {showTopGradient && (
        <div
          className={styles.topGradient}
          style={{ opacity: topGradientOpacity, background: topGradient }}></div>
      )}
      {showBottomGradient && (
        <div
          className={styles.bottomGradient}
          style={{ opacity: bottomGradientOpacity, background: bottomGradient }}></div>
      )}
    </div>
  );
};

export default memo(AnimatedList, (prevProps, nextProps) => {
  // 快速检查：如果引用相同，肯定没有变化
  if (prevProps.items === nextProps.items) return true;

  // 如果引用不同，进行深度比较
  return isEqual(prevProps.items, nextProps.items);
});
