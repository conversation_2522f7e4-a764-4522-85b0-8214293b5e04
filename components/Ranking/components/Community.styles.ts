import styled from 'styled-components';

const RankItemHeight = {
  first: 110,
  second: 100,
  third: 100,
  fourth: 100,
  fifth: 100,
};

export const CommunityContainer = styled.div`
  width: 100%;
  height: 27.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .top {
    position: relative;
    width: 55%;
    margin: 0 auto;
    height: 5.125rem;
    .community-title {
      position: absolute;
      z-index: -100;
      width: 25rem;
      height: 6.25rem;
      left: 50%;
      top: -1.25rem;
      transform: translate(-50%, 0);
    }
    .clock-container {
      position: absolute;
      right: 2.1875rem;
      bottom: 20%;
      transform: translate(100%, 0);
      z-index: 10;
      background: #ffffff;
      border-radius: 1.25rem;
      display: flex;
      align-items: center;
      height: 1.875rem;
      padding-left: 1.25rem;
      padding-right: 0.9375rem;
      box-shadow:
        0.25rem 0.25rem 0.25rem 0rem #ffffff inset,
        0rem 0.25rem 0rem 0rem #d9c4a3,
        0.125rem 0rem 0rem 0rem #d9c4a3,
        0rem 0.375rem 0.75rem -0.125rem rgba(0, 0, 0, 0.15);
      .clock-image {
        position: absolute;
        left: -0.5rem;
        top: -0.625rem;
        z-index: 20;
      }
    }
  }
  .bottom {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    max-height: 21.875rem;
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    width: 100%;
    /* 新增：滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #d9c4a3 #f5f5f5;
  }
  .bottom::-webkit-scrollbar {
    width: 0.5rem;
    border-radius: 0.5rem;
    background: #f5f5f5;
  }
  .bottom::-webkit-scrollbar-thumb {
    background: #d9c4a3;
    border-radius: 0.5rem;
  }
  .bottom-content {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
  }

  /* 统一排名基础类 */
  .rank-item {
    position: relative;
    width: calc(100% - 0.3125rem);
    overflow: hidden;
    border-radius: 1.25rem;
    cursor: pointer;

    /* 排名特定高度和样式 */
    &[data-rank='1'] {
      height: 6.875rem;
      border: 0.1875rem solid #ffcd04;
      background: #ffcd04;
    }
    &[data-rank='2'] {
      height: 6.25rem;
      border: 0.1875rem solid #a3a9bc;
      background: #a3a9bc;
    }
    &[data-rank='3'] {
      height: 6.25rem;
      border: 0.1875rem solid #cf8952;
      background: #cf8952;
    }
    &[data-rank='4'],
    &[data-rank='5'] {
      height: 6.25rem;
      border: 0.1875rem solid #14110a;
      background: #14110a;
    }

    /* 排名图标样式 */
    .rank-image {
      position: absolute;

      &[data-rank='1'] {
        left: -0.625rem;
        top: -5%;
        z-index: 100;

        .first-rank-image {
          /* 特殊样式给第一名图标 */
        }
      }

      &[data-rank='2'],
      &[data-rank='3'] {
        left: 0.9375rem;
        top: 0%;
        z-index: 100;
        width: 3.125rem;
        height: 4rem;
        background-size: cover;
      }

      &[data-rank='2'] {
        background: url('/image/2-1.png') no-repeat center center;
        background-size: cover;
      }

      &[data-rank='3'] {
        background: url('/image/3-1.png') no-repeat center center;
        background-size: cover;
      }

      &[data-rank='4'],
      &[data-rank='5'] {
        width: 3.125rem;
        height: 3.125rem;
        border-radius: 50%;
        background: #fca346;
        position: absolute;
        z-index: 20;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.875rem;
        left: 0.9375rem;
        top: 0.375rem;
      }
    }

    /* 排名信息样式 */
    .rank-info {
      position: absolute;
      display: flex;
      align-items: center;
      z-index: 90;

      &[data-rank='1'] {
        left: 3.125rem;
        top: 1.5625rem;
        gap: 0.75rem;
      }

      &[data-rank='2'],
      &[data-rank='3'],
      &[data-rank='4'],
      &[data-rank='5'] {
        left: 3.4375rem;
        top: 0.9375rem;
        gap: 1rem;
        z-index: 10;
      }
    }

    .rank-info-image {
      border-radius: 50%;
      overflow: hidden;
      border: 0.1875rem solid #fff;

      &[data-rank='1'] {
        width: 4.375rem;
        height: 4.375rem;
      }

      &[data-rank='2'],
      &[data-rank='3'],
      &[data-rank='4'],
      &[data-rank='5'] {
        width: 3.75rem;
        height: 3.75rem;
      }
    }

    .rank-info-text {
      display: flex;
      flex-direction: column;

      .potato-count {
        font-size: 1rem;
        color: #fff;
        border-radius: 0.625rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 5rem;
        text-shadow: 0 0 0.0625rem #fff;
        -webkit-text-stroke: 0.0625rem #fff;
      }
    }

    .rank-info-text[data-rank='1'] .potato-count {
      background: #99601a;
      border-radius: 0.75rem;
      height: 2rem;
      padding: 0 0.625rem;
    }

    .rank-info-text[data-rank='2'] .potato-count {
      background: #354a5c;
      height: 1.75rem;
      padding: 0 0.3125rem;
    }

    .rank-info-text[data-rank='3'] .potato-count {
      background: #80430f;
      height: 1.75rem;
      padding: 0 0.3125rem;
    }

    .rank-info-text[data-rank='4'] .potato-count,
    .rank-info-text[data-rank='5'] .potato-count {
      background: #80430f;
      height: 1.75rem;
      padding: 0 0.3125rem;
    }

    /* 默认after样式，设置渐变背景 */
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      backdrop-filter: blur(0.0625rem);
      z-index: 1;
    }

    /* 各社区特定样式 */
    &.community-wangcai {
      border-color: #ffcd04;
      background: #ffcd04;
      &::before {
        content: '';
        position: absolute;
        left: -0.625rem;
        top: 0;
        width: 105%;
        height: 101%;
        background: url('/image/1.png') no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(0.0625rem);
      }
      &::after {
        background: linear-gradient(90deg, rgba(255, 209, 42, 1) 0%, rgba(255, 178, 23, 0) 100%);
      }
    }
    &.community-potato {
      border-color: #a3a9bc;
      background: #a3a9bc;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 103%;
        height: 100%;
        background: url('/image/2.png') no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(0.0625rem);
      }
      &::after {
        background: linear-gradient(90deg, rgba(178, 183, 201, 1) 0%, rgba(142, 147, 166, 0) 100%);
      }
    }
    &.community-thelonelybit {
      border-color: #cf8952;
      background: #cf8952;
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 103%;
        height: 100%;
        background: url('/image/3.png') no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(0.0625rem);
      }
      &::after {
        background: linear-gradient(90deg, rgba(224, 125, 57, 1) 0%, rgba(217, 168, 128, 0) 100%);
      }
    }
    &.community-pizza {
      border-color: #14110a;
      background: #14110a;
      &::before {
        content: '';
        position: absolute;
        left: -1.5625rem;
        top: 0;
        width: 106%;
        height: 100%;
        background: url('/image/4.png') no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(0.0625rem);
      }
      &::after {
        background-color: rgba(0, 0, 0, 0.5);
      }
      .potato-count {
        background: #80430f;
      }
    }
    &.community-domoducks {
      border-color: #14110a;
      background: #14110a;
      &::before {
        content: '';
        position: absolute;
        left: -1.5625rem;
        top: 0;
        width: 106%;
        height: 100%;
        background: url('/image/5.png') no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(0.0625rem);
      }
      &::after {
        background-color: rgba(0, 0, 0, 0.5);
      }
      .potato-count {
        background: #80430f;
      }
    }
  }
`;

export { RankItemHeight };
