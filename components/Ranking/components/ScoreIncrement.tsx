import { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';

export const ScoreIncrement = ({
  value,
  onAnimationComplete,
}: {
  value: number;
  onAnimationComplete?: () => void;
}) => {
  const [animationState, setAnimationState] = useState<'in' | 'out' | 'none'>('in');

  useEffect(() => {
    setAnimationState('in');
    // 淡入动画完成后，1秒后开始淡出
    const timer = setTimeout(() => {
      setAnimationState('out');
    }, 1000);

    return () => clearTimeout(timer);
  }, [value]);

  // 监听淡出动画完成
  useEffect(() => {
    if (animationState === 'out') {
      const timer = setTimeout(() => {
        if (onAnimationComplete) onAnimationComplete();
      }, 500); // 淡出动画持续时间

      return () => clearTimeout(timer);
    }
  }, [animationState, onAnimationComplete]);

  return <IncrementContainer animationState={animationState}>+{value}</IncrementContainer>;
};

// 定义淡入动画
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(0) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(-0.9375rem) scale(1.2);
  }
`;

// 定义淡出动画
const fadeOut = keyframes`
  from {
    opacity: 1;
    transform: translateY(-0.9375rem) scale(1.2);
  }
  to {
    opacity: 0;
    transform: translateY(-1.5625rem) scale(0.8);
  }
`;

// 增量数字组件样式
const IncrementContainer = styled.div<{
  animationState: 'in' | 'out' | 'none';
}>`
  position: absolute;
  top: 20%;
  left: 80%;
  transform: translate(10%, 0%);
  color: #fff;
  font-weight: bold;
  font-size: 1rem;
  pointer-events: none;
  z-index: 100;
  background: #fc7922;
  padding: 0rem 0.375rem;
  border-radius: 0.625rem;
  box-shadow: 0 0.375rem 0.5rem -0.125rem rgba(0, 0, 0, 0.7);

  animation: ${(props) =>
      props.animationState === 'in' ? fadeIn : props.animationState === 'out' ? fadeOut : 'none'}
    ${(props) => (props.animationState === 'in' ? '0.3s' : '0.5s')}
    ${(props) => (props.animationState === 'in' ? 'ease-out' : 'ease-in')} forwards;
`;
