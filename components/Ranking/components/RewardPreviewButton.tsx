import styled from 'styled-components';
import RewardPreviewModal, { AwardType, RewardPreviewRef } from './RewardPreviewModal';
import { useRef } from 'react';

const RewardPreviewButtonContainer = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('/image/rank/preview.png') no-repeat center center;
  width: 11.25rem;
  height: 3.5rem;
  cursor: pointer;
  background-size: cover;
  margin: 0 auto;
`;

interface RewardPreviewButtonProps {
  onPreviewClick: () => void;
  menuType: string;
}

const RewardPreviewButton = ({ onPreviewClick, menuType }: RewardPreviewButtonProps) => {
  const rewardPreviewModalRef = useRef<RewardPreviewRef>(null);
  return (
    <>
      <RewardPreviewButtonContainer
        onClick={() => {
          rewardPreviewModalRef.current?.open();
          onPreviewClick();
        }}
      />
      <RewardPreviewModal ref={rewardPreviewModalRef} menuType={menuType as AwardType} />
    </>
  );
};

export default RewardPreviewButton;
