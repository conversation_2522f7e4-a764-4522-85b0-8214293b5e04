import { memo } from 'react';
import Image from 'next/image';
import { INVENTORY_TYPE_ENUM } from '@/constant/type';
import Tooltip from 'rc-tooltip';
import styled from 'styled-components';

interface TitleRenderProps {
  title?: string;
  url: string;
  showDetail: IDetails;
}

interface IDetails {
  name: string;
  description: string;
  quantity?: number;
  maxDurability?: number;
  currentDurability?: number;
  type: INVENTORY_TYPE_ENUM;
  expirationTime?: string;
  score?: number;
  trait?: string;
  quality?: number; // 品质：1白，2绿，3蓝，4紫，5黄，6红，7彩
  color?: string;
}

function GlovesRender({ url, showDetail }: TitleRenderProps) {
  return (
    <Tooltip
      zIndex={99}
      // trigger="hover"
      overlay={showDetail && <TooltipBox showDetail={showDetail} />}
      showArrow={false}
      styles={{
        root: {
          zIndex: '9999',
        },
      }}
      align={{
        points: ['tl', 'cl'],
        // offset: [90, 25],
        offset: ['20.18%', '12.5%'],
      }}>
      <Image
        src={url}
        alt="gloves"
        width={56}
        height={56}
        style={{
          width: '3.5rem',
          height: '3.5rem',
        }}
      />
    </Tooltip>
  );
}

function TooltipBox({ showDetail }: { showDetail: IDetails }) {
  return (
    <TooltipBoxView color={showDetail.color}>
      <h3 className="tooltip-box-title">{showDetail.name}</h3>
      <div>
        {/* <p>
          Name: <span>{showDetail.name}</span>
        </p> */}
        {showDetail.description && (
          <p>
            Description: <span>{showDetail.description}</span>
          </p>
        )}

        {showDetail.quantity !== undefined && (
          <p>
            Quantity: <span>{showDetail.quantity}</span>
          </p>
        )}
        {showDetail.maxDurability !== undefined && (
          <p>
            Max Durability: <span>{showDetail.maxDurability}</span>
          </p>
        )}
        {showDetail.currentDurability !== undefined && (
          <p>
            Current Durability:{' '}
            <span
              style={{
                color: showDetail.currentDurability === 1 ? '#FF2727' : '',
              }}>
              {showDetail.currentDurability}
            </span>
          </p>
        )}
        {showDetail.trait !== undefined && (
          <p>
            Trait: <span style={{ color: showDetail.color }}>{showDetail.trait}</span>
          </p>
        )}
        {showDetail.expirationTime && (
          <p>
            Expiration Time: <span>{showDetail.expirationTime}</span>
          </p>
        )}
        {showDetail.score !== undefined && (
          <p>
            Score: <span>{showDetail.score}</span>
          </p>
        )}
      </div>
    </TooltipBoxView>
  );
}

const TooltipBoxView = styled.div<{
  color?: string;
}>`
  width: 26.875rem;
  height: auto;
  background: #ffffff;
  border: 0.25rem solid #140f08;
  box-shadow: 0rem 0.25rem 1rem 0rem #00000040;
  padding: 1.5rem;
  box-sizing: border-box;
  border-radius: 2rem;
  position: relative;

  .tooltip-box-title {
    color: ${({ color }) => {
      return color;
    }};
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 1.75rem;
    border: 0.125rem solid
      ${({ color }) => {
        return color;
      }};
    pointer-events: none;
  }

  & > h3 {
    margin: 0;
    font-family: 'JetBrains Mono';
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.98rem;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
  }
  & > div {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1.5rem;
    & > p {
      font-family: 'JetBrains Mono';
      font-size: 1.25rem;
      font-weight: 400;
      line-height: 1.65rem;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #686663;
      margin: 0;
      display: flex;
      & > span {
        flex: 1;
        color: #3f3b37;
      }
    }
  }
`;

export default memo(GlovesRender);
