import { AnimatePresence, motion } from 'motion/react';
import styled from 'styled-components';
import LeftTime from './LeftTime';
// import LowTime from "./lowTime";
import { useMemo } from 'react';

const GroupContainer = styled(motion.div)`
  position: absolute;
  top: 0;
  right: -8.75rem; /* 调整位置，使其显示在树形图右侧 */
  display: flex;
  flex-direction: column;
  gap: 3.125rem; /* 组件之间的间距 */
  width: 100%;
  right: 50%;
  top: 85%;
  transform: translate(50%, 0%);
  z-index: -100;
  height: calc(100% * 2);
`;

const timerVariants = {
  initial: { x: '1.25rem', opacity: 0 },
  animate: { x: '0rem', opacity: 1, transition: { duration: 0.3 } },
  exit: { x: '3.125rem', opacity: 0, transition: { duration: 0.3 } },
};

interface TimerGroupProps {
  // isShowLeftTime: boolean;
  // shouldShowCooldown: boolean;
  // leftTime?: number;
  // onTimeEnd: () => void;
  rockLeftTime?: number;
  treeLeftTime?: number;
  onRockTimeEnd?: () => void;
  onTreeTimeEnd?: () => void;
  isShowRockLeftTime: boolean;
  isShowTreeLeftTime: boolean;
  shouldShowRockCooldown: boolean;
  shouldShowTreeCooldown: boolean;
}

const TimerGroup = ({
  // isShowLeftTime,
  // shouldShowCooldown,
  // leftTime,
  // onTimeEnd,
  rockLeftTime,
  treeLeftTime,
  onRockTimeEnd,
  onTreeTimeEnd,
  isShowRockLeftTime,
  isShowTreeLeftTime,
  shouldShowRockCooldown,
  shouldShowTreeCooldown,
}: TimerGroupProps) => {
  // const isShowLeftTimeStatus = useMemo(() => {
  //   return isShowLeftTime && shouldShowCooldown && leftTime && leftTime > 0;
  // }, [isShowLeftTime, shouldShowCooldown, leftTime]);

  const isShowRockLeftTimeStatus = useMemo(() => {
    return isShowRockLeftTime && shouldShowRockCooldown && rockLeftTime && rockLeftTime > 0;
  }, [isShowRockLeftTime, shouldShowRockCooldown, rockLeftTime]);

  const isShowTreeLeftTimeStatus = useMemo(() => {
    return isShowTreeLeftTime && shouldShowTreeCooldown && treeLeftTime && treeLeftTime > 0;
  }, [isShowTreeLeftTime, shouldShowTreeCooldown, treeLeftTime]);

  return (
    <>
      {isShowRockLeftTimeStatus || isShowTreeLeftTimeStatus ? (
        <AnimatePresence>
          <GroupContainer>
            {/* {isShowLeftTimeStatus && (
              <motion.div
                key="leftTime"
                variants={timerVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
                <LeftTime downTime={leftTime} onTimeEnd={onTimeEnd} />
              </motion.div>
            )} */}
            {/* 挖矿冷却时间 */}
            {isShowRockLeftTimeStatus && (
              <motion.div
                key="rockLeftTime"
                variants={timerVariants}
                initial="initial"
                animate="animate"
                exit="exit">
                <LeftTime
                  downTime={rockLeftTime}
                  label="Ore reset time"
                  onTimeEnd={onRockTimeEnd}
                />
              </motion.div>
            )}
            {/* 砍树冷却时间 */}
            {isShowTreeLeftTimeStatus && (
              <motion.div
                key="treeLeftTime"
                variants={timerVariants}
                initial="initial"
                animate="animate"
                exit="exit">
                <LeftTime
                  downTime={treeLeftTime}
                  label="Tree reset time"
                  onTimeEnd={onTreeTimeEnd}
                />
              </motion.div>
            )}
            {/* 装备更新期 */}
            {/* {isShowLowTimeStatus && (
              <motion.div
                key="lowTime"
                variants={timerVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
                <LowTime
                  downTime={stableTimeStamp || refreshTimeStamp}
                  onTimeEnd={handleCountdownEnd}
                  label="Tools reset time"
                />
              </motion.div>
            )} */}
          </GroupContainer>
        </AnimatePresence>
      ) : null}
    </>
  );
};

export default TimerGroup;
