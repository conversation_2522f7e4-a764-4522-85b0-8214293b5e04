import { useEffect, useState } from 'react';
import NumberFlow, { NumberFlowGroup } from '@number-flow/react';
import dayjs from 'dayjs';
import styled from 'styled-components';

const ClockContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  width: 100%;
  padding-left: 1.25rem;
`;

const DayLabel = styled.span`
  margin-right: 0.3125rem;
  font-weight: bold;
`;

interface Props {
  endTimestamp: number;
  onCountdownEnd?: () => void; // 倒计时结束回调
}

function CountDownClock({ endTimestamp, onCountdownEnd }: Props) {
  // 添加状态来存储剩余时间（小时、分钟、秒）
  const [timeLeft, setTimeLeft] = useState({
    dd: 0,
    hh: 0,
    mm: 0,
    ss: 0,
  });

  // 计算剩余时间并定时更新
  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = dayjs().valueOf(); // 当前时间戳
      const diff = Math.max(0, endTimestamp - now); // 剩余毫秒数（不小于0）

      if (diff <= 0) {
        // 倒计时结束
        setTimeLeft({ dd: 0, hh: 0, mm: 0, ss: 0 });
        onCountdownEnd?.();
        return false; // 返回false表示计时结束
      }

      // 计算天时分秒
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft({
        dd: days,
        hh: hours,
        mm: minutes,
        ss: seconds,
      });

      return true; // 返回true表示计时继续
    };

    // 初始计算
    const shouldContinue = calculateTimeLeft();

    // 如果计时尚未结束，设置定时器
    const timer = shouldContinue
      ? setInterval(() => {
          const shouldContinue = calculateTimeLeft();
          if (!shouldContinue) {
            clearInterval(timer as NodeJS.Timeout);
          }
        }, 1000)
      : null;

    // 清理函数
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [endTimestamp, onCountdownEnd]);

  const { dd, hh, mm, ss } = timeLeft;

  return (
    <NumberFlowGroup>
      <ClockContainer>
        {dd > 0 && (
          <>
            <NumberFlow value={dd} trend={-1} format={{ minimumIntegerDigits: 1 }} />
            <DayLabel>D</DayLabel>
          </>
        )}
        <NumberFlow trend={-1} value={hh} format={{ minimumIntegerDigits: 2 }} />
        <NumberFlow
          prefix=":"
          trend={-1}
          value={mm}
          digits={{ 1: { max: 5 } }}
          format={{ minimumIntegerDigits: 2 }}
        />
        <NumberFlow
          prefix=":"
          trend={-1}
          value={ss}
          digits={{ 1: { max: 5 } }}
          format={{ minimumIntegerDigits: 2 }}
        />
      </ClockContainer>
    </NumberFlowGroup>
  );
}

export default CountDownClock;
