import { memo, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import Tips from '/public/image/tips1.png';
import Tip2 from '/public/image/tip2.png';
import NumberFlow from '@number-flow/react';

const LeftTimeContainer = styled.div<{ isVisible: boolean }>`
  position: relative;
  opacity: ${(props) => (props.isVisible ? 1 : 0)};
  visibility: ${(props) => (props.isVisible ? 'visible' : 'hidden')};
  transition:
    opacity 0.3s ease-out,
    visibility 0.3s ease-out;

  .tips {
    width: 0.625rem;
    height: 1.25rem;
  }

  .tip1 {
    color: transparent;
    position: absolute;
    left: calc(100% - 70%);
    z-index: -1;
  }

  .tip2 {
    color: transparent;
    position: absolute;
    left: 50%;
    top: 0.625rem;
    transform: translate(-50%, 0%);
    z-index: 1;
    & > img {
      width: 13.75rem;
      height: 3.75rem;
    }
  }

  .tip3 {
    color: transparent;
    position: absolute;
    left: calc(100% - 35%);
    z-index: -1;
  }
`;

const CountdownContainer = styled.div`
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  left: 50%;
  top: 45%;
  z-index: 10;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  gap: 0.125rem;
  .number-container {
    display: inline-flex;
    align-items: center;
    margin: 0 0.125rem;
  }
`;

interface LeftTimeProps {
  downTime?: number;
  onTimeEnd?: () => void;
  label: string;
}

function LeftTime({ downTime, onTimeEnd, label }: LeftTimeProps) {
  const [seconds, setSeconds] = useState(downTime || 0);
  const [isVisible, setIsVisible] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (downTime && downTime > 0) {
      setSeconds(downTime);
      setIsVisible(true);
    }
  }, [downTime]);

  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    if (seconds <= 0) {
      setIsVisible(false);
      onTimeEnd?.();
      return;
    }

    timerRef.current = setInterval(() => {
      setSeconds((prev) => prev - 1);
    }, 1000);

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [seconds, onTimeEnd]);

  return (
    <LeftTimeContainer isVisible={isVisible}>
      <Image
        src={Tip2.src}
        alt="tips"
        width={10}
        height={20}
        className="tip1"
        key="tip1"
        style={{ width: '0.625rem', height: '1.25rem' }}
      />
      <div className="tip2">
        <Image
          src={Tips.src}
          alt="tips"
          width={220}
          height={60}
          style={{ width: '13.75rem', height: '3.75rem' }}
        />
        <CountdownContainer>
          {label}:
          <div className="number-container">
            <NumberFlow value={seconds} />{' '}
          </div>
        </CountdownContainer>
      </div>
      <Image
        src={Tip2.src}
        alt="tips"
        width={10}
        height={20}
        className="tip3"
        key="tip3"
        style={{ width: '0.625rem', height: '1.25rem' }}
      />
    </LeftTimeContainer>
  );
}

export default memo(LeftTime);
