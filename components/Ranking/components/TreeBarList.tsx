import { motion } from 'motion/react';
import Image from 'next/image';
import T1 from '/public/image/t1.png';
import T3 from '/public/image/t3.png';
import styled from 'styled-components';
import { memo, useMemo } from 'react';
import { RankItem } from '@/hooks/useRank';

// 定义容器的变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3, // 子元素之间的延迟时间
      delayChildren: 1, // 第一个子元素开始前的延迟
    },
  },
};

// 定义子元素的变体
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

const TreeBarContainer = styled.div<{ $bgSrc: string }>`
  position: relative;
  width: 18rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url(${(props) => props.$bgSrc}) no-repeat center bottom;
  background-size: cover;

  .tree-bar-bg {
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    max-height: 4rem;
  }
  .tree-bar-bg-upcoming {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tree-bar-list {
    /* height: calc(100% - 10px); */
    width: 100%;
    /* top: 5px; */
    height: 100%;
    display: flex;
    color: #fff;
    align-items: center;
    justify-content: space-around;
    position: relative;
    left: 0px;
    z-index: 11;
    .tree-bar-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      /* top: 8px; */
      & > img {
        width: 1.25rem;
        height: 1.25rem;
      }
      .score,
      .rank,
      .time {
        font-size: 1.125rem; // 调整字体大小
        font-weight: 500; // 加粗字体
        color: #fff;
      }
    }
    .community-rank-list {
      position: relative;
      width: 100%;
      height: 100%;

      /* 头像样式 */
      .community-rank-avatar {
        position: absolute;

        img {
          width: 2.25rem;
          height: 2.25rem;
          border-radius: 50%;
          border: 0.125rem solid #000;
          box-sizing: border-box;
          filter: drop-shadow(0.0703125rem 0.2109375rem 0rem rgba(0, 0, 0, 0.25));
        }

        &.rank-1-avatar {
          top: 0rem;
          left: 41%;
          transform: translateX(-50%);
        }

        &.rank-2-avatar {
          top: 0.1875rem;
          left: 0;
          transform: translateX(14%);
        }

        &.rank-3-avatar {
          top: 0.1875rem;
          left: 75%;
          transform: translateX(-50%);
        }
      }

      /* 分数样式 */
      .community-rank-score {
        position: absolute;
        width: 5rem;

        .score {
          font-size: 1.125rem;
          color: #140f08;
          font-weight: 700;
          white-space: nowrap;
          display: block;
          text-align: left;
          letter-spacing: -0.045rem;
          line-height: 1;
        }

        &.rank-1-score {
          bottom: 0.4375rem;
          left: 43%;
          transform: translateX(-20%);
        }

        &.rank-2-score {
          bottom: 0.4375rem;
          left: 10%;
          transform: translateX(-20%);
        }
        &.rank-3-score {
          bottom: 0.4375rem;
          left: 77%;
          transform: translateX(-20%);
        }
      }
    }
  }
`;

// 添加相对定位，以便放置增量数字
const ScoreContainer = styled(motion.div)`
  position: relative;
  display: flex;
  align-items: center;
`;

interface TreeBarProps {
  onClick: (type: string) => void;
  rankList: RankItem | null;
  activityStatus?: 'upcoming' | 'ended' | 'ongoing' | 'none';
  bgImg: string;
  type: string;
  icon: string;
  rank: number;
  score: number;
  time: string;
  rankType: string;
  rankInfo?: any;
}

export const TreeBar = ({
  onClick,
  rankList,
  activityStatus,
  bgImg,
  type,
  icon,
  rank,
  score,
  time,
  rankType,
  rankInfo,
}: TreeBarProps) => {
  const selfRankInfo = useMemo(() => {
    if (rankList) {
      const { selfRankInfo } = rankList;
      return selfRankInfo;
    }
    return null;
  }, [rankList]);

  return (
    <TreeBarContainer
      onClick={() => {
        onClick(type);
      }}
      $bgSrc={bgImg}
      style={
        rankType === 'Community'
          ? {
              backgroundSize: 'contain',
            }
          : {}
      }>
      <>
        {/* <Image src={bgImg} className="tree-bar-bg" alt="tree" width={254} height={70} /> */}
        <TreeBarList
          selfRankInfo={selfRankInfo}
          type={type}
          icon={icon}
          rank={rank}
          score={score}
          time={time}
          rankType={rankType}
          rankInfo={rankInfo}
        />
      </>
    </TreeBarContainer>
  );
};

interface TreeBarListProps {
  selfRankInfo: any;
  type: string;
  icon: string;
  rank: number;
  score: number;
  time: string;
  rankType: string;
  rankInfo?: any;
}

const TreeBarListComponent = ({
  selfRankInfo,
  type,
  icon,
  rank,
  score,
  time,
  rankType,
  rankInfo,
}: TreeBarListProps) => {
  // 使用工具函数计算显示值
  const displayScore = useMemo(() => {
    if (score <= 0) {
      return 0;
    }
    return score;
  }, [score]);

  const displayRank = useMemo(() => {
    if (rank <= 0) {
      return 0;
    }
    return rank;
  }, [rank]);

  // 格式化分数，使用K,M,B
  const scoreFormat = (score: number) => {
    const BILLION = 1_000_000_000;
    const MILLION = 1_000_000;
    const THOUSAND = 1_000;

    if (score >= BILLION) {
      return (score / BILLION).toFixed(2) + 'B';
    } else if (score >= MILLION) {
      return (score / MILLION).toFixed(2) + 'M';
    } else if (score >= THOUSAND) {
      return (score / THOUSAND).toFixed(2) + 'K';
    } else {
      return score.toLocaleString();
    }
  };

  // rankInfo 要过滤出前三名用来展示
  const top3RankInfo = useMemo(() => {
    if (rankInfo && Array.isArray(rankInfo) && rankInfo.length > 0) {
      return rankInfo.filter((item: any) => item.rank <= 3);
    }
    return [];
  }, [rankInfo]);

  return (
    <motion.div
      className="tree-bar-list"
      variants={containerVariants}
      initial="hidden"
      animate="visible">
      {type !== 'menu-0' ? (
        <>
          {/* rank排名 */}
          <motion.div className="tree-bar-item" variants={itemVariants}>
            <Image src={T1.src} alt="tree" width={20} height={20} />
            <span className="rank">{displayRank}</span>
          </motion.div>

          <ScoreContainer className="tree-bar-item" variants={itemVariants}>
            <Image src={icon} alt="tree" width={20} height={20} />
            {/* 使用后端返回的总积分 */}
            <span className="score">{displayScore}</span>

            {/* {incrementData && (
              <ScoreIncrement
                key={incrementData.id}
                value={incrementData.value}
                onAnimationComplete={handleAnimationComplete}
              />
            )} */}
          </ScoreContainer>

          <motion.div className="tree-bar-item" variants={itemVariants}>
            <Image src={T3.src} alt="tree" width={20} height={20} />
            <span className="time">{time}</span>
          </motion.div>
        </>
      ) : (
        <div className="community-rank-list">
          {/* 这是一个排名统计，中间是第一， 左边是第二，右边是第三 */}
          {top3RankInfo.map((item: any) => (
            <div key={item.address}>
              {/* 图片元素单独定位 */}
              <div className={`community-rank-avatar rank-${item.rank}-avatar`}>
                <Image src={item.icon} alt={`rank-${item.rank}`} width={36} height={36} />
              </div>
              {/* 分数元素单独定位 */}
              <div className={`community-rank-score rank-${item.rank}-score`}>
                <span className="score">
                  {typeof item.score === 'number' ? scoreFormat(item.score) : item.score}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export const TreeBarList = memo(TreeBarListComponent);
