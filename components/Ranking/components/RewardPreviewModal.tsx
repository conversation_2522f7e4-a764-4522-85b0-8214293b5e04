'use client';
import { forwardRef, Ref, useEffect, useImperativeHandle, useState } from 'react';
import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import Image from 'next/image';
import styled from 'styled-components';
import { getRankingRewardPreview } from '@/server';
import toast from 'react-hot-toast';
import LocalLoading from '@/components/LoadingContent';
import { last } from 'es-toolkit';
import AnimatedItem from '@/components/EditAvatarPage/RecordingModal/AssistantQuestion/QuestionHistoryModal/components/AnimatedItem';
import GlovesRender from './TitleRender';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { ConfigManager, StrictGlovesCollection } from '@/world/Config/ConfigManager';

const ContentContainerBox = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 1.25rem;
`;

const RankBox = styled.div`
  display: flex;
  flex-direction: column;
  width: calc(100% - 6.25rem);
  height: 100%;
`;

const ItemContainer = styled.div<{ background?: string }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -1.875rem;
    right: 0;
    bottom: 0;
    width: 8.75rem;
    height: 100%;
    background: ${(props) => {
      if (props.background) return props.background;

      return `linear-gradient(135deg,#fca346,#FFAA48)`;
    }};
    border-radius: 1.875rem; /* 使用较大的值确保形成椭圆 */
    box-shadow: 0.125rem 0 0.25rem 0.1875rem rgba(0, 0, 0, 0.1); /* 添加阴影增强立体感 */
  }
`;

const LevelIndicator = styled.div`
  position: absolute;
  left: 1.375rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  color: white;
  font-weight: bold;
  font-size: 1.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.75rem;
  height: 3.75rem;
  text-align: center;
  text-shadow:
    -0.0625rem 0.0625rem 0 #a58061,
    0.0625rem 0.0625rem 0 #a58061,
    0.0625rem -0.0625rem 0 #a58061,
    -0.0625rem -0.0625rem 0 #a58061;

  /* 确保图片和文字都能正确居中显示 */
  @keyframes thing {
    0% {
      background-position: 130%;
      opacity: 1;
    }

    to {
      background-position: -166%;
      opacity: 0;
    }
  }

  .inner {
    width: 100%;
    height: 100%;
    background: transparent;
    overflow: hidden;
    transition: inherit;
    border-radius: 50%;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(-65deg, #0000 40%, #fafafa 50%, #0000 70%);
      background-size: 200% 100%;
      background-repeat: no-repeat;
      animation: thing 3s ease infinite;
      width: 65%;
      height: 75%;
      left: 50%;
      top: 55%;
      transform: translate(-50%, -50%);
      /* 裁剪为六边形形状 */
      clip-path: polygon(
        50% 0%,
        /* 顶部中点 */ 100% 25%,
        /* 右上角 */ 100% 75%,
        /* 右下角 */ 50% 100%,
        /* 底部中点 */ 0% 75%,
        /* 左下角 */ 0% 25% /* 左上角 */
      );
    }

    .rank-icon {
      width: 100%;
      height: 100%;
      object-fit: contain; /* 确保图片适应容器而不变形 */
    }
  }
`;

const ContentContainer = styled.div`
  position: relative;
  z-index: 1; /* 确保内容在伪元素上方 */
  display: flex;
  flex-grow: 1;
  width: 100%;
  height: 5.375rem;
  align-items: center;
  /* gap: 10%; */
  padding-left: 40%;
  padding-right: 10%;
  justify-content: flex-end;
  .reward-preview-content {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    .reward-preview-content-item {
      /* border: 0.0625rem solid #000; */
      /* border-radius: 50%; */
      box-sizing: border-box;
      width: 3.75rem;
      height: 3.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    /* .rank-1 {
      border: 0.0625rem solid #ffbe1c;
      box-shadow: 0 0 0.5rem 0.5rem rgba(255,218,11,0.5);
    }
    .rank-2 {
      border: 0.0625rem solid #ff4516;
      box-shadow: 0 0 0.5rem 0.5rem rgba(255,69,22,0.5);

    }
    .rank-3 {
      border: 0.0625rem solid #ff8316;
      box-shadow: 0 0 0.5rem 0.5rem rgba(255,131,22,0.5);
    } */
  }
`;

interface ModalProps {
  onClose?: () => void;
  menuType: AwardType;
}

export interface RewardPreviewRef {
  open: () => void;
}

const menuToAwardType = {
  'menu-1': 'treeAward',
  'menu-2': 'mineAward',
  'menu-3': 'fishAward',
};

export type AwardType = keyof typeof menuToAwardType;

// 前三名等级图标
const RankIcon1 = '/image/rank-1.png';
const RankIcon2 = '/image/rank-2.png';
const RankIcon3 = '/image/rank-3.png';
const rank123 = {
  1: {
    src: RankIcon1,
    alt: 'rank-1',
  },
  2: {
    src: RankIcon2,
    alt: 'rank-2',
  },
  3: {
    src: RankIcon3,
    alt: 'rank-3',
  },
};

const RenderRankIcon = styled.div<{ src: string }>`
  background-image: url(${(props) => props.src});
  background-size: cover;
  background-position: center;
`;

// 等级渲染函数：前三名使用图标，其余使用数字
const renderRankLevel = (level: number) => {
  if (level >= 1 && level <= 3) {
    // 前三名使用图标
    const rankIconInfo = rank123[level as 1 | 2 | 3];
    return (
      <div className="inner">
        <RenderRankIcon className="rank-icon" src={rankIconInfo.src} />
      </div>
    );
  } else {
    // 其他名次使用数字
    return level;
  }
};

// eslint-disable-next-line react/display-name
const RewardPreviewModal = forwardRef<RewardPreviewRef, ModalProps>(
  ({ onClose, menuType }: ModalProps, ref: Ref<RewardPreviewRef>) => {
    // 提交成功后重置积分和资源
    const [isMounted, setIsMounted] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [rewardPreviewData, setRewardPreviewData] = useState<any[]>([]);
    const [maxRank, setMaxRank] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [gloves, setGloves] = useState<StrictGlovesCollection | null>(null);

    useEffect(() => {
      setIsMounted(true);
      return () => setIsMounted(false);
    }, []);

    const groupByRank = (list: any[]) => {
      if (!list || list.length === 0) {
        return {};
      }

      const maxRank = list[list.length - 1].rank;
      const groups: { [key: string]: any[] } = {};

      list.forEach((item) => {
        const { rank } = item;
        let groupKey = '';

        if (rank === 1) groupKey = '1';
        else if (rank === 2) groupKey = '2';
        else if (rank === 3) groupKey = '3';
        else if (rank >= 4 && rank <= 5) groupKey = '4-5';
        else if (rank >= 6 && rank <= 10) groupKey = '6-10';
        else if (rank >= 11 && rank <= maxRank) groupKey = `11-${maxRank}`;

        if (!groups[groupKey]) groups[groupKey] = [];
        groups[groupKey].push(item);
      });

      return groups;
    };

    useEffect(() => {
      if (isOpen) {
        ConfigManager.getInstance().getData((data) => {
          if (data.gloves) {
            setGloves(data.gloves);
          }
        });
      }
    }, [isOpen]);

    useImperativeHandle(ref, () => ({
      open: async () => {
        setIsLoading(true);
        setIsOpen(true);

        const awardType = menuToAwardType[menuType];
        try {
          const res = await getRankingRewardPreview();
          const { data, code, msg } = res.data;
          if (code === 1) {
            // 只取出该类型奖励的数组
            const awardList = data.map((item: any) => {
              const award = item[awardType];
              const newAward = award.map((k: any) => {
                const url = `/icon/${k.type}/${k.itemTag}.png`;
                return {
                  ...k,
                  url: getCdnLink(url),
                };
              });
              return {
                rank: item.rank,
                award: newAward,
              };
            });
            setMaxRank(last(awardList)?.rank);
            const groupData = groupByRank(awardList);
            if (Object.keys(groupData).length > 0) {
              const list = Object.keys(groupData).map((key) => {
                return {
                  rank: key,
                  award: groupData[key],
                };
              });
              setRewardPreviewData(list);
            }
          } else {
            toast.error(msg);
          }
        } catch (error: unknown) {
          if (error instanceof Error) {
            toast.error(error.message);
          }
        } finally {
          setIsLoading(false);
        }
      },
    }));

    const rankRanges = [
      { min: 1, max: 1, label: '1' },
      { min: 2, max: 2, label: '2' },
      { min: 3, max: 3, label: '3' },
      { min: 4, max: 5, label: '4~5' },
      { min: 6, max: 10, label: '6~10' },
    ];

    const formatRank = (rank: number, maxRank: number) => {
      for (const range of rankRanges) {
        if (rank >= range.min && rank <= range.max) return range.label;
      }
      if (rank >= 11 && rank <= maxRank) return `11~${maxRank}`;
      return '';
    };

    if (!isMounted) {
      return null;
    }

    return (
      <>
        <Dialog isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <ModalContent
            modalBodyPadding="3.75rem 1.5rem 1.875rem 1.5rem"
            onClose={() => {
              setIsOpen(false);
            }}
            modalWidth="32.5rem"
            title={
              <Image
                src="/image/rank/reward-preview-title.png"
                alt="reward-preview-title"
                width={360}
                height={62}
                style={{
                  width: '22.5rem',
                  height: '3.875rem',
                }}
              />
            }>
            <ContentContainerBox>
              {isLoading ? (
                <LocalLoading />
              ) : (
                <RankBox>
                  {rewardPreviewData.length > 0 &&
                    rewardPreviewData.map((item: any, index: number) => {
                      return (
                        <AnimatedItem
                          key={item.rank}
                          index={index}
                          delay={0.1}
                          border={false}
                          padding="0rem"
                          marginBottom="0.75rem">
                          <ItemContainer>
                            <LevelIndicator>
                              {(() => {
                                const rankNum = item.award[0].rank;
                                if (rankNum >= 1 && rankNum <= 3) {
                                  return renderRankLevel(rankNum);
                                }
                                return formatRank(rankNum, maxRank);
                              })()}
                            </LevelIndicator>
                            <ContentContainer>
                              {item.award[0] &&
                                item.award[0].award &&
                                item.award[0].award.length > 0 && (
                                  <div className="reward-preview-content">
                                    {item.award[0].award.map((a: any, j: number) => {
                                      return (
                                        <div
                                          key={j}
                                          className={`reward-preview-content-item rank-${item.rank}`}>
                                          {a.type === 'decoration' && (
                                            <GlovesRender
                                              key={j + a.itemTag}
                                              url={a.url}
                                              showDetail={gloves?.[a.itemTag]}
                                            />
                                          )}
                                          {a.type === 'cloth' && (
                                            <ClothRender itemTag={a.itemTag} key={j + a.itemTag} />
                                          )}
                                        </div>
                                      );
                                    })}
                                  </div>
                                )}
                            </ContentContainer>
                          </ItemContainer>
                        </AnimatedItem>
                      );
                    })}
                </RankBox>
              )}
            </ContentContainerBox>
          </ModalContent>
        </Dialog>
      </>
    );
  }
);

// 衣服
interface ClothRenderProps {
  itemTag: string;
}

function ClothRender({ itemTag }: ClothRenderProps) {
  return (
    <Image
      src="/image/rank/cloth.png"
      alt="cloth"
      width={56}
      height={56}
      style={{ width: '3.5rem', height: '3.5rem' }}
    />
  );
}

export default RewardPreviewModal;
