import { motion } from 'motion/react';
import styled from 'styled-components';

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`;

function LocalLoading() {
  return (
    <LoadingContainer>
      <motion.div
        className="loading-spinner"
        animate={{ rotate: 360 }}
        transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
        style={{
          width: 40,
          height: 40,
          borderRadius: '50%',
          border: '4px solid rgba(0, 0, 0, 0.1)',
          borderTopColor: '#56504d',
          marginBottom: 20,
        }}
      />
      <div className="no-selection-text">Loading..</div>
    </LoadingContainer>
  );
}

export default LocalLoading;
