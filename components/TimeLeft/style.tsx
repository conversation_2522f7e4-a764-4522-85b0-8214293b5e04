import styled from 'styled-components';
import Image from 'next/image';
import { motion } from 'motion/react';

// 时间容器
export const TimeContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
`;

// 时间数字卡片
export const TimeCard = styled.div<{ $isTimeLeft?: boolean }>`
  width: 2rem;
  height: 2.625rem;
  background-color: white;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 500;
  color: ${(props) => (props.$isTimeLeft ? '#FF3300' : '#542D00')};
  border: 0.0625rem solid;
  border-color: ${(props) => (props.$isTimeLeft ? '#FF3300' : '#A58061')};
  box-shadow: 0.0625rem 0.125rem 0.25rem 0rem #00000040 inset;
`;

// 冒号
export const Colon = styled.div`
  font-size: 1.875rem;
  font-weight: bold;
  color: #553311;
  margin: 0 0.125rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .dot {
    width: 0.375rem;
    height: 0.375rem;
    background-color: #553311;
    border-radius: 50%;
    margin: 0.25rem 0;
  }
`;

export const StyledTimeLeft = styled(motion.div)<{ $bgSrc: string }>`
  width: 25rem;
  height: 14rem;
  font-size: 1.25rem;
  color: #542d00;
  display: flex;
  align-items: center;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 3.75rem;
  gap: 0.5rem;
  /* position: fixed;
  left: calc(50% - (25.75rem / 2));
  top: 10%; */
  background: url(${(props) => props.$bgSrc}) no-repeat center center;
  background-size: contain;
  justify-content: flex-start;
  pointer-events: auto;
  user-select: none;

  position: relative;
`;

export const StyledConfetti = styled(Image)`
  width: 22.25rem;
  height: 12.5625rem;
  flex-shrink: 0;
  filter: drop-shadow(0rem 0.1875rem 0rem rgba(0, 0, 0, 0.25));
  position: absolute;
  pointer-events: none;
  user-select: none;
  top: 1.5625rem;
  left: 0.25rem;
`;

export const StyledHurryUp = styled(motion.div)`
  display: flex;
  width: 7.803125rem;
  transform: rotate(11.265deg);
  flex-direction: column;
  align-items: flex-start;
  gap: 0.625rem;
  border-radius: 1.5rem;
  border: 0.125rem solid #f15711;
  position: absolute;
  top: 1.5625rem;
  left: 40%;
  div {
    display: flex;
    padding: 0.75rem 0.625rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    align-self: stretch;
    border-radius: 1.5rem;
    border: 0.125rem solid #fff;
    background: #ff8316;
    span {
      color: #fff;
      text-align: center;
      font-family: 'JetBrains Mono';
      font-size: 1.125rem;
      font-style: normal;
      font-weight: 800;
      line-height: 100%;
      letter-spacing: -0.04em;
      text-transform: capitalize;
    }
  }
`;

export const StyledParagraph = styled.p<{ $textStrokeRem?: number }>`
  margin: 0;
  font-family: 'Baloo 2', sans-serif;
  font-weight: 800;
  font-size: 2rem;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  text-transform: capitalize;
  color: #fff;
  height: 2rem;
  margin-top: 2.125rem;
  position: relative;
  z-index: 1;
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: ${(props) => props.$textStrokeRem || 0.25}rem #542d00;
    text-stroke: ${(props) => props.$textStrokeRem || 0.25}rem #542d00;
    z-index: -1;
    left: 0;
  }
`;
