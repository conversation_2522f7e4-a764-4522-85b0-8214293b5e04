import ChevronLeftIcon from '@/commons/ChevronLeft';
import ChevronRightIcon from '@/commons/ChevronRight';
import React, { useCallback, useEffect, useMemo, useRef, useState, useTransition } from 'react';
import { GetMyPlayer } from '@/world/Character/MyPlayer';
// import { trim } from "lodash";
import { trim } from 'es-toolkit';
import CopyToClipboard from 'react-copy-to-clipboard';
import { GM_BTC_ADDRESS, usePlayerColors } from '@/hooks/usePlayerColors';
import {
  ChatRoomView,
  ChevronLeftIconContainer,
  MessageItem,
  MessageList,
  ToggleButton,
  StyledTimestamp,
} from './ChatRoom.styles';
import ToolBar from './components/ToolBar';
import ChatRoomInput, { ChatRoomInputRef } from './components/ChatRoomInput';
import ChatRoomReplyMessage, { ReplyMessageRef } from './components/ChatRoomReplyMessage';
import { ChatTabType } from '@/model/Chat/ChatType';
import { ChatManager } from '@/model/Chat/ChatManager';
import { ChatListener } from '@/model/Chat/ChatListener';
import { ChatEvent } from '@/model/Chat/ChatEvent';
import { ChatData } from '@/model/Chat/ChatData';
import AirdropModal, { AirdropModalRef } from './components/AirdropModal';
import { Reward, RewardsRef } from './components/Reward';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';

import ChatOptionButton from './components/ChatOptionButton';
import styled from 'styled-components';
import ConfettiGroupBox from './components/Confetti';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
dayjs.extend(isToday);

interface Position {
  x: number;
  y: number;
}

const useRelativeClickPosition = (domRef: React.RefObject<HTMLElement>) => {
  const [position, setPosition] = useState<Position | null>(null);
  const latestRef = useRef(position);

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (domRef.current && domRef.current.contains(e.target as Node)) {
        const rect = domRef.current.getBoundingClientRect();
        setPosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top,
        });
      }
    };

    document.addEventListener('contextmenu', handleClick);

    return () => {
      document.removeEventListener('contextmenu', handleClick);
    };
  }, [domRef]);

  latestRef.current = position as any;

  return latestRef;
};

// 定义自己的 Direction 类型
type Direction = 'up' | 'down' | 'left' | 'right' | undefined;

interface ChatRoomProps {
  // messages?: ChatMessage[];
  // currentPlayerId?: string;
  useSequentialColors?: boolean;
  onExpandChange?: (isExpanded: boolean) => void;
}

/**
 *
 * @deprecated 改组件弃用,改为使用 @/components/GameWindow/components/ChatRoom
 */
const ChatRoom: React.FC<ChatRoomProps> = ({
  useSequentialColors = false, // 默认使用随机颜色
  onExpandChange,
}) => {
  const [isExpanded, setIsExpanded] = useState(true); // 默认展开
  const [messages, setMessages] = useState<ChatData[]>([]);
  const myPlayer = GetMyPlayer();
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(null); // 跟踪复制的消息索引
  const messageListRef = useRef<HTMLDivElement>(null); // 消息列表的引用
  const observerRef = useRef<ResizeObserver | null>(null); // ResizeObserver 引用
  const mutationObserverRef = useRef<MutationObserver | null>(null); // MutationObserver 引用
  // 跟踪最后一次消息数量，用于检测新消息
  const lastMessageCountRef = useRef<number>(0);
  // 控制是否显示新消息提示
  const [showNewMessagePrompt, setShowNewMessagePrompt] = useState(false);
  const [typeList, setTypeList] = useState<ChatTabType[]>([]);
  const [curChatType, setCurChatType] = useState<ChatTabType>(ChatTabType.Node);
  const [adminOnly, setAdminOnly] = useState(false);
  const [replyChatData, setReplyChatData] = useState<ChatData | null>(null);
  const replyRef = useRef<ReplyMessageRef>(null);
  const airdropModalRef = useRef<AirdropModalRef>(null);
  // const { Rewards, openRewards } = useRewards();
  const rewardRef = useRef<RewardsRef>(null);

  const chatRoomViewRef = useRef<HTMLDivElement>(null);
  const pos = useRelativeClickPosition(chatRoomViewRef);

  // 使用自定义hook处理玩家颜色
  const { getPlayerColor, getPlayerTextColor, getSpecialAccountProps, clearColorMap } =
    usePlayerColors(useSequentialColors);

  const inputRef = useRef<ChatRoomInputRef>(null);
  const [isPending, startTransition] = useTransition();
  const [currentOptionIndex, setCurrentOptionIndex] = useState<number | null>(null);

  // 创建一个基础的滚动信息状态对象
  const [scrollInfo, setScrollInfo] = useState({
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    direction: undefined as Direction,
    hasVerticalScroll: false,
    hasHorizontalScroll: false,
    isAtBottom: true,
    isAtTop: true,
    isAtLeft: true,
    isAtRight: false,
    shouldShowNewMessagePrompt: false,
  });

  // 监听 messageListRef.current 的变化，当元素存在时初始化滚动监听
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("MessageList 元素已挂载，设置滚动监听");

    // 添加滚动事件监听器
    const handleScroll = () => {
      // console.log("滚动事件触发");
      updateScrollInfo();
    };

    el.addEventListener('scroll', handleScroll, { passive: true });

    // 初始化
    updateScrollInfo();

    return () => {
      // console.log("清理滚动事件监听");
      el.removeEventListener('scroll', handleScroll);
    };
  }, [messageListRef.current, isExpanded]);

  // 设置ResizeObserver监听元素大小变化
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("设置ResizeObserver监听元素大小变化");

    // 创建ResizeObserver监听容器大小变化
    observerRef.current = new ResizeObserver(() => {
      // console.log("检测到大小变化，更新滚动信息");
      // 容器大小变化时更新滚动信息
      requestAnimationFrame(updateScrollInfo);
    });

    observerRef.current.observe(el);

    return () => {
      // console.log("清理ResizeObserver");
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [messageListRef.current, isExpanded]);

  // 设置MutationObserver监听容器内容变化
  useEffect(() => {
    const el = messageListRef.current;
    if (!el || !isExpanded) return;

    // console.log("设置MutationObserver监听内容变化");

    // 创建MutationObserver监听内容变化
    mutationObserverRef.current = new MutationObserver((mutations) => {
      // 过滤出有意义的变化（比如子节点增删、属性变化等）
      const significantChanges = mutations.some(
        (mutation) =>
          mutation.type === 'childList' ||
          (mutation.type === 'attributes' &&
            ['style', 'class'].includes(mutation.attributeName || ''))
      );

      if (significantChanges) {
        // console.log("检测到内容变化，更新滚动信息");
        // 内容变化时更新滚动信息
        requestAnimationFrame(updateScrollInfo);
      }
    });

    // 配置观察选项
    mutationObserverRef.current.observe(el, {
      childList: true, // 观察子节点增删
      subtree: true, // 观察所有后代节点
      attributes: true, // 观察属性变化
      attributeFilter: ['style', 'class'], // 只关心样式和类变化
    });

    return () => {
      // console.log("清理MutationObserver");
      if (mutationObserverRef.current) {
        mutationObserverRef.current.disconnect();
      }
    };
  }, [messageListRef.current, isExpanded]);

  // 更新滚动信息的函数
  const updateScrollInfo = () => {
    if (!messageListRef.current) return;

    const el = messageListRef.current;
    const left = el.scrollLeft;
    const top = el.scrollTop;
    const scrollHeight = el.scrollHeight;
    const clientHeight = el.clientHeight;
    const scrollWidth = el.scrollWidth;
    const clientWidth = el.clientWidth;
    const right = scrollWidth - (clientWidth + left);
    const bottom = scrollHeight - (clientHeight + top);

    // 判断是否有滚动条
    const hasVerticalScroll = scrollHeight > clientHeight + 1; // 允许0.0625rem误差
    const hasHorizontalScroll = scrollWidth > clientWidth + 1;

    // 判断是否到达边界
    const isAtBottom = hasVerticalScroll && Math.abs(bottom) <= 1;
    const isAtTop = top <= 1;
    const isAtLeft = left <= 1;
    const isAtRight = hasHorizontalScroll && Math.abs(right) <= 1;

    const newInfo = {
      left,
      top,
      right,
      bottom,
      direction: undefined as Direction, // 简化，不计算方向
      hasVerticalScroll,
      hasHorizontalScroll,
      isAtBottom,
      isAtTop,
      isAtLeft,
      isAtRight,
      shouldShowNewMessagePrompt: showNewMessagePrompt, // 使用状态变量
    };

    // 如果滚动到底部，自动隐藏新消息提示
    if (isAtBottom && showNewMessagePrompt) {
      setShowNewMessagePrompt(false);
    }

    // console.log("newInfo=======", newInfo);
    setScrollInfo(newInfo);
  };

  // 处理复制成功回调
  const handleCopySuccess = (text: string, index: number) => {
    // 显示复制成功提示
    setCopiedMessageIndex(index);
    // 1.5秒后重置状态
    setTimeout(() => setCopiedMessageIndex(null), 1500);
  };

  // 通知父组件展开状态变化
  useEffect(() => {
    if (onExpandChange) {
      onExpandChange(isExpanded);
    }
  }, [isExpanded, onExpandChange]);

  // 滚动到底部的函数
  const scrollToBottom = () => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
      updateScrollInfo();
    }
  };

  // 初始加载和聊天室展开时滚动到底部
  useEffect(() => {
    if (isExpanded && messageListRef.current) {
      scrollToBottom();
    }
  }, [isExpanded]);

  // 在 messages 变化时，检查是否有新消息
  useEffect(() => {
    // 检测是否有新消息
    if (messages.length > lastMessageCountRef.current) {
      // 如果不在底部，显示新消息提示
      if (scrollInfo.hasVerticalScroll && !scrollInfo.isAtBottom) {
        setShowNewMessagePrompt(true);
      } else {
        // 如果在底部，直接滚动到底部
        scrollToBottom();
      }
    }

    // 更新消息计数
    lastMessageCountRef.current = messages.length;
  }, [messages, scrollInfo]);

  // 监听 scrollInfo 变化，确保"在底部"状态的滚动行为一致
  useEffect(() => {
    // 当滚动位置变化时，如果用户主动滚动到底部，隐藏提示
    if (scrollInfo.isAtBottom) {
      setShowNewMessagePrompt(false);
    }
  }, [scrollInfo]);

  // 使用事件监听更新消息
  useEffect(() => {
    const handleNewMessage = (tabType: ChatTabType) => {
      if (tabType === curChatType) {
        const chatList = ChatManager.getInstance().getChatList(curChatType);
        if (chatList) {
          chatList.clearNewMessageCount();
          // 初始加载
          const getChatMessages = chatList.getChatDataList();
          // 过滤掉空消息
          const filteredMessages = getChatMessages.filter(
            (msg) => msg.content && trim(msg.content) !== ''
          );

          // const testMsg = {
          //   uuid: '2d9cb865-a0cb-4707-aa21-be15ec6a468e',
          //   playerId: 'system',
          //   content:
          //     'You have received an Easter Egg: PIZZA_RUSH, please complete it within the specified time.',
          //   replyTo: '',
          //   timestamp: 1751038074671,
          //   isTg: false,
          //   admin: false,
          //   isSystem: true,
          // };
          // console.log('filteredMessages====', filteredMessages);
          // setMessages([testMsg]);
          setMessages(filteredMessages);
        } else {
          setMessages([]);
        }
      }
    };

    handleNewMessage(curChatType);

    // 添加监听器
    ChatListener.getInstance().addListener(ChatEvent.ReceiveChat, handleNewMessage);

    // 清理监听器
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.ReceiveChat, handleNewMessage);
    };
  }, [curChatType]);

  useEffect(() => {
    const chatTypeChange = () => {
      setTypeList(ChatManager.getInstance().getChatTypes());
    };
    // 判断是否进入房间
    ChatListener.getInstance().addListener(ChatEvent.ChatTypeChange, chatTypeChange);
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.ChatTypeChange, chatTypeChange);
    };
  }, []);

  function containsChinese(str: string) {
    return /[\u4e00-\u9fa5]/.test(str);
  }

  function isPureEnglishOrNumber(str: string) {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  function formatPlayerId(
    playerId: string,
    isAdmin: boolean,
    isCurrentPlayer: boolean,
    isSpecialAccount: string,
    currentAddress: string
  ) {
    if (!playerId) return '';
    if (isAdmin) {
      playerId += '(AM)';
    } else if (isCurrentPlayer) {
      playerId += '(ME)';
    } else if (isSpecialAccount) {
      playerId += '(GM)';
    }
    // 如果包含中文
    if (containsChinese(playerId)) {
      // 如果全是中文
      if (isPureEnglishOrNumber(playerId.replace(/[\u4e00-\u9fa5]/g, ''))) {
        if (playerId.length <= 6) return playerId;
        return playerId.slice(0, 3) + '...' + playerId.slice(-3);
      }
      // 中英文混合
      if (playerId.length <= 8) return playerId;
      return playerId.slice(0, 3) + '...' + playerId.slice(-3);
    }

    // 纯英文/数字
    if (playerId.length <= 14) return playerId;
    if (isAdmin || isCurrentPlayer || isSpecialAccount)
      return playerId.slice(0, 4) + '...' + playerId.slice(-8);
    if (playerId.startsWith('bc1p') || playerId.startsWith('bc1q'))
      return playerId.slice(0, 6) + '...' + playerId.slice(-6);
    return playerId.slice(0, 5) + '...' + playerId.slice(-5);
  }

  // 右键Reply
  const handleReply = (replyChatData: ChatData) => {
    setReplyChatData(replyChatData);
    startTransition(() => {
      replyRef.current?.openReplyMessage(replyChatData.playerId, replyChatData.content);
      inputRef.current?.focusInput();
    });
  };

  // 关闭回复面板时
  const handleCloseReply = () => {
    setReplyChatData(null);
    replyRef.current?.closeReplyMessage();
  };

  // 监听全局点击/滚动关闭Reply菜单
  useEffect(() => {
    if (currentOptionIndex === null) return;
    const handler = () => {
      setCurrentOptionIndex(null);
    };
    window.addEventListener('click', handler);
    window.addEventListener('scroll', handler, true);
    return () => {
      window.removeEventListener('click', handler);
      window.removeEventListener('scroll', handler, true);
    };
  }, [currentOptionIndex]);

  if (typeList.length === 0) {
    return null;
  }

  return (
    <>
      <ChatRoomView isExpanded={isExpanded} ref={chatRoomViewRef}>
        {isExpanded && (
          <ToolBar
            typeList={typeList}
            onChange={(v) => {
              setCurChatType(v);
            }}
            onAdminChange={(v) => {
              setAdminOnly(v);
            }}
          />
        )}

        <ToggleButton isExpanded={isExpanded} onClick={() => setIsExpanded(!isExpanded)}>
          {isExpanded ? (
            <ChevronRightIcon />
          ) : (
            <ChevronLeftIconContainer>
              <ChevronLeftIcon />{' '}
              <span
                style={{
                  fontSize: '1rem',
                }}>
                Show Chat Dialog
              </span>
            </ChevronLeftIconContainer>
          )}
        </ToggleButton>

        {isExpanded && (
          <MessageList
            ref={messageListRef}
            className={`message-list${scrollInfo.hasVerticalScroll ? ' withScrollBar' : ''}`}>
            {messages.map((message, index) => {
              // 过滤掉空消息
              if (!message.content || trim(message.content) === '') {
                return null;
              }

              if (message.isTime) {
                const timestamp = message.timestamp;
                const isTodayTime = dayjs(timestamp).isToday();

                const format = isTodayTime ? 'HH:mm' : 'MM/DD/YYYY HH:mm';

                return (
                  <StyledTimestamp key={message.uuid}>
                    {dayjs(timestamp).format(format)}
                  </StyledTimestamp>
                );
              }

              //
              if (message.isSystem) {
                return (
                  <StyledMessageSystemItem
                    key={message.uuid}
                    index={index}
                    message={message}
                    tabType={curChatType}
                    currentOptionIndex={currentOptionIndex}
                    onAnyOptionOpen={setCurrentOptionIndex}
                    pos={pos}
                    parentEl={chatRoomViewRef?.current}
                  />
                );
              }

              // 使用hook获取玩家颜色和特殊账号属性
              const specialAccountProps = getSpecialAccountProps(message.playerId);
              const playerColor = getPlayerColor(message.playerId, myPlayer.btcAddress);
              const playerTextColor = getPlayerTextColor(message.playerId, myPlayer.btcAddress);
              const isCurrentPlayer = message.playerId === myPlayer.btcAddress;

              // adminOnly: 为true，那么只显示admin玩家的信息，包括后续推送的数据也必须过滤掉，只能显示admin玩家的信息
              if (adminOnly && !message.admin) {
                return null;
              }

              return (
                <MessageItemWithReply
                  key={message.uuid}
                  index={index}
                  message={message}
                  isCurrentPlayer={isCurrentPlayer}
                  playerColor={playerColor}
                  playerTextColor={playerTextColor}
                  specialAccountProps={specialAccountProps}
                  formatPlayerId={formatPlayerId}
                  tabType={curChatType}
                  handleReply={handleReply}
                  copiedMessageIndex={copiedMessageIndex}
                  handleCopySuccess={handleCopySuccess}
                  currentOptionIndex={currentOptionIndex}
                  onAnyOptionOpen={setCurrentOptionIndex}
                  admin={message.admin}
                  pos={pos}
                  parentEl={chatRoomViewRef?.current}
                />
              );
            })}
          </MessageList>
        )}
        {/* 新消息提示 */}
        {showNewMessagePrompt && isExpanded && messages.length > 0 && (
          <div
            className="new-message-tip"
            onClick={() => {
              scrollToBottom();
              setShowNewMessagePrompt(false);
            }}>
            New Message
          </div>
        )}

        {/* 回复消息 */}
        {isExpanded && (
          <ChatRoomReplyMessage
            ref={replyRef}
            myPlayer={myPlayer}
            handleCloseReply={handleCloseReply}
          />
        )}

        {/* 输入框，出现回复时向下移动 */}
        {isExpanded && (
          <ChatRoomInput
            ref={inputRef}
            replyMode={replyChatData !== null}
            curChatType={curChatType}
            replyChatData={replyChatData}
            onAirdrop={() => {
              airdropModalRef.current?.open();
            }}
            onSend={() => {
              handleCloseReply();
            }}
          />
        )}

        <AirdropModal
          ref={airdropModalRef}
          onClose={() => {
            airdropModalRef.current?.close();
          }}
          onReward={(reward) => {
            rewardRef.current?.open({
              name: reward.name,
              quantity: reward.quantity,
            });
          }}
          setBirthdayButtonLoading={(loading) => {
            inputRef.current?.setIsLoading(loading);
          }}
        />
      </ChatRoomView>
      <Reward ref={rewardRef} />
    </>
  );
};

interface IMessageSystemItemProps {
  index: number;
  tabType: ChatTabType;
  message: ChatData;
  currentOptionIndex: number | null;
  onAnyOptionOpen: (index: number) => void;
  style?: React.CSSProperties;
  className?: string;
  pos?: React.MutableRefObject<Position | null>;
  parentEl?: HTMLDivElement | null;
}

const StyledMessageItem = styled.span`
  flex: 1 0 0;
  color: #a58061;
  font-family: Inter;
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
`;

function useMessageItemReplyPopup({
  parentEl,
  pos,
  isSystem = false,
}: {
  pos?: React.MutableRefObject<Position | null>;
  parentEl?: HTMLDivElement | null;
  isSystem?: boolean;
}) {
  const PADDING_BORDER_TOP_BOTTOM = 50;
  const PADDING_BORDER_LEFT_RIGHT = 50;
  const [showOption, setShowOption] = useState(false);

  const [menuPos, setMenuPos] = useState<{
    x: number;
    y: number;
    xDirection: 'left' | 'right';
    yDirection: 'top' | 'bottom';
  } | null>(null);
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  // const isGM = true;
  const isGM = useMemo(() => {
    return GM_BTC_ADDRESS === btcAddress;
  }, [btcAddress]);

  const clickEventRef = useRef<any>(null);

  const onContextMenu = (e: any) => {
    e.preventDefault();

    clickEventRef.current = {
      e: e,
      rect: e.currentTarget.getBoundingClientRect(),
    };
  };

  useEffect(() => {
    // 延迟执行等待 父组件设置 showOption在设置对应的pos

    setTimeout(() => {
      if (showOption && clickEventRef.current && pos?.current) {
        const e = clickEventRef.current.e;

        const rect = clickEventRef.current.rect;
        // const rect = e.currentTarget.getBoundingClientRect();
        let x = e.clientX - rect.left;
        let y = e.clientY - rect.top;
        let xDirection: 'left' | 'right' = 'left';
        let yDirection: 'top' | 'bottom' = 'top';
        const height = isGM && !isSystem ? 82 : 41;

        if (parentEl && !!pos?.current) {
          const pRect = parentEl.getBoundingClientRect();

          const offsetParentX = e.clientX - pRect.x;
          const offsetParentY = e.clientY - pRect.y;

          const popupXAndWidth = offsetParentX + 103;
          const popupYAndHeight = offsetParentY + height;

          const isInnerRight = popupXAndWidth <= pRect.width - PADDING_BORDER_LEFT_RIGHT;
          const isInnerBottom = popupYAndHeight <= pRect.height - PADDING_BORDER_TOP_BOTTOM;

          if (!isInnerRight) {
            x = rect.right - e.clientX;
            xDirection = 'right';
          }
          if (!isInnerBottom) {
            y = rect.bottom - e.clientY;
            yDirection = 'bottom';
          }
        }

        setMenuPos({
          x,
          y,
          xDirection,
          yDirection,
        });
      }
    }, 0);
  }, [showOption, pos?.current]);

  return {
    showOption,
    setShowOption,
    menuPos,
    setMenuPos,
    onContextMenu,
    isGM,
  };
}

const MessageSystemItem = ({
  index,
  tabType,
  message,
  currentOptionIndex,
  onAnyOptionOpen,
  className,
  parentEl,
  pos,
}: IMessageSystemItemProps) => {
  const { showOption, setShowOption, menuPos, onContextMenu, isGM } = useMessageItemReplyPopup({
    parentEl,
    pos,
    isSystem: true,
  });

  useEffect(() => {
    if (currentOptionIndex === index) {
      setShowOption(true);
    } else {
      setShowOption(false);
    }
  }, [currentOptionIndex, index]);

  const onContextMenuHandler = (e: any) => {
    onContextMenu(e);
    onAnyOptionOpen(index);
  };

  return (
    <div onContextMenu={onContextMenuHandler} className={className ? className : ''}>
      <ConfettiGroupBox />
      <StyledMessageItem>{message.content}</StyledMessageItem>
      {showOption && menuPos && isGM && (
        <div
          style={{
            position: 'absolute',
            [menuPos.yDirection]: menuPos.y,
            [menuPos.xDirection]: menuPos.x,
            zIndex: 9999,
          }}>
          <ChatOptionButton tabType={tabType} message={message} handleReply={() => undefined} />
        </div>
      )}
    </div>
  );
};

const StyledMessageSystemItem = styled(MessageSystemItem)`
  box-sizing: border-box;
  width: 100%;
  position: relative;

  display: flex;
  padding: 0.75rem 1rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 1rem;
  border: 0.0625rem solid #a58061;
  background: #fff;
`;

interface MessageItemWithReplyProps {
  index: number;
  tabType: ChatTabType;
  message: ChatData;
  isCurrentPlayer: boolean;
  playerColor: string;
  playerTextColor: string;
  specialAccountProps: any;
  formatPlayerId: any;
  handleReply: (chatData: ChatData) => void;
  copiedMessageIndex: number | null;
  handleCopySuccess: any;
  currentOptionIndex: number | null;
  onAnyOptionOpen: any;
  admin: boolean;
  pos?: React.MutableRefObject<Position | null>;
  parentEl?: HTMLDivElement | null;
}

const MessageItemWithReply = ({
  index,
  tabType,
  message,
  isCurrentPlayer,
  playerColor,
  playerTextColor,
  specialAccountProps,
  formatPlayerId,
  handleReply,
  copiedMessageIndex,
  handleCopySuccess,
  currentOptionIndex,
  onAnyOptionOpen,
  admin,
  parentEl,
  pos,
}: MessageItemWithReplyProps) => {
  const [replyChatData, setReplyChatData] = useState<ChatData | null>(null);
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const { showOption, setShowOption, menuPos, setMenuPos, onContextMenu } =
    useMessageItemReplyPopup({ parentEl, pos });

  useEffect(() => {
    if (message.replyTo.length > 0) {
      const chatList = ChatManager.getInstance().getChatList(tabType);
      if (chatList) {
        const replyMessage = chatList.findChatData(message.replyTo);
        if (replyMessage) {
          setReplyChatData(replyMessage);
          return;
        }
      }
    }
    setReplyChatData(null);
  }, [message]);

  useEffect(() => {
    if (currentOptionIndex === index) {
      setShowOption(true);
    } else {
      setShowOption(false);
    }
  }, [currentOptionIndex, index]);

  const onContextMenuHandler = (e: any) => {
    onContextMenu(e);
    onAnyOptionOpen(index);
  };

  return (
    <MessageItem
      isCurrentPlayer={isCurrentPlayer}
      className="message-item"
      playerColor={playerColor}
      playerTextColor={playerTextColor}
      style={showOption ? { background: 'rgba(255,255,255,0.15)' } : {}}
      admin={admin}
      isReply={message.replyTo ? true : false}
      onContextMenu={onContextMenuHandler}>
      <div className="player-id-container">
        <div className="player-id">
          {/* tg标识 */}
          {message.isTg && (
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg">
              <path
                d="M10.0002 2.15625C5.67023 2.15625 2.16016 5.66632 2.16016 9.99625C2.16016 14.3262 5.67023 17.8363 10.0002 17.8363C14.3301 17.8363 17.8402 14.3262 17.8402 9.99625C17.8402 5.66632 14.3301 2.15625 10.0002 2.15625ZM13.6341 7.48808C13.5161 8.72778 13.0056 11.7362 12.7458 13.1247C12.6359 13.7122 12.4195 13.9092 12.21 13.9284C11.7548 13.9703 11.409 13.6276 10.9681 13.3385C10.278 12.8862 9.88821 12.6046 9.2184 12.1632C8.44436 11.6531 8.94615 11.3729 9.38728 10.9145C9.50273 10.7946 11.5087 8.97 11.5475 8.80448C11.5524 8.78377 11.557 8.70648 11.5111 8.66589C11.4651 8.6253 11.3976 8.63904 11.3487 8.65008C11.2795 8.6658 10.1772 9.39439 8.04177 10.8359C7.72891 11.0507 7.44551 11.1554 7.19157 11.1499C6.91164 11.1439 6.37318 10.9916 5.97286 10.8615C5.48188 10.7019 5.09165 10.6175 5.12564 10.3465C5.14334 10.2053 5.33776 10.0609 5.70889 9.91339C7.99442 8.91763 9.51846 8.26113 10.281 7.94391C12.4583 7.0383 12.9107 6.88099 13.2055 6.87574C13.2704 6.87467 13.4154 6.89072 13.5093 6.96694C13.5718 7.02124 13.6116 7.097 13.6209 7.17926C13.6369 7.28138 13.6413 7.38497 13.6341 7.48808Z"
                fill="white"
              />
            </svg>
          )}
          <span>
            {formatPlayerId(
              message.playerId,
              admin,
              isCurrentPlayer,
              specialAccountProps,
              btcAddress
            )}
          </span>
        </div>
      </div>
      <CopyToClipboard
        text={trim(message.content)}
        onCopy={() => handleCopySuccess(trim(message.content), index)}>
        <div className="message-content-container">
          {/* 仅在有引用消息时展示 reply-message-container */}
          {replyChatData && (
            <div className="reply-message-container">
              <svg
                width="16"
                height="17"
                viewBox="0 0 16 17"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="reply-message-icon">
                <path
                  d="M9.40136 5.51833C9.37927 5.50861 9.35509 5.50459 9.33104 5.50665C9.30699 5.50871 9.28385 5.51679 9.26374 5.53013C9.24362 5.54347 9.22719 5.56165 9.21594 5.58301C9.20469 5.60436 9.19899 5.6282 9.19936 5.65233V6.80533C9.19936 6.93794 9.14668 7.06512 9.05291 7.15888C8.95914 7.25265 8.83196 7.30533 8.69936 7.30533C8.03235 7.30533 6.68636 7.31033 5.39936 8.12733C4.41536 8.75133 3.40936 9.88733 2.80436 12.0033C3.82436 11.0203 4.98936 10.4873 6.00936 10.2043C6.63624 10.0308 7.2806 9.9282 7.93036 9.89833C8.19629 9.88582 8.46272 9.88849 8.72836 9.90633H8.74136L8.74636 9.90733L8.69936 10.4053L8.74936 9.90733C8.87277 9.91973 8.98717 9.97757 9.07034 10.0696C9.1535 10.1616 9.19948 10.2813 9.19936 10.4053V11.5583C9.19936 11.6663 9.30936 11.7343 9.40136 11.6923L13.3854 8.75933L13.4274 8.73133C13.4491 8.71826 13.4671 8.69979 13.4796 8.67771C13.4921 8.65563 13.4986 8.6307 13.4986 8.60533C13.4986 8.57996 13.4921 8.55503 13.4796 8.53295C13.4671 8.51087 13.4491 8.4924 13.4274 8.47933L13.3854 8.45133L9.40136 5.51833ZM8.19936 10.8913C8.13069 10.8913 8.05636 10.8933 7.97636 10.8973C7.54236 10.9173 6.94236 10.9833 6.27636 11.1683C4.95036 11.5363 3.38036 12.3703 2.33636 14.2483C2.2799 14.3497 2.18994 14.4283 2.0819 14.4706C1.97386 14.5129 1.85446 14.5164 1.74417 14.4803C1.63388 14.4442 1.53956 14.371 1.47738 14.273C1.4152 14.175 1.38903 14.0585 1.40336 13.9433C1.86736 10.2333 3.28936 8.28133 4.86336 7.28333C6.10836 6.49333 7.39036 6.34133 8.19936 6.31233V5.65233C8.19926 5.4464 8.25476 5.24426 8.35999 5.06724C8.46521 4.89023 8.61627 4.7449 8.79722 4.64659C8.97817 4.54828 9.1823 4.50064 9.38808 4.50869C9.59385 4.51674 9.79364 4.58018 9.96636 4.69233L13.9604 7.63233C14.1255 7.7354 14.2616 7.87879 14.3561 8.04901C14.4505 8.21924 14.5 8.41068 14.5 8.60533C14.5 8.79998 14.4505 8.99142 14.3561 9.16164C14.2616 9.33186 14.1255 9.47526 13.9604 9.57833L9.96636 12.5183C9.79364 12.6305 9.59385 12.6939 9.38808 12.702C9.1823 12.71 8.97817 12.6624 8.79722 12.5641C8.61627 12.4658 8.46521 12.3204 8.35999 12.1434C8.25476 11.9664 8.19926 11.7643 8.19936 11.5583V10.8913Z"
                  fill="white"
                />
              </svg>
              <div className="reply-message-content-container">
                <span>{formatPlayerId(replyChatData.playerId, false, false, '', btcAddress)}:</span>
                <span className="reply-message-content">{trim(replyChatData.content)}</span>
              </div>
            </div>
          )}
          <span className="message-content">{trim(message.content)}</span>
        </div>
      </CopyToClipboard>
      {copiedMessageIndex === index && <div className="copy-success-tip">✅ Copied!</div>}
      {showOption && menuPos && (
        <div
          style={{
            position: 'absolute',
            [menuPos.yDirection]: menuPos.y,
            [menuPos.xDirection]: menuPos.x,
            zIndex: 9999,
          }}>
          <ChatOptionButton
            tabType={tabType}
            message={message}
            handleReply={() => {
              handleReply(message);
              setShowOption(false);
              setMenuPos(null);
            }}
          />
        </div>
      )}
    </MessageItem>
  );
};

export default ChatRoom;
