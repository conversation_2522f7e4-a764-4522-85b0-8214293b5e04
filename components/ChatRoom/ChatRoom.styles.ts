import styled from 'styled-components';

export const ChatRoomView = styled.div<{ isExpanded: boolean }>`
  position: relative;
  width: ${(props) => (props.isExpanded ? '35.625rem' : '12.5rem')};
  height: ${(props) => (props.isExpanded ? '17.5rem' : '3.125rem')};
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 1.25rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 0.0625rem solid #fff;
  padding: 1.5rem;
  box-sizing: border-box;

  .new-message-tip {
    position: absolute;
    bottom: 0.625rem; // 保持固定距离底部0.625rem
    left: 50%;
    transform: translateX(-50%);
    width: 6.25rem;
    height: 2.5rem;
    background-color: rgba(0, 0, 0, 0.85);
    color: #ffda0b;
    font-size: 0.75rem;
    line-height: 2.5rem; // 修正行高与高度匹配
    text-align: center; // 文本居中
    border-radius: 1.125rem;
    border: 0.0625rem solid #ffda0b;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100; // 确保在消息上方显示
    pointer-events: auto; // 确保可点击
    box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.3); // 添加阴影增强视觉效果
  }
`;

export const ToggleButton = styled.button<{ isExpanded: boolean }>`
  position: absolute;
  top: 0rem;
  left: 0rem;
  ${(props) =>
    props.isExpanded
      ? `
    width: 100%;
  `
      : `
    width: 100%;
  `}
  background: none; // 移除背景色
  border: none;
  color: white;
  cursor: pointer;
  padding: 0; // 移除内边距
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const MessageList = styled.div`
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; // 禁用水平滚动
  /* padding: 1.25rem 0.625rem 1.25rem 0.625rem; */
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
  padding-right: 1.875rem;

  &.withScrollBar {
    padding-right: 1.5rem;
  }
  /* 自定义滚动条样式 */

  &::-webkit-scrollbar {
    width: 0.375rem; /* 滚动条宽度 */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道背景 */
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 1); /* 滚动条颜色，半透明白色 */
    border-radius: 0.125rem; /* 圆角 */
  }

  /* 鼠标悬停时的滚动条样式 */

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 1); /* 悬停时稍微更亮一些 */
  }

  /* 隐藏 Firefox 的默认滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 1) transparent;
`;

export const MessageItem = styled.div<{
  isCurrentPlayer: boolean;
  playerColor: string;
  playerTextColor: string;
  admin: boolean;
  isReply: boolean;
}>`
  display: flex;
  gap: 0.375rem;
  width: 100%; // 确保消息项占满容器宽度
  align-items: start;
  position: relative; // 添加相对定位，用于复制提示动画定位
  cursor: pointer; // 添加指针样式，提示可点击

  .player-id-container {
    flex-shrink: 0; // 防止玩家ID区域被压缩
    min-width: 8.75rem;
    margin-top: ${(props) => (props.isReply ? '1.375rem' : '0rem')};
    /* min-width: 7.5rem; // 设置固定最小宽度 */
    /* max-width: 7.5rem; // 设置固定最大宽度 */
  }

  .player-id {
    font-size: 0.875rem;
    font-weight: bold;
    color: ${(props) => (props.admin ? '#FFFF27' : props.playerColor)};
    white-space: nowrap; // 防止玩家ID换行
    overflow: hidden; // 超出部分隐藏
    text-overflow: ellipsis; // 超出显示省略号
    display: block; // 确保省略号正常工作
    position: relative;
    display: flex;
    float: right;
    align-items: center;
    gap: 0.125rem;
  }

  .message-content-container {
    flex: 1; // 让消息内容区域占据剩余空间
    min-width: 0; // 允许内容区域被压缩
    font-size: 1rem;
    line-height: 1rem;
    transition: all 0.2s ease;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    position: relative;

    .reply-message-container {
      width: 100%;
      height: 100%;
      color: #c0c0c0;
      font-size: 0.875rem;
      background-color: rgba(255, 255, 255, 0.1);
      box-sizing: border-box;
      padding: 0.125rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .reply-message-icon {
        /* flex: 30%; */
      }

      .reply-message-content-container {
        flex: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .reply-message-content {
          margin-left: 0.625rem;
        }
      }
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.1); // 悬停时的背景色
      text-decoration: underline; // 悬停时添加下划线
      text-decoration-style: dotted; // 下划线样式为点状
      text-decoration-color: rgba(255, 255, 255, 0.5); // 下划线颜色
      text-underline-offset: 0.1875rem; // 下划线偏移
    }

    .message-content {
      color: ${(props) => (props.admin ? '#FFFF27' : props.playerTextColor)};
      word-wrap: break-word; // 允许长单词换行
      word-break: break-all; // 确保所有文本都能换行
      white-space: pre-wrap; // 保留空格和换行，同时允许自动换行
      user-select: text; // 允许文本选择
    }
  }

  .copy-success-tip {
    position: absolute;
    top: -1.875rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.625rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    pointer-events: none; // 防止动画影响鼠标事件
    animation: fadeOutUp 1.5s forwards;
  }

  @keyframes fadeOutUp {
    0% {
      opacity: 0;
      transform: translate(-50%, 0.625rem);
    }
    20% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
    80% {
      opacity: 1;
      transform: translate(-50%, 0);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -0.625rem);
    }
  }
`;

export const ChevronLeftIconContainer = styled.div`
  font-size: 1.875rem;
  line-height: 1.5625rem;
  width: 100%;
  height: 3.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const StyledTimestamp = styled.p`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0rem auto;
`;
