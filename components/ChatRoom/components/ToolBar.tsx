import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { CheckboxContainer, ListContainer, ListItem, ToolBarContainer } from './ToolBar.styles';
import { ChatTabType } from '@/model/Chat/ChatType';
import { ChatListener } from '@/model/Chat/ChatListener';
import { ChatManager } from '@/model/Chat/ChatManager';
import { ChatEvent } from '@/model/Chat/ChatEvent';

const initialList = [
  {
    label: 'Global',
    icon: '/image/chatRoom/global.png',
    type: ChatTabType.Room,
  },
  {
    label: 'Satworld',
    icon: '/image/chatRoom/satworld2.png',
    type: ChatTabType.SatWorld,
    childrenIcon: '/image/chatRoom/telegram.png',
    link: 'https://t.me/UniWorldsHQ',
  },
  {
    label: 'Fractal',
    icon: '/image/chatRoom/fractal1.png',
    type: ChatTabType.Fractal,
    childrenIcon: '/image/chatRoom/telegram.png',
    link: 'https://t.me/fractal_bitcoin_official',
  },
  {
    label: 'WangCai',
    icon: '/image/chatRoom/wangcai.png',
    type: ChatTabType.WangCai,
    childrenIcon: '/image/chatRoom/telegram.png',
    link: 'https://t.me/wangcai_fractal',
  },
];

interface ListItemProps {
  typeList: ChatTabType[];
  onChange: (id: number) => void;
  onAdminChange: (checked: boolean) => void;
}

const ToolBar = ({ typeList, onChange, onAdminChange }: ListItemProps) => {
  const [selectedId, setSelectedId] = useState(ChatTabType.Node);
  const [adminOnly, setAdminOnly] = useState(false);
  const [newMessageMap, setNewMessageMap] = useState<Map<ChatTabType, number>>(new Map());

  useEffect(() => {
    const updateMessageMap = () => {
      typeList.forEach((type) => {
        const chatList = ChatManager.getInstance().getChatList(type);
        if (chatList) {
          setNewMessageMap((prev) => {
            prev.set(type, chatList.getNewMessageCount());
            return new Map(prev);
          });
        }
      });
    };
    updateMessageMap();
    ChatListener.getInstance().addListener(ChatEvent.NewMessageChange, updateMessageMap);
    return () => {
      ChatListener.getInstance().removeListener(ChatEvent.NewMessageChange, updateMessageMap);
    };
  }, [typeList]);

  useEffect(() => {
    if (typeList.includes(ChatTabType.Room)) {
      setSelectedId(ChatTabType.Room);
      onChange(ChatTabType.Room);
      return;
    }
    if (typeList.includes(selectedId)) {
      return;
    }
    setSelectedId(typeList[0]);
    onChange(typeList[0]);
  }, [typeList]);

  return (
    <ToolBarContainer>
      {/* 频道社区列表 */}
      <ListContainer>
        {typeList.map((type) => {
          const active = selectedId === type;
          const newMessageCount = newMessageMap.get(type) || 0;
          const item = initialList.find((item) => item.type === type);
          if (item) {
            return (
              <ListItem
                key={type}
                active={active}
                onClick={() => {
                  setSelectedId(type);
                  onChange(type);
                }}
                onDoubleClick={() => {
                  if (item.link) {
                    window.open(item.link, '_blank');
                  }
                }}>
                <span className="icon-wrapper">
                  <Image
                    src={item.icon}
                    alt={item.label}
                    width={38}
                    height={38}
                    style={{
                      width: '2.375rem',
                      height: '2.375rem',
                    }}
                  />
                  {item.childrenIcon && (
                    <span
                      style={{
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        width: '1.375rem',
                        height: '1.375rem',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Image
                        src={item.childrenIcon}
                        alt="telegram"
                        width={22}
                        height={22}
                        style={{
                          width: '1.375rem',
                          height: '1.375rem',
                        }}
                      />
                    </span>
                  )}
                </span>
                {active && <span className="label">{item.label}</span>}
                {newMessageCount > 0 && !active && (
                  <img
                    src={'./image/chatRoom/redPoint.png'}
                    width={20}
                    height={20}
                    alt={'example'}
                    style={{
                      position: 'absolute',
                      top: '-0.5rem',
                      left: '-0.5rem',
                      width: '1.25rem',
                      height: '1.25rem',
                    }}
                  />
                )}
              </ListItem>
            );
          }
        })}
      </ListContainer>
      {/* 控制Admin消息开关 */}
      <CheckboxContainer checked={adminOnly}>
        <input
          type="checkbox"
          checked={adminOnly}
          onChange={() => {
            setAdminOnly((v) => !v);
            onAdminChange(!adminOnly);
          }}
        />
        <span>Just Admin</span>
      </CheckboxContainer>
    </ToolBarContainer>
  );
};

export default ToolBar;
