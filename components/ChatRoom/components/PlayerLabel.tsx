import styled from 'styled-components';

// 创建PlayerLabel组件
const LabelWrapper = styled.span<{ color: string; left?: string }>`
  /* position: absolute;
  left: ${(props) => props.left || '100%'};
  top: 0px;
  transform: translate(-93%, 0); */
  color: ${(props) => props.color};
`;

// 特殊账号Label的样式可以通过类名来区分
const SpecialLabel = styled(LabelWrapper)`
  &.gm {
    /* animation: pulse 2s infinite; */
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
    100% {
      opacity: 1;
    }
  }
`;

// "Me" Label的样式
const MeLabel = styled.div<{ $myColor?: string; $left?: string }>`
  /* position: absolute;
  left: ${(props) => props.$left || '100%'};
  top: 0px;
  transform: translate(-93%, 0); */
  color: ${(props) => props.$myColor || '#ffff27'};
`;

interface PlayerLabelProps {
  playerId: string;
  currentPlayerId: string;
  specialAccount: {
    label: string;
    color: string;
  } | null;
  myColor?: string;
  left?: string;
  admin?: boolean;
}

const PlayerLabel: React.FC<PlayerLabelProps> = ({
  playerId,
  currentPlayerId,
  specialAccount,
  myColor,
  left,
  admin,
}) => {
  const isCurrentPlayer = playerId === currentPlayerId;

  // 只有特殊账号或当前玩家才显示标签
  // if (!specialAccount && !isCurrentPlayer) {
  //   return null;
  // }

  // 如果是admin玩家，则显示AM
  if (admin) {
    return <div>{`(AM)`}</div>;
  }

  // 当前玩家优先显示"Me"标签，无论是否为特殊账号
  if (isCurrentPlayer) {
    return <div>{`(ME)`}</div>;
  }

  // 特殊账号显示特殊标签(只有不是当前玩家时才显示)
  if (specialAccount) {
    return <div>{`(GM)`}</div>;
  }

  return null;
};

export default PlayerLabel;
