import styled from 'styled-components';

export const ChatWindowWrapper = styled.div<{ replyMode?: boolean }>`
  width: 100%;
  position: absolute;
  left: 50%;
  bottom: ${({ replyMode }) => (replyMode ? '-7.5rem' : '-5rem')};
  transform: translateX(-50%);
  transition: bottom 0.3s ease;
  display: flex;
  z-index: 2;
  pointer-events: auto; // 恢复交互

  .history-btn {
    align-self: flex-end;
    width: 4.5rem;
    height: 4.5rem;
    cursor: pointer;
  }

  .question-box {
    flex: 1;

    .question-history {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      & > span {
        padding: 0.5625rem 1rem;
        word-break: break-all;
        font-family: Inter;
        font-weight: 400;
        font-size: 0.75rem;
        line-height: 0.9075rem;
        color: #ffffff;
        background: #795f49;
        border-radius: 0.75rem;
        cursor: pointer;
      }
    }

    .question-ask {
      margin-top: 0.5rem;
      display: flex;
      align-items: center;
      background: #ffffff;
      border: 0.0625rem solid #ac9d83;
      box-shadow: 0rem 0.25rem 1.5rem 0rem #00000059;
      border-radius: 1.5rem;
      padding: 0.3125rem 1rem;
      box-sizing: border-box;
      height: 4.375rem;
      gap: 0.125rem;

      .question-ask-input {
        flex: 1;
        overflow: hidden;
        height: 100%;

        & > input {
          width: 100%;
          height: 100%;
          padding: 0;
          outline: none;
          border: 0;
          font-family: Inter;
          font-weight: 400;
          font-size: 1.125rem;
          line-height: 1.36125rem;
          color: #140f08;

          &::placeholder {
            color: #686663;
          }
        }
      }

      button {
        width: 2.5rem;
        height: 2.5rem;
        outline: none;
        border-radius: 0.625rem;
        border: 0;
        background: #ff8316;
        box-shadow: 0rem -0.15625rem 0rem 0rem #00000040 inset;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        & > img {
          width: 1.25rem;
          height: 1.25rem;
        }

        &[disabled] {
          cursor: not-allowed;
          background: #c9b7a5;
          box-shadow: 0rem -0.15625rem 0rem 0rem #00000040 inset;
        }

        &.loading {
          & > img {
            animation: loading-ano 1s infinite;
          }
        }
      }
    }
  }
`;

// 添加倒计时样式
export const CooldownText = styled.span`
  font-size: 1.125rem;
  font-weight: bold;
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
