import styled from 'styled-components';

const ChatRoomBirthdayButtonContainer = styled.div`
  width: 2.75rem;
  height: 2.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-sizing: border-box;
  margin-right: 0.25rem;
  .gift-container {
    width: 100%;
    height: 100%;
    background: url('/image/chatRoom/gift1.png') no-repeat center center;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    & > svg {
      width: 1.5625rem;
      height: 1.5625rem;
    }
    .loader {
      width: 1.875rem;
      height: 1.875rem;
      border: 0.1875rem solid #fff;
      border-bottom-color: transparent;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;
      animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
`;

const ChatRoomBirthdayButton = ({
  onAirdrop,
  isLoading,
}: {
  onAirdrop?: () => void;
  isLoading?: boolean;
}) => {
  return (
    <ChatRoomBirthdayButtonContainer onClick={onAirdrop}>
      <div className="gift-container">
        {isLoading ? (
          <div className="loader"></div>
        ) : (
          <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_6432_21291)">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.6795 6.63999H6.38174C6.11422 6.28454 5.94991 5.8622 5.90688 5.41942C5.86745 4.98056 5.97545 4.67542 6.14517 4.48856C6.30117 4.31542 6.68688 4.04799 7.62116 4.04799C8.47316 4.04799 9.34916 4.63085 10.0932 5.65942C10.328 5.98513 10.5217 6.32113 10.6795 6.63999ZM3.34402 5.64571C3.37488 5.98171 3.44174 6.31599 3.54117 6.63999H2.52288C2.06822 6.63999 1.63219 6.8206 1.3107 7.14209C0.989205 7.46358 0.808594 7.89962 0.808594 8.35428V9.86628C0.808594 10.3209 0.989205 10.757 1.3107 11.0785C1.63219 11.3999 2.06822 11.5806 2.52288 11.5806H22.4154C22.8701 11.5806 23.3061 11.3999 23.6276 11.0785C23.9491 10.757 24.1297 10.3209 24.1297 9.86628V8.35428C24.1297 7.89962 23.9491 7.46358 23.6276 7.14209C23.3061 6.8206 22.8701 6.63999 22.4154 6.63999H21.3972C21.4966 6.31599 21.5634 5.98171 21.5943 5.64571C21.6766 4.69942 21.4709 3.61942 20.7029 2.76571C19.9229 1.90342 18.7486 1.47828 17.3172 1.47828C15.26 1.47828 13.712 2.83599 12.7589 4.15942C12.6582 4.29949 12.5616 4.4424 12.4692 4.58799C12.3761 4.44238 12.279 4.29946 12.1777 4.15942C11.228 2.83256 9.67831 1.47656 7.62288 1.47656C6.18974 1.47656 5.01716 1.90171 4.23716 2.76742C3.46745 3.61942 3.26345 4.69942 3.34574 5.64742L3.34402 5.64571ZM14.2589 6.63999H18.5566C18.825 6.28503 18.9894 5.86243 19.0315 5.41942C19.0709 4.98056 18.9629 4.67542 18.7932 4.48856C18.6372 4.31542 18.2515 4.04799 17.3172 4.04799C16.4652 4.04799 15.5892 4.63085 14.8452 5.65942C14.6103 5.98513 14.4166 6.32113 14.2589 6.63999ZM2.52288 13.7234H11.3977V24.5783H4.16174C3.70708 24.5783 3.27104 24.3977 2.94955 24.0762C2.62806 23.7547 2.44745 23.3186 2.44745 22.864V13.7234H2.52288ZM20.7766 24.5783H13.5406V13.7234H22.4154H22.4909V22.864C22.4909 23.3186 22.3103 23.7547 21.9888 24.0762C21.6673 24.3977 21.2312 24.5783 20.7766 24.5783Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_6432_21291">
                <rect width="24" height="24" fill="white" transform="translate(0.46875 0.828125)" />
              </clipPath>
            </defs>
          </svg>
        )}
      </div>
    </ChatRoomBirthdayButtonContainer>
  );
};

export default ChatRoomBirthdayButton;
