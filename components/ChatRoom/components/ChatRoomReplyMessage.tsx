import styled from 'styled-components';
import { forwardRef, Ref, useImperativeHandle, useRef, useState } from 'react';

const ChatRoomReplyMessageContainer = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 3.125rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  position: absolute;
  left: 0;
  bottom: -2.8125rem;

  .reply-message-container {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    flex: 1;
    min-width: 0;
    .user-name {
      color: #ff8316;
      flex-shrink: 0;
      position: relative;
    }
    .message-content {
      color: #686663;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
      display: block;
      flex: 1;
      min-width: 0;
      box-sizing: border-box;
      padding-left: 2.5rem;
    }
  }
  .cancel-reply {
    cursor: pointer;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

interface ReplyMessageProps {
  myPlayer: any;
  handleCloseReply: () => void;
}

export interface ReplyMessageRef {
  openReplyMessage: (user: string, message: string) => void;
  closeReplyMessage: () => void;
}

const ChatRoomReplyMessage = forwardRef((props: ReplyMessageProps, ref: Ref<ReplyMessageRef>) => {
  const { myPlayer, handleCloseReply } = props;
  const [replyMessage, setReplyMessage] = useState({
    user: '',
    message: '',
  });

  const visibleRef = useRef(false);

  useImperativeHandle(ref, () => ({
    openReplyMessage: (user: string, message: string) => {
      setReplyMessage({ user, message });
      visibleRef.current = true;
    },
    closeReplyMessage: () => {
      setReplyMessage({ user: '', message: '' });
      visibleRef.current = false;
    },
  }));

  if (!visibleRef.current) {
    return null;
  }

  function containsChinese(str: string) {
    return /[\u4e00-\u9fa5]/.test(str);
  }

  function isPureEnglishOrNumber(str: string) {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  function formatPlayerId(playerId: string) {
    if (!playerId) return '';

    // 如果包含中文
    if (containsChinese(playerId)) {
      // 如果全是中文
      if (isPureEnglishOrNumber(playerId.replace(/[\u4e00-\u9fa5]/g, ''))) {
        if (playerId.length <= 6) return playerId;
        return playerId.slice(0, 3) + '...' + playerId.slice(-3);
      }
      // 中英文混合
      if (playerId.length <= 8) return playerId;
      return playerId.slice(0, 4) + '...' + playerId.slice(-2);
    }

    // 纯英文/数字
    if (playerId.length <= 10) return playerId;
    return playerId.slice(0, 5) + '...' + playerId.slice(-5);
  }

  return (
    <ChatRoomReplyMessageContainer>
      <div className="reply-message-container">
        <svg
          width="15"
          height="12"
          viewBox="0 0 15 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.80285 1.53621C5.82755 1.52533 5.85459 1.52084 5.88148 1.52315C5.90837 1.52545 5.93425 1.53448 5.95674 1.5494C5.97923 1.56432 5.9976 1.58465 6.01018 1.60853C6.02276 1.63241 6.02914 1.65906 6.02873 1.68605V2.97533C6.02873 3.12361 6.08763 3.26582 6.19248 3.37067C6.29733 3.47552 6.43954 3.53443 6.58783 3.53443C7.33366 3.53443 8.83875 3.54002 10.2779 4.45358C11.3782 5.15134 12.5031 6.42161 13.1796 8.78771C12.039 7.68853 10.7363 7.09253 9.59577 6.77608C8.8948 6.58208 8.17428 6.46731 7.44772 6.43391C7.15035 6.41992 6.85243 6.42291 6.5554 6.44286H6.54086L6.53527 6.44397L6.58783 7.00083L6.53192 6.44397C6.39391 6.45784 6.26599 6.52251 6.173 6.62542C6.08001 6.72833 6.02859 6.86213 6.02873 7.00083V8.29012C6.02873 8.41088 5.90573 8.48692 5.80285 8.43995L1.34795 5.16028L1.30099 5.12897C1.27668 5.11436 1.25656 5.09371 1.24259 5.06902C1.22862 5.04433 1.22128 5.01645 1.22128 4.98808C1.22128 4.95972 1.22862 4.93183 1.24259 4.90714C1.25656 4.88245 1.27668 4.8618 1.30099 4.84719L1.34795 4.81588L5.80285 1.53621ZM7.14692 7.54428C7.22371 7.54428 7.30683 7.54652 7.39628 7.55099C7.88158 7.57335 8.5525 7.64715 9.29721 7.85402C10.7799 8.26552 12.5355 9.19809 13.7029 11.2981C13.766 11.4114 13.8666 11.4993 13.9874 11.5466C14.1083 11.5939 14.2418 11.5978 14.3651 11.5575C14.4884 11.5171 14.5939 11.4352 14.6634 11.3256C14.7329 11.2161 14.7622 11.0858 14.7462 10.957C14.2273 6.80851 12.6373 4.62579 10.8772 3.50983C9.48507 2.62645 8.05154 2.45648 7.14692 2.42406V1.68605C7.14703 1.45577 7.08497 1.22974 6.96731 1.0318C6.84964 0.833866 6.68073 0.671358 6.47839 0.561429C6.27605 0.4515 6.04779 0.398226 5.8177 0.407228C5.5876 0.416231 5.3642 0.487175 5.17107 0.612577L0.704992 3.90008C0.520355 4.01533 0.368086 4.17567 0.262519 4.36601C0.156953 4.55635 0.101562 4.77043 0.101562 4.98808C0.101562 5.20574 0.156953 5.41981 0.262519 5.61015C0.368086 5.80049 0.520355 5.96083 0.704992 6.07609L5.17107 9.36358C5.3642 9.48899 5.5876 9.55993 5.8177 9.56893C6.04779 9.57794 6.27605 9.52466 6.47839 9.41473C6.68073 9.3048 6.84964 9.1423 6.96731 8.94436C7.08497 8.74642 7.14703 8.52039 7.14692 8.29012V7.54428Z"
            fill="#140F08"
          />
        </svg>
        {/* 用户名称 */}
        <div className="user-name">
          {formatPlayerId(replyMessage.user)}
          {/* <PlayerLabel
              playerId={replyMessage.user}
              currentPlayerId={myPlayer.btcAddress}
              specialAccount={null}
              myColor="#ff8316"
              left="138%"
            /> */}
        </div>
        {/* 消息内容 */}
        <span className="message-content">{replyMessage.message}</span>
      </div>

      {/* 取消回复 */}
      <span
        className="cancel-reply"
        onClick={() => {
          handleCloseReply();
        }}>
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M4.11719 4.04688L12.024 11.9537"
            stroke="#140F08"
            strokeWidth="1.1182"
            strokeLinecap="round"
          />
          <path
            d="M12.0254 4.05469L4.11855 11.9615"
            stroke="#140F08"
            strokeWidth="1.1182"
            strokeLinecap="round"
          />
        </svg>
      </span>
    </ChatRoomReplyMessageContainer>
  );
});
ChatRoomReplyMessage.displayName = 'ChatRoomReplyMessage';
export default ChatRoomReplyMessage;
