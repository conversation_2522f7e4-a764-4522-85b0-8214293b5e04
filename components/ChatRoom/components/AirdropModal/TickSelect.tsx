import styled from 'styled-components';
import { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import TaskFBIcon from '/public/image/task/fb.png';
import Image from 'next/image';

interface Option {
  balance: string;
  tick: string;
  anchorAmount?: string;
}

interface TickSelectProps {
  list: Option[];
  onChange: (val: Option) => void;
}

const SelectWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const SelectButton = styled.button<{ isOpen: boolean }>`
  background: #fff7e7;
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  padding: 0;
  position: relative;
  // 如果是打开则旋转180度
  .tick-drop-down-icon {
    position: absolute;
    right: 0;
    bottom: 0;
    transform: ${(props) => (props.isOpen ? 'rotate(180deg)' : 'rotate(0deg)')};
    transition: transform 0.3s ease-in-out;
  }
`;

const Dropdown = styled.div`
  position: absolute;
  top: 3.5rem;
  right: 0;
  min-width: 10rem;
  background: #fff;
  border-radius: 1.125rem;
  box-shadow: 0 0.25rem 1.5rem rgba(0, 0, 0, 0.13);
  padding: 0.625rem 0;
  z-index: 100;
  max-height: 11.25rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0;
  &::-webkit-scrollbar {
    width: 0.375rem;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #e5d6c2;
    border-radius: 0.375rem;
  }
`;

const OptionItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.625rem;
`;

const OptionRow = styled.div<{ selected: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.5rem 1.125rem 0.5rem 0.75rem;
  cursor: pointer;
  font-size: 1.125rem;
  font-weight: 600;
  color: #14110a;
  background: transparent;
  border-radius: 0.75rem;
  position: relative;
  &:hover {
    background: #f5e5c7;
  }
`;

const TickIcon = 'https://fractal-static.unisat.io/icon/brc20/';

const TickIconDropDown = () => {
  return (
    <svg
      style={{ width: '1.375rem', height: '1.375rem' }}
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="tick-drop-down-icon">
      <rect x="0.5" y="0.5" width="21" height="21" rx="5.5" fill="white" />
      <rect x="0.5" y="0.5" width="21" height="21" rx="5.5" stroke="#140F08" />
      <path
        d="M12.4973 15.9163C11.7669 16.9514 10.2318 16.9514 9.50141 15.9163L5.10036 9.67942C4.24338 8.46497 5.11192 6.78906 6.5983 6.78906H15.4004C16.8868 6.78906 17.7553 8.46497 16.8983 9.67942L12.4973 15.9163Z"
        fill="#140F08"
      />
    </svg>
  );
};

const TickSelect = ({ list, onChange }: TickSelectProps) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<Option>(list[0]);
  const ref = useRef<HTMLDivElement>(null);
  const btnRef = useRef<HTMLButtonElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  // 点击外部关闭
  useEffect(() => {
    if (!open) return;
    const handleClick = (e: MouseEvent) => {
      const target = e.target as Node;
      // 判断是否在SelectWrapper或Dropdown内部
      if (
        ref.current &&
        (ref.current.contains(target) ||
          document.querySelector('[data-dropdown]')?.contains(target))
      ) {
        return;
      }
      setOpen(false);
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);

  // 若list变化，重置选中项
  useEffect(() => {
    setSelected(list[0]);
  }, [list]);

  const tickIconUrl = useMemo(() => {
    if (selected.tick === 'sFB___000') {
      return TaskFBIcon.src;
    }
    return `${TickIcon}${selected.tick}`;
  }, [selected]);

  // 计算Dropdown绝对位置
  useEffect(() => {
    if (open && btnRef.current) {
      const rect = btnRef.current.getBoundingClientRect();
      setDropdownStyle({
        position: 'absolute',
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        zIndex: 9999,
        maxWidth: '13.75rem',
        overflowX: 'hidden',
        overflowY: 'auto',
      });
    }
  }, [open]);

  return (
    <SelectWrapper ref={ref}>
      <SelectButton ref={btnRef} onClick={() => setOpen((v) => !v)} isOpen={open}>
        <Image
          src={tickIconUrl}
          alt={selected.tick}
          width={48}
          height={48}
          style={{ borderRadius: '50%', width: '3rem', height: '3rem' }}
        />
        <TickIconDropDown />
      </SelectButton>
      {open &&
        ReactDOM.createPortal(
          <Dropdown data-dropdown style={dropdownStyle}>
            {list.map((item) => {
              const tickUrl =
                item.tick === 'sFB___000' ? TaskFBIcon.src : `${TickIcon}${item.tick}`;
              return (
                <OptionRow
                  key={item.tick}
                  selected={item.tick === selected.tick}
                  onClick={() => {
                    setSelected(item);
                    onChange(item);
                    setOpen(false);
                  }}>
                  <OptionItem>
                    {/* 只有选中才会显示 */}
                    {item.tick === selected.tick ? (
                      <svg
                        style={{
                          width: '1rem',
                          height: '0.8125rem',
                        }}
                        width="16"
                        height="13"
                        viewBox="0 0 16 13"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M2 4.88L6.68293 10L14 2"
                          stroke="#FF8316"
                          strokeWidth="3"
                          strokeLinecap="round"
                        />
                      </svg>
                    ) : (
                      <div style={{ width: '1rem', height: '1.8125rem' }} />
                    )}
                    <Image
                      src={tickUrl}
                      alt={item.tick}
                      width={24}
                      height={24}
                      style={{
                        borderRadius: '50%',
                        width: '1.5rem',
                        height: '1.5rem',
                      }}
                    />
                    <span>{item.tick}</span>
                  </OptionItem>
                </OptionRow>
              );
            })}
          </Dropdown>,
          document.body
        )}
    </SelectWrapper>
  );
};

export default TickSelect;
