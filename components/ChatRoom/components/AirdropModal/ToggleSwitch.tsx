import styled from 'styled-components';

const ToggleSwitchContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 1.875rem;
  margin-bottom: 1.875rem;
  gap: 0.25rem;
`;

const TabsRow = styled.div`
  display: flex;
  gap: 0;
  margin-bottom: 0.25rem;
`;

const TabButton = styled.button<{ active: boolean }>`
  border: none;
  outline: none;
  background: ${({ active }) => (active ? '#fc7922' : '#c1af9c')};
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 1rem;
  height: 3rem;
  padding: 0.125rem 2.5rem;
  margin-right: -1.625rem;
  z-index: ${({ active }) => (active ? 2 : 1)};
  position: relative;
  cursor: pointer;
  transition:
    background 0.2s,
    color 0.2s;
  box-shadow: ${({ active }) => (active ? '0 0.25rem 0 0 #d9c4a3' : 'none')};
`;

const Desc = styled.div`
  font-size: 0.9375rem;
  color: #14110a;
  margin-top: 0.375rem;
  margin-bottom: 0.125rem;
  .desc-content {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }
`;

const MenuList = [
  {
    title: 'Generate Drops',
    id: 1,
  },
  {
    title: 'Equally Distributed',
    id: 2,
  },
];

const PlayersNumber = styled.span`
  color: #fc7922;
  font-weight: 700;
`;

interface ToggleSwitchProps {
  value: number;
  onTabChange: (id: number) => void;
  players: number;
}

const ToggleSwitch = ({ value, onTabChange, players }: ToggleSwitchProps) => {
  const handleTabClick = (id: number) => {
    if (id !== value) {
      onTabChange(id);
    }
  };

  return (
    <ToggleSwitchContainer>
      <TabsRow>
        {MenuList.map((tab) => (
          <TabButton key={tab.id} active={value === tab.id} onClick={() => handleTabClick(tab.id)}>
            {tab.title}
          </TabButton>
        ))}
      </TabsRow>
      <div className="content">
        <Desc>
          {value === 1 ? (
            <>
              Enter token and amount to airdrop
              <>
                {' '}
                (<span style={{ color: '#fc7922', fontWeight: 600 }}>{players || 0}</span> players)
              </>
            </>
          ) : (
            'Enter token and recipients for airdrop.'
          )}
        </Desc>
      </div>
    </ToggleSwitchContainer>
  );
};

export default ToggleSwitch;
