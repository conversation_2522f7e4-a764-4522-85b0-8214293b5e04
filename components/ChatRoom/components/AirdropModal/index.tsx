'use client';
import { forwardRef, Ref, useImperative<PERSON>and<PERSON>, useMemo, useState } from 'react';
import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import Image from 'next/image';
import styled from 'styled-components';
import ToggleSwitch from './ToggleSwitch';
import TabContent from './TabContent';
import { confirmRedPacketSend, getRedPacketTickList, preRedPacketSend } from '@/server';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import toast from 'react-hot-toast';
import SignMessage from '@/utils/signMessage';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';
import { NetPlayerManager } from '@/model/NetPlayer/NetPlayerManager';

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
`;

const MenuContent = styled.div`
  display: flex;
  width: calc(100% - 1.25rem);
  padding: 2.25rem 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.75rem;
  box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15);
  align-items: center;
  justify-content: center;
  box-sizing: border-box;

  .tab-2-content {
    .tab-2-content-list {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 0.625rem;

      .tab-2-content-item {
        display: flex;
        align-items: center;
      }
    }
  }
`;

interface ModalProps {
  onClose: () => void;
  onReward?: (reward: { name: string; quantity: string }) => void;
  setBirthdayButtonLoading: (isLoading: boolean) => void;
}

export interface AirdropModalRef {
  open: () => void;
  close: () => void;
  reset: () => void;
}

const AirdropModal = forwardRef<AirdropModalRef, ModalProps>(
  ({ onClose, onReward, setBirthdayButtonLoading }: ModalProps, ref: Ref<AirdropModalRef>) => {
    const [isOpen, setIsOpen] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [tabId, setTabId] = useState(1);
    const [coinAmount, setCoinAmount] = useState('');
    const [birthdayAmount, setBirthdayAmount] = useState('');
    const [coinAmount2, setCoinAmount2] = useState('');
    const [tickList, setTickList] = useState<{ balance: string; tick: string }[]>([]);
    const [selectedTick, setSelectedTick] = useState<{
      balance: string;
      tick: string;
      anchorAmount?: string;
    } | null>(null);
    const { btcAddress, btcWallet } = useSelector(
      (state: { AppReducer: IAppState }) => state.AppReducer
    );
    const [players, setPlayers] = useState<any[]>([]);

    useImperativeHandle(ref, () => ({
      open: async () => {
        try {
          setBirthdayButtonLoading(true);
          const playerList = NetPlayerManager.getInstance().getPlayerList();
          setPlayers(playerList);
          setBirthdayAmount(playerList.length.toString() || '0');
          const res = await getRedPacketTickList();
          const { code, msg, data } = res.data;
          if (code === 1) {
            setTickList(data);
            setTabId(1);
            setIsOpen(true);
            // 禁止玩家控制
            KeyPressUtil.setEnable(false);
          } else {
            toast.error(msg);
          }
        } catch (error) {
          console.log(error);
        } finally {
          setBirthdayButtonLoading(false);
        }
      },
      // 提交成功后重置积分和资源
      reset: () => undefined,
      close: () => {
        setIsOpen(false);
        setTabId(1);
        KeyPressUtil.setEnable(true);
      },
    }));

    const createParams = (tabId: number, playersList?: any[], birthdayAmount?: string) => {
      const type = tabId === 1 ? 'average' : 'lucky';
      const totalAmount = tabId === 1 ? coinAmount : coinAmount2;
      const targetAddressList = tabId === 1 ? playersList : undefined;
      const message = '恭喜发财';
      const quantity = tabId === 1 ? playersList?.length : birthdayAmount;
      const createAddress = btcAddress;
      const tick = selectedTick?.tick;
      return {
        type,
        totalAmount,
        targetAddressList,
        message,
        quantity,
        createAddress,
        tick,
      };
    };

    const onConfirm = async () => {
      setConfirmLoading(true);
      try {
        const params = createParams(
          tabId,
          NetPlayerManager.getInstance().getPlayerList(),
          birthdayAmount
        );
        const res = await preRedPacketSend(params);
        const { code, msg, data } = res.data;
        if (code === 1) {
          const { params, id, signMsgs } = data;
          if (signMsgs.length > 0) {
            // const sigs = [];
            // for (const msg of signMsgs) {
            //   const signMsg = await SignMessage.signMessage(btcWallet, msg);
            //   sigs.push(signMsg);
            // }
            const sigs = await SignMessage.multiSignMessage(btcWallet, signMsgs);
            const confirmParams = {
              ...params,
              sigs,
            };
            const confirmRes = await confirmRedPacketSend(confirmParams, id);
            const { code, msg } = confirmRes.data;
            if (code === 1) {
              toast.success('Drop Success!');

              handleClose();
            } else {
              toast.error(msg);
            }
          }
        } else {
          toast.error(msg);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setConfirmLoading(false);
      }
    };

    function handleClose() {
      setIsOpen(false);
      setTabId(1);
      setCoinAmount('');
      setBirthdayAmount('');
      setCoinAmount2('');
      if (onClose) onClose();
    }

    const onCoinAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setCoinAmount(e.target.value);
    };

    const onBirthdayAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setBirthdayAmount(e.target.value);
    };

    const onCoinAmount2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
      setCoinAmount2(e.target.value);
    };

    const isOverBalance = useMemo(() => {
      // balance是最大值
      // anchorAmount 是最小值
      // coinAmount 大于max 或者 小于min 都不能发起红包请求
      if (selectedTick) {
        const balance = Number(selectedTick.balance);
        const res = Number(tabId === 1 ? coinAmount : coinAmount2);
        const anchorAmount = Number(selectedTick.anchorAmount);
        const result = res > balance || res < anchorAmount * Number(birthdayAmount);
        return result;
      }

      return false;
    }, [coinAmount, selectedTick, tabId, coinAmount2, birthdayAmount]);

    return (
      <>
        <Dialog isOpen={isOpen}>
          <ModalContent
            confirmLoading={confirmLoading}
            confirmText="Drop"
            confirmDisabled={isOverBalance}
            onConfirm={onConfirm}
            onClose={handleClose}
            onCancel={handleClose}
            title={
              <Image
                src="/image/chatRoom/title.png"
                alt="Drop"
                width={360}
                height={62}
                style={{
                  width: '22.5rem',
                  height: '3.875rem',
                }}
              />
            }
            footerStyle={{
              padding: '0.375rem 2.625rem 1.875rem',
            }}>
            <ContentContainer>
              <ToggleSwitch value={tabId} onTabChange={setTabId} players={players.length} />
              <MenuContent>
                <TabContent
                  tabId={tabId}
                  coinAmount={coinAmount}
                  onCoinAmountChange={onCoinAmountChange}
                  birthdayAmount={birthdayAmount}
                  onBirthdayAmountChange={onBirthdayAmountChange}
                  coinAmount2={coinAmount2}
                  onCoinAmount2Change={onCoinAmount2Change}
                  tickList={tickList}
                  players={players}
                  isSelectedTick={(tick) => {
                    setSelectedTick(tick);
                  }}
                />
              </MenuContent>
            </ContentContainer>
          </ModalContent>
        </Dialog>
      </>
    );
  }
);

AirdropModal.displayName = 'AirdropModal';

export default AirdropModal;
