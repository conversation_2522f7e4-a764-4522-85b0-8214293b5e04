import React, { forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import EmojiButton, { EmojiButtonRef } from '@/commons/EmojiButton';
import { trim } from 'es-toolkit';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';
import SendImg from '/public/image/recording/send.svg';
import { ChatWindowWrapper, CooldownText } from './ChatRoomInput.styles';
import ChatRoomBirthdayButton from './ChatRoomBirthdayButton';
import { ChatManager } from '@/model/Chat/ChatManager';
import { ChatTabType } from '@/model/Chat/ChatType';
import { ChatData } from '@/model/Chat/ChatData';
import { useAppSelector } from '@/hooks/useStore';
import { SCENE_TYPE } from '@/constant/type';

interface ChatRoomInputProps {
  replyMode: boolean;
  curChatType: ChatTabType;
  onAirdrop?: () => void;
  replyChatData: ChatData | null;
  onSend?: () => void;
}

export interface ChatRoomInputRef {
  focusInput: () => void;
  setIsLoading: (isLoading: boolean) => void;
}

const ChatRoomInput = forwardRef<ChatRoomInputRef, ChatRoomInputProps>(
  ({ replyMode, curChatType, onAirdrop, replyChatData, onSend }, ref) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const [inputValue, setInputValue] = useState('');
    const [isOnCooldown, setIsOnCooldown] = useState(false);
    const [cooldownSeconds, setCooldownSeconds] = useState(0);
    const emojiButtonRef = useRef<EmojiButtonRef>(null);
    const cooldownTimerRef = useRef<NodeJS.Timeout | null>(null);
    const myPlayer = GetMyPlayer();
    const [isLoading, setIsLoading] = useState(false);
    const { sceneType } = useAppSelector((state) => state.AppReducer);

    const startCooldown = () => {
      setIsOnCooldown(true);
      setCooldownSeconds(3);

      // 清除任何现有的冷却计时器
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current);
      }

      // 创建新的冷却计时器，每秒减少冷却时间
      cooldownTimerRef.current = setInterval(() => {
        setCooldownSeconds((prev) => {
          if (prev <= 1) {
            // 冷却结束
            clearInterval(cooldownTimerRef.current!);
            setIsOnCooldown(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    };

    const onAsk = () => {
      if (!isOnCooldown && inputValue) {
        // 过滤掉空消息
        if (trim(inputValue) === '') {
          return;
        }
        // 超过100个字符不允许发送
        if (inputValue.length > 100) {
          return;
        }
        if (curChatType === ChatTabType.Room) {
          myPlayer.callAppApi(AppGameApiKey.sendChat, inputValue);
        }
        ChatManager.getInstance().sendChatMessage(
          myPlayer.btcAddress,
          curChatType,
          inputValue,
          replyChatData?.uuid || ''
        );
        setInputValue('');

        // 触发发送冷却
        startCooldown();
        onSend?.();
      }
    };

    // 暴露 focusInput 方法
    useImperativeHandle(ref, () => ({
      focusInput,
      setIsLoading: (isLoading: boolean) => {
        setIsLoading(isLoading);
      },
    }));

    function focusInput() {
      inputRef.current?.focus();
    }

    const showAirDropButton = useMemo(() => {
      return sceneType === SCENE_TYPE.Community && curChatType === ChatTabType.Room;
    }, [curChatType, sceneType]);

    return (
      <ChatWindowWrapper replyMode={replyMode}>
        <div className="question-box">
          <div className="question-ask">
            <EmojiButton
              ref={emojiButtonRef}
              onChangeEmoji={(emoji) => {
                if (emoji) {
                  setInputValue((prevValue) => prevValue + emoji);
                  focusInput();
                }
              }}
            />
            {showAirDropButton && (
              <ChatRoomBirthdayButton onAirdrop={onAirdrop} isLoading={isLoading} />
            )}
            <div className="question-ask-input">
              <input
                type="text"
                placeholder="Please enter the question"
                ref={inputRef}
                maxLength={100}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isOnCooldown) {
                    onAsk();
                  }
                }}
                onFocus={() => {
                  // 禁止玩家控制
                  KeyPressUtil.setEnable(false);
                }}
                onBlur={() => {
                  // 开启玩家控制
                  KeyPressUtil.setEnable(true);
                }}
              />
            </div>

            <button
              onClick={onAsk}
              disabled={isOnCooldown}
              title={isOnCooldown ? `Wait ${cooldownSeconds}s` : 'Send message'}>
              {isOnCooldown ? (
                <CooldownText>{cooldownSeconds}</CooldownText>
              ) : (
                <img src={SendImg.src} alt="" />
              )}
            </button>
          </div>
        </div>
      </ChatWindowWrapper>
    );
  }
);

ChatRoomInput.displayName = 'ChatRoomInput';
export default ChatRoomInput;
