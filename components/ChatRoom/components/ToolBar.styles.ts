import styled from 'styled-components';

export const ToolBarContainer = styled.div`
  display: flex;
  align-items: end;
  width: 100%;
  position: absolute;
  top: -4.0625rem;
  left: 0;
  box-sizing: border-box;
`;

export const ListContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const ListItem = styled.div<{ active: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${({ active }) => (active ? 'rgba(0,0,0,0.8)' : 'rgba(0,0,0,0.6)')};
  border: ${({ active }) => (active ? '0.125rem solid #fff' : '0.125rem solid transparent')};
  border-radius: 0.875rem;
  height: 3.375rem;
  margin-right: 0.625rem;
  cursor: pointer;
  transition:
    border 0.2s,
    background 0.2s,
    width 0.3s,
    min-width 0.3s,
    padding 0.3s;
  color: #fff;
  box-shadow: ${({ active }) => (active ? '0 0 0.5rem 0 #fff3' : 'none')};
  position: relative;
  min-width: ${({ active }) => (active ? '7.5rem' : '3.375rem')};
  width: ${({ active }) => (active ? 'auto' : '3.375rem')};
  padding: ${({ active }) => (active ? '0 1.125rem 0 0.625rem' : '0')};
  overflow: visible;
  & > .icon-wrapper {
    position: relative;
    width: 2.375rem;
    height: 2.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  & > .label {
    margin-left: 0.625rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: #fff;
    white-space: nowrap;
    opacity: ${({ active }) => (active ? 1 : 0)};
    transition: opacity 0.2s;
  }
`;

export const CheckboxContainer = styled.label<{ checked: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  width: 8.75rem;
  height: 2.5rem;
  border-radius: 0.875rem;
  padding: 0 0.625rem;
  box-sizing: border-box;
  cursor: pointer;
  margin-left: auto;
  font-size: 0.875rem;
  font-weight: 500;
  color: #fff;
  position: relative;
  input[type='checkbox'] {
    display: none;
  }
  &::before {
    content: '';
    display: inline-block;
    width: 1.375rem;
    height: 1.375rem;
    border: 0.0625rem solid #fff;
    border-radius: 0.25rem;
    margin-right: 0.625rem;
    background: ${({ checked }) => (checked ? '#fff' : 'transparent')};
    box-sizing: border-box;
    transition: background 0.2s;
  }
  &::after {
    content: '';
    display: ${({ checked }) => (checked ? 'block' : 'none')};
    position: absolute;
    left: 1.125rem;
    top: 0.5625rem;
    width: 0.5rem;
    height: 0.875rem;
    border: solid #222;
    border-width: 0 0.1875rem 0.1875rem 0;
    transform: rotate(45deg);
  }
`;
