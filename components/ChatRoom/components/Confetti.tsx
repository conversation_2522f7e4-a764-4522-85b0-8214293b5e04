import styled from 'styled-components';

const ConfettiSvg1 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="9" viewBox="0 0 8 9" fill="none">
      <path
        d="M7.29682 0.334501C8.56642 -0.363576 7.62744 3.06863 6.99925 4.872C6.10647 10.6307 4.42036 7.49019 2.13904 5.57048C-0.142283 3.65076 -0.935787 2.60365 1.44472 2.42913C3.80201 2.25631 5.67299 1.22745 7.25029 0.360087L7.29682 0.334501Z"
        fill="#55F300"
      />
    </svg>
  );
};
const ConfettiSvg2 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
      <path
        d="M6.61015 2.40116C9.43376 -1.32406 10.3542 1.13176 10.7522 1.68133C13.0654 4.87503 10.9205 6.19956 8.79663 8.08838C6.67276 9.97721 6.95097 11.5503 5.53509 11.8293C4.1192 12.1083 1.96633 10.2656 0.824506 9.064C-0.317321 7.86236 2.81906 7.40278 6.61015 2.40116Z"
        fill="#FF6D70"
      />
    </svg>
  );
};
const ConfettiSvg3 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
      <path
        d="M0.307787 6.35894C-0.361329 5.16535 0.665246 4.67083 1.30081 4.17797C2.9974 2.86325 6.99511 -0.116766 9.24903 0.957753C11.5029 2.03227 12.1701 5.16218 12.2191 7.44402C12.2681 9.72586 12.9469 10.7063 14.9702 9.93862C16.9935 9.17094 16.9324 7.36504 17.1989 6.9432C17.412 6.60573 17.6592 6.72178 17.7562 6.82199C18.2572 7.80468 19.2957 9.84949 19.4412 10.1672C19.5866 10.4849 19.405 10.7572 19.296 10.8536C17.4792 13.5777 14.1232 14.7806 11.9297 13.0016C9.73619 11.2225 9.86902 9.33779 9.832 6.58028C9.79499 3.82277 9.63768 3.31144 9.17718 2.97493C8.71667 2.63842 6.93587 3.93545 5.72459 5.4725C4.99783 6.39473 4.06489 7.87097 2.82921 8.68556C1.59353 9.50014 1.14418 7.85092 0.307787 6.35894Z"
        fill="#489EFF"
      />
    </svg>
  );
};
const ConfettiSvg4 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="24" viewBox="0 0 13 24" fill="none">
      <path
        d="M10.7861 3.9703C9.53898 4.33436 7.99899 5.74004 7.38489 6.39737C6.67647 7.08765 4.97556 8.97603 3.84184 10.6446C2.70811 12.3132 3.13325 13.9818 4.12542 15.8021C5.11759 17.6224 7.38489 18.836 8.66018 18.836C9.93547 18.836 10.7861 18.6843 10.7861 19.291C10.7861 19.8978 8.94361 21.7181 7.38489 23.235C5.82617 24.7519 2.70826 22.1732 0.724235 17.3191C-1.25979 12.4649 1.14938 7.00413 4.12542 3.51523C7.10146 0.0263261 11.2112 -0.428748 12.0615 1.08817C12.9118 2.60508 12.345 3.51523 10.7861 3.9703Z"
        fill="#FFC433"
      />
    </svg>
  );
};

const ConfettiSvg5 = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
      <path
        d="M3.00202 16.3063C1.63799 16.4148 1.61819 15.2755 1.43636 14.492C0.951826 12.4011 -0.0828359 7.5234 1.83593 5.92557C3.7547 4.32774 6.87671 5.03091 8.96986 5.94083C11.063 6.85075 12.2375 6.64431 12.3865 4.48542C12.5355 2.32653 10.8696 1.62663 10.5979 1.20816C10.3805 0.873376 10.5893 0.697377 10.7209 0.651226C11.8231 0.607151 14.1148 0.519188 14.4642 0.519935C14.8136 0.520683 14.985 0.799591 15.027 0.938952C16.7413 3.72859 16.4303 7.28005 13.8968 8.52831C11.3634 9.77658 9.70702 8.8676 7.18683 7.74782C4.66665 6.62803 4.1364 6.55704 3.63812 6.83457C3.13984 7.1121 3.57309 9.27216 4.46258 11.0153C4.99627 12.0612 5.94694 13.5261 6.16998 14.9892C6.39303 16.4523 4.70706 16.1706 3.00202 16.3063Z"
        fill="#C27CFF"
      />
    </svg>
  );
};

const StyledConfettiBox = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  &:nth-of-type(1) {
    right: 0.1875rem;
    bottom: 0.0625rem;
  }
  &:nth-of-type(2) {
    top: -0.268125rem;
    left: 1.268125rem;
  }
  &:nth-of-type(3) {
    bottom: -0.081875rem;
    left: -0.0625rem;
  }
  &:nth-of-type(4) {
    top: -0.358125rem;
    left: 0.25rem;
  }
  &:nth-of-type(5) {
    right: 0.1875rem;
    top: -0.375rem;
  }
`;

const StyledConfettiGroupBox = styled.div`
  position: absolute;
  height: 100%;
  width: 100%;
`;

const ConfettiGroupBox = () => {
  return (
    <StyledConfettiGroupBox>
      <StyledConfettiBox>
        <ConfettiSvg1 />
      </StyledConfettiBox>
      <StyledConfettiBox>
        <ConfettiSvg2 />
      </StyledConfettiBox>
      <StyledConfettiBox>
        <ConfettiSvg3 />
      </StyledConfettiBox>
      <StyledConfettiBox>
        <ConfettiSvg4 />
      </StyledConfettiBox>
      <StyledConfettiBox>
        <ConfettiSvg5 />
      </StyledConfettiBox>
    </StyledConfettiGroupBox>
  );
};

export default ConfettiGroupBox;
