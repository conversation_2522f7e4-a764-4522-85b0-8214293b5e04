import { forwardRef, Ref, useImperativeHandle, useRef, useState } from 'react';
import styled from 'styled-components';
import Dialog from '@/commons/Dialog';
import { motion } from 'framer-motion';
import { destroyItem } from '@/server';
import toast from 'react-hot-toast';
import Image from 'next/image';

const DialogContent = styled.div`
  position: relative;
  width: 31.25rem;

  &:before {
    content: '';
    position: absolute;
    top: -0.25rem;
    left: -0.25rem;
    right: -0.25rem;
    bottom: -0.25rem;
    border-radius: 2.75rem; // 2.5rem + 0.25rem border
    border: 0.25rem solid #14110a;
    z-index: 1;
  }
`;

const Content = styled.div`
  position: relative;
  background-color: #fef1df;
  border-radius: 2.5rem;
  border: 0.5rem solid #e8901c;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: start;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  z-index: 2;
  padding: 0rem 1.875rem;
`;

const Title = styled.div`
  font-size: 1.25rem;
  font-weight: 700;
  color: #14110a;
  width: 100%;
  text-align: center;
  padding-top: 1.125rem;
  padding-bottom: 0.75rem;
`;

const DeleteContent = styled.div`
  font-size: 1rem;
  font-weight: 400;
  color: #686663;
  text-align: center;
  line-height: 1.5rem;
  padding: 0.625rem 0rem 1.875rem 0rem;
  box-sizing: border-box;
  width: 100%;
`;

const Footer = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 1.25rem;
  margin-top: auto; // 将按钮推到底部
  padding-bottom: 1.5rem;

  .cancel {
    color: #fff;
    background-color: #c1af9c;
    width: 12.5rem;
    height: 3.125rem;
    border-radius: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-shadow: 0 0.25rem 0 #a39484; // 底部阴影创造立体感
    transition: all 0.1s ease;

    &:active {
      transform: translateY(0.25rem);
      box-shadow: 0 0 0 #a39484;
    }
  }

  .confirm {
    color: #fff;
    background-color: #fc7922;
    width: 12.5rem;
    height: 3.125rem;
    border-radius: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-shadow: 0 0.25rem 0 #d65b0c; // 底部阴影创造立体感
    transition: all 0.1s ease;

    &:active {
      transform: translateY(0.25rem);
      box-shadow: 0 0 0 #d65b0c;
    }
  }
`;

interface IDeleteModalProps {
  onComplete?: () => void;
}

export interface DeleteModalRef {
  open: (item: any) => void;
}

const DeleteModal = forwardRef<DeleteModalRef, IDeleteModalProps>(
  (props, ref: Ref<DeleteModalRef>) => {
    const { onComplete } = props;
    const [isOpen, setIsOpen] = useState(false);
    const bagInventItemsInfo = useRef<any>(null);
    const [loading, setLoading] = useState(false);

    // 打开面板时刷新设置
    useImperativeHandle(ref, () => ({
      open(item) {
        bagInventItemsInfo.current = item;
        setIsOpen(true);
      },
    }));

    const onConfirm = async () => {
      try {
        setLoading(true);
        const res = await destroyItem({
          userItemId: bagInventItemsInfo.current.userItemId,
        });
        const { code, msg } = res.data;
        if (code == 1) {
          // 销毁成功
          toast.success('Successful destruction!');
          setIsOpen(false);
          onComplete?.();
        } else {
          toast.error(msg);
        }
      } catch (error) {
        toast.error(error?.message);
        setIsOpen(false);
      } finally {
        setLoading(false);
      }
    };

    // 添加取消时的处理
    const onCancel = () => {
      setIsOpen(false);
    };

    return (
      <Dialog isOpen={isOpen} onClose={onCancel}>
        <DialogContent>
          <Content>
            <Title>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.125rem',
                }}>
                <Image
                  src="/image/bag/wain.png"
                  alt="delete"
                  width={30}
                  height={30}
                  style={{
                    position: 'relative',
                    top: '0.125rem',
                  }}
                />
                <span
                  style={{
                    fontWeight: 900,
                    textShadow: '0 0 #000',
                  }}>
                  Delete
                </span>
              </div>
            </Title>
            <DeleteContent>
              Do you want to discard this tool? It will permanently disappear and be lost.
            </DeleteContent>
            <Footer initial="hidden" animate="visible">
              <motion.div
                className="cancel"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  onCancel();
                }}>
                Cancel
              </motion.div>
              <motion.div
                className="confirm"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  onConfirm();
                }}>
                {loading ? 'Loading...' : 'Confirm'}
              </motion.div>
            </Footer>
          </Content>
        </DialogContent>
      </Dialog>
    );
  }
);

DeleteModal.displayName = 'DeleteModal';
export default DeleteModal;
