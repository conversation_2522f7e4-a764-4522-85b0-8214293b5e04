import styled from 'styled-components';

export const SyntheticModalView = styled.div`
  width: 47.5rem;
  background: #fff2e2;
  box-shadow: 0.125rem 0.125rem 0.25rem 0rem #ffffff99 inset;
  border-radius: 3rem;
  border: 0.5rem solid #ed9800;
  box-sizing: border-box;
  position: relative;
  & > .synthetic-title {
    width: 22.375rem;
    height: 5.25rem;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -50%);
  }
  & > .close-btn {
    width: 3.5rem;
    height: 3.5rem;
    position: absolute;
    right: 2.5rem;
    top: -1.375rem;
    cursor: pointer;
  }
  & > .synthetic-content {
    padding: 4.875rem 1.875rem 3.125rem 1.875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    max-height: calc(100vh - 5rem);
    overflow-y: auto;
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 0.5rem;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #595754;
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 0.5rem;
    }
    // 右下角
    &::-webkit-scrollbar-corner {
      background: transparent;
    }
    & > .result-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      & > p {
        margin: 0.5rem 0 0 0;
        font-family: 'JetBrains Mono';
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.25rem;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140f08;
      }
    }
    & > .depletion-list {
      margin-top: 1.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f7e7cd;
      padding: 1rem;
      box-sizing: border-box;
      max-width: 30.5rem;
      gap: 3rem;
      border-radius: 2rem;
      box-shadow: 0rem 0rem 0.5rem 0rem #00000040 inset;
      & > .depletion-item {
        & > p {
          margin: 1rem 0 0 0;
          font-family: 'JetBrains Mono';
          font-size: 1.25rem;
          font-weight: 400;
          line-height: 1.25rem;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #615a57;
          &.not-enough {
            color: #ff8316;
          }
        }
      }
    }
    & > .num-view {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 1rem;
    }
    & > .confirm-btn {
      margin: 2rem auto 0 auto;
      width: 15rem;
      min-height: 4rem;
      background:
        linear-gradient(180deg, #ffbd2e 0%, #ff8316 48%, #ff902f 100%),
        linear-gradient(0deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5));
      box-shadow: 0rem 0.125rem 0.125rem 0rem #ffce47 inset;
      border-radius: 1.5rem;
      border: 0;
      cursor: pointer;
      outline: none;
      & > span {
        font-family: 'JetBrains Mono';
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.65rem;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #ffffff;
        text-shadow:
          0.125rem 0.125rem 0 #6f3400,
          -0.125rem 0.125rem 0 #6f3400,
          0.125rem -0.125rem 0 #6f3400,
          -0.125rem -0.125rem 0 #6f3400;
      }
      &[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
`;

export const NumStepView = styled.div`
  width: 13.5rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  background: #f7e7cd;
  padding: 0.5rem;
  box-sizing: border-box;
  & > div {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.75rem;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.25rem;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
    cursor: pointer;
    &.disabled {
      cursor: not-allowed;
      background: #d8d0ca;
      color: #5f5d5b;
    }
  }
  & > span {
    flex: 1;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.25rem;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
  }
`;
