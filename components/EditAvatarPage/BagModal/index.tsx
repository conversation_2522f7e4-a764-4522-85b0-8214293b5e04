import { BagInventoryView, BagModalBox, BagModalView, BagQuickBarView } from './style';
import Modal from '../BasicComponents/Modal';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState, IBagInventoryItem, STORAGE_MENU_ENUM } from '../../../constant/type';
import ContentItem from './ContentItem';
import LocalLoading from '@/components/LoadingContent';

import { Dispatch, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';
import useBagInventory from '../../../hooks/useBagInventory';
import { setEquipmentShortcut } from '../../../server';
import toast from 'react-hot-toast';
import { setStorageMenu } from '../../../store/app';
import BagInventoryTabs from './BagInventoryTabs';
import StaggeredAnimation from '@/commons/StaggeredAnimation';
import OutfitList from './OutfitList';
import MateriaList from './MateriaList';
import { ConfigManager } from '@/world/Config/ConfigManager';
import { getDurabilityTips } from '@/utils/durabilityTips';
import DeleteModal, { DeleteModalRef } from './DeleteModal';
import { BagInventoryContextProvider, TAB_ENUM, useContextSelector } from './context';

function BagModalInner({ showOnly }: { showOnly?: boolean }) {
  const { storageMenu } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const dispatch = useDispatch();
  /**
   * bagInventoryList： 物品列表数据
   * syntheticsList： 合成列表数据
   * getBagInventoryListDta： 获取物品列表数据
   * getSyntheticsListData： 获取合成列表数据
   */
  const { bagInventoryList, fetchAllData, isPending, materialList, getBagInventoryListDta } =
    useBagInventory(false);
  // 添加一个状态来跟踪弹窗是否可见
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [deleteHighlight, setDeleteHighlight] = useState(false);
  const deleteModalRef = useRef<DeleteModalRef>(null);
  // 监听弹窗可见性变化
  useEffect(() => {
    const visible = storageMenu === STORAGE_MENU_ENUM.BAG_MENU || showOnly === true;
    setIsVisible(visible);
  }, [storageMenu, showOnly]);

  // 销毁物品逻辑
  const handleDropToDelete = async (item: any) => {
    if (item && item.userItemId) {
      deleteModalRef.current?.open(item);
      setDeleteHighlight(false);
      setIsDragging(false);
    }
  };
  const handleDragOverToDelete = () => setDeleteHighlight(true);
  const handleDragLeaveToDelete = () => setDeleteHighlight(false);
  const activeIndex = useContextSelector((state) => state.activeIndex);

  const ref = useRef<HTMLDivElement>(null);

  return (
    <div ref={ref} id="testPortalEL">
      <Modal
        visible={isVisible}
        emptyOnly={true}
        zIndex={9}
        popupClose={true}
        onClose={() => {
          setIsVisible(false);
          dispatch(setStorageMenu(null));
        }}
        portalContainer={ref.current as Element}>
        <BagModalView>
          <BagModalBox
            $activeIndex={activeIndex}
            onClick={(e: any) => {
              e.stopPropagation();
            }}>
            <BagInventory
              setIsDragging={setIsDragging}
              isVisible={isVisible}
              isDragging={isDragging}
              deleteHighlight={deleteHighlight}
              onDropToDelete={handleDropToDelete}
              onDragOverToDelete={handleDragOverToDelete}
              onDragLeaveToDelete={handleDragLeaveToDelete}
              bagInventoryList={bagInventoryList}
              materialList={materialList}
              isPending={isPending}
              fetchAllData={fetchAllData}
            />
            <BagQuickBar isDragging={isDragging} setIsDragging={setIsDragging} />
          </BagModalBox>
        </BagModalView>
      </Modal>
      <DeleteModal
        ref={deleteModalRef}
        onComplete={() => {
          getBagInventoryListDta();
        }}
      />
    </div>
  );
}

export default function BagModal({ showOnly }: { showOnly?: boolean }) {
  return (
    <BagInventoryContextProvider>
      <BagModalInner showOnly={showOnly} />
    </BagInventoryContextProvider>
  );
}

//物品栏
function BagInventory({
  setIsDragging,
  isVisible,
  isDragging,
  deleteHighlight,
  onDropToDelete,
  onDragOverToDelete,
  onDragLeaveToDelete,
  bagInventoryList,
  materialList,
  isPending,
  fetchAllData,
}: {
  setIsDragging: Dispatch<SetStateAction<boolean>>;
  isVisible: boolean;
  isDragging: boolean;
  deleteHighlight?: boolean;
  onDropToDelete?: (item: any) => void;
  onDragOverToDelete?: () => void;
  onDragLeaveToDelete?: () => void;
  bagInventoryList: IBagInventoryItem[];
  materialList: any[];
  isPending: boolean;
  fetchAllData: () => void;
}) {
  // 当前选中的标签 默认是物品背包 （物品背包/合成背包）
  const tab = useContextSelector((state) => state.tab);
  const scoreConfigRef = useRef<{ [key: string]: number }>({});
  const [isMaterialDataList, setIsMaterialDataList] = useState<any[]>([]);

  // 当弹窗可见时获取数据
  useEffect(() => {
    if (isVisible) {
      fetchAllData();
    }
  }, [isVisible]);

  useEffect(() => {
    if (materialList.length > 0) {
      ConfigManager.getInstance().getData((data) => {
        scoreConfigRef.current = data.material_score_config;
        const newMaterialList = materialList.map((item) => {
          // 获取材料的数量
          // const quantity = item.quantity || 0;
          // 获取材料的tag
          const tag = item.tag || '';
          // 获取材料的积分
          const score = scoreConfigRef.current[tag as keyof typeof scoreConfigRef.current];
          return {
            ...item,
            score,
          };
        });
        setIsMaterialDataList(newMaterialList);
      });
    }
  }, [materialList]);

  return (
    <>
      <BagInventoryView>
        {/* 物品栏列表 */}
        <div className="inventory-list-box">
          {isPending ? (
            <LocalLoading />
          ) : (
            <>
              {/* 物品背包列表 */}
              {tab === TAB_ENUM.THING_BAG && (
                <OutfitList
                  bagInventoryList={bagInventoryList}
                  isVisible={isVisible}
                  setIsDragging={setIsDragging}
                />
              )}

              {/* 材料背包列表 */}
              {tab === TAB_ENUM.MATERIAL_BAG && (
                <MateriaList
                  materialList={isMaterialDataList}
                  isVisible={isVisible}
                  setIsDragging={setIsDragging}
                />
              )}
            </>
          )}
        </div>
      </BagInventoryView>
      {/* 物品栏标签切换 */}
      <BagInventoryTabs
        isDragging={isDragging}
        onDropToDelete={onDropToDelete}
        onDragOverToDelete={onDragOverToDelete}
        onDragLeaveToDelete={onDragLeaveToDelete}
        deleteHighlight={deleteHighlight}
      />
    </>
  );
}

interface IBagQuickBarProps {
  isDragging: boolean;
  setIsDragging: Function;
}

// 快捷栏
function BagQuickBar({ isDragging, setIsDragging }: IBagQuickBarProps) {
  /**
   * bagInventoryList： 物品列表数据
   * getBagInventoryListDta： 获取物品列表数据
   * getSyntheticsListData： 获取合成列表数据
   */
  const { bagInventoryList, fetchAllData } = useBagInventory(false);

  // 当前拖拽的快捷栏位置
  const [draggingKey, setDraggingKey] = useState<string>('');

  // 修改onSetQuestKey函数，使用fetchAllData替代单独的数据获取
  const onSetQuestKey = (userItemId: string, draggingKey: string, durability?: number) => {
    const loadingToast = toast.loading('Loading...');
    setEquipmentShortcut({
      userItemId: userItemId,
      shortcut: draggingKey,
    })
      .then(async (res) => {
        if (res.data.code === 1) {
          // 使用fetchAllData一次性刷新所有数据
          await fetchAllData();

          // 添加耐久度提示
          if (durability !== undefined) {
            getDurabilityTips(durability);
          }
        } else {
          toast.error(res.data.msg || res.data.message[0], { duration: 6000 });
        }
      })
      .finally(() => {
        toast.dismiss(loadingToast);
      });
  };
  //快捷键到物品对象映射
  const quickListMap = useMemo(() => {
    return bagInventoryList.reduce(
      (acc, cur) => {
        if (cur.shortcut !== null) {
          acc[cur.shortcut] = cur;
        }
        return acc;
      },
      {} as { [key: string]: IBagInventoryItem }
    );
  }, [bagInventoryList]);

  // 处理拖拽释放事件
  const handleDrop = (e: any) => {
    e.preventDefault(); // 阻止默认行为
    const droppedItem = e.dataTransfer.getData('text/plain');
    try {
      const item: IBagInventoryItem = JSON.parse(droppedItem);
      if (item.itemId) {
        onSetQuestKey(item.userItemId, draggingKey, item.currentDurability);
      }
    } catch (e) {}
    setDraggingKey(''); // 拖拽结束，隐藏边框
  };
  return (
    <BagQuickBarView>
      {/* 快捷键物品栏组件：显示图标，数量，快捷键等信息，支持拖拽，显示物品详情，支持禁用状态，支持'新物品'标记 */}
      {['1', '2', '3', '4', '5'].map((key, index) => (
        <StaggeredAnimation
          index={index}
          initialScale={0.7}
          duration={0.5}
          bounceEffect={true}
          key={key}>
          <ContentItem
            isGlowWeapon={quickListMap[key]?.isGlowWeapon}
            currentDurability={quickListMap[key]?.currentDurability}
            key={key}
            src={quickListMap[key] ? quickListMap[key].icon : undefined}
            // 数量
            num={quickListMap[key] ? quickListMap[key].quantity : undefined}
            // 快捷键
            questKey={key}
            // 拖拽覆盖
            onDragOver={(e: any) => {
              if (!isDragging) {
                return;
              }
              e.preventDefault(); // 阻
              setDraggingKey(key); // 显示容器B的边框止默认行为，允许放置
            }}
            // 拖拽离开
            onDragLeave={(e: any) => {
              setDraggingKey('');
            }}
            // 是否可拖拽
            draggable={!!quickListMap[key]}
            // 拖拽开始
            onDragStart={(e: any) => {
              if (quickListMap[key]) {
                setIsDragging(true);
                // 在拖拽数据中添加标记，表明这是从快捷栏拖出的物品
                // 这将帮助背包区分是普通拖拽还是从快捷栏拖拽
                const itemData = {
                  ...quickListMap[key],
                  fromQuickBar: true,
                };
                e.dataTransfer.setData('text/plain', JSON.stringify(itemData));
              }
            }}
            // 拖拽结束
            onDragEnd={(e: any) => {
              setIsDragging(false);
              e.dataTransfer.clearData();
            }}
            // 拖拽释放
            onDrop={handleDrop}
            // 是否选中/高亮
            check={draggingKey === key}
            // 鼠标样式
            style={{
              cursor: quickListMap[key] ? 'pointer' : 'default',
            }}
          />
        </StaggeredAnimation>
      ))}
    </BagQuickBarView>
  );
}
