import StaggeredAnimation from '@/commons/StaggeredAnimation';
import { Dispatch, SetStateAction } from 'react';
import ContentItem from '../ContentItem';
import { INVENTORY_TYPE_ENUM } from '@/constant/type';

interface MateriaListProps {
  materialList: any[];
  isVisible: boolean;
  setIsDragging: Dispatch<SetStateAction<boolean>>;
}

const MateriaList = ({ materialList, isVisible, setIsDragging }: MateriaListProps) => {
  // 创建一个固定大小的格子数组，总共20个格子
  const TOTAL_SLOTS = 20;
  const slots = [...Array(TOTAL_SLOTS)].map((_, index) => {
    // 如果有对应的物品，使用物品数据；否则返回null表示空格子
    return index < materialList.length ? materialList[index] : null;
  });

  return (
    <div className="inventory-list">
      {slots.map((item, index) => (
        <StaggeredAnimation
          key={item ? item.itemId + item.name + item.type : `empty-material-${index}`}
          index={index}
          isVisible={isVisible}
          initialScale={0.7}
          duration={0.5}
          bounceEffect={true}
        >
          {item ? (
            <ContentItem
              key={item.itemId + item.name + item.type + index}
              src={item.icon}
              num={item.quantity}
              check={false}
              questKey={item.shortcut !== '' && item.shortcut !== null ? item.shortcut : undefined}
              isNew={item.isNew}
              itemId={item.itemId}
              // draggable={item.type === INVENTORY_TYPE_ENUM.equipment}
              // onDragStart={(e: any) => {
              //   setIsDragging(true);
              //   e.dataTransfer.setData(
              //     "text/plain",
              //     JSON.stringify(item)
              //   );
              // }}
              // onDragEnd={(e: any) => {
              //   setIsDragging(false);
              //   e.dataTransfer.clearData();
              // }}
              showDetail={{
                name: item.name,
                description: item.description,
                type: item.type,
                maxDurability:
                  item.type === INVENTORY_TYPE_ENUM.material ? item.maxDurability : undefined,
                currentDurability:
                  item.type === INVENTORY_TYPE_ENUM.material ? item.currentDurability : undefined,
                quantity: item.quantity,
                score: item.score,
              }}
            />
          ) : (
            // 渲染空格子
            <ContentItem
              key={`empty-material-slot-${index}`}
              // 不设置src，显示为空格子
              check={false}
              disabled={false} // 设为false以避免灰度滤镜
              style={{
                cursor: 'default',
                background: '#FBF4E8', // 保持与有物品格子相同的背景色
              }}
            />
          )}
        </StaggeredAnimation>
      ))}
    </div>
  );
};

export default MateriaList;
