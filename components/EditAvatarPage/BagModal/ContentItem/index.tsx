import { ContentItemView, TooltipBoxView } from './style';
import NewPng from '/public/image/bag/new.png';
import { useEffect, useState } from 'react';
import Tooltip from 'rc-tooltip';
import { removeIsNewState } from '../../../../server';
import useBagInventory from '../../../../hooks/useBagInventory';
import { INVENTORY_TYPE_ENUM } from '../../../../constant/type';
import Image from 'next/image';

interface IDetails {
  name: string;
  description: string;
  quantity?: number;
  maxDurability?: number;
  currentDurability?: number;
  type: INVENTORY_TYPE_ENUM;
  expirationTime?: string;
  score?: number;
  trait?: string;
  quality?: number; // 品质：1白，2绿，3蓝，4紫，5黄，6红，7彩
}

interface IProps {
  src?: string;
  num?: number;
  check?: boolean;
  isNew?: boolean;
  itemId?: string;
  questKey?: string;
  redMark?: boolean;
  showDetail?: IDetails; //鼠标移入显示详情
  disabled?: boolean;
  onClick?: Function;
  boxShadow?: string;
  draggable?: boolean;
  isGlowWeapon?: boolean;
  currentDurability?: number;

  [key: string]: any;
  isDragging?: boolean;
}

export default function ContentItem({
  src,
  num,
  check,
  isNew,
  questKey,
  redMark,
  disabled,
  showDetail,
  onClick,
  boxShadow,
  draggable,
  itemId,
  isGlowWeapon,
  currentDurability,
  isDragging,
  ...targetArg
}: IProps) {
  const { getBagInventoryListDta } = useBagInventory();
  const [active, setActive] = useState<boolean>(false);
  const setIsNew = () => {
    if (isNew && itemId) {
      removeIsNewState(itemId)
        .then((res) => {
          if (res.data.code === 1) {
            getBagInventoryListDta();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }
  };

  return (
    <ContentItemView
      check={check || active}
      disabled={disabled}
      boxShadow={boxShadow}
      onClick={() => onClick && onClick()}
      {...targetArg}
      draggable={draggable}
      effect={!!showDetail?.trait}
      isGlowWeapon={isGlowWeapon}
      currentDurability={currentDurability}>
      {src && (
        <Tooltip
          zIndex={99}
          trigger="hover"
          onVisibleChange={(visible) => {
            setActive(!!(visible && showDetail));
          }}
          overlay={showDetail && !isDragging && <TooltipBox showDetail={showDetail} />}
          showArrow={false}
          align={{
            points: ['tl', 'cl'],
            offset: ['20.18%', '12.5%'],
          }}>
          <Image
            src={src}
            alt=""
            className="content-image"
            width={110}
            height={110}
            onDragStart={(e: any) => {
              if (!draggable) {
                e.preventDefault();
              }
            }}
            onClick={setIsNew}
          />
        </Tooltip>
      )}
      <span className="content-num">{num}</span>
      {questKey !== undefined ? (
        <span className="quest-key">{questKey}</span>
      ) : (
        !check &&
        isNew && <Image width={48} height={48} src={NewPng.src} alt="" className="content-new" />
      )}
      {/* redMark 该物品当前是否支持合成 */}
      {/* {redMark && <span className="red-mark" />} */}
    </ContentItemView>
  );
}

export function TooltipBox({ showDetail }: { showDetail: IDetails }) {
  return (
    <TooltipBoxView quality={showDetail.quality}>
      <h3 className="tooltip-box-title">{showDetail.name}</h3>
      <div>
        <p>
          Name: <span>{showDetail.name}</span>
        </p>
        {showDetail.description && (
          <p>
            Description: <span>{showDetail.description}</span>
          </p>
        )}

        {showDetail.quantity !== undefined && (
          <p>
            Quantity: <span>{showDetail.quantity}</span>
          </p>
        )}
        {showDetail.maxDurability !== undefined && (
          <p>
            Max Durability: <span>{showDetail.maxDurability}</span>
          </p>
        )}
        {showDetail.currentDurability !== undefined && (
          <p>
            Current Durability:{' '}
            <span
              style={{
                color: showDetail.currentDurability === 1 ? '#FF2727' : '',
              }}>
              {showDetail.currentDurability}
            </span>
          </p>
        )}
        {showDetail.expirationTime && (
          <p>
            Expiration Time: <span>{showDetail.expirationTime}</span>
          </p>
        )}
        {showDetail.score !== undefined && (
          <p>
            Score: <span>{showDetail.score}</span>
          </p>
        )}
        {showDetail.trait !== undefined && (
          <p>
            Trait: <span>{showDetail.trait}</span>
          </p>
        )}
      </div>
    </TooltipBoxView>
  );
}
