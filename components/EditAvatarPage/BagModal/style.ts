import styled from 'styled-components';

export const BagModalView = styled.div`
  width: 100%;
  height: auto;
  pointer-events: auto;
  max-height: 100vh;
`;

export const BagModalBox = styled.div<{ $activeIndex: number }>`
  width: 44.375rem;
  margin: 0 auto;
  position: relative;

  &::after {
    content: '';
    display: block;
    position: absolute;
    height: 6.5rem;
    width: 0.5rem;
    background: #fff2e2;

    top: 0;
    left: calc(100% - 0.265rem);
    transform: translateY(calc(${({ $activeIndex }) => $activeIndex} * 5rem + 0.25rem));
    transition: transform 0.2s ease-in;
    z-index: 3;
    pointer-events: none;
  }
`;

// 背包
export const BagInventoryView = styled.div`
  width: 100%;
  height: 34.5rem;
  background: #fff2e2;
  box-shadow: 0.125rem 0.125rem 0.25rem 0rem #ffffff99 inset;
  border-top-left-radius: 2rem;
  border-bottom-left-radius: 2rem;
  border-bottom-right-radius: 2rem;
  border: 0.25rem solid #ed9800;
  padding: 0.5rem 0.5rem 0.5rem 0.875rem;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  box-shadow: 0 0.25rem 0.25rem 0 rgba(0, 0, 0, 0.25);
  .inventory-list-box {
    width: 98%;
    height: 100%;
    background: #f7e7cd;
    border: 0.0625rem solid #f2e3c9;
    border-radius: 1.75rem;
    overflow-y: auto;
    z-index: 2;

    &::-webkit-scrollbar {
      width: 0.375rem; /* 垂直滚动条宽度 */
      height: 0.375rem; /* 水平滚动条高度 */
    }

    /* 设置滚动条的轨道 */

    &::-webkit-scrollbar-track {
      background: #f1f1f1; /* 轨道颜色 */
      border-radius: 0.375rem;
      transform: translateX(1.25rem);
    }

    /* 设置滚动条的滑块 */

    &::-webkit-scrollbar-thumb {
      background: #5e5e5e; /* 滑块颜色 */
      border-radius: 0.375rem;
    }
  }
  .inventory-list {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(5, 7.5rem); /* 固定5列，每列7.5rem */
    grid-auto-rows: 7.5rem; /* 行高固定为7.5rem */
    grid-gap: 0.5rem; /* 统一的间距 */
    /* 或者使用不同的行列间距: */
    /* grid-gap: 0.5rem 1rem; */ /* 行间距0.5rem，列间距1rem */
    justify-content: start; /* 确保从左侧开始对齐 */
    align-content: start; /* 确保从顶部开始对齐，避免垂直方向有额外空间 */
    padding: 0.5rem;
    box-sizing: border-box;
    border-radius: 1.75rem;
    position: relative;
  }
  .inventory-list > div {
    width: 7.5rem;
    height: 7.5rem;
    flex: 0 0 7.5rem;
  }
  .inventory-list.drag-over {
    background: rgba(255, 210, 80, 0.2);
    border: 0.125rem dashed #ed9800;
  }
`;
// 快捷栏
export const BagQuickBarView = styled.div`
  height: 9.5rem;
  border-radius: 2rem;
  padding: 1rem;
  box-sizing: border-box;
  background: #fff2e280;
  backdrop-filter: blur(1rem);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 10%;

  @media (max-height: 48.75rem) {
    margin-top: 1.25rem;
  }
`;
