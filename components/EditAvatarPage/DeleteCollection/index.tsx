import { DeleteCollectionView, DeleteModalView } from './style';
import DeleteIcon from '/public/image/delete.svg';
import { IAppState, ICollection } from '../../../constant/type';
import { useState } from 'react';
import Modal from '../BasicComponents/Modal';
import { useSelector } from 'react-redux';
import { deleteCustomCollectionById } from '../../../server';
import toast from 'react-hot-toast';
import { useRouter } from 'next/router';

export interface DeleteCollectionIProps {
  collectionData: ICollection | null;
  initMetadata: Function;
}

interface IProps2 extends DeleteCollectionIProps {
  collectionData: ICollection;
}

export default function DeleteCollection({ collectionData, initMetadata }: DeleteCollectionIProps) {
  if (!collectionData) {
    return null;
  }
  return <DeleteCollectionBox collectionData={collectionData} initMetadata={initMetadata} />;
}

function DeleteCollectionBox({ collectionData, initMetadata }: IProps2) {
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  return (
    <>
      <DeleteCollectionView onClick={() => setShowDeleteModal(true)}>
        <img src={DeleteIcon.src} alt="" />
      </DeleteCollectionView>
      <DeleteModal
        visible={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        collectionData={collectionData}
        initMetadata={initMetadata}
      />
    </>
  );
}

function DeleteModal({
  collectionData,
  visible,
  onClose,
  initMetadata,
}: {
  collectionData: ICollection;
  initMetadata: Function;
  visible: boolean;
  onClose: () => void;
}) {
  const [loading, setLoading] = useState<boolean>(false);
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const router = useRouter();
  const onDelete = () => {
    setLoading(true);
    deleteCustomCollectionById(collectionData.id).then((res: any) => {
      toast.success('Deleted!', { duration: 6000 });
      onClose();
      setLoading(false);
      router.replace(router.pathname);
      initMetadata();
    });
  };

  return (
    <Modal visible={visible} onClose={onClose} title={'Delete Saved Set?'} width={'544px'}>
      <DeleteModalView>
        <div className="collection-info">
          <img src={collectionData.avatar} alt="" />
          <p>{collectionData.collectionName}</p>
        </div>
        <div className="edit-btns">
          <button className="confirm-btn" disabled={loading || !btcAddress} onClick={onDelete}>
            {loading ? 'Loading' : 'Delete'}
          </button>
          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
        </div>
      </DeleteModalView>
    </Modal>
  );
}
