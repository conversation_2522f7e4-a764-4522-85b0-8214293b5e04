import styled from 'styled-components';

export const DeleteCollectionView = styled.div`
  position: absolute;
  left: 10px;
  bottom: -48px;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #ffffff80;
  border: 1px solid #ffffff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  cursor: pointer;
  & > img {
    width: 26px;
    height: 28px;
  }
`;
export const DeleteModalView = styled.div`
  .collection-info {
    text-align: center;
    margin-top: 24px;
    & > img {
      width: 184px;
      border-radius: 30px;
    }
    & > p {
      font-family: 'JetBrains Mono';
      font-size: 14px;
      font-weight: 400;
      line-height: 16.94px;
      text-align: center;
      color: #686663;
      margin: 16px 0 0 0;
    }
  }
  .edit-btns {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    button {
      height: 56px;
      border-radius: 16px;
      outline: none;
      font-family: 'JetBrains Mono';
      font-size: 20px;
      font-weight: 700;
      line-height: 20px;
      text-align: center;
      color: #ffffff;
      border: 0;
      cursor: pointer;

      &[disabled] {
        cursor: not-allowed;
        opacity: 0.4;
      }

      &.cancel-btn {
        background-color: #9a9a9a;
      }

      &.confirm-btn {
        background: linear-gradient(180deg, #ff8a00 0%, #e57860 100%);
      }
    }
  }
`;
