import styled from 'styled-components';

export const VideoShareModalPopupView = styled.div``;

export const VideoShareModalView = styled.div`
  border-radius: 48px;
  box-sizing: border-box;
  position: relative;
  background: #140f08;
  padding: 4px;
  max-width: 1280px;
  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    cursor: pointer;
    top: -18px;
    right: 40px;
  }
  .modal-content {
    width: 100%;
    height: 100%;
    border: 8px solid #ff8316;
    border-radius: 48px;
    background: #fff2e2;
    padding: 32px 40px;
    box-sizing: border-box;
    .video-box {
      position: relative;
      & > video {
        width: 100%;
        border-radius: 40px;
        max-height: 460px;
      }
      .video-controls {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 908px;
        max-width: 90%;
        height: 72px;
        display: flex;
        align-items: center;
        background: #0000001a;
        border-radius: 50px;
        padding: 0 24px;
        gap: 20px;
        .video-play {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #ff8316;
          box-shadow: 0px -2.5px 0px 0px #00000040 inset;
          cursor: pointer;
          border-radius: 10px;
        }
        .video-time {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 8px;
          & > span {
            width: 64px;
            text-align: center;
            font-family: 'JetBrains Mono';
            font-size: 12px;
            font-weight: 400;
            line-height: 15.84px;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #ffffff;
            text-shadow: 0px 1px 2px #00000040;
          }
          & > div {
            flex: 1;
            height: 1px;
            background: #ffffff;
            border-radius: 50%;
            overflow: hidden;
            & > div {
              height: 100%;
              border-radius: 50%;
              background: #ff8316;
            }
          }
        }
      }
      .subtitle {
        position: absolute;
        left: 50%;
        bottom: 88px;
        background-color: #00000066;
        font-family: 'JetBrains Mono';
        font-size: 12px;
        font-weight: 400;
        line-height: 16.68px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        transform: translateX(-50%);
        color: #ffffff;
        border-radius: 16px;
        padding: 10px 16px;
        max-width: 90%;
        word-break: break-all;
        text-shadow: 0px 1px 2px #00000040;
      }
    }
    & > .actions {
      margin-top: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 24px;
      button {
        cursor: pointer;
        background: #ff8316;
        box-shadow: 0px -4px 0px 0px #00000040 inset;
        height: 64px;
        border-radius: 16px;
        border: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        & > span {
          font-family: 'JetBrains Mono';
          font-size: 20px;
          font-weight: 700;
          line-height: 20px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #ffffff;
        }
        & > img {
          width: 32px;
          height: 32px;
        }
        &[disabled] {
          cursor: not-allowed;
          background: #c9b7a5;
          box-shadow: none;
        }
        &.download {
          width: 80px;
        }
        &.share {
          width: 240px;
        }
        &.delete {
          width: 80px;
          background: #aa4234;
        }
      }
    }
  }
`;
