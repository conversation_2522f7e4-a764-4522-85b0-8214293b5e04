'use client';

import { AssistantQuestionView } from './style';
import HistoryImg from '/public/image/recording/history.svg';
import SendImg from '/public/image/recording/send.svg';
import StopImg from '/public/image/recording/stop.svg';
import Image from 'next/image';
import {
  getAssistantStream,
  GetAssistantStreamResult,
  getTtsListQuickQa,
  getTtsQuickQa,
} from '@root/server';
import { IAppState, IAssistantAnswer, IVTT } from '@root/constant/type';
import { TYPING_EFFECT_RATE } from '@root/constant';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import QuestionHistoryModal from './QuestionHistoryModal';
import LoadingImage from '/public/image/basic/loading.svg';
import { ButlerUtil } from '@/world/Global/GlobalButlerUtil';
import { AnswerTask } from '@/world/SceneUI/TopAnswerUI';
import { CitationFile, StreamMessage } from '@/utils/createStreamRender';
import { useSelector } from 'react-redux';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

interface IQuackQa {
  id: string;
  question: string;
}

export default function AssistantQuestion() {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [prompt, setPrompt] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [visibleModal, setVisibleModal] = useState<boolean>(false);
  const [quackQaList, setQuackQaList] = useState<IQuackQa[]>([]);
  const [isAsk, setIsAsk] = useState<boolean>(false);
  const accumulatedContentRef = useRef('');
  const taskRef = useRef<AnswerTask | null>(null);
  // 添加一个标志来跟踪是否是第一个有效的数据片段
  const isFirstValidChunkRef = useRef(true);
  // 添加一个标志来跟踪是否已经启动了打字效果
  const typingStartedRef = useRef(false);
  // 存储流控制器
  const streamControllerRef = useRef<GetAssistantStreamResult | null>(null);
  // 创建用于存储引用文件的状态
  const citationFilesRef = useRef<CitationFile[]>([]);

  const getQuickQaList = () => {
    getTtsListQuickQa().then((res) => {
      if (res.data.code === 1) {
        setQuackQaList(res.data.data || []);
      } else {
        toast.error(res.data.msg);
      }
    });
  };

  const processStreamData = (data: string): string => {
    // 检查是否是首个令牌时间信息
    if (data.includes('first token cost time')) {
      return ''; // 忽略这个信息，不显示给用户
    }

    // 使用正则表达式移除开头和结尾的双引号
    let processedData = data;

    // 匹配并移除字符串开头和结尾的双引号
    processedData = processedData.replace(/^"(.*)"$/, '$1');

    // 如果是第一个有效的数据片段，不需要考虑前导空格
    if (isFirstValidChunkRef.current) {
      isFirstValidChunkRef.current = false;
      return processedData.trimStart(); // 移除开头的空格
    }

    return processedData;
  };

  const onAsk = () => {
    if (!prompt) {
      return;
    }
    setLoading(true);
    // 重置累积内容和标志
    accumulatedContentRef.current = '';
    isFirstValidChunkRef.current = true;
    typingStartedRef.current = false;

    // 创建提问 3D显示 ...
    const task = ButlerUtil.createAnswer();
    taskRef.current = task;

    streamControllerRef.current = getAssistantStream({
      prompt: prompt,
      address: btcAddress,
      // 通用消息处理器（可选）
      onMessage: (data: StreamMessage) => {
        // console.log("收到消息:", data);
      },

      // 处理文本内容类型消息
      onContent: (contentChunk: string) => {
        // 处理数据（如果需要）
        const processedData = processStreamData(contentChunk);

        // 如果处理后的数据不为空，则累积
        if (processedData && taskRef.current) {
          // 累积内容
          accumulatedContentRef.current += processedData;

          // 3D开始回答动作
          setIsAsk(true);

          // 直接更新 task.content，而不是调用 start 方法
          if (!typingStartedRef.current) {
            // 首次收到有效数据，启动打字效果
            typingStartedRef.current = true;

            const params = {
              content: accumulatedContentRef.current,
              soundUrl: '',
              citationFiles: citationFilesRef.current, // 使用当前收集到的引用文件
            } as IAssistantAnswer;

            // 调用 startAnswerAnim 启动打字效果
            ButlerUtil.startAnswerAnim(taskRef.current, params, () => {
              // 结束回调
              setIsAsk(false);
              setPrompt('');
              // 重置引用
              // accumulatedContentRef.current = "";
              isFirstValidChunkRef.current = true;
              typingStartedRef.current = false;
            });
          } else {
            // 后续数据，直接更新 content
            // 直接修改 task.content，但保持原有的 startTime 和 stopTime 计算逻辑
            taskRef.current.content = accumulatedContentRef.current;

            // 更新 stopTime 以适应新的内容长度
            // 保持原有的打字速度（30毫秒/字符）
            const elapsedTime = Date.now() - taskRef.current.startTime;
            const remainingChars =
              taskRef.current.content.length - elapsedTime / TYPING_EFFECT_RATE;

            if (remainingChars > 0) {
              // 如果还有字符需要显示，更新 stopTime
              taskRef.current.stopTime = Date.now() + remainingChars * TYPING_EFFECT_RATE;
            }
          }
        }
      },

      // 处理引用文件类型消息
      onCitationFiles: (files: CitationFile[]) => {
        // 修复：累积引用文件而不是替换
        // 将新收到的引用文件与现有引用文件合并
        // 使用 Map 来确保文件的唯一性（基于 fileId）
        const filesMap = new Map<string, CitationFile>();

        // 先添加现有的引用文件
        citationFilesRef.current.forEach((file) => {
          filesMap.set(file.fileId, file);
        });

        // 再添加新收到的引用文件（如果有重复的 fileId，会覆盖旧的）
        files.forEach((file) => {
          filesMap.set(file.fileId, file);
        });

        // 更新引用文件列表
        citationFilesRef.current = Array.from(filesMap.values());

        // 如果已经开始了打字效果，更新引用文件
        if (typingStartedRef.current && taskRef.current) {
          // 更新任务的引用文件，确保所有引用文件都被显示
          taskRef.current.citationList = citationFilesRef.current.map((file) => ({
            ...file,
            url: file.webLink || file.fileUrl,
          }));
        }
      },

      onError: (error: any) => {
        toast.error(error.message);
        ButlerUtil.closeAnswer();
        setLoading(false);
      },

      onComplete() {
        // 不要在这里设置 setIsAsk(false) 和 setPrompt("")
        // 让打字效果自然完成，它会自动调用回调函数
        setLoading(false);
        // 重置标志，为下一次请求做准备
        isFirstValidChunkRef.current = true;
        typingStartedRef.current = false;
        streamControllerRef.current = null;
        citationFilesRef.current = [];
      },
    });
  };

  function stopStream() {
    if (streamControllerRef.current) {
      streamControllerRef.current.onStop?.();
      streamControllerRef.current = null;
    }
  }

  const onStopAsk = () => {
    // 终止流式请求
    stopStream();
    // 停答案动画
    ButlerUtil.stopAnswerAnim();
    taskRef.current = null;
    setIsAsk(false);
    setPrompt('');
    setLoading(false);

    // 重置引用
    accumulatedContentRef.current = '';
    isFirstValidChunkRef.current = true;
    typingStartedRef.current = false;
  };

  useEffect(() => {
    // 组件卸载时清理资源
    return () => {
      if (streamControllerRef.current) {
        streamControllerRef.current.onStop?.();
        streamControllerRef.current = null;
      }

      if (taskRef.current) {
        ButlerUtil.stopAnswerAnim();
      }
    };
  }, []);

  useEffect(() => {
    getQuickQaList();
  }, []);

  const onQuestAsk = (item: IQuackQa) => {
    getTtsQuickQa(item.id).then((res) => {
      if (res.data.code === 1) {
        const bindData = res.data.data;
        bindData.subtitle = bindData.subtitle || [];
        const vttData = bindData.subtitle.map((i: IVTT) => ({
          start: i.start_time,
          end: i.end_time,
          content: i.text,
        }));
        // 先让音频预加载
        const audio = new Audio(bindData.cdn);
        // 使用正确的音频加载事件
        audio.oncanplaythrough = () => {
          ButlerUtil.updateBroadcast(vttData, bindData.cdn, true);
          setLoading(false);
        };
      } else {
        toast.error(res.data.msg);
      }
    });
  };

  const inputRef: any = useRef();
  useEffect(() => {
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current.focus();
        setPrompt('');
      }, 100);
    }
  }, []);

  return (
    <AssistantQuestionView>
      <Image
        src={HistoryImg.src}
        alt=""
        className="history-btn"
        width={72}
        height={72}
        onClick={() => setVisibleModal(true)}
      />
      <div className="question-box">
        <div className="question-history">
          {quackQaList.map((item) => (
            <span key={item.id} onClick={() => onQuestAsk(item)}>
              {item.question}
            </span>
          ))}
        </div>
        <div className="question-ask">
          <div className="question-ask-input">
            <input
              type="text"
              placeholder="Please enter the question"
              value={prompt}
              readOnly={loading}
              onChange={(e) => {
                setPrompt(e.target.value);
              }}
              ref={inputRef}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  onAsk();
                }
              }}
              onFocus={() => {
                // 禁止玩家控制
                KeyPressUtil.setEnable(false);
              }}
              onBlur={() => {
                // 开启玩家控制
                KeyPressUtil.setEnable(true);
              }}
            />
          </div>
          {isAsk ? (
            <button onClick={onStopAsk}>
              <img src={StopImg.src} alt="" />
            </button>
          ) : (
            <button onClick={onAsk} disabled={loading} className={loading ? 'loading' : ''}>
              <img src={loading ? LoadingImage.src : SendImg.src} alt="" />
            </button>
          )}
        </div>
      </div>
      <QuestionHistoryModal visible={visibleModal} onClose={() => setVisibleModal(false)} />
    </AssistantQuestionView>
  );
}
