import { memo } from 'react';
import { AnswerSectionWrapper } from '../style';
import MarkdownRender from '@/components/MarkdownRender';

interface AnswerSectionProps {
  content: string;
}

function AnswerSection({ content }: AnswerSectionProps) {
  return (
    <AnswerSectionWrapper className="answer">
      <div className="answer-box">
        <MarkdownRender context={content} />
      </div>
    </AnswerSectionWrapper>
  );
}

export default memo(AnswerSection);
