import { IAssistantQuestionHistory } from '@/constant/type';
import { CitationSectionWrapper } from '../style';
import { memo } from 'react';

interface CitationSectionProps {
  files: IAssistantQuestionHistory['files'];
}

function CitationSection({ files }: CitationSectionProps) {
  return (
    <CitationSectionWrapper>
      <span>Citations:</span>
      {files.map(
        (file, index) =>
          file && (
            <a key={index} href={file.webLink} target="_blank" rel="noopener noreferrer">
              {file.title}
            </a>
          )
      )}
    </CitationSectionWrapper>
  );
}

export default memo(CitationSection);
