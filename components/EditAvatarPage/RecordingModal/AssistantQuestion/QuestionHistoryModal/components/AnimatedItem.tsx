import React, { <PERSON>EventHand<PERSON>, useRef } from 'react';
import { motion, useInView } from 'motion/react';
import styled from 'styled-components';

interface AnimatedItemProps {
  children: JSX.Element;
  delay?: number;
  index: number;
  onMouseEnter?: MouseEventHandler<HTMLDivElement>;
  onClick?: MouseEventHandler<HTMLDivElement>;
  className?: string;
  border?: boolean;
  marginBottom?: string;
  padding?: string;
  title?: string;
  status?: number;
  id?: string;
}

// 优化后的样式，增强厚重感
const HistoryItem = styled.div<{ border?: boolean; padding?: string }>`
  background: #fbf4e8;
  /* 增加多层阴影效果，模拟卡片厚度 */
  box-shadow: 
    /* 内阴影，增加凹陷感 */
    4px 4px 4px 0px #ffffff inset,
    /* 底部阴影，增加厚度感 */ 0px 4px 0px 0px #d9c4a3,
    /* 右侧阴影，增加厚度感 */ 2px 0px 0px 0px #d9c4a3,
    /* 外阴影，增加浮动感 */ 0px 6px 12px -2px rgba(0, 0, 0, 0.15);

  /* 添加边框，增强边界感 */
  border: ${({ border }) => (border ? '1px solid rgba(165, 128, 97, 0.3)' : 'none')};
  border-bottom: 2px solid rgba(165, 128, 97, 0.5);
  border-right: 2px solid rgba(165, 128, 97, 0.5);

  padding: ${({ padding }) => padding};
  box-sizing: border-box;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 20px;
  color: #140f08;
  overflow: hidden;
  border-radius: 12px;

  /* 添加过渡效果，使变化更平滑 */
  transition: all 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
  transform-origin: center bottom;

  &:hover {
    background: #ecdbc6;
    /* border: 1px solid #a58061; */
    border-bottom: 4px solid #a58061;
    box-sizing: border-box;

    /* 悬停时改变阴影效果，模拟按压感 */
    box-shadow:
      2px 2px 2px 0px #ffffff inset,
      0px 2px 0px 0px #d9c4a3,
      1px 0px 0px 0px #d9c4a3,
      0px 4px 8px -2px rgba(0, 0, 0, 0.1);

    /* 轻微下沉，增强交互感 */
    transform: translateY(2px);
  }

  &.active {
    background: #ecdbc6;
    border: 1px solid #a58061;
    border-bottom: 2px solid #a58061;
    box-sizing: border-box;

    /* 激活状态类似于悬停，但阴影更弱 */
    box-shadow:
      2px 2px 2px 0px #ffffff inset,
      0px 1px 0px 0px #d9c4a3,
      0px 2px 4px -2px rgba(0, 0, 0, 0.1);

    /* 更明显的下沉效果 */
    transform: translateY(3px);
  }
`;

const AnimatedItem: React.FC<AnimatedItemProps> = ({
  children,
  delay = 0,
  index,
  onMouseEnter,
  onClick,
  className,
  border = true,
  marginBottom = '1.2rem',
  padding = '10px',
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const inView = useInView(ref, { amount: 0.3, once: false });
  return (
    <motion.div
      ref={ref}
      data-index={index}
      onMouseEnter={onMouseEnter}
      onClick={onClick}
      initial={{ scale: 0.8, opacity: 0, y: 20 }}
      animate={inView ? { scale: 1, opacity: 1, y: 0 } : { scale: 0.8, opacity: 0, y: 20 }}
      transition={{
        duration: 0.3,
        delay,
        type: 'spring',
        stiffness: 260,
        damping: 20,
      }}
      style={{ marginBottom: marginBottom }}
    >
      <HistoryItem className={className} border={border} padding={padding}>
        {children}
      </HistoryItem>
    </motion.div>
  );
};

export default AnimatedItem;
