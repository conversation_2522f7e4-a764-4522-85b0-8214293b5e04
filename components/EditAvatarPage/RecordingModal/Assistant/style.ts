import styled from 'styled-components';

export const AssistantView = styled.div`
  width:100%;
  height: 746px;
  max-height: calc(100vh - 200px);
  box-sizing: border-box;
  background: #FFF2E2;
  border: 4px solid #ED9800;
  border-radius: 32px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  .menus{
    display: flex;
    gap: 12px;
    &>div{
      padding: 9px 16px;
      border-radius: 12px;
      font-family: 'JetBrains Mono';
      font-weight: 400;
      font-size: 18px;
      line-height: 23.76px;
      background: #C69F7E;
      color: #fff;
      cursor: pointer;
      &.active{
        background: #FF7600;
        box-shadow: 0px 4px 4px 0px #00000040 inset;
      }
    }
  }
  .panel{
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .content{
      flex: 1;
      overflow: hidden;
      .file-list{
        overflow-y: auto;
        height: 100%;
      }
      .no-data{
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Inter;
        font-weight: 400;
        font-size: 18px;
        line-height: 21.78px;
        color: #140F08;
        text-align: center;
        opacity: 0.5;

      }
      .file-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        box-sizing: border-box;
        margin: 8px 0;
        background: #fff;
        border: 1px solid #CABFAB;
        border-radius: 24px;
        gap: 24px;

        .file-item-title {
          font-family: Inter;
          font-weight: 400;
          font-size: 18px;
          line-height: 21.78px;
          color: #140F08;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

        }
      }
      .delete-icon{
        width: 40px;
        height: 40px;
        cursor: pointer;
        box-shadow: 0px -2.5px 0px 0px #00000040 inset;
        border-radius: 10px;
        background: #AA4234;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
          width: 20px;
          height: 20px;
        }
      }
      .disabled{
        opacity: 0.6;
      }
      .error-icon{
        font-size: 12px;
        color: #AA4234;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
    .upload-box{
      width: 100%;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 4px;
      background: #FF8316;
      box-shadow: 0px -4px 0px 0px #00000040 inset;
      border-radius: 16px;
      transition: all 0.3s ease;
      border: 2px dashed transparent;
      cursor: pointer;
      box-sizing: border-box;
      margin-top: 14px;
      &.dragging {
        border: 2px dashed #FFFFFF;
        background: #FF9838;
        transform: scale(1.02);
      }

      img{
        width: 48px;
        height: 48px;
      }
      .upload-box-title{
        font-family: 'JetBrains Mono';
        font-weight: 700;
        font-size: 20px;
        line-height: 20px;
        color: #FFFFFF;
        margin: 0;
      }
      p{
        font-family: Inter;
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        letter-spacing: -4%;
        color: #FFFFFF;
        margin: 0;
      }
    }
    .upload-url-box{
      width: 100%;
      padding: 20px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 14px;
      .upload-url-textarea{
        width: 100%;
        height: 128px;
        border-radius: 24px;
        border: 1px solid #CABFAB;
        padding: 16px;
        box-sizing: border-box;
        font-family: Inter;
        font-weight: 400;
        font-size: 18px;
        line-height: 21.78px;
        color: #140F08;
        &::placeholder{
          color: #686663;
          opacity: 0.5;
        }
          //隐藏右下角
          resize: none;
          outline: none;
      }
      .upload-url-button{
        width: 240px;
        height: 64px;
        border-radius: 16px;
        background: #FF7600;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0px -4px 0px 0px #00000040 inset;
        margin: 24px auto 0 auto;
        cursor: pointer;
        img{
          width: 32px;
          height: 32px;
        }
      }
  }
`;
export const ProgressWrap = styled.div`
  display: inline-block;
  position: relative;

  .g-progress {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: conic-gradient(
      $activeColor 0,
      $activeColor 25%,
      $notActiveColor 25%,
      $notActiveColor 1
    );

    $maskSize: (40px / 2) - 4px;
    mask: radial-gradient(transparent, transparent $maskSize, #000 $maskSize + 0.5px, #000 100%);
    -webkit-mask: radial-gradient(
      transparent,
      transparent $maskSize,
      #000 $maskSize + 0.5px,
      #000 100%
    );
  }

  .g-circle {
    position: absolute;
    top: 3px;
    left: 3px;
    right: 0;
    bottom: 0;
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
    background: #fff;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    & > span {
      position: absolute;
      top: 34px / 2;
      left: 34px / 2;
      width: 50%;
      transform-origin: left;
    }

    & .g-circle-after {
      transform: rotate(0deg);
    }
  }

  .g-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: #ff7600;
  }
`;
