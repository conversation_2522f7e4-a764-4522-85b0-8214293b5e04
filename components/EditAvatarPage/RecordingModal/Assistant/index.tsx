import { AssistantView, ProgressWrap } from './style';
import React, { useEffect, useRef, useState } from 'react';
import UploadIcon from '/public/image/recording/upload.svg';
import {
  assistantUploadFile,
  assistantUploadUrl,
  deleteAssistant,
  getAssistantList,
} from '../../../../server';
import toast from 'react-hot-toast';
import { IAppState, IAssistant } from '../../../../constant/type';
import DeleteIcon from '/public/image/recording/delete.svg';
import { useSelector } from 'react-redux';
import classNames from 'classnames';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

enum MENUS_ENUM {
  document = 'document',
  url = 'url',
}

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
];

const ALLOWED_FILE_EXTENSIONS = ['.pdf', '.txt', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];

export default function Assistant() {
  const [activeMenu, setActiveMenu] = useState<MENUS_ENUM>(MENUS_ENUM.document);

  return (
    <AssistantView>
      <div className="menus">
        <div
          className={activeMenu === MENUS_ENUM.document ? 'active' : ''}
          onClick={() => setActiveMenu(MENUS_ENUM.document)}
        >
          My Documents
        </div>
        <div
          className={activeMenu === MENUS_ENUM.url ? 'active' : ''}
          onClick={() => setActiveMenu(MENUS_ENUM.url)}
        >
          My Url
        </div>
      </div>
      <DocumentPanel activeMenu={activeMenu} />
      <UrlPanel activeMenu={activeMenu} />
    </AssistantView>
  );
}

function DocumentPanel({ activeMenu }: { activeMenu: MENUS_ENUM }) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  const [uploadFileList, setUploadFileList] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [fileList, setFileList] = useState<IAssistant[]>([]);
  // 正在上传中的文件索引
  const [uploadingIndex, setUploadingIndex] = useState<number>(0);

  const validateFile = (file: File): boolean => {
    // 检查文件类型
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      // 如果文件类型不匹配，检查文件扩展名
      const fileName = file.name.toLowerCase();
      const hasValidExtension = ALLOWED_FILE_EXTENSIONS.some((ext) => fileName.endsWith(ext));
      if (!hasValidExtension) {
        toast.error(
          `Not supported file format: ${file.name}\nSupported formats: ${ALLOWED_FILE_EXTENSIONS.join(', ')}`
        );
        return false;
      }
    }
    return true;
  };
  const getData = () => {
    if (!btcAddress) {
      return;
    }
    getAssistantList(MENUS_ENUM.document).then((res) => {
      if (res.data.code === 1) {
        setFileList(res.data?.data || []);
      } else {
        toast.error(res.data.msg);
      }
    });
  };

  const ref = useRef<NodeJS.Timeout | null>(null);

  const uploadSuccessCallback = () => {
    ref.current && clearTimeout(ref.current);
    ref.current = setTimeout(() => {
      getData();
    }, 500);
  };
  useEffect(() => {
    if (activeMenu === MENUS_ENUM.document) {
      getData();
    }
  }, [activeMenu, btcAddress]);
  const onUpload = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    // validateFile
    const validFiles = Array.from(files).filter(validateFile);
    if (validFiles.length === 0) {
      return;
    }
    setUploadFileList([...uploadFileList, ...validFiles]);
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) {
      return;
    }
    const namesMap: { [key: string]: boolean } = {};
    for (let i = 0; i < fileList.length; i++) {
      namesMap[fileList[i].title] = true;
    }
    const validFiles = Array.from(files)
      .filter(validateFile)
      .filter((file) => !namesMap[file.name]);
    if (validFiles.length === 0) {
      toast.error('No new file selected');
      return;
    }
    setUploadFileList([...uploadFileList, ...validFiles]);
  };

  return (
    <div
      className="panel"
      style={{ display: activeMenu === MENUS_ENUM.document ? 'flex' : 'none' }}
    >
      <div className="content">
        <div className="file-list">
          {uploadFileList.map((item, index) => (
            <FileItem
              key={index}
              file={item}
              uploadSuccessCallback={uploadSuccessCallback}
              thisIndex={index}
              uploadingIndex={uploadingIndex}
              setUploadingIndex={setUploadingIndex}
            />
          ))}
          {fileList.map((item, index) => (
            <RenderItem key={index} item={item} getData={getData} />
          ))}
          {fileList.length === 0 && uploadFileList.length === 0 && (
            <div className="no-data">No data</div>
          )}
        </div>
      </div>

      <div
        className={`upload-box ${isDragging ? 'dragging' : ''}`}
        onClick={onUpload}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <img src={UploadIcon.src} alt="upload" />
        <div className="upload-box-title">Click to upload or drag & drop</div>
        <p>supports text files, csv's, spreadsheets, and more!</p>
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileChange}
          accept={ALLOWED_FILE_EXTENSIONS.join(',')}
          multiple
        />
      </div>
    </div>
  );
}

function UrlPanel({ activeMenu }: { activeMenu: MENUS_ENUM }) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [urlList, setUrlList] = useState<IAssistant[]>([]);
  const [url, setUrl] = useState<string>('');
  const [uploadUrlList, setUploadUrlList] = useState<string[]>([]);
  const [uploadingIndex, setUploadingIndex] = useState<number>(0);
  const getData = () => {
    if (!btcAddress) {
      return;
    }
    getAssistantList(MENUS_ENUM.url).then((res) => {
      if (res.data.code === 1) {
        setUrlList(res.data?.data || []);
      } else {
        toast.error(res.data.msg);
      }
    });
  };
  useEffect(() => {
    if (activeMenu === MENUS_ENUM.url) {
      getData();
    }
  }, [activeMenu, btcAddress]);
  const onUploadUrl = () => {
    if (!url) {
      return;
    }
    const urls = url
      .trim()
      .split('\n')
      .filter((item) => item !== '');
    for (let i = 0; i < urls.length; i++) {
      // 判断是否为url 提示第几行
      if (!urls[i].startsWith('http')) {
        toast.error(`Invalid url: line ${i + 1}`);
        return;
      }
    }
    setUploadUrlList([...uploadUrlList, ...urls]);
    setUrl('');
  };
  const ref: any = useRef(null);
  const uploadSuccessCallback = () => {
    ref.current && clearTimeout(ref.current);
    ref.current = setTimeout(() => {
      getData();
    }, 500);
  };
  return (
    <div className="panel" style={{ display: activeMenu === MENUS_ENUM.url ? 'flex' : 'none' }}>
      <div className="content">
        <div className="file-list">
          {uploadUrlList.map((item, index) => (
            <FileItem
              key={index}
              url={item}
              uploadSuccessCallback={uploadSuccessCallback}
              thisIndex={index}
              uploadingIndex={uploadingIndex}
              setUploadingIndex={setUploadingIndex}
            />
          ))}
          {urlList.map((item, index) => (
            <RenderItem key={index} item={item} getData={getData} />
          ))}
          {urlList.length === 0 && uploadUrlList.length === 0 && (
            <div className="no-data">No data</div>
          )}
        </div>
      </div>
      <div className="upload-url-box">
        <textarea
          className="upload-url-textarea"
          placeholder="One for each line"
          value={url}
          onFocus={() => {
            // 禁止玩家控制
            KeyPressUtil.setEnable(false);
          }}
          onBlur={() => {
            // 开启玩家控制
            KeyPressUtil.setEnable(true);
          }}
          onChange={(e) => setUrl(e.target.value)}
        />
        <div className="upload-url-button" onClick={onUploadUrl}>
          <img src={UploadIcon.src} alt="upload" />
        </div>
      </div>
    </div>
  );
}

function FileItem({
  file,
  url,
  uploadSuccessCallback,
  thisIndex,
  uploadingIndex,
  setUploadingIndex,
}: {
  file?: File;
  url?: string;
  uploadSuccessCallback: () => void;
  thisIndex: number;
  uploadingIndex: number;
  setUploadingIndex: (index: number) => void;
}) {
  const [progress, setProgress] = useState<number>(0);
  const [isError, setIsError] = useState<boolean>(false);
  useEffect(() => {
    if (thisIndex !== uploadingIndex) {
      return;
    }
    if (file) {
      assistantUploadFile({
        file,
        onUploadProgress: (progress) => setProgress(progress === 100 ? 99 : progress),
      }).then((res) => {
        if (res.data.code === 1) {
          setProgress(100);
        } else if (res.data.code === 5007) {
          toast.error('File already exists');
          setIsError(true);
        } else {
          setIsError(true);
          toast.error(res.data.msg);
        }
        // 上传成功后，更新上传索引
        setUploadingIndex(thisIndex + 1);
        uploadSuccessCallback();
      });
    }
  }, [file, uploadingIndex]);

  const progressRef: any = useRef(0);
  useEffect(() => {
    if (thisIndex !== uploadingIndex) {
      return;
    }
    if (url) {
      // 添加一个假进度条
      const interval = setInterval(() => {
        if (progressRef.current < 99) {
          progressRef.current++;
          setProgress(progressRef.current);
        } else {
          clearInterval(interval);
        }
      }, 50);

      assistantUploadUrl({
        url,
      }).then((res) => {
        if (res.data.code === 1) {
          clearInterval(interval);
          setProgress(100);
        } else if (res.data.code === 5007) {
          toast.error('Url already exists');
          clearInterval(interval);
          setIsError(true);
        } else {
          clearInterval(interval);
          setIsError(true);
          toast.error(res.data.msg);
        }
        // 上传成功后，更新上传索引
        setUploadingIndex(thisIndex + 1);
        uploadSuccessCallback();
      });
    }
  }, [url, uploadingIndex]);

  if (progress === 100) {
    return null;
  }

  return (
    <div className="file-item">
      <div className="file-item-title">{file?.name || url}</div>
      {isError ? <div className="error-icon">Error</div> : <Progress progress={progress} />}
    </div>
  );
}

function RenderItem({ item, getData }: { item: IAssistant; getData: Function }) {
  const [loading, setLoading] = useState<boolean>(false);
  const onDelete = () => {
    if (loading) {
      return;
    }
    setLoading(true);
    deleteAssistant(item.fileId)
      .then((r) => {
        if (r.data.code === 1) {
          toast.success('Delete success');
          getData();
        } else {
          toast.error(r.data.msg);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };
  return (
    <div className="file-item">
      <div className="file-item-title">{item.title}</div>
      <div className={classNames('delete-icon', loading ? 'disabled' : '')} onClick={onDelete}>
        <img src={DeleteIcon.src} alt="delete" />
      </div>
    </div>
  );
}

function Progress({ progress }: { progress: number }) {
  const proportion = `${progress}%`;

  // 计算角度 0(0) -> 100(360);
  const rotate = (progress / 100) * 360;

  function getProgressStyle(rotate: number) {
    return {
      background: `conic-gradient(#FF7600 0deg, #FF7600 ${rotate}deg, #ffffff ${rotate}deg, #ffffff)`,
    };
  }

  function getCircleAfterStyle(rotate: number) {
    return {
      transform: `rotate(${rotate}deg)`,
    };
  }

  return (
    <ProgressWrap>
      <div className="g-progress" style={getProgressStyle(rotate)}></div>
      <div className="g-circle">
        <span className="g-circle-before"></span>
        <span className="g-circle-after" style={getCircleAfterStyle(rotate)}></span>
      </div>
      <div className="g-text">{proportion}</div>
    </ProgressWrap>
  );
}
