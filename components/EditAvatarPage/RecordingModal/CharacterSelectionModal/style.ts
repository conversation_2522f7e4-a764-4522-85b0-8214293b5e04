import styled from 'styled-components';

export const CharacterSelectionModalView = styled.div`
  .modal-grid {
    border-radius: 32px;
    padding: 16px;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    margin-top: 24px;
    .modal-grid-item {
      width: 176px;
      height: 176px;
      border: 1.47px solid #cabfab;
      border-radius: 32px;
      box-sizing: border-box;
      background: #30303066;
      cursor: pointer;
      overflow: hidden;
      & > img {
        width: 100%;
        height: 100%;
      }
      &.active {
        border: 4px solid #ff8316;
      }
      &.disabled {
        cursor: not-allowed;
        filter: grayscale(100%);
      }
    }
  }
  & > button {
    width: 100%;
    height: 56px;
    background: #ff8316;
    font-family: 'JetBrains Mono';
    font-size: 20px;
    font-weight: 700;
    line-height: 20px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    outline: none;
    border: 0;
    border-radius: 16px;
    margin-top: 24px;
    text-align: center;
    cursor: pointer;
  }
`;
