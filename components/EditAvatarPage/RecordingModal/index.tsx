import { <PERSON><PERSON><PERSON>View, HistoryListView, RecordingBoxView, RecordingView } from './style';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  IAppState,
  IBasicSummaryData,
  IBindInfo,
  IttsBuildDataHistory,
  IVTT,
  SCENE_TYPE,
  STORAGE_MENU_ENUM,
} from '../../../constant/type';
import { getAudioUrl, getInfoBindById, getTtsBindList, textToAudio } from '../../../server';
import toast from 'react-hot-toast';
import AIDarkImage from '/public/image/recording/ai-dark.svg';
import AILightImage from '/public/image/recording/ai-light.svg';
import PlayImage from '/public/image/basic/play.svg';
import PauseImage from '/public/image/basic/pause.svg';
import LoadingImage from '/public/image/basic/loading.svg';
import LoadingSvg from '/public/image/basic/loading.svg';
import RightImage from '/public/image/basic/right.svg';
import VideoImage from '/public/image/basic/video.svg';
import HistoryLightImage from '/public/image/recording/history-light.svg';
import HistoryDarkImage from '/public/image/recording/history-dark.svg';
import AssistantDarkImage from '/public/image/recording/assistant-dark.svg';
import AssistantLightImage from '/public/image/recording/assistant-lisht.svg';
import VideoShareModal from './VideoShareModal';
import { setIsRecording, setStorageMenu } from '../../../store/app';
import { ButlerData, ButlerUtil } from '../../../world/Global/GlobalButlerUtil';
import GlobalSpaceEvent, { GlobalDataKey } from '../../../world/Global/GlobalSpaceEvent';
import classNames from 'classnames';
import DSvg from '/public/image/recording/d.svg';
import dayjs from 'dayjs';
import Assistant from './Assistant';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';

enum MENU_TEB {
  RECORDING = 1,
  HISTORY = 2,
  ASSISTANT = 3,
}

export const videoMimeType = 'video/mp4; codecs="avc1.4D401E"';

export default function RecordingModal({
  basicSummaryData,
}: {
  basicSummaryData: IBasicSummaryData | null;
}) {
  const { storageMenu, isRecording, isTtsWhiteList } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const prevStoreMenuRef: any = useRef();
  const [activeMenu, setActiveMenu] = useState(MENU_TEB.RECORDING);
  const [isHidden, setIsHidden] = useState(false);
  useEffect(() => {
    if (isTtsWhiteList) {
      const oldMusicSwitch = localStorage.getItem('music') === 'true';
      if (storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU) {
        // 更新管家位置
        ButlerUtil.updateButlerTransform();
        // 开启自由视角/并隐藏玩家
        GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.OpenFreeCamera, true);
        localStorage.setItem('music', 'false');
      } else if (prevStoreMenuRef.current === STORAGE_MENU_ENUM.RECORDING_MENU) {
        // 隐藏管家
        ButlerUtil.hideButler();
        // 关闭自由视角/并显示玩家
        GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.OpenFreeCamera, false);
        localStorage.setItem('music', oldMusicSwitch ? 'true' : 'false');
      }
      prevStoreMenuRef.current = storageMenu;
    }
  }, [storageMenu, isTtsWhiteList]);
  useEffect(() => {
    // 如果没有白名单，只显示历史列表，且用户点击历史记录时切换管家位置
    if (!isTtsWhiteList) {
      setActiveMenu(MENU_TEB.HISTORY);
    } else {
      setActiveMenu(MENU_TEB.RECORDING);
    }
  }, [isTtsWhiteList]);

  const onSetActiveMenu = (menu: MENU_TEB) => {
    if (isRecording) {
      return;
    }
    setActiveMenu(menu);
  };

  if (storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU) {
    return (
      <RecordingBoxView
        isHidden={isHidden}
        width={activeMenu === MENU_TEB.HISTORY ? '304px' : '488px'}
      >
        {activeMenu === MENU_TEB.RECORDING && <RecordingModalBox />}
        {activeMenu === MENU_TEB.HISTORY && <HistoryList basicSummaryData={basicSummaryData} />}
        {activeMenu === MENU_TEB.ASSISTANT && <Assistant />}
        <div className="recording-box-menu">
          {isTtsWhiteList && (
            <div
              className={classNames({
                'recording-box-menu-item': true,
                active: activeMenu === MENU_TEB.RECORDING,
              })}
              onClick={() => {
                onSetActiveMenu(MENU_TEB.RECORDING);
              }}
            >
              <img
                src={activeMenu === MENU_TEB.RECORDING ? AIDarkImage.src : AILightImage.src}
                alt=""
              />
            </div>
          )}
          <div
            className={classNames({
              'recording-box-menu-item': true,
              active: activeMenu === MENU_TEB.HISTORY,
            })}
            onClick={() => {
              onSetActiveMenu(MENU_TEB.HISTORY);
            }}
          >
            <img
              src={activeMenu === MENU_TEB.HISTORY ? HistoryDarkImage.src : HistoryLightImage.src}
              alt=""
            />
          </div>
          {isTtsWhiteList && (
            <div
              className={classNames({
                'recording-box-menu-item': true,
                active: activeMenu === MENU_TEB.ASSISTANT,
              })}
              onClick={() => {
                onSetActiveMenu(MENU_TEB.ASSISTANT);
              }}
            >
              <img
                src={
                  activeMenu === MENU_TEB.ASSISTANT
                    ? AssistantDarkImage.src
                    : AssistantLightImage.src
                }
                alt=""
              />
            </div>
          )}
          <img
            src={DSvg.src}
            alt=""
            className="hidden-btn"
            onClick={() => {
              setIsHidden(!isHidden);
            }}
          />
        </div>
      </RecordingBoxView>
    );
  }
  return null;
}

// 录屏
function RecordingModalBox() {
  const { isRecording, storageMenu } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();
  const recorderRef: any = useRef(null);
  const [inputText, setInputText] = useState('Welcome to SatWorld');
  const [requestId, setRequestId] = useState<string>('');
  const [audioSrc, setAudioSrc] = useState<string>('');
  const [requestLoading, setRequestLoading] = useState<boolean>(false);
  const [audioDuration, setAudioDuration] = useState(0); // 音频总时长
  const [currentTime, setCurrentTime] = useState(0); // 当前播放时间
  const [isLoading, setIsLoading] = useState(false); // 音频加载状态
  const [isAudioReady, setIsAudioReady] = useState(false); // 音频是否准备就绪
  const [retryCount, setRetryCount] = useState(0);
  const [showStep, setShowStep] = useState(1);
  const [videoSrc, setVideoSrc] = useState<string>('');
  const [videoBlob, setVideoBlob] = useState<Blob | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioBuffer | null>(null);
  const [processedAudioUrl, setProcessedAudioUrl] = useState<string>('');

  const getAudio = () => {
    if (!MediaRecorder.isTypeSupported(videoMimeType)) {
      toast.error('Please upgrade to the latest Chrome.');
      return;
    }
    setAudioSrc('');
    setRequestLoading(true);
    const isZh = /[\u4e00-\u9fa5]/.test(inputText);
    // const isEn = /[A-Za-z]/.test(inputText)
    textToAudio({
      content: inputText,
      ttsLang: isZh ? 'zh' : 'en',
    })
      .then((res) => {
        if (res.data.code === 1) {
          setRequestId(res.data.data.requestId);
        } else {
          toast.error(res.data.msg);
          setRequestLoading(false);
        }
      })
      .catch(() => {
        setRequestLoading(false);
        toast.error('Network error');
      });
  };
  const timeRef: any = useRef(null);
  const getAudioUrlForRequestId = () => {
    if (requestId) {
      clearTimeout(timeRef.current);
      timeRef.current = setTimeout(() => {
        getAudioUrl(requestId).then((res) => {
          if (res.data.code === 1) {
            if (res.data.data.status === 'all_completed') {
              // 处理音频添加2秒空白
              processAudio(res.data.data.cdn);
              setAudioSrc(res.data.data.cdn);
              setRequestLoading(false);
              setIsLoading(true);
            } else {
              getAudioUrlForRequestId();
            }
          } else {
            toast.error(res.data.msg);
          }
        });
      }, 2000);
    }
  };
  useEffect(() => {
    getAudioUrlForRequestId();
    return () => {
      clearTimeout(timeRef.current);
    };
  }, [requestId]);
  const audioRef: any = useRef(null);
  const [isPlay, setIsPlay] = useState(false);
  const onPlayAudio = () => {
    if (audioRef.current) {
      setIsPlay(true);
      audioRef.current.play();
    }
  };
  const onStopAudio = () => {
    if (audioRef.current) {
      setIsPlay(false);
      audioRef.current.pause();
    }
  };

  const stopVideo = () => {
    recorderRef.current.stop();
  };
  const startVideo = async () => {
    dispatch(setIsRecording(true));
    const renderTarget = document.getElementById('render-target-game') as HTMLCanvasElement;
    const canvas = renderTarget.getElementsByTagName('canvas')[0];
    // 获取 Canvas 的流
    const stream = canvas.captureStream(30); // 30fps

    // 获取音频轨道 - 确保使用处理后的音频
    const audioElement = audioRef.current;
    audioElement.currentTime = 0; // 重置音频时间
    const audioStream = audioElement.captureStream();
    const audioTrack = audioStream.getAudioTracks()[0];
    stream.addTrack(audioTrack);

    // 创建 MediaRecorder 对象
    let recordedChunks: any[] = [];
    // recorderRef.current = new MediaRecorder(stream);
    recorderRef.current = new MediaRecorder(stream, { mimeType: videoMimeType });

    recorderRef.current.ondataavailable = (event: any) => {
      recordedChunks.push(event.data);
    };

    recorderRef.current.onstop = async () => {
      // const blob = new Blob(recordedChunks, {type: 'video/webm;codecs=vp9'});
      const blob = new Blob(recordedChunks, { type: videoMimeType });
      const url = URL.createObjectURL(blob);
      setVideoSrc(url);
      setVideoBlob(blob);
      dispatch(setIsRecording(false));
    };

    recorderRef.current.start();
    onPlayAudio();
    setTimeout(() => {
      // 开始说动作
      ButlerUtil.startSpeakAction();
      // ButlerUtil.updateBroadcast(inputText, audioSrc, true)
    }, 2000);
  };
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  useEffect(() => {
    if (!audioSrc) {
      setIsLoading(false);
      setCurrentTime(0);
      setAudioDuration(0);
      setIsAudioReady(false);
      setRetryCount(0);
    }
  }, [audioSrc]);

  // 添加处理音频的函数
  const processAudio = async (originalUrl: string) => {
    try {
      // 创建新的 AudioContext
      const audioContext = new AudioContext();
      audioContextRef.current = audioContext;

      // 获取原始音频数据
      const response = await fetch(originalUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // 创建新的 AudioBuffer，长度为原始音频加2秒
      const newBuffer = audioContext.createBuffer(
        audioBuffer.numberOfChannels,
        audioContext.sampleRate * 2 + audioBuffer.length,
        audioContext.sampleRate
      );

      // 复制原始音频数据到新缓冲区，位置在2秒后
      for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
        const newChannelData = newBuffer.getChannelData(channel);
        const originalChannelData = audioBuffer.getChannelData(channel);

        // 前2秒保持为0（静音）
        for (let i = 0; i < audioContext.sampleRate * 2; i++) {
          newChannelData[i] = 0;
        }

        // 复制原始音频数据
        for (let i = 0; i < originalChannelData.length; i++) {
          newChannelData[i + audioContext.sampleRate * 2] = originalChannelData[i];
        }
      }

      // 创建 Blob
      const wavBuffer = await audioBufferToWav(newBuffer);
      const suffix = originalUrl.split('.').pop();
      const blob = new Blob([wavBuffer], { type: `audio/${suffix}` });
      const url = URL.createObjectURL(blob);
      setProcessedAudioUrl(url);
      audioBufferRef.current = newBuffer;
    } catch (error) {
      console.error('Error processing audio:', error);
      // 如果处理失败，使用原始音频
      setProcessedAudioUrl(originalUrl);
    }
  };

  // 添加 audioBufferToWav 辅助函数
  function audioBufferToWav(buffer: AudioBuffer) {
    const numberOfChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numberOfChannels * bytesPerSample;

    const dataLength = buffer.length * blockAlign;
    const bufferLength = 44 + dataLength;

    const arrayBuffer = new ArrayBuffer(bufferLength);
    const view = new DataView(arrayBuffer);

    // RIFF identifier
    writeString(view, 0, 'RIFF');
    // file length minus RIFF identifier length and file description length
    view.setUint32(4, 36 + dataLength, true);
    // RIFF type
    writeString(view, 8, 'WAVE');
    // format chunk identifier
    writeString(view, 12, 'fmt ');
    // format chunk length
    view.setUint32(16, 16, true);
    // sample format (raw)
    view.setUint16(20, format, true);
    // channel count
    view.setUint16(22, numberOfChannels, true);
    // sample rate
    view.setUint32(24, sampleRate, true);
    // byte rate (sample rate * block align)
    view.setUint32(28, sampleRate * blockAlign, true);
    // block align (channel count * bytes per sample)
    view.setUint16(32, blockAlign, true);
    // bits per sample
    view.setUint16(34, bitDepth, true);
    // data chunk identifier
    writeString(view, 36, 'data');
    // data chunk length
    view.setUint32(40, dataLength, true);

    const channels = [];
    for (let i = 0; i < numberOfChannels; i++) {
      channels.push(buffer.getChannelData(i));
    }

    let offset = 44;
    for (let i = 0; i < buffer.length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, channels[channel][i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7fff, true);
        offset += 2;
      }
    }

    return arrayBuffer;
  }

  function writeString(view: DataView, offset: number, string: string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  return (
    <RecordingView>
      <div className="recording-box">
        {showStep === 1 && (
          <textarea
            value={inputText}
            onFocus={() => {
              // 禁止玩家控制
              KeyPressUtil.setEnable(false);
            }}
            onBlur={() => {
              // 开启玩家控制
              KeyPressUtil.setEnable(true);
            }}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Enter text: eg. Welcome to satWorld"
          />
        )}
        <div className="audio-view">
          <span>{isAudioReady ? formatTime(currentTime) : '--'}</span>
          <div>
            <div style={{ width: `${audioDuration ? (currentTime / audioDuration) * 100 : 0}%` }} />
          </div>
          <span>{isAudioReady ? formatTime(audioDuration) : '--'}</span>
        </div>
        {audioSrc && (
          <audio
            preload="auto"
            ref={audioRef}
            crossOrigin="anonymous"
            // 使用处理后的音频URL
            src={processedAudioUrl || audioSrc}
            onLoadedMetadata={(e) => {
              setAudioDuration(e.currentTarget.duration);
              setIsLoading(false);
              setIsAudioReady(true);
              setRetryCount(0);
            }}
            onTimeUpdate={(e) => {
              setCurrentTime(e.currentTarget.currentTime);
            }}
            onEnded={() => {
              if (isRecording) {
                // 结束说话动作
                ButlerUtil.stopSpeakAction();
                onStopAudio();
                setTimeout(() => {
                  stopVideo();
                  setIsPlay(false);
                  setCurrentTime(0);
                }, 2000);
              } else {
                setIsPlay(false);
                setCurrentTime(0);
              }
            }}
            onError={(e) => {
              if (retryCount < 1) {
                setRetryCount((prev) => prev + 1);
                setIsLoading(true);
                setTimeout(() => {
                  const audio = audioRef.current;
                  if (audio) {
                    audio.load();
                  }
                }, 1000);
              } else {
                toast.error('Audio load failed, please try again');
              }
            }}
            controls={false}
          ></audio>
        )}
        {showStep === 1 && (
          <div className="actions">
            <button
              className="get-audio"
              disabled={!inputText || requestLoading || isLoading}
              onClick={getAudio}
            >
              {requestLoading ? (
                <img src={LoadingImage.src} alt="" className="loading-ano" />
              ) : (
                <img src={AILightImage.src} alt="" />
              )}
            </button>
            <button
              className="audio-play"
              disabled={!isAudioReady || isLoading}
              onClick={isPlay ? onStopAudio : onPlayAudio}
            >
              {isLoading ? (
                <img src={LoadingImage.src} alt="" className="loading-ano" />
              ) : (
                <img src={isPlay ? PauseImage.src : PlayImage.src} alt="" />
              )}
            </button>
            <button
              className="next"
              disabled={!audioSrc || isLoading}
              onClick={() => {
                setShowStep(2);
                onStopAudio();
                // 设置音频进度为0
                audioRef.current.currentTime = 0;
                setCurrentTime(0);
              }}
            >
              <img src={RightImage.src} alt="" />
            </button>
          </div>
        )}
        {showStep === 2 && (
          <div className="actions">
            <button className="prev" onClick={() => setShowStep(1)} disabled={isRecording}>
              <img src={RightImage.src} alt="" />
            </button>
            {isRecording ? (
              <button className="recording" onClick={stopVideo} disabled={isRecording}>
                <img src={PauseImage.src} alt="" />
              </button>
            ) : (
              <button className="recording" onClick={startVideo}>
                <img src={VideoImage.src} alt="" />
              </button>
            )}
          </div>
        )}
      </div>
      <VideoShareModal
        visible={!!videoSrc}
        videoSrc={videoSrc}
        onClose={() => {
          setVideoSrc('');
          setAudioSrc('');
          dispatch(setStorageMenu(null));
        }}
        audioDuration={audioDuration}
        requestId={requestId}
        videoBlob={videoBlob}
        inputText={inputText}
      />
    </RecordingView>
  );
}

// 历史列表
function HistoryList({ basicSummaryData }: { basicSummaryData: IBasicSummaryData | null }) {
  //历史记录
  const [historyLoading, setHistoryLoading] = useState(false);
  const [list, setList] = useState<IttsBuildDataHistory[]>([]);
  const [edit, setEdit] = useState<boolean>(false);
  const [modalData, setModalData] = useState<{ info: IBindInfo; videoId: string } | null>(null);
  const [checkRequestId, setCheckRequestId] = useState<string | null>(null);
  const requestId = basicSummaryData?.ttsResult?.requestId;
  useEffect(() => {
    if (requestId) {
      setCheckRequestId(requestId);
    }
  }, [requestId]);
  const getHistoryList = () => {
    setHistoryLoading(true);
    getTtsBindList({
      pageNo: 0,
      pageSize: 100,
    })
      .then((res: any) => {
        setList(res.data.data?.infos || []);
        setEdit(res.data.data?.edit || false);
      })
      .finally(() => {
        setHistoryLoading(false);
      });
  };
  useMemo(() => {
    getHistoryList();
  }, []);

  return (
    <HistoryListView>
      <div>
        {list.length > 0 && (
          <div>
            {list.map((item, index) => (
              <HistoryItem
                key={index}
                item={item}
                setModalData={setModalData}
                edit={edit}
                setCheckRequestId={setCheckRequestId}
                checkRequestId={checkRequestId}
              />
            ))}
          </div>
        )}
        {list.length === 0 && (
          <div className="empty-history">{historyLoading ? <p>Loading...</p> : <p>No Data</p>}</div>
        )}
      </div>
      {modalData && (
        <VideoShareModal
          videoId_={modalData.videoId}
          videoBlob={new Blob()}
          videoSrc={modalData.info.video}
          onClose={() => {
            setModalData(null);
          }}
          audioDuration={0}
          visible={true}
          requestId={modalData.info.requestId}
          subtitle={modalData.info.subtitle}
          getHistoryList={getHistoryList}
          edit={edit}
          inputText={modalData.info.content}
        />
      )}
    </HistoryListView>
  );
}

function HistoryItem({
  item,
  setModalData,
  edit,
  setCheckRequestId,
  checkRequestId,
}: {
  item: IttsBuildDataHistory;
  setModalData: Function;
  edit: boolean;
  setCheckRequestId: Function;
  checkRequestId: string | null;
}) {
  const [loading, setLoading] = useState<boolean>(false);
  const { isTtsWhiteList } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  const onEdit = () => {
    setLoading(true);
    getInfoBindById(item.id)
      .then((res) => {
        // 编辑 弹出弹窗
        setModalData({
          info: res.data.data,
          videoId: item.id,
        });
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const onPlay = () => {
    if (!item.data) {
      return;
    }
    setLoading(true);
    getInfoBindById(item.id)
      .then((res) => {
        const bindData = res.data.data;
        bindData.subtitle = bindData.subtitle || [];
        const vttData = bindData.subtitle.map((i: IVTT) => ({
          start: i.start_time,
          end: i.end_time,
          content: i.text,
        }));
        const butlerDataParams: ButlerData = {
          butlerPosition: bindData.data.butlerPosition,
          butlerQuaternion: bindData.data.butlerQuaternion,
          butlerSceneType: bindData.data.butlerSceneType as SCENE_TYPE,
          usePet: bindData.data.usePet || '',
          vttData: vttData,
          mp3Url: bindData.cdn,
          visitorPositionList: bindData.data.visitorPositionList || [],
        };
        // 先让音频预加载
        const audio = new Audio(bindData.cdn);
        // 使用正确的音频加载事件
        audio.oncanplaythrough = () => {
          ButlerUtil.resetData(butlerDataParams);
          ButlerUtil.enterVisitor();
          setCheckRequestId(item.requestId);
          setLoading(false);
        };
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <HistoryItemView
      bg={item.image}
      active={isTtsWhiteList ? false : checkRequestId === item.requestId}
    >
      <div className="poster-img">
        <div className="poster-img-body">
          <img src={item.image} alt="" />
        </div>
        <div
          className={classNames({
            'poster-img-action': true,
            loading: loading,
          })}
        >
          {loading ? (
            <img src={LoadingSvg.src} alt="" className="loading-img" />
          ) : isTtsWhiteList ? (
            <div className="play-btn" onClick={onEdit}>
              <img src={PlayImage.src} alt="" />
            </div>
          ) : (
            <div className="play-btn play-btn-view" onClick={onPlay}>
              <span>Go & view</span>
            </div>
          )}
        </div>
      </div>
      <p className="desc">{item.content}</p>
      <p className="time">{dayjs(item.created).format('MMM DD, YYYY HH:mm:ss')}</p>
    </HistoryItemView>
  );
}
