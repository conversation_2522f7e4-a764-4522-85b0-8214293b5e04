import { NftMenuView } from './style';
import { getNFTImgLink } from '../../../utils';
import SvgComponent from '../../../AvatarOrdinalsBrowser/components/AvatarPage/SvgComponent';
import { SVG_FILE } from '../../../AvatarOrdinalsBrowser/constant/staticFile';
import { useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { getInscriptionImageList, wallNftSave } from '../../../server';
import { useSelector } from 'react-redux';
import { IAppState, IWallNft } from '../../../constant/type';
import toast from 'react-hot-toast';
import ScrollLoad from '../../Basic/ScrollLoad';
import XSvg from '/public/image/x.svg';
import GlobalSpaceEvent, { GlobalDataKey } from '../../../world/Global/GlobalSpaceEvent';

interface IProps {
  wallNftList: IWallNft[];
  viewNftStatus: {
    left: boolean;
    open: boolean;
    position: number;
  } | null;
  getWallNftList: Function;
  onClose: Function;
  wallListLoading: boolean;
}

export default function NftMenu({
  wallNftList,
  viewNftStatus,
  getWallNftList,
  onClose,
  wallListLoading,
}: IProps) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const pageSize = 120;
  const [page, setPage] = useState<number>(1);
  const [nftList, setNftList] = useState<string[]>([]);
  const [loadEnd, setLoadEnd] = useState<boolean>(false);

  const debounceRef: any = useRef(null);
  const [loadLoading, setLoadLoading] = useState(true);

  const getList = () => {
    if (!btcAddress || loadEnd) {
      return;
    }
    setLoadLoading(true);
    getInscriptionImageList({
      address: btcAddress,
      pageNo: 1,
      pageSize: pageSize * page,
    })
      .then((res: any) => {
        const data = res.data.data || [];
        setNftList(data);
        // if (data.length < pageSize) {
        //   setLoadEnd(true)
        // }
      })
      .finally(() => {
        setLoadLoading(false);
      });
  };
  useMemo(() => {
    clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      getList();
    }, 100);
    return () => clearTimeout(debounceRef.current);
  }, [btcAddress, page]);

  const wallNftListMap = useMemo(() => {
    return wallNftList.reduce(
      (acc, cur) => {
        acc[cur.content] = cur.position;
        return acc;
      },
      {} as Record<string, number>
    );
  }, [wallNftList]);
  const [isSetLoading, setIsSetLoading] = useState<boolean>(false);
  const onSetNft = (inscriptionId: string) => {
    if (!viewNftStatus || isSetLoading || wallListLoading) {
      return;
    }
    if (viewNftStatus.position === 0) {
      toast.error('Please select a location on the wall first');
      return;
    }
    setIsSetLoading(true);
    const loadingToast = toast.loading('Updating...');
    // 点击其他使用中NFT：两个NFT位置互换
    if (
      wallNftListMap[inscriptionId] &&
      wallNftListMap[inscriptionId] !== 0 &&
      viewNftStatus.position !== 0 &&
      wallNftListMap[inscriptionId] !== viewNftStatus.position
    ) {
      const inscriptionId1 = inscriptionId;
      const inscriptionId2 = wallNftList.find((i) => i.position === viewNftStatus.position)
        ?.content as string;
      const promiseList = [
        wallNftSave({
          position: viewNftStatus.position,
          content: inscriptionId1,
        }),
      ];
      if (inscriptionId2) {
        promiseList.push(
          wallNftSave({
            position: wallNftListMap[inscriptionId],
            content: inscriptionId2,
          })
        );
      }

      Promise.all(promiseList)
        .then(([res0, res1]) => {
          if (res0.data.code === 1 && (!res1 || (res1 && res1.data.code === 1))) {
            toast.success('Updated');
            getWallNftList();
          } else {
            toast.error('Failed');
          }
        })
        .finally(() => {
          toast.dismiss(loadingToast);
          setIsSetLoading(false);
        });
      return;
    }
    // 点击了选中相同的nft，取消
    if (
      wallNftListMap[inscriptionId] !== 0 &&
      wallNftListMap[inscriptionId] === viewNftStatus.position
    ) {
      wallNftSave({
        position: 0,
        content: inscriptionId,
      })
        .then((res: any) => {
          if (res.data.code === 1) {
            toast.success('Updated');
            getWallNftList();
          } else {
            toast.error(res.data.msg);
          }
        })
        .finally(() => {
          toast.dismiss(loadingToast);
          setIsSetLoading(false);
        });
      return;
    }
    wallNftSave({
      position: viewNftStatus.position,
      content: inscriptionId,
    })
      .then((res: any) => {
        if (res.data.code === 1) {
          toast.success('Updated');
          getWallNftList();
        } else {
          toast.error(res.data.msg);
        }
      })
      .finally(() => {
        toast.dismiss(loadingToast);
        setIsSetLoading(false);
      });
  };

  const isRight: boolean = useMemo(() => {
    return viewNftStatus !== null && viewNftStatus.left && viewNftStatus.position !== 0;
  }, [viewNftStatus]);

  const isShowMenu = useMemo(() => {
    return !!viewNftStatus && viewNftStatus.open && viewNftStatus.position !== 0;
  }, [viewNftStatus]);
  const showNftList: string[] = useMemo(() => {
    if (viewNftStatus && viewNftStatus.position !== 0) {
      const checkedNft: IWallNft | undefined = wallNftList.find(
        (i) => i.position === viewNftStatus.position
      );
      if (checkedNft) {
        for (let i = 0; i < nftList.length; i++) {
          if (nftList[i].includes(checkedNft.content)) {
            const nftList_ = JSON.parse(JSON.stringify(nftList));
            // 将当前项提到数组最前面
            nftList_.splice(i, 1);
            nftList_.unshift(nftList[i]);
            return nftList_;
          }
        }
      }
    }
    return nftList;
  }, [nftList, viewNftStatus, wallNftList]);

  return (
    <NftMenuView pd={nftList.length > 4} isRight={isRight} isShowMenu={isShowMenu}>
      <div className="nft-menu-title">
        <span>NFT list</span>
        <img
          src={XSvg.src}
          alt=""
          onClick={() => {
            onClose();
            GlobalSpaceEvent.SetDataValue<number>(GlobalDataKey.LookingNftIndex, 0);
          }}
        />
      </div>
      <div className="nft-menu-list">
        {!btcAddress || (!loadLoading && showNftList.length === 0) ? (
          <div className="empty-box">
            <SvgComponent svg={SVG_FILE.menuEmptyIcon} />
            <p>No Assets for now</p>
          </div>
        ) : (
          <div className="nft-menu-list-box">
            {showNftList.map((inscriptionId, index) => {
              const isChecked =
                !!viewNftStatus && wallNftListMap[inscriptionId] === viewNftStatus.position;
              const isUsed =
                !!viewNftStatus &&
                wallNftListMap[inscriptionId] !== undefined &&
                wallNftListMap[inscriptionId] !== 0;
              return (
                <NftImage
                  key={inscriptionId}
                  inscriptionId={inscriptionId}
                  onSetNft={onSetNft}
                  wallNftListMap={wallNftListMap}
                  isChecked={isChecked}
                  isUsed={isUsed}
                />
              );
            })}
          </div>
        )}
        <ScrollLoad
          getData={() => {
            setPage((page_) => page_ + 1);
          }}
          loadLoading={!!btcAddress && loadLoading}
        />
      </div>
    </NftMenuView>
  );
}

function NftImage({
  inscriptionId,
  onSetNft,
  wallNftListMap,
  isChecked,
  isUsed,
}: {
  inscriptionId: string;
  onSetNft: Function;
  wallNftListMap: {
    [key: string]: number;
  };
  isChecked: boolean;
  isUsed: boolean;
}) {
  const [loadStatus, setLoadStatus] = useState<'loading' | 'error' | 'done'>('done');
  // useEffect(() => {
  //   const img = new Image()
  //   img.onload = () => setLoadStatus('done')
  //   img.onerror = () => setLoadStatus('error')
  //   img.src = getServerLink(inscriptionId)
  // }, [inscriptionId])
  return (
    <div
      className={classNames('nft-menu-item', isChecked && 'active')}
      onClick={() => onSetNft(inscriptionId)}
    >
      {isUsed && <span className="used">USED</span>}
      {loadStatus === 'loading' ? (
        'Loading...'
      ) : loadStatus === 'error' ? (
        'Error'
      ) : (
        <img src={getNFTImgLink(inscriptionId)} alt="" />
      )}
    </div>
  );
}
