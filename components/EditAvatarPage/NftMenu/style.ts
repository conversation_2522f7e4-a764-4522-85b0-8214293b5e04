import styled from 'styled-components';
import { THEME_MEDIA_ENUM } from '../../../AvatarOrdinalsBrowser/constant';

export const NftMenuView = styled.div<{ pd: boolean; isRight: boolean; isShowMenu: boolean }>`
  position: fixed;
  width: 18rem;
  height: 38.5rem;
  max-height: calc(100% - 13.125rem);
  background: #fafafa99;
  top: 50%;
  left: 0;
  border-top-right-radius: 2rem;
  border-bottom-right-radius: 2rem;
  transform: translate(0, -50%);
  z-index: 3;
  backdrop-filter: blur(0.25rem);
  padding: ${({ pd }) => (pd ? '1rem 0.5rem 1rem 1rem' : '1rem')};
  box-sizing: border-box;
  display: ${({ isShowMenu }) => (isShowMenu ? 'flex' : 'none')};
  flex-direction: column;
  gap: 1rem;

  .nft-menu-title {
    border-radius: 0.75rem;
    background: #ff7600;
    box-shadow: 0rem 0.25rem 0.25rem 0rem #00000040 inset;
    padding: 0.625rem 1rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > span {
      font-family: 'JetBrains Mono';
      font-size: 1.25rem;
      line-height: 1.25rem;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #140f08;
    }
    & > img {
      width: 1.5rem;
      height: 1.5rem;
      cursor: pointer;
    }
  }

  .nft-menu-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: ${({ pd }) => (pd ? '0.5rem' : '0')};
    box-sizing: border-box;
    &::-webkit-scrollbar {
      width: 0.375rem; /* 垂直滚动条宽度 */
      height: 0.375rem; /* 水平滚动条高度 */
    }

    /* 设置滚动条的轨道 */

    &::-webkit-scrollbar-track {
      background: #f1f1f1; /* 轨道颜色 */
      border-radius: 0.375rem;
      transform: translateX(1.25rem);
    }

    /* 设置滚动条的滑块 */

    &::-webkit-scrollbar-thumb {
      background: #5e5e5e; /* 滑块颜色 */
      border-radius: 0.375rem;
    }

    .nft-menu-list-box {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-row-gap: 1rem;
      grid-column-gap: 1rem;

      & > .nft-menu-item {
        width: 7.5rem;
        height: 7.5rem;
        border-radius: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'JetBrains Mono';
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.21rem;
        text-align: center;
        color: #615a57;
        position: relative;
        overflow: hidden;

        &.active {
          border: 0.25rem solid #ff8316;
          box-sizing: border-box;
        }

        & > img {
          max-width: 100%;
          max-height: 100%;
        }
        & > .used {
          position: absolute;
          right: 0.4375rem;
          top: 0.5rem;
          padding: 0.21875rem 0.53125rem;
          background: #ff8316;
          border: 0.0625rem solid #140f08;
          border-radius: 1rem;
          font-family: 'JetBrains Mono';
          font-size: 0.625rem;
          font-weight: 700;
          line-height: 0.825rem;
          letter-spacing: -0.05em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #140f08;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;
        }
      }
    }

    .empty-box {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: -2rem;

      & > p {
        font-family: 'JetBrains Mono';
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.21rem;
        text-align: center;
        color: #615a57;
        margin: 0.5rem 0 0 0;
      }
    }
  }

  ${THEME_MEDIA_ENUM.FRONTEND_LARGE} {
    left: ${({ isRight }) => (isRight ? 'auto' : '25%')};
    right: ${({ isRight }) => (isRight ? '25%' : 'auto')};
    top: 50%;
    border-radius: 2rem;
    transform: ${({ isRight }) =>
      isRight ? 'translate(50%, -50%)' : 'translate(-50%, -50%)'} !important;
  }
`;
