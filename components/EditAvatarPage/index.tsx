import { FrontendPageView } from './style';
// import Header from '../Header';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  claimPotato,
  getAvatar,
  getBasicSummaryData,
  getCustomCollectionById,
  getNftListWallByAddress,
  getUnusableInfo,
  getUsableAddress,
  saveAvatar,
} from '../../server';
import { useDispatch, useSelector } from 'react-redux';
import {
  IAppState,
  IBasicSummaryData,
  ICollection,
  IWallNft,
  SCENE_TYPE,
  STORAGE_MENU_ENUM,
} from '../../constant/type';

import SubmitChain, { SubmitChainIProps } from './SubmitChain';

import NftMenu from './NftMenu';
import StorageMenu from './StoreMenu';
import { getNFTImgLink } from '../../utils';
import GameWindow from '../../components/GameWindow';
import GlobalSpaceEvent, { GlobalDataKey, SpaceStatus } from '../../world/Global/GlobalSpaceEvent';
import { IAvatarMetadata, UsableAddressDataType } from '../../AvatarOrdinalsBrowser/constant/type';
import { ENABLE_BASIC_SUMMARY_NFT, IS_PRO_TEMPORARY_VERSION } from '../../constant';
import { setPageLoadingRate, setSceneType, setStorageMenu, setUnusableInfo } from '../../store/app';
import toast from 'react-hot-toast';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';

interface GetDefaultInscriptionIdParams {
  isProTemporaryVersion: boolean;
  isLoading: boolean;
  collectionData: ICollection | null;
  defaultInscriptionId: string;
}

// 获取要显示的默认铭文ID
const getDefaultInscriptionId = (params: GetDefaultInscriptionIdParams): string => {
  // 如果是临时版本或正在加载，返回空字符串
  if (params.isProTemporaryVersion || params.isLoading) {
    return '';
  }

  // 如果有集合数据，使用集合的铭文ID
  if (params.collectionData) {
    return params.collectionData.inscriptionId;
  }

  // 否则使用默认铭文ID
  return params.defaultInscriptionId;
};

export default function FrontendPage() {
  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  // 从 Redux store 获取状态
  const { btcAddress, defaultInscriptionId, potatoTime, sceneType, storageMenu, isRecording } =
    useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  // 组件自身状态
  const [collectionData, setCollectionData] = useState<ICollection | null>(null);
  const [loadLoading, setLoadLoading] = useState<boolean>(true);
  const [basicSummaryData, setBasicSummaryData] = useState<IBasicSummaryData | null>(null);
  const [wallNftList, setWallNftList] = useState<IWallNft[]>([]);
  // 当前nft预览状态
  const [viewNftStatus, setViewNftStatus] = useState<{
    left: boolean;
    open: boolean;
    position: number;
  } | null>(null);
  const [wallListLoading, setWallListLoading] = useState<boolean>(false);
  ///获取临时装扮
  const [tempAvatar, setTempAvatar] = useState<IAvatarMetadata | undefined>(undefined);

  // 可用装扮地址
  const [usableAddress, setUsableAddress] = useState<UsableAddressDataType>(
    {} as UsableAddressDataType
  );

  const btcAddressRef = useRef('');

  useEffect(() => {
    const collectionId = searchParams.get('collectionId');
    // 如果存在 collectionId 且有 btcAddress，则获取自定义收藏品
    if (collectionId && btcAddress) {
      getCustomCollectionById(collectionId)
        .then(async (res) => {
          if (res.data) {
            setCollectionData(res.data);
            setLoadLoading(false);
          } else {
            setCollectionData(null);
            setLoadLoading(false);
          }
        })
        .catch(() => {
          setLoadLoading(false);
        });
    } else {
      setCollectionData(null);
      setLoadLoading(false);
    }
  }, [searchParams, btcAddress]);

  // 获取墙上的nft列表
  const getWallNftList = () => {
    if (btcAddress) {
      setWallListLoading(true);
      getNftListWallByAddress(btcAddress)
        .then((res) => {
          setWallNftList(res.data.data || []);
        })
        .finally(() => {
          setWallListLoading(false);
        });
    }
  };
  // 查看btc账号下我的临时装扮
  const getTempAvatarData = (btcAddress_: string = btcAddress) => {
    if (!btcAddress_) {
      setTempAvatar({} as IAvatarMetadata);
      return;
    }
    getAvatar(btcAddress_).then((res) => {
      try {
        if (!res.data.data) {
          setTempAvatar({} as IAvatarMetadata);
          return;
        }
        setTempAvatar(res.data.data);
      } catch (e) {}
    });
  };

  // 获取我的可用装扮地址
  const getUsableAddressData = async () => {
    try {
      const res = await getUsableAddress();
      if (res.data.code === 1) {
        setUsableAddress(res.data.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /// 获取btc地址的基础数据
  const getBasicSummaryDataFn = (btcAddress_: string = btcAddress) => {
    if (btcAddress_) {
      // 查看Btc地址基础数据接口
      getBasicSummaryData({ address: btcAddress_ }).then((res: any) => {
        const summaryData: IBasicSummaryData = res.data.data;
        dispatch(setUnusableInfo(summaryData?.unusableInfo ?? null));
        // data.inscriptionResult = data.inscriptionResult.map((i: string) => getServerLink(i))
        setBasicSummaryData(summaryData);
      });
      getWallNftList();
      if (IS_PRO_TEMPORARY_VERSION) {
        getTempAvatarData(btcAddress_);
        getUsableAddressData();
      }
    } else {
      setBasicSummaryData(null);
      setTempAvatar({} as IAvatarMetadata);
      setUsableAddress({} as UsableAddressDataType);
    }
  };

  useEffect(() => {
    getBasicSummaryDataFn();
  }, [btcAddress]);

  // 轮询获取不可用信息
  const getUnusableInfoFn = async () => {
    return;
    const res = await getUnusableInfo();
    if (res.data.code === 1) {
      dispatch(setUnusableInfo(res.data.data));
    }
  };

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    // 如果钱包地址存在(已登录)，开始轮询
    if (btcAddress) {
      // 设置轮询间隔，每10秒查询一次
      intervalId = setInterval(
        () => {
          // getUnusableInfoFn();
          getUsableAddressData();
        },
        1 * 60 * 1000
      ); // 1分钟轮询一次，可以根据需要调整间隔
    }

    // 清除函数，组件卸载或btcAddress变化时执行
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [btcAddress]); // 依赖于btcAddress变化

  // 获取默认ID
  const showDefaultInscriptionId = getDefaultInscriptionId({
    isProTemporaryVersion: IS_PRO_TEMPORARY_VERSION,
    isLoading: loadLoading,
    collectionData: collectionData,
    defaultInscriptionId: defaultInscriptionId,
  });

  // 处理单个 NFT 数据
  const processNFTItem = (nft: IWallNft): IWallNft => ({
    ...nft,
    content: getNFTImgLink(nft.content),
  });

  // 过滤并处理 NFT 列表
  const filterAndProcessNFTList = (nftList: IWallNft[]): IWallNft[] => {
    return nftList
      .filter((nft) => nft.position !== 0) // 过滤掉 position 为 0 的 NFT
      .map(processNFTItem); // 处理每个 NFT 的图片链接
  };

  // 将 inscriptionResult 转换为 IWallNft[] 类型
  const convertInscriptionToNFT = (inscriptions: string[]): IWallNft[] => {
    return inscriptions.map((inscription, index) => ({
      id: index + 1, // 确保 id 是 number 类型
      position: 1, // 确保 position 是 number 类型
      content: inscription,
    }));
  };

  const getNFTList = (
    wallNftList: IWallNft[],
    basicSummaryData: IBasicSummaryData | null
  ): IWallNft[] => {
    // 如果 wallNftList 为空且有 basicSummaryData，使用 basicSummaryData 的数据
    if (wallNftList.length === 0 && basicSummaryData && ENABLE_BASIC_SUMMARY_NFT) {
      const res = convertInscriptionToNFT(basicSummaryData.inscriptionResult || []);
      return res;
    }
    // 否则使用 wallNftList
    return wallNftList;
  };

  const showWallNftList: IWallNft[] = useMemo(() => {
    const nftList = getNFTList(wallNftList, basicSummaryData);
    return filterAndProcessNFTList(nftList);
  }, [basicSummaryData, wallNftList]);

  // useEffect(() => {
  //   if (sceneType === SCENE_TYPE.Room && storageMenu === null) {
  //     dispatch(setStorageMenu(STORAGE_MENU_ENUM.PLAY_MENU));
  //   }
  // }, [sceneType]);

  useEffect(() => {
    const oldViewNftStatus = {
      open: false, // 是否打开 NFT 查看
      left: false, // 是否在左侧查看
      position: 0, // 当前查看的 NFT 位置
    };
    // 查看空间状态变化
    const viewNftStatusListener = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        // 根据不同的空间状态设置不同的菜单
        switch (status) {
          case SpaceStatus.Avatar:
            // dispatch(setStorageMenu(STORAGE_MENU_ENUM.PATH_MENU));
            break;
          case SpaceStatus.NFT:
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.NFT_MENU));
            break;
          case SpaceStatus.Game:
            dispatch(setStorageMenu(STORAGE_MENU_ENUM.PLAY_MENU));
            break;
          default:
            break;
        }
        // 更新 NFT 查看状态
        oldViewNftStatus.open = status == SpaceStatus.NFT;
        setViewNftStatus({
          open: oldViewNftStatus.open,
          left: oldViewNftStatus.left,
          position: oldViewNftStatus.position,
        });
      }
    );
    // 监听 NFT 查看左侧状态变化
    const viewNftLeftListener = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.LookingNftLeft,
      (left) => {
        oldViewNftStatus.left = left;
        setViewNftStatus({
          open: oldViewNftStatus.open,
          left: oldViewNftStatus.left,
          position: oldViewNftStatus.position,
        });
      }
    );
    // 监听当前查看的 NFT 位置变化
    const viewNftPositionListener = GlobalSpaceEvent.ListenKeyDataChange<number>(
      GlobalDataKey.LookingNftIndex,
      (position) => {
        oldViewNftStatus.position = position;
        setViewNftStatus({
          open: oldViewNftStatus.open,
          left: oldViewNftStatus.left,
          position: oldViewNftStatus.position,
        });
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, viewNftStatusListener);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.LookingNftLeft, viewNftLeftListener);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.LookingNftIndex, viewNftPositionListener);
    };
  }, [dispatch]);

  useEffect(() => {
    switch (storageMenu) {
      case STORAGE_MENU_ENUM.PATH_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Avatar);
        break;
      case STORAGE_MENU_ENUM.NFT_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.NFT);
        break;
      case STORAGE_MENU_ENUM.PLAY_MENU:
        GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game);
        break;
      default:
        break;
    }
  }, [storageMenu]);

  // 监听场景加载
  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (SceneLoading: boolean) => {
        dispatch(setPageLoadingRate(SceneLoading ? 0 : 100));
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, unsubscribe);
    };
  }, []);

  useEffect(() => {
    btcAddressRef.current = btcAddress;
  }, [btcAddress]);

  // 领取成熟土豆
  const onClaimPotato = () => {
    claimPotato().then((res) => {
      if (res.data.code === 1) {
        toast.success('Claim Success');
        GlobalSpaceEvent.SetDataValue<boolean>(GlobalDataKey.ShowIslandPotato, false);
        // 手动保存一下土豆装扮
        const tempAvatar_ = tempAvatar ? JSON.parse(JSON.stringify(tempAvatar)) : {};
        // TODO 下一个升级铭文版本需要改成动态取接口数据
        tempAvatar_.petId = '74f1c602036fc449a2323665ac88e922082e25149863f0fb0c97f83dec04b386i0';
        // saveTempAvatar(JSON.stringify(tempAvatar_))
        saveAvatar({ content: tempAvatar_ })
          .then((res: any) => {
            if (res.data.code === 1) {
              getTempAvatarData(btcAddressRef.current);
            }
          })
          .finally(() => {
            getBasicSummaryDataFn(btcAddressRef.current);
          });
      } else {
        toast.error(res.data.msg || res.data.message[0], { duration: 6000 });
      }
    });
  };
  // 场景类型回调
  const screenTypeCallback = (screenType: SCENE_TYPE) => {
    dispatch(setSceneType(screenType));
  };

  // 是否可以领取成熟土豆
  // const isClaimPotato = basicSummaryData
  //   ? basicSummaryData.petInfo.claim
  //   : true;

  // 是否可以领取成熟土豆
  const isClaimPotato = useMemo(() => {
    if (basicSummaryData) {
      return basicSummaryData.petInfo.claim;
    }
    return true;
  }, [basicSummaryData]);

  useEffect(() => {
    const myPlayer = GetMyPlayer();
    myPlayer.setAppApi(AppGameApiKey.setMetaData, (metaData: IAvatarMetadata) => {
      setTempAvatar(metaData);
    });
  }, [btcAddress]);

  return (
    <FrontendPageView>
      {/* <Header isFixed={true} isClaimPotato={isClaimPotato} /> */}
      <GameWindow
        potatoTime={potatoTime}
        onClaimPotato={onClaimPotato}
        collectionData={collectionData}
        defaultAvatarMetadata={tempAvatar}
        usableAddressData={usableAddress}
        defaultInscriptionId={showDefaultInscriptionId}
        submitChainSlot={(props: SubmitChainIProps) => (
          <SubmitChain {...props} getTempAvatarData={getTempAvatarData} />
        )}
        // deleteCollectionSlot={(props: DeleteCollectionIProps) => <DeleteCollection {...props}/>}
        isFrontend={true}
        basicSummaryData={basicSummaryData}
        showWallNftList={showWallNftList}
        doorKey={{
          hasAresKey: true,
          aresKeyUnlocksTime: 1734068906,
          openCallback: () => undefined,
        }}
        isDisabledPet={basicSummaryData ? !basicSummaryData.petInfo.claim : true}
        screenTypeCallback={screenTypeCallback}
        butlerData={null}
        isVisitor={false}
        hiddenChangeScreen={isRecording || storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU}
      />
      <StorageMenu basicSummaryData={basicSummaryData} />
      {storageMenu === STORAGE_MENU_ENUM.NFT_MENU && (
        <NftMenu
          wallNftList={wallNftList}
          viewNftStatus={viewNftStatus}
          getWallNftList={getWallNftList}
          wallListLoading={wallListLoading}
          onClose={() => undefined}
        />
      )}
    </FrontendPageView>
  );
}
