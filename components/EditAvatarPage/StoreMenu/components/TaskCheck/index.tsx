// 创建一个独立的CheckButton组件

import { useEffect, useRef, useState } from 'react';

interface CheckButtonProps {
  taskId: string;
  clientRefresh?: string[];
  onCheck: (taskId: string, clientRefresh?: string[]) => void;
  isLoading?: boolean;
}

const CheckButton: React.FC<CheckButtonProps> = ({
  taskId,
  clientRefresh,
  onCheck,
  isLoading = false,
}) => {
  // 状态管理
  const [cooldownEndTime, setCooldownEndTime] = useState<number | null>(null);
  const [countdown, setCountdown] = useState<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 检查是否在冷却中
  const isInCooldown = cooldownEndTime !== null && Date.now() < cooldownEndTime;

  // 处理点击事件
  const handleClick = () => {
    if (isInCooldown || isLoading) return;

    // 设置20秒冷却
    const endTime = Date.now() + 20000;
    setCooldownEndTime(endTime);
    setCountdown(20);

    // 启动倒计时
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      const remaining = Math.ceil((endTime - Date.now()) / 1000);

      if (remaining <= 0) {
        // 倒计时结束
        clearInterval(timerRef.current!);
        setCooldownEndTime(null);
        setCountdown(0);
      } else {
        // 更新倒计时
        setCountdown(remaining);
      }
    }, 1000);

    // 调用父组件提供的回调
    onCheck(taskId, clientRefresh);
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  return (
    <span
      className="check-text"
      onClick={handleClick}
      style={{
        color: isLoading || isInCooldown ? '#AF8E79' : '#FF7A27',
        pointerEvents: isLoading || isInCooldown ? 'none' : 'auto',
        cursor: isInCooldown ? 'not-allowed' : 'pointer',
        display: 'inline',
      }}
    >
      {isInCooldown ? `${countdown}s` : 'Check'}
    </span>
  );
};

export default CheckButton;
