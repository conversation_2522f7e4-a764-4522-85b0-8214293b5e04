import styled, { css } from 'styled-components';

export const TaskMissionContainer = styled.div<{ $isMobile: boolean }>`
  // 定位在左上角
  position: fixed;
  top: 15%;
  left: 1.875rem;
  z-index: 100;
  // 边框为白色
  display: flex;
  flex-direction: column;
  height: 2rem;
  box-sizing: border-box;
  ${({ $isMobile }) =>
    $isMobile &&
    css`
      left: 2.3474178404%;
      top: 15.1898734177%;
    `}
  .title {
    display: flex;
    /* align-items: center; */
    gap: 0.625rem;
    background: #140f08;
    color: #fff;
    border-radius: 3.125rem;
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    min-width: 18.75rem;

    .icon {
      width: 2.25rem;
      height: 2.5rem;
      position: absolute;
      left: -0.125rem;
      top: -0.75rem;
    }
    .text {
      text-shadow: 0 0 black;
      font-weight: 900;
      margin: 0;
      flex: 1;
      padding-left: 2.5rem;
    }
  }

  // 所有任务完成的样式
  .all-completed {
    display: flex;
    padding: 0.625rem 2.5rem;
    color: #fff051;

    .dot-complete {
      font-size: 1rem;
      font-weight: bold;
      display: flex;
      align-items: center;
      text-underline-offset: 0.25rem;
      cursor: pointer;
      /* 下划线 */
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    padding: 0;
    margin: 0;
    width: 100%;
    color: #fff;
    /* padding: 0.625rem; */
    padding: 0.625rem 1.875rem;
    box-sizing: border-box;
    color: #fff;
    .content-item {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 0.625rem;
      /* justify-content: space-between; */

      .dot-incomplete {
        width: 0.5rem;
        height: 0.5rem;
        min-width: 0.5rem;
        background: #fff;
        border-radius: 50%;
      }

      .dot-complete {
        color: #e3e3e3;
        opacity: 0.5;
      }

      &.completed {
        color: #e3e3e3; // Gray text for completed tasks
        opacity: 0.5;
      }

      .check-text {
        color: #fff051;
        font-size: 1rem;
        font-weight: 900;
        text-underline-offset: 0.25rem;
        cursor: pointer;
        text-decoration: underline;
      }
    }

    .confirm {
      color: #fff051;
      text-underline-offset: 0.25rem;
      cursor: pointer;
      font-weight: 900;
      text-decoration: underline;
    }
  }
`;
