const Time = ({ timeRemaining }: { timeRemaining: string }) => {
  return (
    <div className="time-container">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_5186_19525"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="20"
          height="20"
        >
          <circle cx="10" cy="10" r="9.375" fill="#D9D9D9" />
        </mask>
        <g mask="url(#mask0_5186_19525)">
          <circle cx="10" cy="10" r="9.0625" fill="#CA5500" stroke="#140F08" strokeWidth="0.625" />
          <circle
            cx="9.375"
            cy="9.375"
            r="8.625"
            fill="#FFCC22"
            stroke="#140F08"
            strokeWidth="0.25"
          />
          <g filter="url(#filter0_i_5186_19525)">
            <circle cx="9.375" cy="9.375" r="6.875" fill="#EDE1CC" />
          </g>
          <circle cx="9.375" cy="9.375" r="6.75" stroke="#140F08" strokeWidth="0.25" />
          <circle cx="10" cy="10" r="9.0625" stroke="#140F08" strokeWidth="0.625" />
          <path
            d="M10.398 5.02269C10.5549 4.73235 10.9106 4.61408 11.2101 4.7526L11.2788 4.78436C11.5784 4.92289 11.7186 5.27045 11.5991 5.57807L9.99685 9.70025L8.29499 8.91329L10.398 5.02269Z"
            fill="#47351B"
          />
          <path
            d="M5.59318 9.80952C5.27305 9.75444 5.04848 9.46331 5.07648 9.13969L5.08518 9.03917C5.11319 8.71555 5.38444 8.46732 5.70928 8.46806L8.85686 8.47522L8.6952 10.3432L5.59318 9.80952Z"
            fill="#47351B"
          />
          <circle
            cx="8.75"
            cy="9.375"
            r="1.125"
            fill="#FF8316"
            stroke="#140F08"
            strokeWidth="0.25"
          />
        </g>
        <defs>
          <filter
            id="filter0_i_5186_19525"
            x="2.5"
            y="2.5"
            width="13.75"
            height="13.75"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dx="0.625" dy="0.625" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_5186_19525" />
          </filter>
        </defs>
      </svg>

      <div className="time">{timeRemaining}</div>
    </div>
  );
};

export default Time;
