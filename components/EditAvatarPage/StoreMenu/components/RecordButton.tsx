import { IAppState, SCENE_TYPE, STORAGE_MENU_ENUM } from '@/constant/type';
import { setStorageMenu } from '@/store/app';
import { forwardRef, Ref, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { DOMAIN_JUMP } from '@/constant';
import { IconContainer, MenuItemSvgWrapper, StorageMenuItem } from '../style';

interface RecordButtonProps {}

interface RecordButtonRef {}

const RecordSvgIcon = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.1699 25.1845L17.195 23.3022C17.2022 23.2563 17.2111 23.2103 17.2218 23.1643L19.5768 13.0165L23.7652 11.8942L23.7559 11.9366L21.3785 22.1812L30.1759 19.8239L30.1843 19.7871L32.6225 9.52092L36.8228 8.39544L36.816 8.4255L34.3761 18.6985L46.2786 15.5093L44.6591 9.46517C44.2213 7.83199 42.5427 6.86278 40.9094 7.30039L10.7152 15.3909C9.08198 15.8285 8.11278 17.5072 8.55039 19.1404L10.1699 25.1845ZM10.1741 25.1846H47.5564V45.7415C47.5564 47.4321 46.1858 48.803 44.495 48.803H13.2356C11.5448 48.803 10.1741 47.4321 10.1741 45.7415V25.1846ZM16.8299 40.5015C16.8299 39.445 17.6866 38.5881 18.7434 38.5881H22.9208C23.9775 38.5881 24.8342 39.445 24.8342 40.5015C24.8342 41.5583 23.9775 42.415 22.9208 42.415H18.7434C17.6866 42.415 16.8299 41.5583 16.8299 40.5015Z"
          fill="white"
        />
      </svg>
    </MenuItemSvgWrapper>
  );
};

const RecordButton = forwardRef((props: RecordButtonProps, ref: Ref<RecordButtonRef>) => {
  const { storageMenu, domainOwner, isRecording, btcAddress, isTtsWhiteList, sceneType } =
    useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const dispatch = useDispatch();
  const [isUserDomain, setIsUserDomain] = useState<boolean>(false);

  /**
   * 判断是否是"localhost" | ".uniworlds"域名
   * 判断是否是白名单
   * 判断是否在社区，如果在社区则不显示 sceneType SCENE_TYPE.Community比较
   */
  useEffect(() => {
    // 判断是否用户的域名
    const isDomain = window.location.href.includes(DOMAIN_JUMP);
    const isLocalhost = window.location.href.includes('http://localhost');
    const isTtsWhiteList = window.localStorage.getItem('isTtsWhiteList');
    if (isDomain || isLocalhost || isTtsWhiteList) {
      setIsUserDomain(true);
    }
  }, []);

  if (!isUserDomain || sceneType === SCENE_TYPE.Community) {
    return null;
  }

  return (
    <StorageMenuItem
      className={storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU ? 'active' : ''}
      onClick={() => {
        dispatch(
          setStorageMenu(
            storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU
              ? null
              : STORAGE_MENU_ENUM.RECORDING_MENU
          )
        );
      }}>
      <IconContainer>
        <RecordSvgIcon />
        {/* <img src={ReloadingMenuSvg.src} className="default-icon" alt="Recording Menu" /> */}
      </IconContainer>
    </StorageMenuItem>
  );
});

RecordButton.displayName = 'RecordButton';

export default RecordButton;
