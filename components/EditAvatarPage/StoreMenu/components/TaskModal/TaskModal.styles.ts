import { motion } from 'motion/react';
import styled from 'styled-components';

export const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  border: 0.125rem solid #ff8316;
  background: #fff2e2;
  border-radius: 1.25rem;
  box-sizing: border-box;
  position: relative;
  max-width: 57.5rem;
  min-height: 31.25rem;
  width: 100%;
  padding: 0.625rem;
  z-index: 100;
  .close-btn {
    position: absolute;
    cursor: pointer;
    top: -1.125rem;
    right: 2.5rem;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -9%;
    transform: translate(-50%, 10%);
  }
  .task-content {
    width: 100%;
    height: 29.375rem;
    display: flex;
    overflow-y: auto;
    box-sizing: border-box;
    padding-top: 1.875rem;
  }
`;
export const ModalWrapper = styled.div`
  position: relative;
  height: 100%;
`;
