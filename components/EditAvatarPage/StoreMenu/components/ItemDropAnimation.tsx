import { useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

interface ItemDropAnimationProps {
  itemImage: string;
  onComplete: () => void;
}

const ItemDropAnimation = ({ itemImage, onComplete }: ItemDropAnimationProps) => {
  useEffect(() => {
    // 完成动画后通知父组件
    const timeout = setTimeout(() => {
      onComplete();
    }, 1000);

    return () => clearTimeout(timeout);
  }, [onComplete]);

  return (
    <AnimatePresence>
      <motion.div
        style={{
          position: 'absolute',
          top: '-9.375rem',
          left: '50%',
          zIndex: 10,
          width: '3.75rem',
          height: '3.75rem',
        }}
        initial={{ y: '0rem', x: '-50%', scale: 1, opacity: 1, rotate: 0 }}
        animate={{ y: '9.375rem', x: '-50%', scale: 0.5, opacity: 0, rotate: 360 }}
        transition={{
          duration: 3,
          ease: [0.175, 0.885, 0.32, 1.275],
          // opacity: { delay: 0.7, duration: 0.2 },
          rotate: {
            duration: 1.5,
            repeat: 1,
          },
        }}>
        <img src={itemImage} alt="Collected item" style={{ width: '100%', height: '100%' }} />
      </motion.div>
    </AnimatePresence>
  );
};

export default ItemDropAnimation;
