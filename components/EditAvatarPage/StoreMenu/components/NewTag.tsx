import { motion } from 'motion/react';

const NewTag = () => {
  return (
    <motion.div
      style={{
        position: 'absolute',
        top: '-0.75rem',
        right: '-1.125rem',
        backgroundColor: '#FFCC00',
        color: 'white',
        fontSize: '1rem',
        fontWeight: 'bold',
        borderRadius: '3.125rem',
        zIndex: 20,
        border: '0.0625rem solid #000000',
        letterSpacing: '0.03125rem',
        width: '3rem',
        height: '1.875rem',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        transformOrigin: 'center center',
        boxShadow: '0rem 0.25rem 0.25rem 0rem rgba(0, 0, 0, 0.25)',
      }}
      initial={{
        rotate: 30,
        scale: 1,
      }}
      animate={{
        rotate: 30, // 保持30度倾斜
        scale: [1, 1.05, 1], // 只缩放不改变旋转角度
      }}
      transition={{
        duration: 0.8,
        repeat: Infinity,
        repeatType: 'loop',
        ease: 'easeInOut',
      }}
    >
      NEW
    </motion.div>
  );
};

export default NewTag;
