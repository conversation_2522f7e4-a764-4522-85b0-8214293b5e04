import { motion } from 'motion/react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';
import { useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import NewTag from './NewTag';
import TaskModal, { TaskModalRef } from './TaskModal';
import { useTaskContext } from '@/contexts/TaskContext';
import { IconContainer, MenuItemSvgWrapper, StorageMenuItem, StyledKeyTag } from '../style';

interface TaskButtonProps {
  onTaskMenuToggle?: (isOpen: boolean) => void;
}

const TaskSvg = () => {
  return (
    <MenuItemSvgWrapper>
      <svg
        width="56"
        height="56"
        viewBox="0 0 56 56"
        fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path
          d="M31.9258 7.21875C33.2016 7.21884 34.4268 7.71734 35.3398 8.6084L44.8604 17.9004C45.803 18.8203 46.3349 20.0813 46.335 21.3984V43.8848C46.335 46.5848 44.1463 48.7734 41.4463 48.7734H14.5576C11.8576 48.7734 9.66797 46.5848 9.66797 43.8848V12.1074C9.66797 9.40736 11.8576 7.21875 14.5576 7.21875H31.9258ZM24.2393 33.8809C23.4401 33.2596 22.2875 33.4031 21.666 34.2021L18.5293 38.2344L16.9258 36.9521L16.7734 36.8428C15.9929 36.3387 14.9416 36.4972 14.3486 37.2383C13.7559 37.9792 13.8316 39.0395 14.4941 39.6904L14.6348 39.8154L17.6904 42.2598C18.0734 42.5662 18.5637 42.7058 19.0508 42.6484C19.5379 42.5909 19.982 42.3412 20.2832 41.9541L24.5605 36.4541C25.1821 35.6549 25.0385 34.5025 24.2393 33.8809ZM26.7803 37.7734V41.4404H40.2256V37.7734H26.7803ZM24.2393 21.6611C23.44 21.0396 22.2876 21.1833 21.666 21.9824L18.5293 26.0146L16.9258 24.7324L16.7734 24.623C15.9929 24.1188 14.9417 24.2773 14.3486 25.0186C13.7561 25.7596 13.8314 26.8199 14.4941 27.4707L14.6348 27.5957L17.6904 30.04C18.0734 30.3464 18.5637 30.4861 19.0508 30.4287C19.5378 30.3712 19.982 30.1213 20.2832 29.7344L24.5605 24.2344C25.1822 23.4351 25.0385 22.2828 24.2393 21.6611ZM26.7803 29.2197H40.2256V25.5527H26.7803V29.2197Z"
          fill="white"
        />
      </svg>
    </MenuItemSvgWrapper>
  );
};

const TaskButton = ({ onTaskMenuToggle }: TaskButtonProps) => {
  const [isTaskMenuOpen, setIsTaskMenuOpen] = useState(false);
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const taskModalRef = useRef<TaskModalRef>(null);
  const { hasAnyNewTask } = useTaskContext();
  // 监听键盘T键
  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(['T', 't'], (event: KeyboardEvent) => {
      setIsTaskMenuOpen((prev) => {
        const newState = !prev;
        onTaskMenuToggle?.(newState);
        return newState;
      });
    });
    return () => {
      cancel();
    };
  }, [onTaskMenuToggle]);

  // 当isTaskMenuOpen状态变化时，控制TaskModal的显示
  useEffect(() => {
    if (isTaskMenuOpen) {
      taskModalRef.current?.open();
    } else {
      taskModalRef.current?.close();
    }
  }, [isTaskMenuOpen]);

  const isKeyDown = useMemo(() => {
    return isTaskMenuOpen;
  }, [isTaskMenuOpen]);

  if (!btcAddress) {
    return null;
  }

  const toggleTaskMenu = () => {
    setIsTaskMenuOpen((prev) => {
      const newState = !prev;
      onTaskMenuToggle?.(newState);
      return newState;
    });
  };

  const hasNewTask = hasAnyNewTask();

  return (
    <>
      <StorageMenuItem
        style={{ position: 'relative' }}
        className={isTaskMenuOpen ? 'active' : ''}
        onClick={toggleTaskMenu}>
        <IconContainer whileHover="hover" initial="initial">
          {/* New 标签 - 添加抖动动画 */}
          {hasNewTask && <NewTag />}

          <TaskSvg />
          <StyledKeyTag>T</StyledKeyTag>
        </IconContainer>
      </StorageMenuItem>
      <TaskModal ref={taskModalRef} onClose={() => setIsTaskMenuOpen(false)} />
    </>
  );
};

export default TaskButton;
