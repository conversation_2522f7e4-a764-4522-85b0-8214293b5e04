import React, { useEffect, useMemo, useRef, useState } from 'react';

import * as THREE from 'three';
import { AppGameApiKey, GetMyPlayer } from '@/world/Character/MyPlayer';
import { DoorConfig } from '@/world/Config/DoorConfig';
import GlobalSpaceEvent, {
  CharacterType,
  GlobalDataKey,
  SpaceStatus,
} from '@/world/Global/GlobalSpaceEvent';
import { SCENE_TYPE, STORAGE_MENU_ENUM } from '@/constant/type';
import { IconContainer, StorageMenuItem, StyledKeyTag } from '../style';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { setSceneType } from '@/store/app';
import styled, { css } from 'styled-components';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';
import { useAnimate } from 'motion/react';

const ChangeSceneButton = (props: any) => {
  const [isGame, setIsGame] = useState(false);
  const myPlayer = GetMyPlayer();
  const { sceneType, isRecording, storageMenu, isMobile } = useAppSelector(
    (state) => state.AppReducer
  );
  const dispatch = useAppDispatch();

  useEffect(() => {
    const unsubscribe1 = GlobalSpaceEvent.ListenKeyDataChange<SCENE_TYPE>(
      GlobalDataKey.SceneType,
      (sceneType: SCENE_TYPE) => {
        dispatch(setSceneType(sceneType));
      }
    );
    const unsubscribe2 = GlobalSpaceEvent.ListenKeyDataChange<SpaceStatus>(
      GlobalDataKey.SpaceStatus,
      (status) => {
        setIsGame(status == SpaceStatus.Game);
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneType, unsubscribe1);
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, unsubscribe2);
    };
  }, []);

  // 监听键盘T键

  const showScene: null | { img: string; text: string } = useMemo(() => {
    switch (sceneType) {
      case SCENE_TYPE.Island:
        return {
          img: '/image/storage-menu/homeland.png',
          text: 'Cabin',
        };
      case SCENE_TYPE.Room:
        return {
          img: '/image/storage-menu/cabin.png',
          text: 'Homeland',
        };
      case SCENE_TYPE.Community:
        return {
          img: '/image/storage-menu/cabin.png',
          text: 'Homeland',
        };
      default:
        return null;
    }
  }, [sceneType]);

  const switchScene = () => {
    let targetDoorId = 1001;
    if (sceneType === SCENE_TYPE.Island) {
      targetDoorId = 2001;
    } else if (sceneType === SCENE_TYPE.Community) {
      targetDoorId = 3001;
    }
    DoorConfig.getInstance().getData(targetDoorId, (doorData) => {
      myPlayer.callAppApi(AppGameApiKey.setLoaderType, doorData.loadingType);
      const targetPosition = new THREE.Vector3(
        doorData.targetPos[0],
        doorData.targetPos[1],
        doorData.targetPos[2]
      );
      const cameraPosition = new THREE.Vector3(
        doorData.targetCamPos[0],
        doorData.targetCamPos[1],
        doorData.targetCamPos[2]
      );
      const cameraDirection = cameraPosition.clone().sub(targetPosition).normalize();
      GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
        characterType: CharacterType.Player,
        position: targetPosition,
        sceneType: doorData.targetMapId,
        camDirection: cameraDirection,
      });
    });
  };

  const latestSwitchSceneFnRef = useRef(switchScene);

  latestSwitchSceneFnRef.current = switchScene;

  useEffect(() => {
    const cancel = KeyPressUtil.registerKeyPress(['H', 'h'], (event: KeyboardEvent) => {
      latestSwitchSceneFnRef.current?.();
    });
    return () => {
      cancel();
    };
  }, []);
  const [itemRef, itemAnimate] = useAnimate();

  const hiddenChangeScreen = useMemo(() => {
    return (
      isRecording ||
      storageMenu === STORAGE_MENU_ENUM.RECORDING_MENU ||
      !isGame ||
      showScene === null
    );
  }, [isGame, isRecording, showScene, storageMenu]);

  useEffect(() => {
    if (hiddenChangeScreen) {
      itemAnimate(
        itemRef.current,
        {
          opacity: 0,
          scale: 0,
        },
        { duration: 0.2, ease: 'circOut' }
      );
    } else {
      itemAnimate(
        itemRef.current,
        {
          opacity: 1,
          scale: 1,
        },
        { duration: 0.2, ease: 'circIn' }
      );
    }
  }, [hiddenChangeScreen]);

  return (
    <StorageMenuItem
      onClick={() => {
        if (hiddenChangeScreen) return;
        switchScene();
      }}
      ref={itemRef}>
      <StyledIconContainer>
        <ImageIcon $bgSrc={showScene?.img} $isMobile={isMobile} />
        {/* <img src={showScene.img} alt="" /> */}
        {/* <div>{showScene.text}</div> */}
        <StyledKeyTag>H</StyledKeyTag>
        <StyledArrowContainer>
          <ArrowSvg />
          <span data-text={showScene?.text}>{showScene?.text}</span>
        </StyledArrowContainer>
      </StyledIconContainer>
    </StorageMenuItem>
  );
};

const StyledIconContainer = styled(IconContainer)`
  align-items: flex-start;
`;

const ImageIcon = styled.div<{ $bgSrc?: string; $isMobile: boolean }>`
  width: 5rem;
  height: 5rem;
  flex-shrink: 0;
  background: url(${(props) => props.$bgSrc}) 50% / cover no-repeat;
  ${({ $isMobile }) =>
    $isMobile &&
    css`
      width: 31.5px;
      height: 29.045px;
      flex-shrink: 0;
    `}
`;

const StyledArrowContainer = styled.div`
  display: flex;
  /* width: 5.1389375rem; */
  width: auto;
  padding: 0 0.25rem;
  height: 1.5rem;
  bottom: 0;
  right: 0;
  transform: rotate(9.406deg) translate(1.25rem, -0.5rem);
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  flex-shrink: 0;
  border-radius: 3.125rem;
  border: 0.0625rem solid #000;
  background: #00b2e3;
  box-shadow: 0rem 0.25rem 0rem 0rem rgba(0, 0, 0, 0.25);
  box-sizing: border-box;

  position: absolute;
  transform-origin: right bottom;
  & > svg {
    width: 1rem;
    height: 0.75rem;
  }
  & > span {
    color: #fff;
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 12px */
    letter-spacing: -0.03rem;
    text-transform: capitalize;
    position: relative;
    white-space: nowrap;
    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.125rem #4b2800;
      z-index: -1;
      left: 0;
    }
  }
`;

const ArrowSvg = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 13" fill="none">
      <path
        d="M9.27816 1.85238L8.26264 3.40554C9.23032 3.73661 10.2861 4.39894 11.2485 5.21371C12.4654 6.24397 13.6014 7.57337 14.2913 8.93682L14.5352 9.4187L14.0357 9.62501L9.9414 11.3116L9.49943 11.4937L9.298 11.0605C8.8043 10.0005 8.16038 8.99577 7.51344 8.19713C7.05405 7.63003 6.61184 7.1933 6.24 6.9092L5.36426 8.89731L4.95049 9.83546L4.46572 8.93159L1.28699 2.99934L0.957428 2.38349L1.64638 2.26994L8.77763 1.08546L9.9003 0.899247L9.27816 1.85238Z"
        fill="white"
        stroke="#542D00"
      />
    </svg>
  );
};

export default ChangeSceneButton;
