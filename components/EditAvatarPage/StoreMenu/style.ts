import styled, { css } from 'styled-components';
import { motion } from 'motion/react';
import SvgWrapper from '@/components/SvgWrapper';

export const StorageMenuView = styled.div<{ $isMobile: boolean }>`
  display: none;
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  position: fixed;
  /* top: 50%; */
  /* transform: translate(0, -50%); */

  ${({ $isMobile }) =>
    $isMobile
      ? css`
          left: 2.3474178404%;
          bottom: 30.6329113924%;
          gap: 14px;
          ${StorageMenuItem} {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            ${MenuItemSvgWrapper} {
              width: 24px;
              height: 24px;
            }
          }
        `
      : css`
          left: 2.08333333333%;
          bottom: 21.2962962963%;
        `}
`;

export const StorageMenuItem = styled(motion.div)`
  width: 5.5rem;
  height: 5.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1.225rem;
  cursor: pointer;
  user-select: none;
  background: linear-gradient(to bottom, #ff8316 0%, #fe6a00 100%);
  box-shadow:
    0px 0.15275rem 0.15275rem 0px rgba(255, 255, 255, 0.25) inset,
    0px -0.3819375rem 0px 0px rgba(0, 0, 0, 0.25) inset;

  &:hover:not(.active) {
    background: linear-gradient(to bottom, #fdbb4b 0%, #fb8f1e 100%);
  }
  &.active,
  &:hover:active {
    background: linear-gradient(180deg, #261c0e 0%, #423524 100%);
    box-shadow:
      0px -0.125rem 0.125rem 0px rgba(255, 255, 255, 0.25) inset,
      0px 0.3125rem 0px 0px #000 inset;
  }
`;

export const IconContainer = styled(motion.div)`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const MenuItemSvgWrapper = styled(SvgWrapper)`
  width: 3.5rem;
  height: 3.5rem;
  & > svg {
    /* width: 3.625rem;
    height: 3.625rem; */
    /* max-width: 3.625rem;
    max-height: 3.625rem; */
  }
`;

export const StyledKeyTag = styled.span`
  display: flex;
  width: 1.5rem;
  height: 1.5rem;
  padding: 0.625rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  flex-shrink: 0;

  color: #140f08;
  font-family: Inter;
  font-size: 1rem;
  font-style: normal;
  font-weight: 600;
  line-height: 100%;
  text-transform: capitalize;

  border-radius: 0.375rem;
  background: #fff;
  box-shadow: 0rem 0rem 0.5rem 0rem rgba(0, 0, 0, 0.25);
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  transform: translateY(50%);
`;
