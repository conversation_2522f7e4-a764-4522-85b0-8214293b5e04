import styled from 'styled-components';

export const SubmitModalView = styled.div`
  .title-desc {
    font-family: 'JetBrains Mono';
    font-size: 14px;
    font-weight: 400;
    line-height: 16.94px;
    text-align: center;
    color: #686663;
    margin: 8px 0 0 0;
  }
  .collection-name-input {
    margin-top: 24px;
    padding: 16px;
    box-sizing: border-box;
    border: 1px solid #686663;
    border-radius: 24px;
    .collection-name-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      & > p {
        font-family: 'JetBrains Mono';
        font-size: 18px;
        font-weight: 700;
        line-height: 21.78px;
        text-align: left;
        color: #140f08;
        margin: 0;
      }
      & > span {
        font-family: 'JetBrains Mono';
        font-size: 14px;
        font-weight: 400;
        line-height: 18.48px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
      }
    }
    & > input {
      margin-top: 16px;
      width: 100%;
      height: 48px;
      border: 1px solid #9a9a9a;
      outline: none;
      background-color: #ececec;
      border-radius: 12px;
      padding: 0 10px;
      box-sizing: border-box;
      font-family: 'JetBrains Mono';
      font-size: 16px;
      font-weight: 700;
      line-height: 21.78px;
      text-align: left;
      color: #140f08;
      &::placeholder {
        color: #9f9e9c;
      }
    }
  }
  .metadata-json {
    margin-top: 24px;
    max-height: 184px;
    background: #686663;
    padding: 16px;
    box-sizing: border-box;
    border-radius: 24px;
    div {
      width: 100%;
      height: 100%;
      overflow: auto;
      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
        border-radius: 8px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: #595754;
        width: 8px;
        height: 8px;
        border-radius: 8px;
      }
      // 右下角
      &::-webkit-scrollbar-corner {
        background: transparent;
      }

      pre {
        font-family: 'JetBrains Mono';
        font-size: 12px;
        font-weight: 400;
        line-height: 14.52px;
        text-align: left;
        color: #ffffff;
      }
    }
  }

  .total-view {
    border: 1px solid #9a9a9a;
    padding: 16px;
    box-sizing: border-box;
    margin-top: 16px;
    border-radius: 24px;

    & > .top-value {
      display: flex;
      justify-content: space-between;

      & > span {
        font-family: 'JetBrains Mono';
        font-size: 18px;
        font-weight: 700;
        line-height: 21.78px;
        text-align: left;
        color: #140f08;
      }

      & > div {
        display: flex;
        align-items: center;

        & > strong {
          font-family: 'JetBrains Mono';
          font-size: 18px;
          font-weight: 700;
          line-height: 21.78px;
          text-align: left;
          color: #ff8316;

          & > span {
            margin-left: 24px;
            font-family: 'JetBrains Mono';
            font-size: 14px;
            font-weight: 400;
            line-height: 16.94px;
            text-align: left;
            color: #686663;
          }
        }

        & > span {
          margin-left: 8px;
          font-family: 'JetBrains Mono';
          font-size: 18px;
          font-weight: 400;
          line-height: 21.78px;
          text-align: left;
          color: #140f08;
        }
      }
    }

    .btc-value {
      text-align: right;
      margin: 16px 0 0 0;
      font-family: 'JetBrains Mono';
      font-size: 18px;
      font-weight: 400;
      line-height: 21.78px;
      color: #686663;
    }
  }

  .balance-view {
    border: 1px solid #9a9a9a;
    padding: 16px;
    box-sizing: border-box;
    margin-top: 16px;
    border-radius: 24px;

    display: flex;
    align-items: center;
    justify-content: space-between;

    & > span {
      font-family: 'JetBrains Mono';
      font-size: 18px;
      font-weight: 700;
      line-height: 21.78px;
      text-align: left;
      color: #140f08;
    }

    & > strong {
      font-family: 'JetBrains Mono';
      font-size: 18px;
      font-weight: 700;
      line-height: 21.78px;
      text-align: left;
      color: #140f08;

      & > span {
        margin-left: 8px;
        font-family: 'JetBrains Mono';
        font-size: 18px;
        font-weight: 400;
        line-height: 21.78px;
        text-align: left;
        color: #686663;
      }
    }
  }

  .actions {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    & > button {
      height: 56px;
      border-radius: 16px;
      cursor: pointer;
      font-family: 'JetBrains Mono';
      font-size: 20px;
      font-weight: 700;
      line-height: 20px;
      color: #ffffff;
      text-align: center;
      border: 0;

      &.cancel-btn {
        background-color: #9a9a9a;
      }

      &.confirm-btn {
        background: linear-gradient(180deg, #ff8a00 0%, #e57860 100%);
        cursor: pointer;

        &[disabled] {
          cursor: not-allowed;
          opacity: 0.4;
        }
      }
    }
  }
`;
export const FeesView = styled.div`
  border: 1px solid #9a9a9a;
  border-radius: 24px;
  padding: 16px;
  margin-top: 24px;
  .fee-items {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    height: 72px;
    gap: 16px;
    & > div {
      border-radius: 16px;
      background: #ececec;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      & > h2 {
        margin: 0;
        font-family: 'JetBrains Mono';
        font-size: 16px;
        font-weight: 700;
        line-height: 19.36px;
        color: #140f08;
      }
      & > p {
        font-family: 'JetBrains Mono';
        font-size: 14px;
        font-weight: 400;
        line-height: 16.94px;
        color: #686663;
        margin: 0;
        & > span {
          font-family: 'JetBrains Mono';
          color: #140f08;
          font-weight: 700;
          margin-right: 8px;
        }
      }
      &.active {
        background: #ebd2bc;
        border: 1px solid #ff8316;
        & > h2 {
          color: #f27100;
        }
        & > p {
          color: #f27100;
        }
      }
    }
  }
  .custom-fee {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
    .custom-slider {
      flex: 1;
      .rc-slider-rail {
        background: #ff831633;
      }
      .rc-slider-handle {
        border: 0;
        width: 12px;
        height: 12px;
        margin-top: -5px;
        background-color: #ff8316;
      }
      .rc-slider-step,
      .rc-slider-rail {
        height: 2px;
      }
      .rc-slider-track {
        background: #ff831680;
        height: 2px;
      }
      .rc-slider-handle-dragging.rc-slider-handle-dragging.rc-slider-handle-dragging {
        box-shadow: none;
      }
    }
    & > input {
      width: 80px;
      height: 40px;
      background: #ececec;
      border: 1px solid #ff8316;
      box-sizing: border-box;
      border-radius: 12px;
      padding: 0 12px;
      outline: none;
    }
  }
`;
