import { Fe<PERSON><PERSON>iew, SubmitModalView } from './style';
import Modal from '../../BasicComponents/Modal';
import { IAppState } from '../../../../constant/type';
import { useMemo, useRef, useState } from 'react';
import Slider from 'rc-slider';
import 'rc-slider/assets/index.css';
import {
  dressCustomCollection,
  getOrderEstimate,
  getRecommendedFees,
  orderCreate,
  orderPayment,
} from '../../../../server';
import { useSelector } from 'react-redux';
import BigNumber from 'bignumber.js';
import { compressImageUtil, getPublicKey, satsToBtc, signPsbt } from '../../../../utils';
import toast from 'react-hot-toast';
import useBalance from '../../../../hooks/useBalance';
import { IAvatarMetadata } from '../../../../AvatarOrdinalsBrowser/constant/type';
import { AVATAR_VERSION } from '../../../../constant';

interface IProps {
  visible: boolean;
  onClose: () => void;
  userAvatarMetadata: IAvatarMetadata;
  exportPreviewImage: Function;
}

enum EFF_ENUM {
  NORMAL = 'Normal',
  FAST = 'Fast',
  CUSTOM = 'Custom',
}

interface IServerFee {
  fastestFee: number;
  halfHourFee: number;
  hourFee: number;
  economyFee: number;
  minimumFee: number;
}

export default function SubmitModal({
  visible,
  onClose,
  userAvatarMetadata,
  exportPreviewImage,
}: IProps) {
  const { btcAddress, btcWallet } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [fee, setFee] = useState<number>(0);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const userAvatarMetadataV = {
    ...userAvatarMetadata,
    avatarVersion: AVATAR_VERSION,
  };

  const [recommendedFees, setRecommendedFees] = useState<IServerFee>({
    fastestFee: 0,
    halfHourFee: 0,
    hourFee: 0,
    economyFee: 0,
    minimumFee: 0,
  });
  const [fractalBitcoinPrice, setFractalBitcoinPrice] = useState<number>(0);
  const [emitResult, setEmitResult] = useState({
    delegateId: '',
    mime: '',
    estimateFee: 0,
    inscriptionSats: 0,
  });
  // const [collectionName, setCollectionName] = useState<string>('')

  const { balance } = useBalance(true);
  const emitFee = () => {
    if (!btcAddress || !fee || !visible) {
      return;
    }
    getOrderEstimate({
      address: btcAddress,
      avatarVersion: AVATAR_VERSION,
      feeRate: fee,
      metadata: userAvatarMetadataV,
    }).then((res: any) => {
      if (res.data.code === 1) {
        setEmitResult(res.data.data);
      }
    });
  };
  useMemo(() => {
    if (visible) {
      getRecommendedFees().then((res: any) => {
        const recommendedFees = res.data.data.recommendedFees;
        setRecommendedFees(recommendedFees);
        setFee(recommendedFees.fastestFee);
        setFractalBitcoinPrice(res.data.data.fractalBitcoinPrice);
      });
    }
  }, [visible]);

  const onConfirm = async () => {
    // let collectionName_ = collectionName.trim()
    // if (!checkCollectionName(collectionName_)){
    //   toast.error("The collection name contains illegal characters", {duration: 6000, id: "error_collection_name"})
    //   return
    // }
    if (!btcAddress || !fee) {
      return;
    }
    setSubmitLoading(true);
    try {
      const avatar = await compressImageUtil(exportPreviewImage());
      // 暂存套装
      const collectionId = await dressCustomCollection({
        addFlag: true,
        // collectionName: collectionName_,
        metadata: JSON.stringify(userAvatarMetadataV),
        avatar: avatar as string,
        avatarVersion: AVATAR_VERSION,
      }).then((res: any) => res.data.data.id);

      const orderData = await orderCreate({
        collectionId: collectionId,
        avatarVersion: AVATAR_VERSION,
        feeRate: fee,
        pubKey: await getPublicKey(btcWallet),
      })
        .then((res: any) => {
          if (res.data.code === 1) {
            return res.data.data;
          } else {
            toast.error(res.data.msg || res.data.message[0], { duration: 6000 });
            return null;
          }
        })
        .catch(() => {
          setSubmitLoading(false);
        });
      if (!orderData) {
        setSubmitLoading(false);
        new Error('orderCreate error');
        return;
      }
      const { orderId, unsignedPsbt } = orderData;
      const signedPsbtHex = await signPsbt(btcWallet, unsignedPsbt, btcAddress);
      orderPayment({
        orderId,
        signedPsbt: signedPsbtHex,
      })
        .then((res: any) => {
          if (res.data.code === 1) {
            toast.success('Inscribe Success!', {
              duration: 4000,
            });
            setSubmitLoading(false);
            onClose();
          } else {
            setSubmitLoading(false);
            toast.error(res.data.message[0], { duration: 6000 });
          }
        })
        .catch(() => {
          setSubmitLoading(false);
        });
    } catch (e: any) {
      console.log(e);
      setSubmitLoading(false);
    }
  };

  const debounceRef: any = useRef(null);
  useMemo(() => {
    clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      emitFee();
    }, 100);
    return () => clearTimeout(debounceRef.current);
  }, [btcAddress, fee, visible]);
  const totalSats = BigNumber(emitResult.estimateFee || 0).plus(emitResult.inscriptionSats || 0);
  return (
    <Modal visible={visible} onClose={onClose} title="Confirmation" width="608px">
      <SubmitModalView>
        <p className="title-desc">Pleas confirm the transaction below:</p>
        {/*<div className="collection-name-input">
        <div className="collection-name-title">
          <p>Collection Name:</p>
          <span>[1 ~32 Charsets]</span>
        </div>
        <input type="text" value={collectionName} onChange={(e)=>setCollectionName(e.target.value)} placeholder="aaa or a·a·a or 123:1..."/>
      </div>*/}
        <div className="metadata-json">
          <div>
            <pre>{JSON.stringify(userAvatarMetadataV, null, 2)}</pre>
          </div>
        </div>
        <Fees fee={fee} setFee={setFee} recommendedFees={recommendedFees} />
        <div className="total-view">
          <div className="top-value">
            <span>Total:</span>
            <div>
              <strong>
                ≈ {totalSats.toFormat()} <span>sats</span>
              </strong>
              <span>
                ≈ $
                {BigNumber(satsToBtc(totalSats.toString()))
                  .multipliedBy(fractalBitcoinPrice)
                  .toFormat(2, 0)}
              </span>
            </div>
          </div>
          <p className="btc-value">{satsToBtc(totalSats.toString())} FB</p>
        </div>
        <div className="balance-view">
          <span>Available Balance</span>
          <strong>
            {balance}
            <span>FB</span>
          </strong>
        </div>
        <div className="actions">
          <button className="cancel-btn" onClick={onClose}>
            Cancel
          </button>
          <button
            className="confirm-btn"
            onClick={onConfirm}
            disabled={submitLoading || !btcAddress || fee <= 0}
          >
            {submitLoading ? 'Loading' : 'Confirm'}
          </button>
        </div>
      </SubmitModalView>
    </Modal>
  );
}

function Fees({
  fee,
  setFee,
  recommendedFees,
}: {
  fee: number;
  setFee: (fee: number) => void;
  recommendedFees: IServerFee;
}) {
  const [feeType, setFeeType] = useState<EFF_ENUM>(EFF_ENUM.NORMAL);
  return (
    <FeesView>
      <div className="fee-items">
        <div
          className={feeType === EFF_ENUM.NORMAL ? 'active' : ''}
          onClick={() => {
            setFeeType(EFF_ENUM.NORMAL);
            setFee(recommendedFees.economyFee);
          }}
        >
          <h2>Normal</h2>
          <p>
            <span>{recommendedFees.economyFee}</span>sats/vB
          </p>
        </div>
        <div
          className={feeType === EFF_ENUM.FAST ? 'active' : ''}
          onClick={() => {
            setFeeType(EFF_ENUM.FAST);
            setFee(recommendedFees.fastestFee);
          }}
        >
          <h2>fast</h2>
          <p>
            <span>{recommendedFees.fastestFee}</span>sats/vB
          </p>
        </div>
        <div
          className={feeType === EFF_ENUM.CUSTOM ? 'active' : ''}
          onClick={() => setFeeType(EFF_ENUM.CUSTOM)}
        >
          <h2>Custom</h2>
          <p>
            <span>{fee}</span>sats/vB
          </p>
        </div>
      </div>
      {feeType === EFF_ENUM.CUSTOM && (
        <div className="custom-fee">
          <div className="custom-slider">
            <Slider value={fee} min={0} max={500} onChange={(value) => setFee(value as number)} />
          </div>
          <input
            type="text"
            value={fee}
            onChange={(e) => {
              const val = e.target.value;
              if (val && !isNaN(+val)) {
                setFee(Math.min(Math.abs(+val || 0), 500));
              }
            }}
          />
        </div>
      )}
    </FeesView>
  );
}
