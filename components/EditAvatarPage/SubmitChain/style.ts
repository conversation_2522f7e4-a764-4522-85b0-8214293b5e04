import styled from 'styled-components';

export const SubmitChainView = styled.div`
  position: fixed;
  left: 50%;
  bottom: 1.5rem;
  transform: translate(-50%, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  z-index: 2;
  .submit-btn {
    display: flex;
    height: 4rem;
    padding: 1rem 1.5rem;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    align-self: stretch;
    border-radius: 1.5rem 0.5rem;
    background: #fff0e3;
    box-shadow:
      -0.125rem -0.25rem 0 0 #f39948 inset,
      0 0.25rem 0 0 #fff inset;
    cursor: pointer;
    border: none;
    outline: none;
    color: #140f08;
    font-family: 'JetBrains Mono';
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    &[disabled] {
      cursor: not-allowed;
    }
  }
  .reset-btn {
    display: flex;
    width: 4rem;
    height: 4rem;
    padding: 1.142875rem 1.7143125rem;
    justify-content: center;
    align-items: center;
    gap: 0.7143125rem;
    border-radius: 0.75rem;
    background: #fff0e3;
    border: none;
    outline: none;
    box-shadow:
      -0.125rem -0.25rem 0 0 #d36808 inset,
      0 0.25rem 0 0 #fff inset;
    cursor: pointer;

    & > img {
      width: 1.8125rem;
      height: 1.8125rem;
    }

    &[disabled] {
      cursor: not-allowed;
    }
  }
`;

export const ButtonWrapper = styled.div`
  border-radius: 1.75rem 0.75rem;
  border: 0.25rem solid #452c00;
`;
export const RestButtonWrapper = styled.div`
  border-radius: 1rem;
  border: 0.25rem solid #452c00;
`;
