import styled from 'styled-components';

export const ModalView = styled.div<{ zIndex?: number }>`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(16px);
  z-index: ${({ zIndex }) => zIndex};
  opacity: 0;
  transition: opacity 0.3s;

  &.animating {
    opacity: 1;
  }
`;

export const ModalViewBox = styled.div<{ width: string | number }>`
  box-sizing: border-box;
  margin: 0px;
  min-width: 0px;
  background: #d6d6d6;
  width: ${({ width }) => (typeof width === 'number' ? `${width}px` : width)};
  max-width: 98vw;
  max-height: 100vh;
  overflow-y: auto;
  border: 1px solid #ffffff;
  padding: 24px 32px;
  border-radius: 20px;
  position: relative;
  & > .close-btn {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
  }
`;

export const ModalHeaderView = styled.div`
  position: relative;
  text-align: center;
  & > span {
    font-family: 'JetBrains Mono';
    font-size: 20px;
    font-weight: 700;
    line-height: 24.2px;
    text-align: center;
    color: #140f08;
  }
`;
