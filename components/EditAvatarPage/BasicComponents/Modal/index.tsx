'use client';

import React, { ReactElement, useCallback, useEffect, useState } from 'react';
import { ModalHeaderView, ModalView, ModalViewBox } from './style';
import ReactDOM, { createPortal } from 'react-dom';

interface Props {
  children?: React.ReactNode;
  width?: string | number;
  visible: boolean;
  onClose?: () => void;
  title?: string | React.ReactNode;
  zIndex?: number;
  emptyOnly?: boolean;
  popupClose?: boolean;
  portalContainer?: Element;
}

type ModalReturnType = ReturnType<typeof createPortal> | null;

export default function Modal({
  children,
  width = '480px',
  visible,
  onClose,
  zIndex = 99,
  title,
  emptyOnly,
  popupClose,
  portalContainer,
}: Props): ModalReturnType {
  const [isAnimating, setIsAnimating] = useState(false);
  const [load, setLoad] = useState<boolean>(false);
  const closeModal = useCallback(() => {
    if (!onClose) {
      return;
    }
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  }, [onClose]);

  // 处理键盘事件
  useEffect(() => {
    if (!visible) return;

    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') closeModal();
    };

    document.addEventListener('keydown', handleEsc);
    return () => document.removeEventListener('keydown', handleEsc);
  }, [visible, closeModal]);

  useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        setIsAnimating(true);
      }, 20);
      return () => clearTimeout(timer);
    }
  }, [visible]);

  useEffect(() => {
    setLoad(true);
  }, []);

  if (!visible || !load) {
    return null;
  }
  const doc = portalContainer ?? document.body;
  return ReactDOM.createPortal(
    <ModalView
      className={isAnimating ? 'animating' : ''}
      zIndex={zIndex}
      onClick={(e) => {
        e.stopPropagation();
        if (popupClose) {
          onClose && onClose();
        }
      }}>
      {emptyOnly ? (
        <div>{children}</div>
      ) : (
        <ModalViewBox width={width} onClick={(e) => e.stopPropagation()}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            onClick={closeModal}
            className="close-btn">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M5.39387 5.39412C5.72861 5.05938 6.27132 5.05938 6.60606 5.39412L12 10.788L17.3939 5.39412C17.7286 5.05938 18.2713 5.05938 18.6061 5.39412C18.9408 5.72885 18.9408 6.27157 18.6061 6.6063L13.2121 12.0002L18.6061 17.3941C18.9408 17.7289 18.9408 18.2716 18.6061 18.6063C18.2713 18.941 17.7286 18.941 17.3939 18.6063L12 13.2124L6.60606 18.6063C6.27132 18.941 5.72861 18.941 5.39387 18.6063C5.05914 18.2716 5.05914 17.7289 5.39387 17.3941L10.7878 12.0002L5.39387 6.6063C5.05914 6.27157 5.05914 5.72885 5.39387 5.39412Z"
              fill="#686663"
            />
          </svg>
          <ModalHeaderView>
            <span>{title}</span>
          </ModalHeaderView>
          <div>{children}</div>
        </ModalViewBox>
      )}
    </ModalView>,
    doc
  );
}
