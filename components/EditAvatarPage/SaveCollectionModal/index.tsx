import Modal from '../BasicComponents/Modal';
import { SaveCollectionModalView } from './style';
import { IAppState, ICollection } from '../../../constant/type';
import { useState } from 'react';
import { dressCustomCollection } from '../../../server';
import { checkCollectionName, compressImageUtil } from '../../../utils';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';
import { IAvatarMetadata } from '../../../AvatarOrdinalsBrowser/constant/type';
import { AVATAR_VERSION } from '../../../constant';

interface IProps {
  visible: boolean;
  onClose: () => void;
  userAvatarMetadata: IAvatarMetadata;
  exportPreviewImage: Function;
  collectionData: ICollection | null;
}

export default function SaveCollectionModal({
  visible,
  onClose,
  exportPreviewImage,
  userAvatarMetadata,
  collectionData,
}: IProps) {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const router = useRouter();
  const [collectionName, setCollectionName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const onSave = async () => {
    setLoading(true);
    const avatar = await compressImageUtil(exportPreviewImage());
    const id = collectionData ? collectionData.id : undefined;
    const collectionName_ = (
      collectionData ? collectionData.collectionName : collectionName
    ).trim();
    if (!checkCollectionName(collectionName_)) {
      toast.error('The collection name contains illegal characters', {
        duration: 6000,
        id: 'error_collection_name',
      });
      setLoading(false);
      return;
    }
    // 暂存套装
    dressCustomCollection({
      id,
      addFlag: id === undefined,
      collectionName: collectionName_,
      metadata: JSON.stringify(userAvatarMetadata),
      avatar: avatar as string,
      avatarVersion: AVATAR_VERSION,
    }).then((res: any) => {
      const collectionId = res.data.data.id;
      toast.success('Saved!', { duration: 6000 });
      router.replace(`${router.pathname}?collectionId=${collectionId}`);
      onClose();
      setLoading(false);
    });
  };
  if (collectionData) {
    return (
      <Modal visible={visible} onClose={onClose} title={'Save and overwrite data?'} width={'464px'}>
        <SaveCollectionModalView>
          <p className="title-desc">
            You are editing an existing collection. Saving will overwrite the old data.
          </p>
          <div className="edit-btns">
            <button className="cancel-btn" onClick={onClose}>
              Cancel
            </button>
            <button className="confirm-btn" disabled={loading || !btcAddress} onClick={onSave}>
              {loading ? 'Loading' : 'Confirm'}
            </button>
          </div>
        </SaveCollectionModalView>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} onClose={onClose} title={'Save as temporary copy'} width={'544px'}>
      <SaveCollectionModalView>
        <p className="title-desc">Pleas confirm the transaction below:</p>
        <div className="collection-name-input">
          <div className="collection-name-title">
            <p>Collection Name:</p>
            <span>[1 ~32 Charsets]</span>
          </div>
          <input
            type="text"
            value={collectionName}
            onChange={(e) => setCollectionName(e.target.value)}
            placeholder="aaa or a·a·a or 123:1..."
          />
        </div>
        <button
          className="save-btn"
          disabled={!collectionName || loading || !btcAddress}
          onClick={onSave}
        >
          {loading ? 'Loading' : 'Save'}
        </button>
      </SaveCollectionModalView>
    </Modal>
  );
}
