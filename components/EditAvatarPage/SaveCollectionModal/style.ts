import styled from 'styled-components';

export const SaveCollectionModalView = styled.div`
  .title-desc {
    font-family: 'JetBrains Mono';
    font-size: 14px;
    font-weight: 400;
    line-height: 16.94px;
    text-align: center;
    color: #686663;
    margin: 8px 0 0 0;
  }
  .collection-name-input {
    margin-top: 24px;
    padding: 16px;
    box-sizing: border-box;
    border: 1px solid #686663;
    border-radius: 24px;
    .collection-name-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      & > p {
        font-family: 'JetBrains Mono';
        font-size: 18px;
        font-weight: 700;
        line-height: 21.78px;
        text-align: left;
        color: #140f08;
        margin: 0;
      }
      & > span {
        font-family: 'JetBrains Mono';
        font-size: 14px;
        font-weight: 400;
        line-height: 18.48px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
      }
    }
    & > input {
      margin-top: 16px;
      width: 100%;
      height: 48px;
      border: 1px solid #9a9a9a;
      outline: none;
      background-color: #ececec;
      border-radius: 12px;
      padding: 0 10px;
      box-sizing: border-box;
      font-family: 'JetBrains Mono';
      font-size: 16px;
      font-weight: 700;
      line-height: 21.78px;
      text-align: left;
      color: #140f08;
    }
  }
  .save-btn {
    margin-top: 24px;
    width: 100%;
    height: 56px;
    border-radius: 16px;
    background: linear-gradient(180deg, #ff8a00 0%, #e57860 100%);
    outline: none;
    font-family: 'JetBrains Mono';
    font-size: 20px;
    font-weight: 700;
    line-height: 20px;
    text-align: center;
    color: #ffffff;
    border: 0;
    cursor: pointer;
    &[disabled] {
      cursor: not-allowed;
      opacity: 0.4;
    }
  }
  .edit-btns {
    margin-top: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    button {
      height: 56px;
      border-radius: 16px;
      outline: none;
      font-family: 'JetBrains Mono';
      font-size: 20px;
      font-weight: 700;
      line-height: 20px;
      text-align: center;
      color: #ffffff;
      border: 0;
      cursor: pointer;
      &[disabled] {
        cursor: not-allowed;
        opacity: 0.4;
      }
      &.cancel-btn {
        background-color: #9a9a9a;
      }
      &.confirm-btn {
        background: linear-gradient(180deg, #ff8a00 0%, #e57860 100%);
      }
    }
  }
`;
