import Failed from '@/components/AnimationSequence/Failed';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { RewardType } from '@/constant/type';
import { AppGameApiKey, useMyPlayer } from '@/world/Character/MyPlayer';

export interface IOrderTreeFailedRef {
  open: (config: RewardType) => void;
  close: () => void;
}

const OrderTreeFailed = forwardRef<IOrderTreeFailedRef, any>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [rewardType, setRewardType] = useState<RewardType>();

  useImperativeHandle(ref, () => ({
    open: (config: RewardType) => {
      setIsOpen(true);
      setRewardType(config);
    },
    close: () => setIsOpen(false),
  }));

  // const imageConfig = useMemo(() => {
  //   if (rewardType) {
  //     return AnimationSequenceConfig[rewardType];
  //   }
  //   return AnimationSequenceConfig.wangcai;
  // }, [rewardType]);

  return (
    <>
      {isOpen && (
        <Failed
          isVisible={isOpen}
          onComplete={() => {
            setIsOpen(false);
          }}
          failedImage={'/image/easterEgg/orderTree_failed.png'}
          // failedImage={imageConfig?.failureImageSrc}
        />
      )}
    </>
  );
});

OrderTreeFailed.displayName = 'OrderTreeFailed';

export default OrderTreeFailed;

export function useOrderTreeFailed() {
  const myPlayer = useMyPlayer();
  const orderTreeFailedRef = useRef<IOrderTreeFailedRef>(null);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(AppGameApiKey.showEasterEggFailed, (config: RewardType) => {
        orderTreeFailedRef.current?.open(config);
      });
    }
  }, [myPlayer]);

  return orderTreeFailedRef;
}
