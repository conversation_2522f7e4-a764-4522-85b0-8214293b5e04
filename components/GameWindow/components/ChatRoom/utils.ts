function containsChinese(str: string) {
  return /[\u4e00-\u9fa5]/.test(str);
}

function isPureEnglishOrNumber(str: string) {
  return /^[a-zA-Z0-9]+$/.test(str);
}

export function formatPlayerId(playerId: string) {
  if (!playerId) return '';

  // 如果包含中文
  if (containsChinese(playerId)) {
    // 如果全是中文
    if (isPureEnglishOrNumber(playerId.replace(/[\u4e00-\u9fa5]/g, ''))) {
      if (playerId.length <= 6) return playerId;
      return playerId.slice(0, 3) + '...' + playerId.slice(-3);
    }
    // 中英文混合
    if (playerId.length <= 8) return playerId;
    return playerId.slice(0, 4) + '...' + playerId.slice(-2);
  }

  // 纯英文/数字
  if (playerId.length <= 10) return playerId;
  return playerId.slice(0, 4) + '...' + playerId.slice(-4);
}
