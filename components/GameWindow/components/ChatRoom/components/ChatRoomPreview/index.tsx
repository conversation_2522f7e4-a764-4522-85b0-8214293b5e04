import { SpriteSvg } from '@/components/SvgWrapper';
import MessageList from '../MessageList';
import { motion } from 'framer-motion';
import { PreviewButton, PreviewButtonSvg, PreviewMessageBox, StyleButtonBox } from './styles';
import ChatRoomLine from './ChatRoomLine';
import { IS_MOBILE_ENV } from '@/constant';
import { useContextSelector } from '../../context';

interface IChatRoomPreviewProps {
  onExpand: () => void;
}

const ChatRoomPreview: React.FC<IChatRoomPreviewProps> = ({ onExpand }) => {
  const haveNewMessage = useContextSelector((state) => state.haveNewMessage);
  return (
    <>
      <StyleButtonBox>
        <ChatRoomLine />
        <PreviewButton onClick={onExpand}>
          <PreviewButtonSvg $withNew={haveNewMessage}>
            <SpriteSvg id="message" />
          </PreviewButtonSvg>
          <span className="text">Enter</span>
        </PreviewButton>
      </StyleButtonBox>
      {!IS_MOBILE_ENV && (
        <PreviewMessageBox onClick={onExpand}>
          <MessageList componentType="preview" />
        </PreviewMessageBox>
      )}
    </>
  );
};

export default ChatRoomPreview;
