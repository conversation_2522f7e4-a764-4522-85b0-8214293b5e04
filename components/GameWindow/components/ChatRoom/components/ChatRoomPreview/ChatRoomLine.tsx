import { AnimatePresence, motion } from 'framer-motion';
import styled, { css } from 'styled-components';
import { PreviewButton, PreviewButtonSvg } from './styles';
import { SpriteSvg } from '@/components/SvgWrapper';
import { useEffect, useRef, useState } from 'react';
import { useNetWork } from '@/world/hooks/useNetWork';

const RoomBox = styled.div<{ $dotColor?: string }>`
  display: flex;
  height: 3rem;
  padding: 0rem 1rem;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease-in;
  flex-wrap: nowrap;

  flex-shrink: 0;
  border-radius: 1rem;
  background: transparent;
  transition:
    background 0.2s ease-in,
    height 0.2s ease-in;

  & > span {
    color: #140f08;
    font-family: Inter;
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    text-transform: capitalize;
    white-space: nowrap;
    &:last-of-type {
      font-size: 1.25rem;
      font-weight: 800;
    }
  }

  ${({ $dotColor }) =>
    $dotColor &&
    css`
      &::before {
        content: '';
        border-radius: 50%;
        display: block;
        width: 0.75rem;
        height: 0.75rem;
        background: ${$dotColor};
      }
    `}
`;

const CurrentRoomBox = styled.div`
  display: flex;
  width: 6.125rem;
  height: 4rem;
  padding: 0.5rem 1.5rem;
  border: 0.0625rem solid transparent;
  flex-shrink: 0;

  background: transparent;
  align-items: center;
  border-radius: 1.5rem;
  gap: 1rem;
  transition:
    background 0.2s ease-in,
    width 0.1s ease-in,
    border-radius 0.1s ease-in;
  overflow: hidden;
`;

const RoomBoxItemWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  & > ${RoomBox}:last-of-type {
    margin-bottom: 0.5rem;
  }
  ::after {
    content: '';
    width: 100%;
    border-bottom: 0.0625rem solid #cabfab;
  }
`;

const RoomList = styled(motion.div)`
  width: 17.8125rem;
  bottom: calc(100% - 0.5rem);
  /* display: none; */
  flex-shrink: 0;
  flex: 1;
  border-top-left-radius: 1.5rem;
  border-top-right-radius: 1.5rem;
  border: 0.0625rem solid #a58061;
  border-bottom: none;
  background: #fff;
  display: flex;
  padding: 0rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  transition:
    padding 0.2s ease-in,
    height 0.2s ease-in;
  & ${RoomBox} {
    height: 0rem;
    overflow: hidden;
    :hover {
      background: #fbf4e8;
    }
  }
`;

const SwitchRoomBox = styled(motion.div)<{ $isExpanded?: boolean; $isHover?: boolean }>`
  position: relative;
  z-index: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column-reverse;
  cursor: pointer;
  position: absolute;
  left: -1.5rem;
  /* top: 0; */
  bottom: 50%;
  z-index: 11;
  ${({ $isHover = false, $isExpanded = false }) =>
    $isHover &&
    css`
      width: 17.8125rem;
      & ${CurrentRoomBox} {
        width: 17.8125rem;
        padding: 0.5rem 0.5rem 0.5rem 1.5rem;
        border: 0.0625rem solid #a58061;
        background: #fff;
        ${$isExpanded &&
        css`
          border-top: 0;
          border-top-left-radius: 0;
          border-top-right-radius: 0;
        `}

        & ${RoomBox} {
          opacity: 1;
        }
      }

      & ${RoomList} {
        display: flex;
        padding: 0.5rem 0.5rem 0.5rem 1.5rem;
        ${$isExpanded &&
        css`
          & ${RoomBox} {
            height: 3rem;
            opacity: 1;
            justify-content: flex-end;
            gap: 0.5rem;
          }
        `}
      }
    `}
`;

interface GameNodeInfo {
  id: string;
  mode: number;
  modeIndex: number; // 线路
  playerCount: number; // 人数
  maxPlayerCount?: number; // 最大人数
}

const dotColorList = {
  FREE: '#16FF26', // 空闲 (0-60%)
  BUSY: '#FF8316', // 繁忙 (60%-100%)
  FULL: '#FF2424', // 火爆 (100%)
};

const ChatRoomLine = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEnterRoom, setIsEnterRoom] = useState(false);
  const [lineList, setLineList] = useState<GameNodeInfo[]>([]);
  const [currentRoom, setCurrentRoom] = useState<GameNodeInfo | null>(null);
  const { enterRoom, getRoomList, watchRoomStatus } = useNetWork();
  const [isLoading, setIsLoading] = useState(false);
  const mapIdRef = useRef<any>('');
  const mapIndexRef = useRef<any>('');

  // 根据服务器人数百分比获取dot颜色
  const getDotColor = (item: GameNodeInfo) => {
    // 如果没有maxPlayerCount，默认为空闲状态
    if (!item.maxPlayerCount || item.maxPlayerCount <= 0) {
      return dotColorList.FREE;
    }

    // 计算百分比
    const percentage = (item.playerCount / item.maxPlayerCount) * 100;

    // 根据百分比选择颜色
    if (percentage === 100) {
      return dotColorList.FULL; // 火爆 (100%)
    } else if (percentage >= 60) {
      return dotColorList.BUSY; // 繁忙 (60%-100%)
    } else {
      return dotColorList.FREE; // 空闲 (0-60%)
    }
  };

  // 获取线路列表
  const getLineList = async () => {
    setIsLoading(true);
    const res = await getRoomList();

    setLineList(res);
    // mock data
    // setLineList([
    //   ...res,
    //   {
    //     mode: 'map_3',
    //     modeIndex: 2,
    //     playerCount: 1,
    //     maxPlayerCount: 50,
    //   } as any,
    // ]);

    // 根据mapIndex设置当前房间
    const currentRoom = res.find((item) => item.modeIndex === mapIndexRef.current);
    if (currentRoom) {
      setCurrentRoom(currentRoom);
    }

    setIsLoading(false);
  };

  useEffect(() => {
    if (isExpanded) {
      getLineList();
    }
  }, [isExpanded]);

  useEffect(() => {
    // 判断是否进入房间
    watchRoomStatus((data) => {
      mapIdRef.current = data.mapId;
      mapIndexRef.current = data.mapIndex;
      getLineList();
      setIsEnterRoom(data.isEnterRoom);
    });
  }, []);

  if (!isEnterRoom) {
    return null;
  }

  return (
    <>
      <SwitchRoomBox
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        $isExpanded={isExpanded}
        $isHover={isHovered || isExpanded}>
        <CurrentRoomBox
          onClick={() => {
            setIsExpanded(!isExpanded);
          }}>
          <PreviewButton>
            <PreviewButtonSvg>
              <SpriteSvg id={isHovered || isExpanded ? 'switchChannelActive' : 'switchChannel'} />
            </PreviewButtonSvg>
          </PreviewButton>
          {!!currentRoom && (
            <RoomBox $dotColor={getDotColor(currentRoom)}>
              <span>Current Server:</span>
              <span>{currentRoom.modeIndex}</span>
            </RoomBox>
          )}
        </CurrentRoomBox>
        <AnimatePresence>
          {isExpanded && (
            <RoomList
              key="roomListAnimation"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}>
              <RoomBoxItemWrapper>
                {isLoading ? (
                  <RoomBox>
                    <LoadingWrapper>Loading...</LoadingWrapper>
                  </RoomBox>
                ) : (
                  <>
                    {lineList?.map((item) => {
                      return (
                        <RoomBox
                          key={item.mode + item.modeIndex}
                          $dotColor={getDotColor(item)}
                          onClick={() => {
                            enterRoom(mapIdRef.current, item.modeIndex);
                            setIsExpanded(false);
                            setIsHovered(false);
                          }}>
                          <span>Current Server:</span>
                          <span>{item.modeIndex}</span>
                        </RoomBox>
                      );
                    })}
                  </>
                )}
              </RoomBoxItemWrapper>
            </RoomList>
          )}
        </AnimatePresence>
      </SwitchRoomBox>
    </>
  );
};

const LoadingWrapper = styled.p`
  width: 9.4375rem;
  color: #140f08;
  font-family: Inter;
  font-size: 1rem;
  font-weight: 400;
  line-height: 100%;
  text-transform: capitalize;
  margin: 0;
  display: inline-flex;
  justify-content: center;
  align-items: center;
`;

export default ChatRoomLine;
