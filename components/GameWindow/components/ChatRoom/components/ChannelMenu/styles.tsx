import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';
import styled, { css } from 'styled-components';

export const ChannelMenuItemWrapper = styled.div<{ $active?: boolean }>`
  width: 13rem;
  height: 5rem;
  border-radius: 12.5rem;
  transition: all 0.1s ease-in;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  ${({ $active }) =>
    $active
      ? css`
          border: 0.25rem solid #140f08;
          box-shadow: 0rem 0.375rem 0rem 0rem rgba(0, 0, 0, 0.25);
        `
      : css`
          border: 0.25rem solid transparent;
        `}
`;

export const ChannelMenuItem = styled.div<{ $active?: boolean; $withNew?: boolean }>`
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  gap: 0.75rem;
  border-radius: 12.5rem;
  background: #f8f1e6;
  cursor: pointer;
  position: relative;

  ${({ $active }) =>
    $active
      ? css`
          border: 0.25rem solid #ff8316;
          background: #fff;
          border-radius: 12.5rem;
          border: 0.25rem solid #ff8316;
          padding: 0.25rem 0.75rem;
        `
      : css`
          border: 0.0625rem solid #a58061;
          padding: 0.4375rem 0.9375rem;

          padding:;
        `}
  ${({ $withNew = false }) =>
    $withNew
      ? css`
          &::before {
            content: '';
            display: block;
            width: 1.625rem;
            height: 0.75rem;
            border-radius: 3.125rem;
            background: #ff4516;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
          }
        `
      : css``}
`;

export const ChannelIcon = styled.div<{ $bgSrc?: string }>`
  width: 2.5rem;
  height: 2.5rem;
  background: url(${({ $bgSrc }) => $bgSrc}) 50% / cover no-repeat;
  position: relative;
`;

export const ChannelChildrenIcon = styled(SvgWrapper)`
  width: 1.5625rem;
  height: 1.5625rem;
  flex-shrink: 0;
  border: 0.125rem solid #fff;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  right: 0;
  bottom: 0;
  transform: translate(50%, 0%);
`;

export const ChannelTgIcon = () => {
  return (
    <ChannelChildrenIcon>
      <SpriteSvg id="telegramIcon" />
    </ChannelChildrenIcon>
  );
};

export const ChannelName = styled.span`
  color: #140f08;
  text-align: right;
  font-family: Inter;
  font-size: 1.25rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

export const ChannelMenuContainer = styled.div`
  width: 14rem;
  height: auto;
  padding-right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
`;

export const ChannelMenuWrapper = styled.div`
  position: relative;
  height: calc(100% - 2.75rem);
  overflow-y: overlay;
  overflow-x: hidden;

  width: 14rem;
  padding-right: 1rem;
  margin-top: 2.75rem;
`;

export const ScrollBox = styled.div`
  position: absolute;
  height: 100%;
  overflow-y: scroll;
  width: 16rem;
  padding-right: 1rem;
  margin-top: 7.0625rem;
  margin-left: 1.875rem;
  left: 0;
  top: 0;
  z-index: 1;
  pointer-events: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

export const ChannelSelectArrowWrapper = styled.div<{ $activeIndex?: number }>`
  position: absolute;
  height: 2.0134375rem;
  width: 2.9874375rem;
  left: 12rem;
  top: 0;
  transform: translateY(calc(${({ $activeIndex = 0 }) => $activeIndex * 7 + 2.5}rem - 75%));
  transition: transform 0.1s ease-in;
  filter: drop-shadow(0px 0.25rem 0px #d74304);
  z-index: 1;
`;
