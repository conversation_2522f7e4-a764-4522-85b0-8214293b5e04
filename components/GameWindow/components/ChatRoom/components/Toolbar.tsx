import styled from 'styled-components';
import { useContextDispatch, useContextSelector } from '../context';
import SvgWrapper, { SpriteSvg } from '@/components/SvgWrapper';

const ToolbarContainer = styled.div`
  width: 100%;
  height: 2.25rem;
`;

const CheckboxContainer = styled.label<{ checked: boolean }>`
  height: 100%;
  display: inline-flex;
  padding: 0.625rem 1rem;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  border-radius: 6.25rem;
  border: 0.0625rem solid #a58061;
  background: #fff;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  input[type='checkbox'] {
    display: none;
  }

  & > span {
    color: #140f08;
    font-family: Inter;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 100%;
    text-transform: capitalize;
  }
`;
const StyledSvgWrapper = styled(SvgWrapper)`
  width: 1.25rem;
  height: 1rem;
  justify-content: flex-start;
`;

const Toolbar = () => {
  const adminOnly = useContextSelector((state) => state.adminOnly);
  const contextDispatch = useContextDispatch();

  return (
    <ToolbarContainer>
      <CheckboxContainer checked={adminOnly}>
        <StyledSvgWrapper>
          <SpriteSvg id={adminOnly ? 'checkBoxActive' : 'checkBox'} />
        </StyledSvgWrapper>
        <input
          type="checkbox"
          checked={adminOnly}
          onChange={() => {
            contextDispatch({
              type: 'UPDATE',
              payload: (state) => ({ adminOnly: !state.adminOnly }),
            });
            // onAdminChange(!adminOnly);
          }}
        />
        <span>Just Admin</span>
      </CheckboxContainer>
    </ToolbarContainer>
  );
};

export default Toolbar;
