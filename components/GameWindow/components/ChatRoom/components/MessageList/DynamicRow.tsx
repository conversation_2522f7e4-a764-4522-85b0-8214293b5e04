import { useCallback, useEffect, useRef } from 'react';
import { VariableSizeList as List } from 'react-window';
import styled from 'styled-components';

interface DynamicRowProps<T> {
  data: T[];
  index: number;
  setSize: (selectedTab: number, index: number, size: number) => void;
  windowWidth: number;
  renderItem: (item: T) => React.ReactNode;
  selectedTab: number;
}

interface UseDynamicItemSizeProps {
  itemCount: number;
}

interface UseDynamicItemSizeReturn {
  listRef: React.RefObject<List>;
  setSize: (selectedTab: number, index: number, size: number) => void;

  getSize: (selectedTab: number, index: number) => number;
  resetAfterIndex: (index: number, shouldForceUpdate?: boolean) => void;
}

export function useDynamicItemSize({
  itemCount,
}: UseDynamicItemSizeProps): UseDynamicItemSizeReturn {
  const listRef = useRef<List>(null);
  const sizeMap = useRef<{
    [key: number]: {
      [k: number]: number;
    };
  }>({});

  const setSize = useCallback((selectedTab: number, index: number, size: number) => {
    sizeMap.current = {
      ...sizeMap.current,
      [selectedTab]: { ...sizeMap.current[selectedTab], [index]: size },
    };
    if (listRef.current) {
      listRef.current.resetAfterIndex(index);
    }
  }, []);

  const getSize = (selectedTab: number, index: number) => {
    return sizeMap.current[selectedTab]?.[index] || 50;
  };

  const resetAfterIndex = (index: number, shouldForceUpdate = true) => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(index, shouldForceUpdate);
    }
  };

  return {
    listRef,
    setSize,
    getSize,
    resetAfterIndex,
  };
}

const DynamicRow = <T,>({
  data,
  selectedTab,
  setSize,
  index,
  windowWidth,
  renderItem,
}: DynamicRowProps<T>) => {
  const rowRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (rowRef.current) {
      setSize(selectedTab, index, rowRef.current.getBoundingClientRect().height);
    }
  }, [setSize, windowWidth, selectedTab, index]);

  const item = data[index];
  return <StyledItemWrapper ref={rowRef}>{renderItem(item)}</StyledItemWrapper>;
};

const StyledItemWrapper = styled.div`
  position: relative;
  width: calc(100% - 0.5rem);
  display: flex;
  align-content: center;
  justify-content: center;
  padding-top: 0.5rem;
`;

export default DynamicRow;
