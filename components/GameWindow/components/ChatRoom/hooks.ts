import { useRef, useEffect, useState, useCallback } from 'react';

interface ScrollState {
  horizontal: boolean;
  vertical: boolean;
}

type DebouncedFunction<T extends any[]> = (...args: T) => void;

const useDebounce = <T extends any[]>(
  fn: (...args: T) => void,
  delay: number
): DebouncedFunction<T> => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return (...args: T) => {
    clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => fn(...args), delay);
  };
};

export const useDynamicScrollDetection = (): [React.RefObject<HTMLDivElement>, ScrollState] => {
  const ref = useRef<HTMLDivElement>(null);
  const [hasScroll, setHasScroll] = useState<ScrollState>({
    horizontal: false,
    vertical: false,
  });

  const checkScroll = useCallback((): void => {
    if (ref.current) {
      setHasScroll({
        horizontal: ref.current.scrollWidth > ref.current.clientWidth,
        vertical: ref.current.scrollHeight > ref.current.clientHeight,
      });
    }
  }, []);

  const debouncedCheck = useDebounce(checkScroll, 100);

  useEffect(() => {
    if (!ref.current) return;

    checkScroll();

    const resizeObserver = new ResizeObserver(debouncedCheck);
    resizeObserver.observe(ref.current);

    const mutationObserver = new MutationObserver(debouncedCheck);
    mutationObserver.observe(ref.current, {
      childList: true,
      subtree: true,
      characterData: true,
      attributes: true,
    });

    return () => {
      resizeObserver.disconnect();
      mutationObserver.disconnect();
    };
  }, [checkScroll, debouncedCheck]);

  return [ref, hasScroll];
};
