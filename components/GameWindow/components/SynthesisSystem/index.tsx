import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import { forwardRef, Ref, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Image from 'next/image';
import SynthesisContent from './SynthesisContent';
import SynthesisRecipe from './SynthesisRecipe';
import useSynthesis, { RecipesWithStatusItemType, SynthesisType } from './hooks/useSynthesis';
import LocalLoading from '@/components/LoadingContent';
import { IAppState } from '@/constant/type';
import { useSelector } from 'react-redux';
import styled, { css } from 'styled-components';
import { AppGameApiKey, useMyPlayer } from '@/world/Character/MyPlayer';

const StyledModalContent = styled(ModalContent)<{ $componentType?: SynthesisType }>`
  ${({ $componentType = 'tool' }) =>
    $componentType === 'petBed'
      ? css`
          border: 0.5rem solid #efbd73;
          box-shadow: none;
        `
      : css`
          border: 0.25rem solid #140f08;
          box-shadow: inset 0 0 0 0.5rem #ed9800;
        `}
`;

export interface SynthesisSystemRef {
  open: (confirmCallback: () => void) => void;
}

interface ModalProps {
  onClose: () => void;
  componentType?: SynthesisType;
}

interface IRecipeItem {
  synthesisTag: string;
  itemTag: string;
  itemName?: string;
  itemIcon?: string;
  active?: boolean;
  maxDurability: number;
  quality: number;
}

const SynthesisSystem = forwardRef<SynthesisSystemRef, ModalProps>(
  ({ onClose, componentType = 'tool' }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const confirmCallbackRef = useRef<() => void>();
    const { userBasicInfo } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

    const {
      loading,
      synthesizing,
      recipes,
      recipesWithStatus,
      selectedRecipe,
      setSelectedRecipe,
      fetchSynthesisList,
      synthesize,
      canSynthesize,
      reset,
    } = useSynthesis(componentType);

    useImperativeHandle(ref, () => ({
      open: (confirmCallback: () => void) => {
        // 打开弹窗前获取最新数据
        confirmCallbackRef.current = confirmCallback;
        fetchSynthesisList();
        setIsOpen(true);
      },
    }));

    // 处理选择配方
    const handleSelectRecipe = (recipe: IRecipeItem) => {
      // 查找完整的配方信息
      const fullRecipe = recipes.find((item) => item.synthesisTag === recipe.synthesisTag);
      if (fullRecipe) {
        setSelectedRecipe(fullRecipe);
      }
    };

    const handleCancel = () => {
      onClose();
      setIsOpen(false);
      // 清空弹窗的数据
      reset();
    };

    const onVerify = async () => {
      try {
        if (selectedRecipe) {
          // 执行合成
          const res = await synthesize(selectedRecipe.synthesisTag);

          if (res) {
            // 成功后关闭窗口
            if (confirmCallbackRef.current) {
              confirmCallbackRef.current();
            }
            onClose();
            setIsOpen(false);
            reset();
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 执行合成操作
    const handleConfirm = async (token?: string) => {
      if (userBasicInfo?.refreshFlag) {
        await onVerify();
        return;
      }

      if (!selectedRecipe) {
        // toast.error("请先选择一个配方");
        return;
      }

      // 合成前检查材料是否足够
      if (!canSynthesize(selectedRecipe)) {
        // toast.error("材料不足，无法合成");
        return;
      }

      // 执行合成
      const res = await synthesize(selectedRecipe.synthesisTag, token, () => {
        // setIsModalOpen(true);
      });

      if (res) {
        // 成功后关闭窗口
        if (confirmCallbackRef.current) {
          confirmCallbackRef.current();
        }
        onClose();
        setIsOpen(false);
        reset();
      }
    };

    const titleEl = useMemo(() => {
      return componentType === 'tool' ? (
        <Image
          src="/image/title-bg.png"
          alt="synthesis"
          width={358}
          height={84}
          draggable={false}
          style={{
            width: '22.375rem',
            height: '5.25rem',
          }}
        />
      ) : (
        <PetBedManufactureTitleContainer>
          <PetBedManufactureTitle data-text={'Manufacture'}>Manufacture</PetBedManufactureTitle>
        </PetBedManufactureTitleContainer>
      );
    }, [componentType]);

    return (
      <>
        <Dialog
          isOpen={isOpen}
          onClose={() => {
            onClose();
            setIsOpen(false);
          }}>
          <div
            style={{
              display: 'flex',
              width: '100%',
              gap: '1.25rem',
              alignItems: 'stretch',
            }}>
            <StyledModalContent
              $componentType={componentType}
              modalHeight="30.5rem"
              modalWidth="36.25rem"
              confirmText="Confirm"
              onConfirm={handleConfirm}
              confirmLoading={synthesizing}
              maxHeight="27.5rem"
              cancelText="Cancel"
              onCancel={handleCancel}
              onClose={handleCancel}
              modalBodyPadding="3.75rem 2.625rem 0.625rem"
              footerStyle={{
                justifyContent: 'space-evenly',
                gap: '0rem',
                padding: '1rem 3.75rem',
              }}
              buttonStyle={{
                width: '12.5rem',
              }}
              modalHeaderStyle={{
                top: '-2.9875rem',
              }}
              modalCloseBtnStyle={{
                right: '2.25rem',
                top: '35%',
              }}
              title={titleEl}
              confirmDisabled={!selectedRecipe || !canSynthesize(selectedRecipe) || synthesizing}>
              {loading ? (
                <div
                  style={{
                    textAlign: 'center',
                    width: '100%',
                    height: '18.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <LocalLoading />
                </div>
              ) : selectedRecipe ? (
                <SynthesisContent
                  isBatchSynthesis={false}
                  currentItem={convertToSynthesisContentFormat(selectedRecipe)}
                />
              ) : (
                <div
                  style={{
                    textAlign: 'center',
                    width: '100%',
                    height: '18.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  No available formulas
                </div>
              )}
            </StyledModalContent>
            {/* 右侧配方栏，里面是配方列表，采用双栏布局 两两组合 */}
            <SynthesisRecipe
              loading={loading}
              recipeList={convertToRecipeFormat(recipesWithStatus)}
              onSelectRecipe={handleSelectRecipe}
              selectedRecipeId={selectedRecipe?.synthesisTag}
              componentType={componentType}
            />
          </div>
        </Dialog>
        {/* <CapWidgetModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onVerify={onVerify}
          isFooter={false}
        /> */}
      </>
    );
  }
);

// 转换数据格式，适配SynthesisRecipe组件
function convertToRecipeFormat(recipes: RecipesWithStatusItemType[]): IRecipeItem[] {
  return recipes.map((recipe) => ({
    synthesisTag: recipe.synthesisTag,
    itemTag: recipe.itemTag,
    itemName: recipe.itemName || '',
    itemIcon: recipe.itemIcon || '',
    active: recipe.active,
    maxDurability: recipe.maxDurability,
    quality: recipe.quality,
  }));
}

// 转换数据格式，适配SynthesisContent组件
function convertToSynthesisContentFormat(recipe: any) {
  return {
    itemId: recipe.synthesisTag,
    name: recipe.itemName || '',
    description: recipe.description || '',
    icon: recipe.itemIcon || '',
    canSynthesize: recipe.canSynthesize || 0,
    synthetics: recipe.synthesis.map((material: any) => ({
      itemId: material.tag,
      name: material.name || '',
      description: material.description || '',
      icon: material.icon || '',
      currentQuantity: material.currentCount,
      needQuantity: material.count,
      currentDurability: material.currentDurability,
    })),
    maxDurability: recipe.maxDurability,
    quality: recipe.quality,
  };
}

SynthesisSystem.displayName = 'SynthesisSystem';

export function useSynthesisSystemModal() {
  const synthesisSystemRef = useRef<SynthesisSystemRef>(null);
  const [componentType, setComponentType] = useState<SynthesisType>();
  const myPlayer = useMyPlayer();

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.openSynthesis,
        (type: SynthesisType, sureCallback: () => void) => {
          setComponentType(type);

          synthesisSystemRef.current?.open(sureCallback);
        }
      );
    }
  }, []);

  return { synthesisSystemRef, componentType };
}

const PetBedManufactureTitleContainer = styled.div`
  width: 20rem;
  height: 5.3125rem;
  flex-shrink: 0;
  background-image: url('/image/petBed_title.png');
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  align-items: flex-end;
  justify-content: center;
`;

const PetBedManufactureTitle = styled.p`
  margin: 0;
  margin-bottom: 0.5rem;
  color: #fff;
  text-align: center;
  font-family: 'Baloo 2';
  font-size: 2.5rem;
  font-style: normal;
  font-weight: 800;
  line-height: 100%;
  text-transform: capitalize;
  position: relative;
  z-index: 1;
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.25rem #664830;
    text-stroke: 0.25rem #664830;
    z-index: -1;
    left: 0;
  }
`;

export default SynthesisSystem;
