import React, { useMemo, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';
import Tooltip from 'rc-tooltip';
import { TooltipBox } from '@/components/EditAvatarPage/BagModal/ContentItem';

// Styled Components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ItemCard = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ItemImage = styled.div<{ bgImage: string }>`
  width: 6.25rem;
  height: 6.25rem;
  background-image: ${(props) => `url(${props.bgImage})`};
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 1rem;
  margin-bottom: 0.625rem;
  box-shadow: 0rem 0rem 1.26125rem 0rem #e94a0680;
  border: 0.0625rem solid #c2b8a2;
`;

const ItemTitle = styled.h3`
  font-family: 'JetBrains Mono', monospace;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  padding: 0;
  color: #140f08;
`;

const RequirementGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2.5rem;
  margin-top: 1.25rem;
  padding: 0.75rem 1rem;
  background: #f7e7cd;
  border-radius: 1.5rem;
  box-shadow: 0rem 0rem 0.5rem 0rem #00000040 inset;
`;

const RequirementItem = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
`;

const RequirementImage = styled.div<{
  isEnough: boolean;
  currentDurability?: number;
}>`
  width: 6.25rem;
  height: 6.25rem;
  border-radius: 1rem;
  margin-bottom: 0.5rem;
  background-color: #faf3e5;
  border: 0.0625rem solid #c2b8a2;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  box-shadow: 0 0 0.3125rem rgba(0, 0, 0, 0.3);
  position: relative;
  .material-icon {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 6;
  }
  &::after {
    content: ${({ currentDurability }) => (currentDurability === 1 ? '""' : 'none')};
    position: absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    border-radius: 0.625rem;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.9) 50%,
      rgba(255, 182, 193, 0.7) 50%,
      rgba(255, 105, 180, 0.4) 75%,
      rgba(255, 20, 147, 0.2) 90%,
      rgba(255, 105, 180, 0) 100%
    );
    display: ${({ currentDurability }) => (currentDurability === 1 ? 'block' : 'none')};
    z-index: 5;
    filter: blur(0.4375rem);
    opacity: 1;
  }
`;

const RequirementStatus = styled.p<{ isEnough: boolean }>`
  font-family: 'JetBrains Mono', monospace;
  font-size: 1rem;
  margin: 0;
  color: ${(props) => (props.isEnough ? '#615A57' : '#FF8316')};
  font-weight: ${(props) => (props.isEnough ? 'normal' : 'bold')};
`;

const QuantityControl = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7e7cd;
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  margin: 1rem 0;
`;

const QuantityButton = styled.button<{ disabled?: boolean }>`
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  background: ${(props) => (props.disabled ? '#D8D0CA' : '#FFFFFF')};
  color: ${(props) => (props.disabled ? '#5F5D5B' : '#140F08')};
  font-size: 1.25rem;
  font-weight: bold;
  border: none;
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};

  &:hover:not(:disabled) {
    background: #f1f1f1;
  }

  &:active:not(:disabled) {
    background: #e0e0e0;
  }
`;

const QuantityDisplay = styled.span`
  font-family: 'JetBrains Mono', monospace;
  font-size: 1.25rem;
  padding: 0 1rem;
  color: #140f08;
`;

// 新的合成物品接口，与API返回的数据结构对应
export interface ISynthesisContentItem {
  synthesisTag: string;
  itemTag: string;
  itemName?: string;
  itemIcon?: string;
  canSynthesize: number;
  synthesis: {
    tag: string;
    type: string;
    count: number;
    currentCount: number;
    name?: string;
    icon?: string;
  }[];
}

interface SynthesisContentProps {
  // 是否支持批量合成
  isBatchSynthesis: boolean;
  // 当前合成物品
  currentItem: ISynthesisContentItem | any; // 使用any暂时兼容旧的数据结构
}

// 设计合成内容
const SynthesisContent = ({ isBatchSynthesis = false, currentItem }: SynthesisContentProps) => {
  const [quantity, setQuantity] = useState(1);
  // const [canSynthesize, setCanSynthesize] = useState(true);

  // 检查是否使用新的数据结构
  const isNewDataStructure = 'synthesis' in currentItem && 'synthesisTag' in currentItem;

  const handleDecrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const handleIncrementQuantity = () => {
    if (quantity < currentItem.canSynthesize) {
      setQuantity(quantity + 1);
    }
  };

  // 获取正确的物品名称和图标
  const itemName = isNewDataStructure ? currentItem.itemName || '' : currentItem.name;
  const itemIcon = isNewDataStructure ? currentItem.itemIcon || '' : currentItem.icon;

  const showDetail = useMemo(() => {
    return {
      name: currentItem.name,
      maxDurability: currentItem.maxDurability,
      quality: currentItem.quality,
      trait: 'Chance to craft shining tools! Double the resources!',
    };
  }, [currentItem]);

  // 获取材料列表
  const materialsList = isNewDataStructure ? currentItem.synthesis : currentItem.synthetics;
  // console.log("materialsList======", materialsList);
  return (
    <Container>
      <Tooltip
        zIndex={1001}
        trigger="hover"
        overlay={<TooltipBox showDetail={showDetail as any} />}
        showArrow={false}
        align={{
          points: ['tl', 'cl'],
          offset: ['20.18%', '12.5%'],
        }}>
        <ItemCard>
          <ItemImage bgImage={itemIcon} />
          <ItemTitle>{itemName}</ItemTitle>
        </ItemCard>
      </Tooltip>

      <RequirementGroup>
        {materialsList.map((item: any, index: number) => {
          // 根据数据结构获取正确的属性
          const available = isNewDataStructure ? item.currentCount : item.currentQuantity;
          const required = isNewDataStructure
            ? item.count * quantity
            : item.needQuantity * quantity;
          const isEnough = available >= required;
          const materialTag = isNewDataStructure ? item.tag : item.itemId;
          const materialIcon = isNewDataStructure ? item.icon || '' : item.icon;

          return (
            <RequirementItem key={materialTag}>
              <RequirementImage isEnough={isEnough} currentDurability={item.currentDurability}>
                <Image
                  src={materialIcon}
                  alt="material"
                  width={100}
                  height={100}
                  className="material-icon"
                />
              </RequirementImage>
              <RequirementStatus isEnough={isEnough}>
                {available}/{required}
              </RequirementStatus>
            </RequirementItem>
          );
        })}
      </RequirementGroup>

      {isBatchSynthesis && (
        <QuantityControl>
          <QuantityButton onClick={handleDecrementQuantity} disabled={quantity <= 1}>
            -
          </QuantityButton>
          <QuantityDisplay>
            {quantity}/{currentItem.canSynthesize}
          </QuantityDisplay>
          <QuantityButton
            onClick={handleIncrementQuantity}
            disabled={quantity >= currentItem.canSynthesize}>
            +
          </QuantityButton>
        </QuantityControl>
      )}
    </Container>
  );
};

export default SynthesisContent;
