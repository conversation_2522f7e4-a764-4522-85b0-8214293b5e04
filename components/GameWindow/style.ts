import styled from 'styled-components';

export const GameWindowView = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  position: relative;

  #render-target-game {
    flex: 1;
    height: 100%;
    width: 100%;
    background: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
  }
  .controlKeys {
    position: absolute;
    width: 20rem;
    left: 50%;
    margin-left: -10rem;
    bottom: 13%;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-drag: none;
    -webkit-user-select: none;
    -ms-user-select: none;
  }
  & > .avatarPageView {
    & > .menuView {
      left: 11.125rem;
      top: 50% !important;
      border-radius: 2rem;
      transform: translate(0, -50%) !important;
      width: 19rem;
      height: 39.5rem;

      .top-menu-tabs {
        gap: 0.75rem;
        padding: 0.5rem;
        box-sizing: border-box;
        overflow-y: hidden;
        height: 3.5rem;

        & > .menu-tab {
          width: 3rem;
          min-width: 3rem;
          height: 2.5rem;
          border-radius: 0.75rem;
          & > img {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
        &::-webkit-scrollbar {
          width: 0.5rem;
          height: 0.5rem;
          border-radius: 0.5rem;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 0.5rem;
        }
      }
      & > .menuContentView {
        padding: 0.5rem;

        box-sizing: border-box;
        height: calc(100% - 3.5rem);

        & > .select-spin {
          gap: 1rem;
          & > div {
            height: 7.5rem;
            border-radius: 1.25rem;
            border: 0.0625rem solid #cabfab;
            &.active {
              box-shadow: none;
              border: 0.25rem solid #ff8316;
            }
          }
        }
        &::-webkit-scrollbar {
          width: 0.5rem;
          height: 0.5rem;
          border-radius: 0.5rem;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 0.5rem;
        }
      }
      & > .right-menu-tabs {
        gap: 0.5rem;
        & > .right-menu-item {
          z-index: 1;
          overflow: hidden;
          position: relative;
          &:not(.active) {
            ::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: url('/image/bag/tabBg.png') lightgray 50% / cover no-repeat;
              opacity: 0.2;
              z-index: -1;
            }
          }

          .arrow {
            width: 0.625rem;
            height: 0.625rem;
            margin-left: -0.03125rem;
          }
          .right-icon {
            width: 3rem;
            height: 3rem;
          }
        }
      }
    }
    & > .infoPanelBox {
      right: 11.125rem;
      transform: translate(0, -50%);
      & .infoPanelView {
        width: 19.25rem;
      }
      & .infoPanelEditView {
        width: 15.875rem;
      }
      & .infoPanelView,
      .infoPanelEditView {
        backdrop-filter: blur(0.25rem);
        padding: 1.5rem;
      }
      & .infoPanelView {
        & > h2 {
          font-size: 1.375rem;
          line-height: 1.625rem;
        }
        & > .info-panel-content {
          margin-top: 1.5rem;
          gap: 0.5rem;
          & > p {
            font-size: 1rem;
            line-height: 1.25rem;
          }
        }
      }
      & .infoPanelEditView {
        & > .set-title {
          gap: 0.625rem;
          & > div {
            & > svg {
              width: 1.5rem;
              height: 1.5rem;
            }
          }
          & > span {
            font-size: 1.125rem;
            line-height: 1.36125rem;
          }
        }
        & > .set-panel-box {
          margin-top: 0.5rem;
          gap: 1rem;
          & > .color-set,
          .texture-set {
            gap: 0.5rem;
            & > span {
              font-size: 1rem;
              line-height: 1.25rem;
            }
            & > .empty-box {
              width: 1.75rem;
              height: 1.75rem;
              border-radius: 0.25rem;
              &.active {
                border: 0.0625rem solid #140f08;
              }
              & > .empty-icon {
                & > svg {
                  width: 1.25rem;
                  height: 1.25rem;
                }
              }
            }
            & > .eyes-icon {
              & > svg {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
          }

          & > .texture-set > .texture-value {
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 0.25rem;
            & > img {
              width: 1.25rem;
              height: 1.25rem;
            }
          }
          & > .color-set > .color-value {
            height: 1.75rem;
            gap: 0.25rem;
            border-radius: 0.25rem;
            padding: 0.25rem;
            border-radius: 0.25rem;
            border: none;
            background: rgba(255, 255, 255, 0.8);
            & > div {
              width: 1.25rem;
              height: 1.25rem;
              border-radius: 0.125rem;
            }
            & > span {
              font-size: 1.25rem;
              line-height: 1.25rem;
            }
          }
        }
      }
      & .selectColorPanelView {
        width: 19rem;
        &.active {
          max-height: 31.25rem;
        }
        & > .select-color-panel {
          padding: 1.125rem;

          & > .react-colorful {
            height: 12.5rem;

            & .react-colorful__pointer {
              width: 1.5rem;
              height: 1.5rem;
              border: 0.375rem solid #fff;

              /* box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2); */
            }

            & > .react-colorful__saturation {
              box-shadow: inset 0 0 0 0.0625rem rgba(0, 0, 0, 0.05);
              border-bottom: none;
              border-radius: 0;
            }
            & > .react-colorful__hue {
              /* border-radius: 0 0 0.5rem 0.5rem; */
              height: 1.625rem;
            }
          }
          & > .select-color-btn {
            margin-top: 0.625rem;
            height: 1.75rem;
            border: 0.0625rem solid #140f08;
            border-radius: 1rem;
            box-sizing: border-box;
            padding: 0.25rem;
            & > div {
              height: 1.25rem;
              width: 1.25rem;
            }
            & > span {
              font-size: 1.25rem;
            }
          }
          & > .action-btns {
            grid-gap: 1rem;
            margin-top: 0.625rem;
            & > div {
              height: 2.25rem;
              border-radius: 1rem;
              & > div svg {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
          }
        }
      }
      & .selectTexturePanelView {
        width: 19rem;
        &.active {
          max-height: 31.25rem;
        }
        & > .select-texture-panel {
          padding: 1.125rem;

          & > h2 {
            font-size: 1rem;
            line-height: 120%;
          }
          & > input {
            margin-top: 0rem;
            height: 3rem;
            font-size: 1.25rem;
            line-height: 1.5rem;
            padding: 0 1rem;
            border-radius: 1rem;
            border: 0.0625rem solid rgba(20, 15, 8, 0.4);
          }
          & > .texture-show {
            margin-top: 1rem;
            height: 16rem;
            & > img {
              width: 12.5rem;
            }
          }
          & > .action-btns {
            grid-gap: 1rem;
            margin-top: 0.625rem;
            & > div {
              height: 2.25rem;
              border-radius: 0.25rem;
              & > div svg {
                width: 1.5rem;
                height: 1.5rem;
              }
            }
          }
        }
      }
    }
  }
`;
