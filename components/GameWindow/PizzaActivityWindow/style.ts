import styled from 'styled-components';

// 设置蒙层
export const Mask = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
`;

export const StyledTimeLeft = styled.div<{ $bgSrc?: string }>`
  width: 25.75rem;
  height: 14.375rem;
  font-size: 1.25rem;
  position: relative;
  color: #542d00;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 3.75rem;
  gap: 0.3125rem;
  font-family: 'JetBrains Mono';
  pointer-events: auto;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(${(props) => props.$bgSrc}) no-repeat center center;
    background-size: contain;
    z-index: -1;
  }
`;

export const PizzaActivityWindowView = styled.div`
  font-family: 'JetBrains Mono';
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  //height: 100vh;
  top: 5%;
  left: 40%;

  .start {
    &::after {
      background: url('/image/pizza/start-1.png') no-repeat center center;
      background-size: contain;
      z-index: 11;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: '';
      width: 22.5rem;
      height: 22.5rem;
    }

    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
    }
  }

  .time-up {
    &::after {
      background: url('/image/pizza/time-up.png') no-repeat center center;
      background-size: contain;
      z-index: 11;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: '';
      width: 22.5rem;
      height: 22.5rem;
    }

    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10;
    }
  }
`;
