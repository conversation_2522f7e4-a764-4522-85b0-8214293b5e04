import { NpcConfig } from '@/world/Config/NpcConfig';
import * as THREE from 'three';
import { AppGameApiKey, useMyPlayer } from '@/world/Character/MyPlayer';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey } from '@/world/Global/GlobalSpaceEvent';
import { LoadingPageType } from '@/world/Config/DoorConfig';
import { SCENE_TYPE } from '@/constant/type';
import toast from 'react-hot-toast';
import { StyledTimeLeft } from './style';
import { Time } from '@/components/TimeLeft';

interface PizzaRushProps {
  pizzaRushSecond: number;
}

const PIZZA_NPC_ID = 2003;

const PizzaRush = ({ pizzaRushSecond }: PizzaRushProps) => {
  const myPlayer = useMyPlayer();
  const onClickMenu = () => {
    const npcId = PIZZA_NPC_ID;
    if (npcId) {
      NpcConfig.getInstance().getData(npcId, (data) => {
        const transformPos = new THREE.Vector3(
          data.transformPosition[0],
          data.transformPosition[1] + 1,

          data.transformPosition[2]
        );
        const direction = transformPos
          .clone()
          .sub(new THREE.Vector3(data.position[0], data.position[1] + 1, data.position[2]));
        myPlayer.callAppApi(AppGameApiKey.setLoaderType, LoadingPageType.Default);

        GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
          characterType: CharacterType.Player,
          position: transformPos,
          sceneType: data.transformMapId as SCENE_TYPE,
          camDirection: direction,
        });
      });
    } else {
      toast.error('No npc configured');
    }
  };

  return (
    <StyledTimeLeft onClick={onClickMenu} $bgSrc="/image/pizza/rush.png">
      <span>Begins in</span>
      <Time time={pizzaRushSecond} />
    </StyledTimeLeft>
  );
};

export default PizzaRush;
