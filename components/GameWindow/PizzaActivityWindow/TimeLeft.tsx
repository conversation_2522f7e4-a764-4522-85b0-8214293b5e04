import { Time } from '@/components/TimeLeft';

import { StyledTimeLeft } from './style';

interface TimeLeftProps {
  timeLeftSecond: number;
}

const TimeLeft = ({ timeLeftSecond }: TimeLeftProps) => {
  return (
    <StyledTimeLeft $bgSrc="/image/pizza/time-left.png" className="time-left">
      <Time time={timeLeftSecond} isTimeLeft={true} />
    </StyledTimeLeft>
  );
};

export default TimeLeft;
