import styled from 'styled-components';

export const GameOpWindowView = styled.div`
  font-family: 'JetBrains Mono';
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  //height: 100vh;
  margin: 0;

  .currency-box {
    display: flex;
    flex-direction: column;
    gap: 0.9375rem;
    & > .currency-box-btns-box {
      cursor: pointer;
      user-select: none;
      width: 29.25rem;
      height: 3.75rem;
      padding: 0.1875rem;
      box-sizing: border-box;
      border-radius: 1.125rem;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.4) 80%, rgba(255, 255, 255, 0) 95%);

      @keyframes opacity-ani {
        0% {
          opacity: 0;
          transform: translateX(6.25rem);
        }
        100% {
          opacity: 1;
          transform: translateX(0);
        }
      }
      animation: opacity-ani 0.2s ease-in-out;
      animation-fill-mode: none;
      & > .currency-box-btns {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border-radius: 1.125rem;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0.8) 60%,
          rgba(255, 255, 255, 0) 95%
        );
        & > .key-f {
          width: 2.625rem;
          height: 2.625rem;
          box-shadow: 0px 0px 1.125rem 0px #00000080;
          background: #140f08;
          border-radius: 0.375rem;
          font-family: 'JetBrains Mono';
          font-size: 1.125rem;
          font-weight: 700;
          line-height: 0.9375rem;
          letter-spacing: -0.05em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #ffffff;
          margin-left: -1.6875rem;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.1s linear;
        }
        & > img.key-icon {
          width: 2rem;
          height: 2rem;
        }

        & > .key-f-hide {
          width: 2.625rem;
          height: 2.625rem;
          border-radius: 0.375rem;
          margin-left: -1.6875rem;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.1s linear;
        }
        & > span {
          font-family: 'JetBrains Mono';
          font-size: 1.3125rem;
          font-weight: 400;
          line-height: 1.3125rem;
          letter-spacing: -0.05em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #140f08;
        }
        & > p {
          font-family: 'JetBrains Mono';
          font-size: 0.9375rem;
          font-weight: 400;
          line-height: 0.9375rem;
          letter-spacing: -0.05em;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #686663;
        }
      }
      &.disabled {
        cursor: not-allowed;
        & > .currency-box-btns {
          & > .key-f {
            background: #686663;
          }
          & > span {
            color: #686663;
          }
        }
      }
      &:not(.disabled):hover {
        & > .currency-box-btns {
          & > .key-f {
            background: #ff8316;
            box-shadow: 0px 0px 2.5rem 0px #ff8316;
          }
        }
      }
    }
  }
`;
