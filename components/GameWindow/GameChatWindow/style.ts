import styled from 'styled-components';

export const GameChatWindowView = styled.div`
  width: 588px;
  position: absolute;
  left: 50%;
  bottom: 32px;
  transform: translateX(-50%);
  display: flex;

  .history-btn {
    align-self: flex-end;
    width: 72px;
    height: 72px;
    cursor: pointer;
  }

  .question-box {
    flex: 1;

    .question-history {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      & > span {
        padding: 9px 16px;
        word-break: break-all;
        font-family: Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 14.52px;
        color: #ffffff;
        background: #795f49;
        border-radius: 12px;
        cursor: pointer;
      }
    }

    .question-ask {
      margin-top: 8px;
      display: flex;
      align-items: center;
      background: #ffffff;
      border: 1px solid #ac9d83;
      box-shadow: 0px 4px 24px 0px #00000059;
      border-radius: 24px;
      padding: 16px;
      box-sizing: border-box;

      .question-ask-input {
        flex: 1;
        overflow: hidden;

        & > input {
          width: 100%;
          height: 100%;
          padding: 0;
          outline: none;
          border: 0;
          font-family: Inter;
          font-weight: 400;
          font-size: 18px;
          line-height: 21.78px;
          color: #140f08;

          &::placeholder {
            color: #686663;
          }
        }
      }

      button {
        width: 40px;
        height: 40px;
        outline: none;
        border-radius: 10px;
        border: 0;
        background: #ff8316;
        box-shadow: 0px -2.5px 0px 0px #00000040 inset;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        & > img {
          width: 20px;
          height: 20px;
        }

        &[disabled] {
          cursor: not-allowed;
          background: #c9b7a5;
          box-shadow: 0px -2.5px 0px 0px #00000040 inset;
        }

        &.loading {
          & > img {
            animation: loading-ano 1s infinite;
          }
        }
      }
    }
  }
`;
