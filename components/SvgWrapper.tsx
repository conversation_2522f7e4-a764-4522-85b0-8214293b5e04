import styled from 'styled-components';

const SvgWrapper = styled.span`
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const StyledSvg = styled.svg`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SVG_ICON_PREFIX = 'svgIcon_';
/**
 *
 * @param id 对应sprite.svg 中的id
 * @returns
 */
export const SpriteSvg = ({ id }: { id: string }) => {
  return (
    <StyledSvg>
      <use href={`./image/sprite.svg#${SVG_ICON_PREFIX}${id}`} />
    </StyledSvg>
  );
};

export default SvgWrapper;
