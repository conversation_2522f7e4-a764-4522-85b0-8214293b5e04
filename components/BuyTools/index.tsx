import { forwardRef, Ref, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Dialog from '@/commons/Dialog';
import Image from 'next/image';
import styled from 'styled-components';
import { motion } from 'motion/react';
import { getBuyEnergyInfo, payConfirmSend, prePay } from '@/server';
import SignMessage from '@/utils/signMessage';
import toast from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { setUserBasicInfo } from '@/store/app';
import { AppGameApiKey, useMyPlayer } from '@/world/Character/MyPlayer';
import { KeyPressUtil } from '@/world/Global/GlobalKeyPressUtil';
import BasicCommunityModalContent from '../BasicCommunityModalContent';

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 2rem;
`;

const MenuContent = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.25rem;

  box-sizing: border-box;

  border-radius: 2rem;
  background: #f7e7cd;
  box-shadow: 0px 0px 0.5rem 0px rgba(0, 0, 0, 0.25) inset;
`;

const TextContainer = styled.div`
  margin-right: auto;
  padding-left: 2.5rem;

  color: #140f08;

  font-size: 1.125rem;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: -0.048rem;
`;

const List = styled.ul`
  list-style: none;
  padding: 0 1rem;
  margin: 0;
  padding: 0 1rem 0 1.75rem;
  & > li {
    /* display: inline-flex; */
    /* align-items: center; */
    /* flex-wrap: wrap; */
    padding-left: 0.5rem;
    list-style-type: '*';

    color: #140f08;
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    & > p {
      margin: 0;
      display: inline-flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }
`;

interface ModalProps {
  onClose?: () => void;
}

interface BuyEnergyData {
  payAmount: string;
  addressTickBalance: string;
  enough: boolean;
  dailyBuyTotalCount: number; // 每日总限量数
  dailyCurrentBuyCount: number; // 当前已消耗的次数
  energy: number;
}

export interface BuyEnergyRef {
  open: (
    data: BuyEnergyData,
    communityType: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks',
    singleType: string
  ) => void;
}

const communityText = `After purchase confirmation:`;

const StyledSvgWrapper = styled.span`
  display: inline-flex;
  align-items: center;
  svg {
    display: inline-flex;
    width: 1.25rem;
    height: 1.25rem;
    justify-content: center;
    align-items: center;
  }
`;
const EnergySvg = () => {
  return (
    <StyledSvgWrapper>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="11"
        height="16"
        viewBox="0 0 11 16"
        fill="none">
        <path
          d="M6.90728 1.20834C6.9531 1.04178 6.95978 0.861176 6.90481 0.694473C6.84995 0.52784 6.73992 0.38987 6.593 0.307602C6.44607 0.225334 6.27093 0.203629 6.1002 0.243951C5.92935 0.284204 5.77886 0.384287 5.6608 0.510405C5.43483 0.753004 5.21169 0.997189 4.99139 1.24296C3.6116 2.78224 2.34295 4.38374 1.18544 6.04747L0.0807691 7.63564L2.10406 8.74292C3.47304 9.49232 4.87166 10.1723 6.29994 10.7828L5.36649 7.61099C5.36212 7.61744 5.35775 7.6239 5.35337 7.63036C4.10968 9.46821 3.16673 11.5096 2.52452 13.7545C2.43131 14.0803 2.34444 14.4104 2.2639 14.7448C2.22521 14.9084 2.21323 15.0824 2.25607 15.2457C2.29884 15.4089 2.39168 15.5471 2.5232 15.6361C2.65471 15.7251 2.81757 15.7599 2.98496 15.7389C3.15243 15.718 3.30948 15.6422 3.44703 15.5454C3.72751 15.3463 4.00165 15.1429 4.26945 14.9353C6.11463 13.5045 7.65907 11.8701 8.90276 10.0323C8.90714 10.0258 8.9115 10.0194 8.91587 10.0129L10.3715 7.85955L7.98243 6.84112C6.55356 6.23203 5.09504 5.6924 3.60688 5.22223L4.5255 7.91768C5.33899 6.06132 6.04135 4.14273 6.63258 2.16192C6.72698 1.84565 6.81854 1.52779 6.90728 1.20834Z"
          fill="#F8B81D"
        />
      </svg>
    </StyledSvgWrapper>
  );
};

const BuyEnergy = forwardRef<BuyEnergyRef, ModalProps>(
  ({ onClose = () => false }: ModalProps, ref: Ref<BuyEnergyRef>) => {
    const dispatch = useDispatch();
    const { btcWallet, userBasicInfo } = useSelector(
      (state: { AppReducer: IAppState }) => state.AppReducer
    );
    const [isMounted, setIsMounted] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [amount, setAmount] = useState<string>('');
    const [energy, setEnergy] = useState<number>(0);
    const [enough, setEnough] = useState<boolean>(false); // 判断当前用户是否有足够余额购买道具
    const [communityType, setCommunityType] = useState<
      'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks'
    >('potato');
    // 判断是否达到购买上限
    const isOverLimitRef = useRef(false);

    const [confirmLoading, setConfirmLoading] = useState(false);

    useEffect(() => {
      setIsMounted(true);
      return () => setIsMounted(false);
    }, []);

    useImperativeHandle(ref, () => ({
      open: (data: BuyEnergyData, communityType, singleType: string) => {
        setCommunityType(communityType);
        isOverLimitRef.current = data.dailyBuyTotalCount <= data.dailyCurrentBuyCount;
        setAmount(data.payAmount);
        setEnough(data.enough);
        setEnergy(data.energy ?? 0);
        setIsOpen(true);
        KeyPressUtil.setEnable(false);
      },
    }));

    const onCloseModal = () => {
      setIsOpen(false);
      setAmount('');
      setEnergy(0);
      setEnough(false);
      setConfirmLoading(false);
      isOverLimitRef.current = false;
      KeyPressUtil.setEnable(true);

      onClose();
    };

    const onConfirm = async () => {
      try {
        setConfirmLoading(true);
        // 1.发起预支付，获取需要签名的信息以及需要发送购买接口的信息
        const result = await prePay(communityType);
        const { code, msg, data } = result.data;
        if (code === 1) {
          const { params, orderId, signMsgs } = data;
          if (signMsgs.length > 0) {
            const sigs = await SignMessage.multiSignMessage(btcWallet, signMsgs);
            const buyParams = {
              ...params,
              sigs,
            };
            // 3. 发送购买接口
            const buyRes = await payConfirmSend(buyParams, {
              orderId,
            });
            const { code, msg, data } = buyRes.data;
            if (code === 1) {
              toast.success('Purchase Successfully!');
              onCloseModal();
              if (userBasicInfo) {
                const dailyCurrentBuyCount = userBasicInfo?.toolConfig!.dailyCurrentBuyCount + 1;

                const toolConfig = {
                  ...userBasicInfo.toolConfig,
                  dailyCurrentBuyCount,
                };
                //更新一下显示数量
                dispatch(
                  setUserBasicInfo({
                    ...userBasicInfo,
                    toolConfig: toolConfig as any,
                  })
                );
              }
            } else {
              toast.error(msg);
              setConfirmLoading(false);
            }
          }
        } else {
          toast.error(msg);
          setConfirmLoading(false);
        }
      } catch (error) {
        console.error(error);
        setConfirmLoading(false);
      }
    };

    // 1.用户是否有足够的余额购买道具
    // 2.用户是否达到每日购买上限
    const isDisabled = useMemo(() => {
      return !enough || isOverLimitRef.current;
    }, [enough, isOverLimitRef.current]);

    if (!isMounted) {
      return null;
    }

    return (
      <Dialog isOpen={isOpen} onClose={onCloseModal}>
        <BasicCommunityModalContent
          confirmLoading={confirmLoading}
          confirmText="Buy"
          onConfirm={onConfirm}
          onClose={onCloseModal}
          confirmDisabled={isDisabled || confirmLoading}
          onCancel={onCloseModal}
          title={
            <StyledTitle>
              <Image
                src="/image/buy-bg.png"
                alt="buy-tools"
                width={360}
                height={64}
                draggable={false}
              />
              <p data-text={'Buy Energy'}>Buy Energy</p>
            </StyledTitle>
          }>
          <ContentContainer>
            <TextContainer>{communityText}</TextContainer>
            <MenuContent
              initial={{ y: 50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}>
              <List>
                <li>
                  <p>
                    A Pizzaswap transaction for {amount} ${communityType} will be executed.
                  </p>
                </li>
                <li>
                  <p>
                    {`You'll receive ${energy}`}
                    <EnergySvg /> Energy upon success.
                  </p>
                </li>
                <li>
                  <p>Spent ${communityType} will be deposited into the Community Pool for Drop</p>
                </li>
              </List>
            </MenuContent>
          </ContentContainer>
        </BasicCommunityModalContent>
      </Dialog>
    );
  }
);

BuyEnergy.displayName = 'BuyEnergy';

const StyledTitle = styled.div`
  position: relative;
  width: 22.5rem;
  height: 4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  & > img {
    position: absolute;
    top: 0;
    display: block;
    left: 0;
    width: 22.5rem;
    height: 4rem;
  }
  & > p {
    z-index: 1;
    color: #fff;
    margin: 0;
    text-align: center;
    text-shadow: 0.0625rem 0.125rem 0px #000b22;
    font-family: 'Baloo 2';
    font-size: 2.5rem;
    font-style: normal;
    font-weight: 800;

    line-height: 100%;
    text-transform: capitalize;
    position: relative;

    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 0.046875rem #4b2800;
      text-stroke: 0.046875rem #4b2800;
      z-index: -1;
      left: 0;
    }
  }
`;

export default BuyEnergy;

export function useBuyEnergy() {
  const myPlayer = useMyPlayer();
  const buyEnergyRef = useRef<BuyEnergyRef>(null);

  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.buyEnergy,
        (
          buyCommunity: 'potato' | 'wangcai' | 'TheLonelyBit' | 'Pizza' | 'DomoDucks',
          singleType: string
        ) => {
          const open = async () => {
            const res = await getBuyEnergyInfo(buyCommunity);
            const { code, msg, data } = res.data;
            if (code === 1) {
              buyEnergyRef.current?.open(data, buyCommunity, singleType);
            } else {
              console.error(msg);
            }
          };
          open().then();
        }
      );
    }
  }, []);

  return buyEnergyRef;
}
