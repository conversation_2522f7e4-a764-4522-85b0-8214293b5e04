import Dialog from '@/commons/Dialog';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import rewardBg from '/public/image/reward-bg.png';
import rewardTitle from '/public/image/claim-rewards.png';
// import rewardAxe1 from "/public/image/rewardsAxe-1.png";
// import potato from "/public/image/wangcai.png";
import Image from 'next/image';
import Star1 from '/public/image/star1.svg';
import Star2 from '/public/image/star2.svg';
import { IAppState, RewardType } from '@/constant/type';
import { useAppDispatch, useAppSelector } from '@/hooks/useStore';
import { setEasterEggReward } from '@/store/app';
import {
  Container,
  StyledIcon,
  Items,
  RewardNumber,
  ShakeButton,
  StyledAxeImageBox,
  StyledDialogBody,
  StyledImage,
  StyledMainTitle,
  StyledRewardsContent,
  StyledRewardsContentText,
  StyledRewardTitle,
  StyledStar1,
  StyledStar2,
  StyledStarGroup,
  StyledSubTitle,
} from './styles';
import { AppGameApiKey, GetMyPlayer, useMyPlayer } from '@/world/Character/MyPlayer';
import { COMMUNITY_ICON_MAP } from '@/constant';
import { px2rem } from '@/utils/px2rem';

export interface RewardsProps {
  onCloseCallback?: () => void;
  onClaimReward?: () => void;
}

export interface IEasterEggRewardRef {
  open: (config: IEasterEggRewardOpenConfig) => void;
  close: () => void;
  setLoading: (loading: boolean) => void;
}

interface IEasterEggRewardInfo {
  quantity: string;
  name: string;
  tag: string;
}

type ModalStyleType = 'pickaxes' | 'axe';

export interface IEasterEggRewardOpenConfig {
  mainTitle?: React.ReactNode;
  subTitle?: React.ReactNode;
  confirmText?: string;
  contentPaddingTop?: number;
  contentPaddingBottom?: number;
  contentPaddingLeft?: number;
  contentPaddingRight?: number;
  modalStyleType?: ModalStyleType;

  quantity?: number;
  tag?: string;
  eventType?: string;
}

const defaultModalConfig: IEasterEggRewardOpenConfig = {
  mainTitle: 'Hidden Challenge',
  subTitle: 'ChopMasterSequence',
  confirmText: 'Claim',
  contentPaddingTop: 74,
  contentPaddingRight: 31,
  contentPaddingBottom: 53,
  contentPaddingLeft: 23,
  modalStyleType: 'axe',
};

const titleBgImageConfig: Record<ModalStyleType, string> = {
  axe: '/image/axe_1.png',
  pickaxes: '/image/pickaxes_1.png',
};

const EasterEggReward = forwardRef<IEasterEggRewardRef, RewardsProps>((props, ref) => {
  const { onCloseCallback, onClaimReward } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [modalConfig, setModalConfig] = useState<IEasterEggRewardOpenConfig>(defaultModalConfig);

  // 因为打地鼠彩蛋，在成功动画关闭之后才打开这个弹窗，所以才混合使用了 redux
  const { easterEggReward } = useAppSelector((state) => state.AppReducer);
  const dispatch = useAppDispatch();

  useImperativeHandle(ref, () => ({
    open: (config: IEasterEggRewardOpenConfig) => {
      setIsOpen(true);
      setModalConfig(() => {
        return {
          mainTitle: 'Hidden Challenge',
          contentPaddingTop: 74,
          contentPaddingRight: 31,
          contentPaddingBottom: 53,
          contentPaddingLeft: 23,
          confirmText: 'Claim',
          ...config,
        };
      });
    },
    close: () => setIsOpen(false),
    setLoading: (loading: boolean) => setIsLoading(loading),
  }));

  const onShare = () => {
    setIsOpen(false);
    props.onCloseCallback?.();
  };

  const [communityIcon, quantity] = useMemo(() => {
    const modalConfigTag = modalConfig.tag;
    if (modalConfigTag) {
      return [COMMUNITY_ICON_MAP[modalConfigTag as RewardType], modalConfig.quantity];
    } else {
      if (easterEggReward?.tag) {
        return [COMMUNITY_ICON_MAP[easterEggReward.tag as RewardType], easterEggReward?.quantity];
      }
    }
    return ['', '-'];
  }, [easterEggReward, modalConfig]);

  return (
    <Dialog
      isOpen={isOpen}
      onClose={() => {
        setIsOpen(false);
        onCloseCallback?.();
        dispatch(setEasterEggReward(null));
        setModalConfig({ ...defaultModalConfig });
      }}>
      <StyledDialogBody>
        <StyledAxeImageBox>
          <StyledImage
            src={titleBgImageConfig[modalConfig.modalStyleType || 'axe']}
            alt="axe"
            width={272}
            height={553}
            draggable={false}
          />
          <StyledImage
            src={titleBgImageConfig[modalConfig.modalStyleType || 'axe']}
            alt="axe"
            width={272}
            height={553}
            $turnOver={true}
            draggable={false}
          />
        </StyledAxeImageBox>
        <StyledRewardTitle>
          <StyledMainTitle>{modalConfig.mainTitle}</StyledMainTitle>
          <StyledSubTitle data-text={modalConfig.subTitle}>{modalConfig.subTitle}</StyledSubTitle>
        </StyledRewardTitle>
        <StyledStar1
          src={Star1}
          alt="star1"
          width={60}
          height={60}
          style={{
            width: '3.75rem',
            height: '3.75rem',
          }}
        />
        <StyledStar2
          src={Star2}
          alt="star2"
          width={34}
          height={34}
          style={{
            width: '2.125rem',
            height: '2.125rem',
          }}
        />
        <Container
          style={{
            paddingTop: `${px2rem(modalConfig.contentPaddingTop ?? 0)}rem`,
            paddingRight: `${px2rem(modalConfig.contentPaddingRight ?? 0)}rem`,
            paddingBottom: `${px2rem(modalConfig.contentPaddingBottom ?? 0)}rem`,
            paddingLeft: `${px2rem(modalConfig.contentPaddingLeft ?? 0)}rem`,
          }}>
          <StyledRewardsContent>
            <StyledRewardsContentText>
              <p className="rewards-content-item">
                Congratulations on completing
                <br />
                the Hidden Gameplay -
              </p>
              <p className="rewards-content-item highlight">Everything is in Order.</p>
              <p className="rewards-content-item">You can get the following rewards:</p>
            </StyledRewardsContentText>

            <RewardNumber>
              <Items>
                <StyledIcon>
                  <Image
                    src={communityIcon}
                    alt=""
                    width={56}
                    height={56}
                    className="icon-image"
                    style={{
                      width: '3.5rem',
                      height: '3.5rem',
                    }}
                    draggable={false}
                  />
                </StyledIcon>
                <div className="value">{quantity}</div>
              </Items>
            </RewardNumber>
            <ShakeButton text={modalConfig.confirmText} onClick={onShare} loading={isLoading} />
          </StyledRewardsContent>
        </Container>
        <StyledStarGroup />
      </StyledDialogBody>
    </Dialog>
  );
});

EasterEggReward.displayName = 'EasterEggReward';

export default EasterEggReward;

export function useEasterEggReward() {
  const easterEggRewardRef = useRef<IEasterEggRewardRef>(null);
  const myPlayer = useMyPlayer();
  useEffect(() => {
    if (myPlayer) {
      myPlayer.setAppApi(
        AppGameApiKey.showEasterEggRewards,
        (config: IEasterEggRewardOpenConfig) => {
          easterEggRewardRef.current?.open(config);
        }
      );
    }
  }, [myPlayer]);

  return easterEggRewardRef;
}
