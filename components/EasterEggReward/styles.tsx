import styled, { CSSProperties } from 'styled-components';
import { motion } from 'framer-motion';
import Image from 'next/image';

export const StyledDialogBody = styled.div`
  position: relative;
  width: 46.5rem;
  height: 27.5rem;
  filter: drop-shadow(0rem 0rem 4.5rem #ff9d00);
`;

interface ShakeButtonProps {
  text: string;
  onClick?: () => void;
  className?: string;
  loading?: boolean;
  style?: CSSProperties;
}

const StyledShakeButton = styled(motion.button)<{ $isLoading?: boolean }>`
  color: white;
  align-items: center;
  cursor: ${(props) => (props.$isLoading ? 'not-allowed' : 'pointer')};
  margin: 0;

  border-radius: 1rem;
  border: 0.0625rem solid #5b402d;

  width: 11.75rem;
  height: 3.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'JetBrains Mono';
  background-image: url('/image/eventBtn.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: 0rem -0.25rem 0rem 0rem #00000040 inset;

  span {
    display: flex;
    height: 3.75rem;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
  }
  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.3125rem solid #fff;
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
  }

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const initialVariant = {
  rotate: 0,
  transition: {
    duration: 0.15,
    ease: 'circOut',
  },
};

export const ShakeButton = ({
  text = 'Send',
  onClick,
  className,
  loading = false,
  style = {},
}: ShakeButtonProps) => {
  return (
    <StyledShakeButton
      initial={initialVariant}
      animate={initialVariant}
      whileHover={
        loading
          ? initialVariant
          : {
              rotate: [0, -5, 5, -5, 5, 0],
              transition: {
                duration: 0.5,
                repeat: Infinity,
                repeatType: 'loop',
              },
            }
      }
      onClick={loading ? undefined : onClick}
      className={className}
      $isLoading={loading}
      disabled={loading}
      style={style}>
      {loading ? <div className="loading-spinner"></div> : <span>{text}</span>}
    </StyledShakeButton>
  );
};

export const Container = styled.div`
  position: relative;
  width: 46.5rem;
  height: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  border-radius: 4rem;
  border: 0.625rem solid #ff8316;
  background: #f6e9dd;
  box-shadow: 0rem 0rem 0.25rem 0rem #ffb43d;
  overflow: hidden;
  /* filter: drop-shadow(0rem 0rem 4.5rem #ff9d00); */

  .rewards-content {
    background: #f6e9dd;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: start;
    padding-top: 10%;
    flex-direction: column;
    gap: 0.375rem;

    .following {
      text-shadow: none;
      width: 100%;
      -webkit-text-stroke: 0rem;
    }
  }
`;

export const StyledRewardsContent = styled.div`
  background: #f6e9dd;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: start;
  flex-direction: column;
  gap: 1.5rem;
`;
export const StyledRewardsContentText = styled.div`
  & > p {
    color: #140f08;
    margin: 0;
    padding: 0;
  }

  .rewards-content-item {
    font-family: JetBrains Mono;
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 120%;
    letter-spacing: -0.04em;
    text-align: center;
    vertical-align: middle;
    &.highlight {
      font-weight: 800;
      font-size: 1.75rem;
      color: #ff8316;
    }
  }
`;

export const RewardNumber = styled.div`
  border-radius: 1.875rem;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.25rem 0rem;
  box-sizing: border-box;
  width: 80%;
  height: 9.375rem;

  border: none;
  outline: none;
  gap: 0.625rem;
`;

export const StyledIcon = styled.div`
  display: flex;
  align-items: center;
  .icon-image {
    border-radius: 50%;
    left: -0.625rem;
    position: relative;
  }
`;

export const Items = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-shadow: 0rem 0.25rem 0.125rem 0rem #00000040 inset;
  background: #cabfab;
  border-radius: 1rem;
  height: 3rem;
  box-sizing: border-box;
  position: relative;

  .value {
    color: #fff;
    padding-right: 0.625rem;
    margin-left: auto;
    text-align: right;
    /* -webkit-text-stroke: 0.0625rem #a58061; */
    filter: drop-shadow(0.0625rem 0.0625rem 0rem #a58061)
      drop-shadow(-0.0625rem 0.0625rem 0rem #a58061) drop-shadow(0.0625rem -0.0625rem 0rem #a58061)
      drop-shadow(-0.0625rem -0.0625rem 0rem #a58061);
    font-size: 1.875rem;
    font-style: normal;
    font-weight: 800;
    line-height: 100%;
  }
`;

export const StyledMainTitle = styled.p`
  margin: 0;
  color: #ffffff;
  font-family: Bevan;
  font-weight: 400;
  font-size: 2.875rem;
  line-height: 100%;
  letter-spacing: -0.04em;
  text-align: center;
  vertical-align: middle;
  filter: drop-shadow(0.12625rem 0.2525rem 0rem #000b22);
`;

export const StyledSubTitle = styled.p`
  margin: 0;
  color: #ff8316;
  font-family: Inter;
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  margin-top: 0.23125rem;
  vertical-align: middle;
  /* filter: drop-shadow(0.230625rem 0.230625rem #4b2800) drop-shadow(-0.230625rem 0.230625rem #4b2800)
    drop-shadow(0.230625rem -0.230625rem #4b2800) drop-shadow(-0.230625rem -0.230625rem #4b2800); */
  /* -webkit-text-stroke: 0.230625rem #4b2800; */

  /* --borderWidth: 0.230625rem;
  --negBorderWidth: -0.230625rem;
  text-shadow:
    var(--negBorderWidth) var(--borderWidth) 0rem #4b2800,
    var(--borderWidth) var(--negBorderWidth) 0rem #4b2800,
    var(--negBorderWidth) var(--negBorderWidth) 0rem #4b2800,
    var(--borderWidth) var(--borderWidth) 0rem #4b2800; */
  position: relative;
  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 0.46125rem #4b2800;
    text-stroke: 0.46125rem #4b2800;
    z-index: -1;
    left: 0;
  }

  .text {
    filter: url(#shadow);
  }
`;

export const StyledRewardTitle = styled.div<{ $bgSrc?: string }>`
  width: 34rem;
  height: 7.5rem;
  background-image: url(${(props) => props.$bgSrc || '/image/easterEggReward_title_1.png'});
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: -6%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background-position: bottom;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  /* filter: drop-shadow(0rem -0.625rem 1.25rem #dea36e); */
`;

export const StyledImage = styled(Image)<{ $turnOver?: boolean }>`
  /* filter: drop-shadow(0rem 0rem 4.5rem #ff9d00); */
  width: 17.030875rem;
  height: 34.586625rem;
  transform: rotate(${(props) => (props.$turnOver ? 68.743 : -68.743)}deg)
    scaleX(${(props) => (props.$turnOver ? -1 : 1)});
  flex-shrink: 0;
  aspect-ratio: 272.49/553.39;
`;

export const StyledAxeImageBox = styled.div`
  z-index: -1;
  position: absolute;
  top: 2%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 8.875rem;
`;

export const StyledStar1 = styled(Image)`
  position: absolute;
  top: 10%;
  left: -4%;
  z-index: 1;
`;
export const StyledStar2 = styled(Image)`
  position: absolute;
  position: absolute;
  top: 25%;
  left: -6%;
  z-index: 1;
`;

// export const StyledTopLeftStarGroup =

export const StyledStarGroup = styled.div<{ $bgSrc?: string }>`
  position: absolute;
  bottom: -5%;
  right: -8%;
  background: url(${(props) => props.$bgSrc || '/image/start-group.png'});
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 10.375rem;
  height: 12rem;
`;
