import styled from 'styled-components';
import Image from 'next/image';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

const SlotListWrapper = styled.div`
  display: flex;
  gap: 0.625rem;
  flex-wrap: nowrap;
  overflow-x: auto;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
  padding: 0.5rem 0rem 0rem 0.5rem;
`;

const SlotItem = styled.div<{ $active?: boolean }>`
  width: 5rem;
  height: 5rem;
  border-radius: 1rem;
  background: ${(props) => (props.$active ? '#faf3e5' : ' #F8E5C6')};
  border: 0.125rem solid ${(props) => (props.$active ? '#FF8316' : '#A58061')};
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 auto;

  /* 删除按钮: 左上角 */
  .delete-btn {
    content: '';
    position: absolute;
    top: -0.375rem;
    left: -0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background: url('/image/submission/delete.png') no-repeat center center;
    background-size: 100% 100%;
    display: ${(props) => (props.$active ? 'block' : 'none')};
    cursor: pointer;
  }

  .item-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .item-image {
      width: 3.75rem;
      height: 3.75rem;
      object-fit: contain;
    }

    .item-count {
      position: absolute;
      bottom: 0.25rem;
      font-size: 0.75rem;
      color: #fff;
      background: rgba(0, 0, 0, 0.6);
      padding: 0.125rem 0.375rem;
      border-radius: 0.625rem;
    }
  }

  .empty-slot {
    font-size: 1.5rem;
    color: #a58061;
  }
`;

interface SlotListProps {
  materialList: any[];
  onRemove: (index: number) => void;
}

export interface SlotListRef {
  setSelectedItems: (selectedItems: any[]) => void;
}

const SlotList = forwardRef<SlotListRef, SlotListProps>((props, ref) => {
  const { materialList, onRemove } = props;
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const scrollRef = useRef<HTMLDivElement>(null);

  // 鼠标滚轮横向滚动
  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement) return;
    const handleWheel = (e: WheelEvent) => {
      if (e.deltaY === 0) return;
      e.preventDefault();
      scrollElement.scrollLeft += e.deltaY;
    };
    scrollElement.addEventListener('wheel', handleWheel, { passive: false });
    return () => {
      scrollElement.removeEventListener('wheel', handleWheel);
    };
  }, []);

  useImperativeHandle(ref, () => ({
    setSelectedItems: (selectedItems: any[]) => {
      setSelectedItems(selectedItems);
    },
  }));

  return (
    <SlotListWrapper ref={scrollRef}>
      {materialList.map((_, idx) => {
        const selected = selectedItems[idx];
        return (
          <SlotItem $active={!!selected} key={idx}>
            {selected ? (
              <>
                <div className="delete-btn" onClick={() => onRemove(selected.index)} />
                <div className="item-content">
                  <Image
                    src={selected.item.icon}
                    alt={selected.item.name}
                    width={60}
                    height={60}
                    style={{
                      width: '3.75rem',
                      height: '3.75rem',
                    }}
                  />
                  <span className="item-count">
                    {selected.value}/{selected.item.quantity}
                  </span>
                </div>
              </>
            ) : (
              <div className="empty-slot">+</div>
            )}
          </SlotItem>
        );
      })}
    </SlotListWrapper>
  );
});

SlotList.displayName = 'SlotList';

export default SlotList;
