import styled from 'styled-components';
import Image from 'next/image';
import Rank2 from '/public/image/rank2.png';
import Rank1 from '/public/image/rank1.png';
import Rank3 from '/public/image/rank3.png';
import Rank4 from '/public/image/rank4.png';
import Rank5 from '/public/image/rank5.png';
import { ConfigManager } from '@/world/Config/ConfigManager';
import { useEffect, useState } from 'react';

const CommunityInfoWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  gap: 1.25rem;
  .community-info-left {
    width: 6.875rem;
    height: 6.875rem;
    border: 0.1875rem solid #ff8316;
    border-radius: 50%;
  }
  .community-info-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 0.625rem;
    .community-info-right-top {
      font-size: 1.875rem;
      font-weight: bold;
      color: #000;
      font-weight: 900;
      text-shadow: 0 0 0.03125rem #000;
    }
    .community-info-right-bottom {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
`;

export const COMMUNITY_RANK_IMAGES = {
  potato: Rank2.src,
  wangcai: Rank1.src,
  TheLonelyBit: Rank3.src,
  Pizza: Rank4.src,
  DomoDucks: Rank5.src,
};

interface CommunityInfoProps {
  communityType?: keyof typeof COMMUNITY_RANK_IMAGES;
}

const TelegramButton = styled.div`
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  position: relative;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  background: #fff;
  border-radius: 1.875rem;

  &::before {
    content: '';
    position: absolute;
    top: -0.25rem;
    left: -0.25rem;
    right: -0.25rem;
    bottom: -0.25rem;
    background: #fc7922;
    border-radius: 2rem;
    z-index: -1;
  }

  &::after {
    content: '';
    position: absolute;
    top: -0.5rem;
    left: -0.5rem;
    right: -0.5rem;
    bottom: -0.5rem;
    background: #5a0002;
    border-radius: 2.125rem;
    z-index: -2;
  }

  .telegram-icon {
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:hover {
    transform: translateY(0.125rem);
  }
`;

type CommunityInfo = {
  twitter: string;
  link: string;
};

function CommunityInfo(props: CommunityInfoProps) {
  const { communityType } = props;
  const [communityInfo, setCommunityInfo] = useState<CommunityInfo | null>(null);
  useEffect(() => {
    if (communityType) {
      ConfigManager.getInstance().getData((data) => {
        setCommunityInfo(data.community[communityType]);
      });
    }
  }, [communityType]);
  return (
    <CommunityInfoWrapper>
      <div className="community-info-left">
        {communityType && (
          <Image
            src={communityType ? COMMUNITY_RANK_IMAGES[communityType] : ''}
            alt="community-info-left"
            width={110}
            height={110}
            style={{
              width: '6.875rem',
              height: '6.875rem',
            }}
          />
        )}
      </div>
      <div className="community-info-right">
        <div className="community-info-right-top">{communityType}</div>
        <div className="community-info-right-bottom">
          <TelegramButton
            onClick={() => {
              // 新窗口打开推特链接
              window.open(communityInfo?.link, '_blank');
            }}>
            <div className="telegram-icon">
              <Image
                src="/image/submission/x.png"
                alt="x"
                width={24}
                height={24}
                style={{
                  width: '1.5rem',
                  height: '1.5rem',
                }}
              />
            </div>
            <span>{communityInfo?.twitter}</span>
          </TelegramButton>
        </div>
      </div>
    </CommunityInfoWrapper>
  );
}

export default CommunityInfo;
