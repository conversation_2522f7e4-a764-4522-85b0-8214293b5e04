import { motion } from 'motion/react';
import styled from 'styled-components';
import Image from 'next/image';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

const containerVariants = {
  hidden: {},
  show: {
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  show: { opacity: 1, y: 0, transition: { duration: 0.4, type: 'spring', stiffness: 60 } },
};

const MaterialItemWrapper = styled(motion.div)<{ $isActive: boolean }>`
  border: ${(props) => (props.$isActive ? '0.125rem solid #fc7922' : '0.125rem solid #a58061')};
  border-radius: 1.375rem;
  padding: 0;
  background: #fff7ec;
  box-sizing: border-box;
  display: block;
  margin-bottom: 0.625rem;
  position: relative;
  // 右下角会有选中的伪类元素
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 2.1875rem;
    height: 2.1875rem;
    background: url('/image/submission/active.png') no-repeat center center;
    background-size: 100% 100%;
    display: ${(props) => (props.$isActive ? 'block' : 'none')};
  }
`;

const MaterialItem = styled.div<{
  $isActive: boolean;
  $minBtnDisabled: boolean;
  $maxBtnDisabled: boolean;
}>`
  width: 100%;
  height: 7.5rem;
  border-radius: 1.25rem;
  border: none;
  box-shadow:
    0rem 0.25rem 0rem 0rem #d9c4a3,
    0rem 0rem 0rem 0rem #d9c4a3,
    0rem 0.375rem 0.75rem -0.125rem rgba(0, 0, 0, 0.15);
  box-sizing: border-box;
  padding: 0.625rem 3.125rem 0.625rem 1.25rem;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  position: relative;
  overflow: hidden;
  .item-left {
    width: 5rem;
    height: 5rem;
    border-radius: 1.25rem;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: 0.0625rem solid #cabfab;
    box-shadow:
      0 0.1875rem 0.5rem 0 #e5dacb,
      0 0.09375rem 0 0 #f5e7d6 inset;
    position: relative;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      left: 0.1875rem;
      top: 0.1875rem;
      right: 0.375rem;
      bottom: 0.375rem;
      background: #fbf4e8;
      border-radius: 1rem;
      z-index: 0;
      width: 100%;
      height: 100%;
    }
    .item-left-img {
      position: relative;
      z-index: 1;
    }
  }

  .item-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    .counter-box {
      display: flex;
      align-items: center;
      min-width: 15rem;
      box-sizing: border-box;
      gap: 0.375rem;
    }
    .counter-btn {
      width: 3.75rem;
      height: 2.1875rem;
      border-radius: 0.625rem;
      border: none;
      background: #ff8316;
      color: #fff;
      font-size: 1.5rem;
      font-weight: bold;
      cursor: pointer;
      transition: filter 0.1s;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      // 置灰
      &:disabled {
        background: #c2b8a2;
        cursor: not-allowed;
      }
    }
    .minus {
      border-radius: 1.25rem 0.25rem 0.25rem 1.25rem;
      box-shadow: ${(props) =>
        props.$minBtnDisabled ? '0 0.25rem 0 #c2b8a2' : '0 0.25rem 0 #e07c00'};
      position: relative;
      .minus-icon {
        position: relative;
        left: 0.125rem;
      }
    }
    .plus {
      border-radius: 0.25rem 1.25rem 1.25rem 0.25rem;
      box-shadow: ${(props) =>
        props.$maxBtnDisabled ? '0 0.25rem 0 #c2b8a2' : '0 0.25rem 0 #e07c00'};
      position: relative;
      .plus-icon {
        position: relative;
        left: -0.125rem;
      }
    }
    .counter-btn:active {
      filter: brightness(0.95);
    }
    .counter-value {
      color: #fff;
      font-size: 1.25rem;
      font-weight: bold;
      border-top: 0.1875rem solid #8c8475;
      box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
      height: 2.375rem;
      width: 100%;
      background: #c2b8a2;
      border-radius: 0.625rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 0.125rem;

      input:focus {
        outline: none;
      }
      &:has(input:focus) {
        border: 0.125rem solid #fc7922;
      }

      input {
        width: 100%;
        height: 100%;
        text-align: center;
        background: transparent;
        border: none;
        color: inherit;
        font-size: inherit;
        font-weight: inherit;
        outline: none;
        text-shadow:
          -0.0625rem 0.0625rem 0 #a58061,
          0.0625rem 0.0625rem 0 #a58061,
          0.0625rem -0.0625rem 0 #a58061,
          -0.0625rem -0.0625rem 0 #a58061;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
          font-size: 1rem;
        }

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        &[type='number'] {
          -moz-appearance: textfield;
        }
      }
    }
    .max-box {
      background: #fff;
      border-radius: 0.625rem;
      border: 0.0625rem solid #a58061;
      color: #a58061;
      font-size: 1rem;
      font-weight: 500;
      width: 100%;
      text-align: center;
      box-sizing: border-box;
      cursor: pointer;
      box-shadow: 0 0.25rem 0 #b3afa5;
      transition:
        box-shadow 0.1s,
        transform 0.1s;
      box-sizing: border-box;
      padding: 0.25rem 0;
      &:hover {
        box-shadow: 0 0.125rem 0 #b3afa5;
        transform: translateY(0.125rem);
      }
    }
    .max-value {
      font-weight: normal;
    }
  }
`;

const MaterialItemComponents = ({
  item,
  inputValue,
  onInputChange,
}: {
  item: any;
  inputValue: number;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  const isActive = inputValue > 0;
  const minBtnDisabled = inputValue <= 0;
  const maxBtnDisabled = inputValue >= item.quantity;

  const onMaxValue = () => {
    // 设置最大值
    onInputChange({ target: { value: String(item.quantity) } } as any);
  };

  return (
    <MaterialItem
      $isActive={isActive}
      $minBtnDisabled={minBtnDisabled}
      $maxBtnDisabled={maxBtnDisabled}>
      <div className="item-left">
        <Image
          src={item.icon}
          alt={item.name}
          width={70}
          height={70}
          className="item-left-img"
          style={{
            width: '4.375rem',
            height: '4.375rem',
          }}
        />
      </div>
      <div className="item-right">
        <div className="counter-box">
          <button
            className="counter-btn minus"
            disabled={inputValue <= 0}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({ target: { value: String(Math.max(0, inputValue - 1)) } } as any);
            }}>
            <span className="minus-icon">-</span>
          </button>
          <div className="counter-value">
            <input type="text" value={inputValue ?? 0} onChange={onInputChange} />
          </div>
          <button
            className="counter-btn plus"
            disabled={inputValue >= item.quantity}
            onClick={(e) => {
              e.stopPropagation();
              onInputChange({
                target: { value: String(Math.min(item.quantity, inputValue + 1)) },
              } as any);
            }}>
            <span className="plus-icon">+</span>
          </button>
        </div>
        <div className="max-box" onClick={onMaxValue}>
          Max: <span className="max-value">{item.quantity.toLocaleString()}</span>
        </div>
      </div>
    </MaterialItem>
  );
};

interface MaterialListProps {
  materialList: any[];
  onSelectedItemsChange?: (
    selectedItems: Array<{ item: any; value: number; index: number }>
  ) => void;
}

export interface MaterialListRef {
  setActiveIndex: (index: number | null) => void;
  resetAllInputValue: () => void;
  setAllInputValue: () => void;
}

const MaterialList = forwardRef<MaterialListRef, MaterialListProps>((props, ref) => {
  const { materialList } = props;
  const [selectedIndexes, setSelectedIndexes] = useState<number[]>([]);
  const [inputValues, setInputValues] = useState<number[]>(() => materialList.map(() => 0));

  useImperativeHandle(ref, () => ({
    setActiveIndex: (index: number | null) => {
      setInputValues((prev) => {
        const next = [...prev];
        if (index !== null && index < next.length) {
          next[index] = 0;
        }
        return next;
      });
    },
    // 全部取消选中
    resetAllInputValue: () => {
      setInputValues((prev) => {
        const next = [...prev];
        next.fill(0);
        return next;
      });
    },
    // 全部选中，并且input设置最大值
    setAllInputValue: () => {
      setInputValues(() => {
        return materialList.map((item) => item.quantity);
      });
    },
  }));

  useEffect(() => {
    setInputValues((vals) => {
      if (materialList.length === vals.length) return vals;
      return materialList.map((_, i) => vals[i] ?? 0);
    });
  }, [materialList]);

  useEffect(() => {
    if (props.onSelectedItemsChange && selectedIndexes.length > 0) {
      const selectedItems = selectedIndexes
        .map((index) => ({
          item: materialList[index],
          value: inputValues[index],
          index,
        }))
        .filter((entry) => entry.value > 0 && entry.item);
      props.onSelectedItemsChange(selectedItems);
    }
  }, [selectedIndexes, inputValues, materialList, props]);

  useEffect(() => {
    setSelectedIndexes((prev) => {
      const newIndexes = prev.filter((idx) => inputValues[idx] > 0);
      inputValues.forEach((val, idx) => {
        if (val > 0 && !newIndexes.includes(idx)) {
          newIndexes.push(idx);
        }
      });
      return newIndexes;
    });
  }, [inputValues]);

  const handleInputChange = (index: number, value: string) => {
    let num = parseInt(value.replace(/\D/g, ''), 10) || 0;
    const max = materialList[index].quantity;
    if (num > max) num = max;
    setInputValues((prev) => {
      const next = [...prev];
      next[index] = num;
      return next;
    });
  };

  return (
    <motion.div
      className="material-list-box"
      variants={containerVariants}
      initial="hidden"
      animate="show">
      {materialList.map((item, index) => {
        const isActive = inputValues[index] > 0;
        return (
          <MaterialItemWrapper
            key={index}
            className={`material-item-border${isActive ? ' active' : ''}`}
            variants={itemVariants}
            $isActive={isActive}>
            <MaterialItemComponents
              item={item}
              inputValue={inputValues[index] ?? 0}
              onInputChange={(e) => handleInputChange(index, e.target.value)}
            />
          </MaterialItemWrapper>
        );
      })}
    </motion.div>
  );
});

MaterialList.displayName = 'MaterialList';

export default MaterialList;
