import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { COMMUNITY_RANK_IMAGES } from './CommunityInfo';

export interface CommunityTextRef {
  setTotalScore: (totalScore: number) => void;
}

interface CommunityTextProps {
  communityType?: keyof typeof COMMUNITY_RANK_IMAGES;
}

// eslint-disable-next-line react/display-name
const CommunityText = forwardRef<CommunityTextRef, CommunityTextProps>((props, ref) => {
  const { communityType } = props;
  const [totalScore, setTotalScore] = useState(0);
  const communityText = useMemo(() => {
    return `Confirm submitting all backpack resources to ${communityType} Community for development?
        ${communityType} Community will receive [${totalScore}] points.`;
  }, [communityType, totalScore]);

  useImperativeHandle(ref, () => ({
    setTotalScore: (totalScore: number) => {
      setTotalScore(totalScore);
    },
  }));
  return (
    <div
      className="community-text"
      style={{
        // wordBreak: 'break-all',
        width: '100%',
        height: '8.75rem',
      }}>
      {communityText}
      <span
        style={{
          display: 'block',
        }}>
        You will submit:
      </span>
    </div>
  );
});

export default CommunityText;
