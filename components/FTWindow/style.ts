import styled from 'styled-components';

export const FTWindowView = styled.div`
  font-family: 'JetBrains Mono';
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  //height: 100vh;
  margin: 0;
  //background-color: #f4f4f4;
  pointer-events: none;
  user-select: none;

  .currency-box {
    transform: translate(-90px, 0);
    transition: all 0.3s linear;
    @keyframes op {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    animation: op 0.3s linear;
    animation-fill-mode: none;
    .fixed-nail {
      position: absolute;
      left: 0;
      top: 0;
      transform: translate(50%, -100%);
    }
    .currency-box-bg {
      position: absolute;
      left: -13px;
      top: 14px;
      border-radius: 24px;
      background: linear-gradient(270deg, rgba(250, 250, 250, 0) 0%, rgba(250, 250, 250, 0.5) 73%);
      width: 100%;
      height: 100%;
    }
    .currency-box-content {
      border-radius: 24px;
      background: #fafafa4d;
      backdrop-filter: blur(8px);
      width: 100%;
      padding: 18px;

      & > p {
        font-family: 'JetBrains Mono';
        font-size: 20px;
        font-weight: 400;
        line-height: 26.4px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
        margin: 0;
        span {
          color: #3f3b37;
        }
      }
    }
  }
`;
