// components/Combo/ComboManager.tsx
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import Combo, { ValidCombo } from './Combo';
import { fadeIn, fadeOut } from '@/animates/keyFrames';

const ComboContainer = styled.div<{ active: boolean }>`
  position: fixed;
  top: 35%;
  left: 35%;
  transform: translate(-50%, -50%);
  z-index: 1000;

  ${(props) =>
    props.active
      ? css`
          animation: ${fadeIn} 0.3s forwards;
        `
      : css`
          animation: ${fadeOut} 0.3s forwards;
        `}
`;

export const useComboManager = () => {
  const [active, setActive] = useState(false);
  const [currentCombo, setCurrentCombo] = useState<ValidCombo>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialRender = useRef(true);

  const clearExistingTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  const showCombo = useCallback(() => {
    clearExistingTimer();
    setActive(true);

    timerRef.current = setTimeout(() => {
      setActive(false);
      timerRef.current = null;
    }, 1000);
  }, [clearExistingTimer]);

  const changeCombo = useCallback((combo: ValidCombo) => {
    setCurrentCombo(combo);
  }, []);

  const showComboWithType = useCallback(
    (combo: ValidCombo) => {
      clearExistingTimer();
      setCurrentCombo(combo);
      setActive(true);

      timerRef.current = setTimeout(() => {
        setActive(false);
        timerRef.current = null;
      }, 1000);
    },
    [clearExistingTimer]
  );

  // 组件卸载时清除计时器
  useEffect(() => {
    return () => {
      clearExistingTimer();
    };
  }, [clearExistingTimer]);

  // 使用useMemo优化ComboDisplay
  const ComboDisplay = useMemo(() => {
    // 初始渲染时不显示组件
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return null;
    }

    return () => (
      <ComboContainer active={active}>
        <Combo combo={currentCombo} />
      </ComboContainer>
    );
  }, [active, currentCombo]);

  // 重置连击数的方法
  const resetCombo = useCallback(() => {
    setCurrentCombo(0);
  }, []);
  // 增加连击数的方法
  const increaseCombo = useCallback(() => {
    setCurrentCombo((prev) => prev + 1);
    showCombo();
  }, []);

  return {
    showCombo,
    changeCombo,
    showComboWithType,
    ComboDisplay,
    currentCombo,
    resetCombo,
    increaseCombo,
  };
};
