import { FilterView, MyOrdersPageView, SelectStatusView } from './style';
import SearchSvg from '/public/image/search.svg';
import Arrow2Svg from '/public/image/arrow2.svg';
import CopySvg from '/public/image/copy.svg';
import CPagination from '../Basic/CPagination';
import { useMemo, useRef, useState } from 'react';
// @ts-ignore
import CopyToClipboard from 'react-copy-to-clipboard';
import toast from 'react-hot-toast';
import { getOrderList } from '../../server';
import { IAppState, ORDER_STATUS } from '../../constant/type';
import { useSelector } from 'react-redux';
import { toFormatAccount } from '../../utils';
import EmptyImg from '/public/image/empty.png';
import NotConnect from '../Basic/NotConnect';

interface IOrder {
  id: number;
  status: ORDER_STATUS;
  txId: string;
  orderId: string;
  created: string;
  updated: string;
}

const statusMap = {
  inscribing: 'Inscribing',
  minted: 'Minted',
  payment: 'Payment',
  pending: 'Pending',
};
export default function MyOrdersPage() {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [list, setList] = useState<IOrder[]>([]);
  const pageSize = 10;
  const [page, setPage] = useState<number>(1);
  const [total, setTotal] = useState<number>(110);
  const [searchContent, setSearchContent] = useState<string>('');
  const [status, setStatus] = useState<ORDER_STATUS | null>(null);
  const [loadLoading, setLoadLoading] = useState(false);
  const getList = () => {
    setLoadLoading(true);
    getOrderList({
      pageSize,
      pageNo: page,
      status: status ?? undefined,
      searchContent: searchContent || undefined,
    })
      .then((res) => {
        const data = res.data.data;
        setList(data.data);
        setTotal(data.total);
      })
      .finally(() => {
        setLoadLoading(false);
      });
  };
  const queryRef: any = useRef(null);
  useMemo(() => {
    if (!btcAddress) {
      return;
    }
    clearTimeout(queryRef.current);
    queryRef.current = setTimeout(() => {
      getList();
    }, 100);
    return () => clearTimeout(queryRef.current);
  }, [status, page, searchContent, btcAddress]);

  return (
    <MyOrdersPageView>
      {btcAddress ? (
        <div className="my-order-page">
          <h1>My Orders</h1>
          <div className="my-order-card">
            <Filter
              value={searchContent}
              onChange={setSearchContent}
              status={status}
              setStatus={setStatus}
            />
            {list.length === 0 && !loadLoading ? (
              <div className="no-data">
                <img src={EmptyImg.src} alt="" />
                <p>No order</p>
              </div>
            ) : loadLoading ? (
              <div className="loading-view">
                <p>Loading...</p>
              </div>
            ) : (
              <>
                <div className="order-list">
                  <div className="order-item-title">
                    <div>Order ID</div>
                    <div>Hash</div>
                    <div>Status</div>
                    <div>Date</div>
                  </div>
                  {list.map((item, index) => (
                    <OrderItem key={index} item={item} />
                  ))}
                </div>
                <CPagination page={page} setPage={setPage} total={total} pageSize={pageSize} />
              </>
            )}
          </div>
        </div>
      ) : (
        <NotConnect />
      )}
    </MyOrdersPageView>
  );
}

function Filter({
  value,
  onChange,
  status,
  setStatus,
}: {
  value: string;
  onChange: Function;
  status: ORDER_STATUS | null;
  setStatus: Function;
}) {
  return (
    <FilterView>
      <div className="search-view">
        <img src={SearchSvg.src} alt="" />
        <input
          type="text"
          placeholder="Search"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      </div>
      <SelectStatus status={status} setStatus={setStatus} />
    </FilterView>
  );
}

function OrderItem({ item }: { item: IOrder }) {
  return (
    <div className="order-item">
      <div>#{item.orderId}</div>
      <div className="item-hash">
        <span>{item.txId ? toFormatAccount(item.txId, 6, 7) : '-'}</span>
        {item.txId && (
          <CopyToClipboard
            text={item.txId}
            onCopy={() => {
              toast.success('copied!', { duration: 6000 });
            }}>
            <img src={CopySvg.src} alt="" />
          </CopyToClipboard>
        )}
      </div>
      <div>{statusMap[item.status]}</div>
      <div>{item.created}</div>
    </div>
  );
}

function SelectStatus({ status, setStatus }: { status: ORDER_STATUS | null; setStatus: Function }) {
  const statusList = [
    {
      label: 'All',
      value: null,
    },
    {
      label: 'Inscribing',
      value: ORDER_STATUS.inscribing,
    },
    {
      label: 'Minted',
      value: ORDER_STATUS.minted,
    },
    {
      label: 'Payment',
      value: ORDER_STATUS.payment,
    },
    {
      label: 'Pending',
      value: ORDER_STATUS.pending,
    },
  ];
  return (
    <SelectStatusView>
      <span>{status ? statusMap[status] : 'All'}</span>
      <img src={Arrow2Svg.src} alt="" />
      <div className="status-list-view">
        <div className="line" />
        <div className="connected-menu-list">
          <div className="status-list">
            {statusList.map((item, index) => (
              <div key={index} className="status-item" onClick={() => setStatus(item.value)}>
                <span>{item.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </SelectStatusView>
  );
}
