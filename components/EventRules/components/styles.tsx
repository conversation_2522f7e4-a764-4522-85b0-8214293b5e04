import styled from 'styled-components';
import Image from 'next/image';
import { motion } from 'framer-motion';

export const StyledRulesContentContainer = styled.div<{ bgImgSrc: string }>`
  position: relative;
  /* width: 1120px; */
  /* height: 648px; */
  width: 70rem;
  height: 40.5rem;
  background-image: url(${(props) => props.bgImgSrc});
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;

  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 6.25rem;
  box-sizing: border-box;

  .event-image {
    z-index: 10;
    position: relative;
    top: -10%;
    display: block;
    margin: 0 auto;
  }
  .event-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.625rem;
    position: relative;
    .tep2 {
      position: relative;
      top: -3.125rem;
    }
  }
`;

export const StyledEventTitle = styled.div<{ $bgSrc: string }>`
  /* width: 532px; */
  /* height: 140px; */
  width: 33.25rem;
  height: 8.75rem;
  background-image: url(${(props) => props.$bgSrc});
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
`;

export const EventImageContainer = styled(motion.div)`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  position: relative;
  .tep2 {
    position: relative;
    top: -3.125rem;
  }
`;
