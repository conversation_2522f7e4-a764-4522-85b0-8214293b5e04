import { px2rem } from '@/utils/px2rem';
import BasicRulesContent from './BasicRulesContent';
import Event1 from '/public/image/easterEgg/whackAMole_bg.png';
import { useAppSelector } from '@/hooks/useStore';
import { useMemo } from 'react';
import { AnimationSequenceConfig } from '@/components/AnimationSequence/config';
import styled from 'styled-components';

const popupImages = [
  {
    src: '/image/easterEgg/whackAMole_step1.png',
    imgObj: '',
    alt: 'Popup image 1',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Tap ‘Start’ to begin Tap & Stick Fun.',
  },
  {
    src: '/image/easterEgg/whackAMole_step2.png',
    imgObj: '',
    alt: 'Popup image 2',
    width: px2rem(252),
    height: px2rem(252),
    className: 'tep2',
    description: 'Quickly tap the stickers in order.',
  },
  {
    src: '/image/easterEgg/easterRule_step3.png',
    imgObj: 'tep3',
    alt: 'Popup image 3',
    // width: 260,
    // height: 340,
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Complete the challenge to earn token rewards!',
  },
];

const event1 = {
  src: '/image/easterEgg/whackAMole_title.png',
  alt: 'event2',
  width: px2rem(400),
  height: px2rem(100),
  className: '',
};

const StyledPreloadImage = styled.div<{
  $img1?: string;
  $img2?: string;
  $img3?: string;
  $img4?: string;
}>`
  /* display: none; */
  visibility: hidden;
  pointer-events: none;
  &::before {
    content: '';
    display: block;
    position: absolute;
    background:
      url(${(props) => props.$img1}) no-repeat -9999px -9999px,
      url(${(props) => props.$img2}) no-repeat -9999px -9999px,
      url(${(props) => props.$img3}) no-repeat -9999px -9999px,
      url(${(props) => props.$img4}) no-repeat -9999px -9999px;
    width: 0;
    height: 0;
  }
`;

function usePreloadWhackAMoleImage() {
  const { whackAMoleEasterEgg } = useAppSelector((state) => state.AppReducer);
  const rewardType = whackAMoleEasterEgg?.rewardType;

  const imageConfig = useMemo(() => {
    return rewardType ? AnimationSequenceConfig[rewardType] : null;
  }, [rewardType]);

  const preloadImageEL = useMemo(() => {
    return (
      <StyledPreloadImage
        $img1={imageConfig?.eventImageActiveSrc}
        $img2={imageConfig?.eventImageSrc}
        $img3={imageConfig?.failureImageSrc}
        $img4={imageConfig?.victoryImageSrc}
      />
    );
  }, [imageConfig]);

  return preloadImageEL;
}

const WhackAMoleRules = ({ onClose }: { onClose: () => void }) => {
  const preloadImageEL = usePreloadWhackAMoleImage();

  return (
    <>
      <BasicRulesContent
        onClose={onClose}
        eventTitle={event1}
        popupImages={popupImages}
        bgImgSrc={Event1.src}
        mainTitle="Hidden Challenge"
        subTitle="Tap & Stick Fun"
      />
      {preloadImageEL}
    </>
  );
};

export default WhackAMoleRules;
