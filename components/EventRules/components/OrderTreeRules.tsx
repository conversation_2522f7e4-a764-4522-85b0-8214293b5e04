import { px2rem } from '@/utils/px2rem';
import BasicRulesContent from './BasicRulesContent';
import Event1 from '/public/image/easterEgg/orderTree_bg.png';

const popupImages = [
  {
    src: '/image/easterEgg/orderTree_step1.png',
    imgObj: '',
    alt: 'Popup image 1',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Craft axes from the NPC to start chopping trees.',
  },
  {
    src: '/image/easterEgg/orderTree_step2.png',
    imgObj: '',
    alt: 'Popup image 2',
    width: px2rem(252),
    height: px2rem(252),
    className: 'tep2',
    description: 'Find glowing trees and chop them in the right order.',
  },
  {
    src: '/image/easterEgg/easterRule_step3.png',
    imgObj: 'tep3',
    alt: 'Popup image 3',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Complete the challenge to earn token rewards!',
  },
];

// TODO:
const event1 = {
  src: '/image/easterEgg/orderTree_title.png',

  alt: 'event2',
  width: px2rem(400),
  height: px2rem(100),
  className: '',
};

const OrderTreeRules = ({ onClose }: { onClose: () => void }) => {
  return (
    <BasicRulesContent
      onClose={onClose}
      eventTitle={event1}
      popupImages={popupImages}
      bgImgSrc={Event1.src}
      mainTitle="Hidden Challenge"
      subTitle="Chop Master"
    />
  );
};

export default OrderTreeRules;
