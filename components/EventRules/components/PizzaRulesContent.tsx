import { px2rem } from '@/utils/px2rem';
import BasicRulesContent from './BasicRulesContent';
import Event1 from '/public/image/easterEgg/pizzaRush_bg.png';

const popupImages = [
  {
    src: '/image/easterEgg/pizzaRule_step1.png',
    imgObj: '',
    alt: 'Popup image 1',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Pick up the pizza boxes and put them in your backpack before the countdown ends.',
  },
  {
    src: '/image/easterEgg/pizzaRule_step2.png',
    imgObj: '',
    alt: 'Popup image 2',
    width: px2rem(252),
    height: px2rem(252),
    className: 'tep2',
    description:
      'Use your skills to choose the best route and collect as many pizza boxes as possible.',
  },
  {
    src: '/image/easterEgg/easterRule_step3.png',
    imgObj: 'tep3',
    alt: 'Popup image 3',
    // width: 260,
    // height: 340,
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: `The more pizza boxes you collect, the more tokens you’ll earn.`,
  },
];

const event1 = {
  src: '/image/easterEgg/pizzaRush_title.png',
  alt: 'event2',
  width: px2rem(400),
  height: px2rem(100),
  className: '',
};

const PizzaRulesContent = ({ onClose }: { onClose: () => void }) => {
  return (
    <BasicRulesContent
      onClose={onClose}
      eventTitle={event1}
      popupImages={popupImages}
      bgImgSrc={Event1.src}
      titleStyle={{
        backgroundSize: 'contain',
      }}
    />
  );
};

export default PizzaRulesContent;
