import { AvatarPageView } from './style';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { BeatSaberAvatar } from '../../renderAvatar';
import AvatarData from '../../renderAvatar/Avatar/Data/AvatarData';
import Menu from './Menu';
import {
  DEFAULT_FATHER_INSCRIPTION,
  IS_LOCAL_TEST,
  loadAvatarModel,
  ORD_SERVER,
} from '../../constant';
import {
  FATHER_TYPE_ENUM,
  IAvatarMetadata,
  IChildrenInscription,
  ICollection,
  IFatherInscription,
  PATH_ID_ENUM,
  UsableAddressDataType,
} from '../../constant/type';
import { checkIsInscriptionId, decodeFetchResponse, getLocationParams } from '../../utils';
import AvatarObject from '../../renderAvatar/Avatar/AvatarObject';
import { UsableAddressProvider } from './context';

const initAvatarData = {
  /*  actionId: "",
      pantsId: "",
      shirtId: "",
      shirtTextureId: '',
      shirtColor: '',
      hatId: "",
      shoesId: "",*/
};

interface IProps {
  collectionData: ICollection | null;
  submitChainSlot?: Function;
  avatarObjectLoaded?: (avatarObject: AvatarObject) => void;
  deleteCollectionSlot?: Function;
  exportPreviewImage: Function;
  isFrontend: boolean;
  transparentBackground?: boolean;
  defaultInscriptionId?: string;
  defaultAvatarMetadata?: IAvatarMetadata; //默认的metadata，优先级比defaultInscriptionId高
  hiddenMenu?: boolean;
  // 需要预加载列表的类型
  lazyLoadGroups?: FATHER_TYPE_ENUM[];
  isDisabledPet?: boolean; // 是否禁用宠物（这个字段是临时的，在升级铭文那一版本需要改成seq判断）
  usableAddressData?: UsableAddressDataType;
  isLogin?: boolean; // 是否登录
}

const AvatarPage = forwardRef(
  (
    {
      collectionData,
      submitChainSlot,
      avatarObjectLoaded,
      deleteCollectionSlot,
      exportPreviewImage,
      isFrontend,
      defaultInscriptionId,
      defaultAvatarMetadata,
      transparentBackground = false,
      hiddenMenu,
      lazyLoadGroups,
      isDisabledPet,
      usableAddressData,
      isLogin,
    }: IProps,
    ref: any
  ) => {
    const [globalParts, setGlobalParts] = useState<IFatherInscription[]>(
      DEFAULT_FATHER_INSCRIPTION
    );

    const [userAvatarMetadata, setUserAvatarMetadata] = useState<IAvatarMetadata>(
      initAvatarData as IAvatarMetadata
    );
    const [cacheUserAvatarMetadata, setCachetUserAvatarMetadata] = useState<IAvatarMetadata>(
      initAvatarData as IAvatarMetadata
    );
    const [loadAvatarLoading, setLoadAvatarLoading] = useState<boolean>(!IS_LOCAL_TEST);
    const [globalLoading, setGlobalLoading] = useState<boolean>(true);
    const avatarRenderRef: any = useRef(null);

    const initMetadata = () => {
      setUserAvatarMetadata(initAvatarData as IAvatarMetadata);
      setCachetUserAvatarMetadata(initAvatarData as IAvatarMetadata);
    };

    // 获取metadata
    async function getInscriptionMetadata(inscriptionId: string) {
      const childMetadataResp = await fetch(`${ORD_SERVER}/r/metadata/${inscriptionId}`);
      return await decodeFetchResponse(childMetadataResp);
    }

    // 通过铭文id获取套装信息
    const fetchUserAvatarMetadata = () => {
      return new Promise(async (resolve) => {
        if (defaultAvatarMetadata) {
          // 如果有默认的metadata，优先渲染
          resolve(defaultAvatarMetadata);
          return;
        }
        const userAvatarInscriptionId =
          defaultInscriptionId ||
          getLocationParams('inscriptionId') ||
          window.location.pathname.split('/').pop();
        if (!userAvatarInscriptionId || !checkIsInscriptionId(userAvatarInscriptionId)) {
          resolve(null);
        } else {
          try {
            // 1、获取它是不是代理铭文，如果是 则获取它代理铭文的id的content
            const resp1 = await fetch(`${ORD_SERVER}/r/inscription/${userAvatarInscriptionId}`);
            let metadata1 = await resp1.text();
            const satPart = metadata1.split('"sat":')[1]; // 获取 "sat" 及其值的部分
            const sat_number = satPart.split(',')[0].trim(); // 提取冒号后的值，去掉多余字符
            // 判断该铭文是否存在reinscribe铭文，如果response内的ids长度为1，则表示该sat只铭刻了一个铭文，则按原逻辑走获取铭文的metadata数据，加载装饰（到此逻辑结束）
            const resp2 = await fetch(`${ORD_SERVER}/r/sat/${sat_number}`);
            let metadata2 = await resp2.text();
            const { ids } = JSON.parse(metadata2);
            // 获取当前sat的最新铭文（index = -1 则表达获取当前sat的最新铭文，继续往下看步骤五）
            if (ids.length > 1) {
              // 获取最新的铭文
              const resp3 = await fetch(`${ORD_SERVER}/r/sat/${sat_number}/at/${-1}`);
              let metadata3 = await resp3.text();
              const { id } = JSON.parse(metadata3);
              const resp4 = await fetch(`${ORD_SERVER}/content/${id}`);
              let metadata4 = await resp4.text();
              resolve(JSON.parse(metadata4));
            } else {
              const resp = await fetch(`${ORD_SERVER}/r/metadata/${userAvatarInscriptionId}`);
              // 解码metadata
              const metadataObject = await decodeFetchResponse(resp);
              if (metadataObject?.deployer && metadataObject?.version) {
                resolve(null);
              } else {
                resolve(metadataObject);
              }
            }
          } catch (e: any) {
            console.log('Metadata not found');
            resolve(null);
          }
        }
      });
    };

    // 获取子铭文数据
    async function fetchChildrenInscription(inscriptionId: string) {
      // 这里可以分页，默认开始为0，每页数据固定返回100条
      const page = 0;
      const resp = await fetch(`${ORD_SERVER}/r/children/${inscriptionId}/${page}`);
      const data = await resp.json();
      const childrenInscriptionIds = data.ids;
      const childrenInscriptionData: IChildrenInscription[] = [];
      for (const childItem of childrenInscriptionIds) {
        const metadataObject = await getInscriptionMetadata(childItem);
        childrenInscriptionData.push({
          inscriptionId: childItem,
          metadata: metadataObject,
        });
      }
      return childrenInscriptionData;
    }

    // 获取指定菜单
    const getMenuPantsData = (gItemInscriptionId: string) => {
      return fetchChildrenInscription(gItemInscriptionId).then(
        (childDataList: IChildrenInscription[]) => {
          setGlobalParts((globalParts_) => {
            for (let i = 0; i < globalParts_.length; i++) {
              if (globalParts_[i].inscriptionId === gItemInscriptionId) {
                globalParts_[i].childrenInscription = childDataList;
                break;
              }
            }
            avatarRenderRef.current.avatarPartsModel.init(globalParts_);
            return globalParts_;
          });
        }
      );
    };

    // 获取人物配件的metadata
    const getAvatarPathsMetadataData = async (userAvatarMetadata_: IAvatarMetadata | null) => {
      if (!userAvatarMetadata_) {
        return [];
      }
      // const promiseList = []
      // 如果要把用户套装同步到将菜单里面，则取消克隆
      const globalParts_ = JSON.parse(JSON.stringify(DEFAULT_FATHER_INSCRIPTION));
      // 映射key对应的下标
      /*const indexMap: { [key: string]: number } = {}
        for (const key_ in userAvatarMetadata_) {
          const key = key_ as PATH_ID_ENUM
          //排除一些key|贴图|颜色  等等
          if (EXCLUDE_APPENDIX_PATH_ID.includes(key)) {
            continue
          }

          if (userAvatarMetadata_[key]) {
            // promiseList.push(await getInscriptionMetadata(userAvatarMetadata_[key]))
            // indexMap[key] = promiseList.length - 1
          }
        }*/
      // if (promiseList.length > 0) {
      // const metadataList = await Promise.all(promiseList)
      for (let i = 0; i < globalParts_.length; i++) {
        const pathIdKey = globalParts_[i].pathIdKey as PATH_ID_ENUM;
        if (userAvatarMetadata_[pathIdKey]) {
          globalParts_[i].childrenInscription = [
            {
              inscriptionId: userAvatarMetadata_[pathIdKey],
              metadata: {}, //metadataList[indexMap[pathIdKey]]
            },
          ];
        }
        // }
      }

      return globalParts_;
      // }
    };

    // 更新套装渲染
    const setAvatarDataRef: any = useRef(null);
    const setAvatarData = (userAvatarMetadata_: IAvatarMetadata) => {
      clearTimeout(setAvatarDataRef.current);
      setAvatarDataRef.current = setTimeout(() => {
        if (avatarRenderRef.current) {
          // @ts-ignore
          avatarRenderRef.current.renderInstance.setAvatarData(getAvatarData(userAvatarMetadata_));
        } else {
          setAvatarData(userAvatarMetadata_);
        }
      }, 100);
    };

    // 更新套装渲染
    useEffect(() => {
      setAvatarData(userAvatarMetadata);
      return () => clearTimeout(setAvatarDataRef.current);
    }, [userAvatarMetadata]);
    // 初始化加载
    const renderAvatar = (globalParts_ = globalParts, userAvatarMetadata_ = userAvatarMetadata) => {
      let renderTarget: HTMLElement | null = document.getElementById('render-target');
      if (avatarRenderRef.current) {
        if (globalParts_) {
          avatarRenderRef.current.avatarPartsModel.init(globalParts_);
        }
        if (userAvatarMetadata_) {
          setAvatarData(userAvatarMetadata_);
        }
        return;
      }

      avatarRenderRef.current = BeatSaberAvatar.setup(
        renderTarget,
        globalParts_,
        getAvatarData(userAvatarMetadata_),
        {
          transparentBackground,
          onLoad: (avatarObject: AvatarObject) => {
            setGlobalLoading(false);
            if (avatarObjectLoaded) {
              avatarObjectLoaded(avatarObject);
            }
          },
        }
      );
      setLoadAvatarLoading(false);
    };
    // 获取当前的套装信息并渲染
    const getAvatarMetadataData = () => {
      fetchUserAvatarMetadata().then(async (metadataObject) => {
        const userAvatarMetadata_ = metadataObject as IAvatarMetadata | null;
        if (userAvatarMetadata_) {
          setUserAvatarMetadata(userAvatarMetadata_);
          setCachetUserAvatarMetadata(userAvatarMetadata_);
          // 预加载
          /* const paths: string[] = []
                 for (const key_ in userAvatarMetadata_) {
                   const key = key_ as PATH_ID_ENUM
                   if (EXCLUDE_APPENDIX_PATH_ID.includes(key)) {
                     continue
                   }
                   userAvatarMetadata_[key] && paths.push(getOrdLink(userAvatarMetadata_[key]).content)
                 }
                 await loadFiles(paths)*/
          const avatarGParts: IFatherInscription[] =
            await getAvatarPathsMetadataData(userAvatarMetadata_);
          renderAvatar(avatarGParts, userAvatarMetadata_ ?? undefined);
        } else {
          initMetadata();
          const avatarGParts: IFatherInscription[] = await getAvatarPathsMetadataData(
            initAvatarData as IAvatarMetadata
          );
          renderAvatar(avatarGParts, initAvatarData as IAvatarMetadata);
        }
      });
    };

    useEffect(() => {
      if (IS_LOCAL_TEST) {
        renderAvatar();

        let index = 0;
        for (let i = 0; i < DEFAULT_FATHER_INSCRIPTION.length; i++) {
          const element = DEFAULT_FATHER_INSCRIPTION[i];
          loadAvatarModel(element, () => {
            index++;
            if (index === DEFAULT_FATHER_INSCRIPTION.length) {
              avatarRenderRef.current?.avatarPartsModel.init(DEFAULT_FATHER_INSCRIPTION);
            }
          });
        }
        return;
      }
      getAvatarMetadataData();
    }, [defaultInscriptionId, defaultAvatarMetadata]);

    const getAvatarData = (userAvatarMetadata: IAvatarMetadata | null): AvatarData | null => {
      const avatarData = new AvatarData();
      if (userAvatarMetadata) {
        for (const avatarDataKey in userAvatarMetadata) {
          // 判断有没有这个属性
          if (avatarData.hasOwnProperty(avatarDataKey)) {
            // @ts-ignore
            avatarData[avatarDataKey] = userAvatarMetadata[avatarDataKey] || null;
          }
        }
        return avatarData;
      } else {
        return null;
      }
    };

    // 菜单选择更新套装数据
    const updateAvatarPartData = (data: IAvatarMetadata) => {
      let newData: any = {};
      for (const dataKey_ in data) {
        const dataKey = dataKey_ as PATH_ID_ENUM;
        if (data[dataKey]) {
          newData[dataKey] = data[dataKey];
        }
      }
      setUserAvatarMetadata(newData as IAvatarMetadata);
    };
    // 给父级调用
    useImperativeHandle(ref, () => ({
      // 根据key更新metadata
      updatePath: (key: string, value: string) => {
        updateAvatarPartData({
          ...JSON.parse(JSON.stringify(userAvatarMetadata || {})),
          [key]: value,
        });
      },
      getFatherInscription: () => {
        return globalParts;
      },
    }));

    // 如果加载过一次，让装饰菜单不显示而不销毁
    const [loadedMenu, setLoadedMenu] = useState<boolean>(true);

    useMemo(() => {
      if (!hiddenMenu) {
        setLoadedMenu(true);
      }
    }, [hiddenMenu]);

    useEffect(() => {
      return () => {
        if (avatarRenderRef.current) {
          avatarRenderRef.current.renderInstance.destroy();
        }
      };
    }, []);

    return (
      <UsableAddressProvider defaultUsableAddressDataContext={usableAddressData} isLogin={isLogin}>
        <AvatarPageView className="avatarPageView">
          {(!hiddenMenu || loadedMenu) && !loadAvatarLoading && (
            <Menu
              userAvatarMetadata={userAvatarMetadata}
              updateAvatarPartData={updateAvatarPartData}
              globalParts={globalParts}
              cacheUserAvatarMetadata={cacheUserAvatarMetadata}
              exportPreviewImage={exportPreviewImage}
              collectionData={collectionData}
              initMetadata={initMetadata}
              getMenuPantsData={getMenuPantsData}
              submitChainSlot={submitChainSlot}
              deleteCollectionSlot={deleteCollectionSlot}
              lazyLoadGroups={lazyLoadGroups}
              hiddenMenu={hiddenMenu}
              isDisabledPet={isDisabledPet}
            />
          )}
          {!avatarObjectLoaded && <div id="render-target" />}
          {((globalLoading && !isFrontend) || loadAvatarLoading) && (
            <div className="loading-popup">Loading...</div>
          )}
        </AvatarPageView>
      </UsableAddressProvider>
    );
  }
);
export default AvatarPage;
