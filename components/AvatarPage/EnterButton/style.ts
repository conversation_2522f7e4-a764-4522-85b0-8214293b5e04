import styled from 'styled-components';

export const EnterButtonView = styled.div`
  position: absolute;
  left: 50%;
  bottom: 32px;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 4px solid #452c00;
  border-top-left-radius: 24px;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 24px;
  border-bottom-left-radius: 8px;
  background: #fff0e3;
  box-shadow:
    0px 4px 0px 0px #ffffff inset,
    -2px -4px 0px 0px #f39948 inset;
  width: 308px;
  height: 72px;
  cursor: pointer;
  box-sizing: border-box;
  & > p {
    font-family: 'JetBrains Mono';
    font-size: 20px;
    font-weight: 500;
    line-height: 26.4px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    margin: 0;
  }
  & > span {
    font-family: 'JetBrains Mono';
    font-size: 14px;
    font-weight: 400;
    line-height: 18.48px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    height: 17px;
    margin: 0;
  }
`;
