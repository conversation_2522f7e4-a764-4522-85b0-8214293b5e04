import { MenuContentView, <PERSON>uR<PERSON>Tabs, MenuView, RightMenuItem } from './style';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  FATHER_TYPE_ENUM,
  IAvatarMetadata,
  ICollection,
  IFatherInscription,
} from '../../../constant/type';
import { formatMillisecondsToDDHH, getOrdLink, loadFiles } from '../../../utils';
import InfoPanel from '../InfoPanel';
import { IS_LOCAL_TEST, MENU_GROUP, THEME_MEDIA_NUM } from '../../../constant';
import SvgComponent from '../SvgComponent';
import { SVG_FILE } from '../../../constant/staticFile';
import { useUsableAddress } from '../context';

interface IProps {
  userAvatarMetadata: IAvatarMetadata;
  updateAvatarPartData: Function;
  cacheUserAvatarMetadata: IAvatarMetadata;
  globalParts: IFatherInscription[];
  exportPreviewImage: Function;
  collectionData: ICollection | null;
  initMetadata: Function;
  getMenuPantsData: Function;
  submitChainSlot?: Function;
  deleteCollectionSlot?: Function;
  lazyLoadGroups?: FATHER_TYPE_ENUM[];
  hiddenMenu?: boolean;
  isDisabledPet?: boolean; // 是否禁用宠物（这个字段是临时的，在升级铭文那一版本需要改成seq判断）
}

export default function Menu({
  userAvatarMetadata,
  updateAvatarPartData,
  globalParts,
  cacheUserAvatarMetadata,
  exportPreviewImage,
  collectionData,
  initMetadata,
  getMenuPantsData,
  submitChainSlot,
  deleteCollectionSlot,
  lazyLoadGroups,
  hiddenMenu = false,
  isDisabledPet = false,
}: IProps) {
  const [activeTab, setActiveTab] = useState(0);
  const [showMenuIndex, setShowMenuIndex] = useState<number>(0);
  const [isShowMenu, setIsShowMenu] = useState<boolean>(false);
  const [initAni, setInitAni] = useState<boolean>(false);
  const [isInitMenu, setIsInitMenu] = useState(false);
  useMemo(() => {
    if (isShowMenu && !isInitMenu) {
      setIsInitMenu(true);
    }
  }, [isShowMenu]);
  useEffect(() => {
    setInitAni(true);
    if (window.innerWidth >= THEME_MEDIA_NUM.FRONTEND_LARGE) {
      setShowMenuIndex(0);
      setIsShowMenu(true);
    }
  }, []);

  const showGlobalParts = useMemo(() => {
    const showTypesMap = MENU_GROUP[showMenuIndex].fathersType.reduce(
      (acc, cur) => {
        acc[cur] = true;
        return acc;
      },
      {} as Record<string, boolean>
    );
    const showGlobalParts_ = globalParts.filter((item) => showTypesMap[item.type]);
    // 根据 fathersTyp 对应的顺序进行排序
    showGlobalParts_.sort((a, b) => {
      const aIndex = MENU_GROUP[showMenuIndex].fathersType.indexOf(a.type);
      const bIndex = MENU_GROUP[showMenuIndex].fathersType.indexOf(b.type);
      return aIndex - bIndex;
    });
    return showGlobalParts_;
  }, [userAvatarMetadata, globalParts, showMenuIndex]);

  const [isLoadMenuMap, setIsLoadMenu] = useState<{ [key: string]: boolean }>({});
  const [isLoadMenuLoadingMap, setIsLoadLoadingMenu] = useState<{
    [key: string]: boolean;
  }>({});

  const getMenuPantsDataItem = (globalPartsItem: IFatherInscription) => {
    setIsLoadLoadingMenu((oldData) => {
      return {
        ...oldData,
        [globalPartsItem.inscriptionId]: true,
      };
    });
    getMenuPantsData(globalPartsItem.inscriptionId).then(() => {
      setIsLoadLoadingMenu((oldData) => {
        return {
          ...oldData,
          [globalPartsItem.inscriptionId]: false,
        };
      });
    });
    setIsLoadMenu((oldData) => {
      return {
        ...oldData,
        [globalPartsItem.inscriptionId]: true,
      };
    });
  };
  useEffect(() => {
    const globalPartsItem = showGlobalParts[activeTab];
    if (isShowMenu && globalPartsItem && !isLoadMenuMap[globalPartsItem.inscriptionId]) {
      getMenuPantsDataItem(globalPartsItem);
    }
  }, [isShowMenu, showGlobalParts, activeTab]);
  // 处理预加载项
  useEffect(() => {
    if (IS_LOCAL_TEST) {
      return;
    }
    if (lazyLoadGroups) {
      const map: Record<string, boolean> = {};
      for (let i = 0; i < lazyLoadGroups.length; i++) {
        map[lazyLoadGroups[i]] = true;
      }
      for (let i = 0; i < globalParts.length; i++) {
        if (map[globalParts[i].type] && !isLoadMenuMap[globalParts[i].inscriptionId]) {
          getMenuPantsDataItem(globalParts[i]);
        }
      }
    }
  }, [lazyLoadGroups]);

  const onUpdate = (key: string, value: string) => {
    if (!userAvatarMetadata) {
      updateAvatarPartData({ [key]: value });
      return;
    }
    updateAvatarPartData({
      ...JSON.parse(JSON.stringify(userAvatarMetadata)),
      [key]: userAvatarMetadata[showGlobalParts[activeTab].pathIdKey] === value ? '' : value,
    });
  };

  return (
    <>
      <MenuView
        className={`menuView${isShowMenu ? ' show' : ''}`}
        initAni={initAni}
        style={{ display: hiddenMenu ? 'none' : 'block' }}>
        <div className="top-menu-tabs">
          {isInitMenu &&
            showGlobalParts.map((item, index) => (
              <div
                className={`menu-tab ${isShowMenu && activeTab === index ? 'active' : ''}`}
                key={item.inscriptionId}
                onClick={() => setActiveTab(index)}>
                <img src={getOrdLink(item.inscriptionId).content} alt={item.type} />
              </div>
            ))}
        </div>
        <MenuContent
          fatherInscription={showGlobalParts[activeTab]}
          onUpdate={onUpdate}
          userAvatarMetadata={userAvatarMetadata}
          loadMenuLoading={
            showGlobalParts[activeTab] &&
            isLoadMenuLoadingMap[showGlobalParts[activeTab].inscriptionId] &&
            !IS_LOCAL_TEST
          }
          isDisabledPet={isDisabledPet}
        />
        <MenuRightTabs className="right-menu-tabs">
          {MENU_GROUP.map((item, index) => (
            <RightMenuItem
              className={
                'right-menu-item' + (isShowMenu && showMenuIndex === index ? ' active' : '')
              }
              onClick={() => {
                setActiveTab(0);
                setShowMenuIndex(index);
                if (window.innerWidth >= THEME_MEDIA_NUM.FRONTEND_LARGE) {
                  setIsShowMenu(true);
                  return;
                }
                if (showMenuIndex === index && isShowMenu) {
                  setIsShowMenu(false);
                } else {
                  setIsShowMenu(true);
                }
              }}
              key={index}>
              {/* <SvgComponent svg={SVG_FILE.arrowSvg} className="arrow" /> */}
              <SvgComponent
                svg={isShowMenu && showMenuIndex === index ? item.icon : item.unSelectIcon}
                className="right-icon"
              />
            </RightMenuItem>
          ))}
          {deleteCollectionSlot &&
            deleteCollectionSlot({
              collectionData,
              initMetadata,
            })}
        </MenuRightTabs>
      </MenuView>
      {!hiddenMenu && (
        <>
          <InfoPanel
            userAvatarMetadata={userAvatarMetadata}
            globalParts={showGlobalParts}
            activeTab={activeTab}
            initAni={initAni}
            isShowMenu={isShowMenu}
            onUpdate={onUpdate}
            cacheUserAvatarMetadata={cacheUserAvatarMetadata}
          />
          {submitChainSlot &&
            submitChainSlot({
              userAvatarMetadata,
              cacheUserAvatarMetadata,
              updateAvatarPartData,
              exportPreviewImage,
              collectionData,
            })}
        </>
      )}
    </>
  );
}

function MenuContent({
  fatherInscription,
  onUpdate,
  userAvatarMetadata,
  loadMenuLoading,
  isDisabledPet,
}: {
  fatherInscription?: IFatherInscription;
  onUpdate: Function;
  userAvatarMetadata: IAvatarMetadata;
  loadMenuLoading: boolean;
  isDisabledPet: boolean;
}) {
  const [loading, setLoading] = useState<boolean>(true);
  const { usableAddressData, isLogin } = useUsableAddress();
  const latestFatherInscriptionRef = useRef<IFatherInscription>(
    fatherInscription as IFatherInscription
  );
  latestFatherInscriptionRef.current = fatherInscription as IFatherInscription;

  const lazyLoad = () => {
    if (!fatherInscription) {
      return;
    }
    const urls = fatherInscription.childrenInscription.map(
      (item) => getOrdLink(item.inscriptionId).content
    );
    loadFiles(urls).then(() => {
      setLoading(false);
    });
  };
  useMemo(() => {
    lazyLoad();
  }, [fatherInscription]);

  const sortedFatherInscription = useMemo(() => {
    if (!latestFatherInscriptionRef.current || !isLogin) {
      return latestFatherInscriptionRef.current;
    }

    const pathIdKey = latestFatherInscriptionRef.current.pathIdKey;
    const tempArr = latestFatherInscriptionRef.current.childrenInscription.map((item) => item);

    tempArr.sort((a, b) => {
      const aInscriptionId = a.inscriptionId;
      const bInscriptionId = b.inscriptionId;
      // const bInscriptionId = b.inscriptionId

      const aExpireAt = usableAddressData[pathIdKey]?.find(
        (usableItem) => usableItem.inscriptionId === aInscriptionId
      );
      const bExpireAt = usableAddressData[pathIdKey]?.find(
        (usableItem) => usableItem.inscriptionId === bInscriptionId
      );

      if (aExpireAt && bExpireAt) {
        if (aExpireAt.expireAt === null && bExpireAt.expireAt === null) {
          return 0;
        }
        if (aExpireAt.expireAt === null) {
          if (bExpireAt.expireAt <= 0) {
            return -1;
          }
          return 0;
        } else {
          if (aExpireAt.expireAt <= 0) {
            if (bExpireAt.expireAt === null || bExpireAt.expireAt > 0) {
              return 1;
            } else {
              return 0;
            }
          } else {
            return -1;
          }
        }
      }

      if (aExpireAt && !bExpireAt) {
        return -1;
      }
      if (!aExpireAt && bExpireAt) {
        return 1;
      }
      if (!aExpireAt && !bExpireAt) {
        return 0;
      }

      return 0;
    });

    return {
      ...latestFatherInscriptionRef.current,
      childrenInscription: tempArr,
    };
  }, [latestFatherInscriptionRef.current?.childrenInscription]);

  if (loadMenuLoading) {
    return (
      <MenuContentView className="menuContentView">
        <div className="loading-box">
          <p>Loading...</p>
        </div>
      </MenuContentView>
    );
  }
  if (!fatherInscription) {
    return (
      <MenuContentView className="menuContentView">
        <div className="empty-box">
          <SvgComponent svg={SVG_FILE.menuEmptyIcon} />
          <p>No Assets for now</p>
        </div>
      </MenuContentView>
    );
  }
  return (
    <MenuContentView className="menuContentView">
      {loading ? (
        <div className="loading-view">Loading...</div>
      ) : (
        <div className={`select-spin ${sortedFatherInscription.type}`}>
          {sortedFatherInscription.childrenInscription.map((item) => {
            // @ts-ignore
            const active =
              item.inscriptionId === userAvatarMetadata[sortedFatherInscription.pathIdKey];

            const expireAtConfig = usableAddressData[sortedFatherInscription.pathIdKey]?.find(
              (usableItem) => usableItem.inscriptionId === item.inscriptionId
            );

            let disabled = isLogin ? true : false;
            let haveRemainTime: Boolean = false;

            if (isLogin) {
              //  可用列表中查找不到
              if (!expireAtConfig) {
                disabled = true;
              } else {
                if (expireAtConfig.expireAt === null) {
                  disabled = false;
                } else {
                  haveRemainTime =
                    typeof expireAtConfig.expireAt === 'number' && expireAtConfig.expireAt > 0
                      ? true
                      : false;
                  disabled = !haveRemainTime;
                }
              }
            }

            const remainTimeStamp = expireAtConfig?.expireAt || 0;
            const remainTime = formatMillisecondsToDDHH(remainTimeStamp);
            return (
              <div
                className={disabled ? 'disabled' : active ? 'active' : ''}
                key={item.inscriptionId}
                onClick={() =>
                  !disabled && onUpdate(sortedFatherInscription.pathIdKey, item.inscriptionId)
                }>
                {item.metadata.icon ? (
                  <img
                    src={getOrdLink(item.metadata.icon).content}
                    alt={item.metadata.icon}
                    draggable={false}
                  />
                ) : (
                  <span>{item.inscriptionId}</span>
                )}
                {isLogin && haveRemainTime && remainTimeStamp > 0 && (
                  <div className="remainTimeBox">
                    <SvgComponent svg={SVG_FILE.clockSvgIcon} />
                    <span>{remainTime}</span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </MenuContentView>
  );
}
