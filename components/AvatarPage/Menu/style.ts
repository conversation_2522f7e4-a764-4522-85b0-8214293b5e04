import styled from 'styled-components';
import { THEME_MEDIA_ENUM } from '../../../constant';

export const MenuRightTabs = styled.div`
  position: absolute;
  right: -0.5rem;
  top: 0;
  transform: translate(100%);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 5rem;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }
  justify-content: flex-start;
  align-items: center;
`;

export const RightMenuItem = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 5rem;
  height: 5rem;
  flex-shrink: 0;
  border-radius: 1.8181875rem;
  border: 0.22725rem solid #945f00;
  background: linear-gradient(270deg, #ff8316 49.72%, #9d4a00 312.5%);

  cursor: pointer;
  transition:
    /* background 0.2s ease-in, */ border 0.2s ease-in;
  &.active {
    background: #fff2e2;
    border: 0.22725rem solid #ed9800;
  }

  & > .right-icon {
    width: 2.72725rem;
    height: 2.72725rem;
    display: flex;
    align-items: center;
    justify-content: center;
    & > svg {
      width: 1.9318125rem;
      height: 1.83925rem;
    }
  }
`;

export const MenuView = styled.div<{ initAni: boolean; isFrontend?: boolean }>`
  position: fixed;
  width: 19rem;
  /* height: auto; */
  height: 39.5rem;
  max-height: calc(${({ isFrontend }) => (isFrontend ? '100% - 13.125rem' : '90%')});
  background: #fafafa99;
  top: 50%;
  left: 0;
  transform: translate(-100%, -50%);
  z-index: 3;
  backdrop-filter: blur(0.25rem);
  padding: 1rem;
  border-radius: 1.5rem;
  border: 0.25rem solid #ed9800;
  background: #fff2e2;
  box-sizing: border-box;
  box-shadow:
    0 0.25rem 0.25rem 0 rgba(0, 0, 0, 0.25),
    0.125rem 0.125rem 0.25rem 0 rgba(255, 255, 255, 0.6) inset;

  .top-menu-tabs {
    width: 100%;
    overflow-x: auto;
    display: flex;
    gap: 0.75rem;
    padding: 0.5rem;
    /* padding: 1rem 1rem 0.5rem 1rem; */
    box-sizing: border-box;
    overflow-y: hidden;
    border-top-right-radius: 1.5rem;
    border-top-left-radius: 1.5rem;
    background: #f7e7cd;
    height: 3.5rem;
    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 0.5rem;
      height: 0.5rem;
      background: #00000010;
      border-radius: 0.5rem;
      background: transparent !important;
      cursor: pointer;
    }

    ::-webkit-scrollbar-track {
      background-color: transparent !important;
      cursor: pointer;
    }

    ::-webkit-scrollbar-thumb {
      cursor: pointer;
      border-radius: 0.5rem;

      background: #c69f7e !important;
    }

    & > div {
      background: #c69f7e;
      border-radius: 0.75rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 3rem;
      min-width: 3rem;
      height: 2.5rem;

      & > img {
        width: 1.5rem;
        height: 1.5rem;
        opacity: 0.5;
      }

      &.active {
        background: #ff7600;
        box-shadow: 0rem 0.25rem 0.25rem 0rem #00000040 inset;

        & > img {
          opacity: 1;
        }
      }
    }
  }

  transition: transform ${({ initAni }) => (initAni ? '0.2s ease-in-out' : 'none')};

  &.show {
    transform: translate(0, -50%);
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 12.8125rem;
    height: 27.625rem;
    padding: 0.5rem 0.5rem;
    border-radius: 1rem;

    .top-menu-tabs {
      width: 100%;
      gap: 0.25rem;
      padding: 0.5rem;
      height: 3.125rem;
      border-top-right-radius: 1rem;
      border-top-left-radius: 1rem;
      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 0.375rem !important;
        height: 0.375rem !important;
        border-radius: 0.375rem !important;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 0.375rem !important;
      }

      & > div {
        width: 2.125rem;
        min-width: 2.125rem;
        height: 2.125rem;

        & > img {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    ${MenuRightTabs} {
      width: 3rem;
      gap: 0.75rem;
      ${RightMenuItem} {
        width: 3rem;
        height: 3rem;
        border-radius: 1rem;
        & > .right-icon {
          width: 1.5rem;
          height: 1.5rem;

          & > svg {
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }

  ${THEME_MEDIA_ENUM.ORD_PREVIEW} {
    display: none;
  }

  ${THEME_MEDIA_ENUM.FRONTEND_LARGE} {
    left: 11.125rem;
    top: 50%;
    border-radius: 2rem;
    transform: translate(0, -50%) !important;
  }
`;

export const MenuContextViewWrapper = styled.div``;

export const MenuContentView = styled.div`
  flex: 1;
  overflow-y: auto;
  /* padding: 0.5rem 1rem 0.5rem 1rem; */
  padding: 0.5rem;
  box-sizing: border-box;
  height: calc(100% - 3.5rem);
  background: #f7e7cd;
  border-bottom-right-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
  /* height: calc(7.5rem * 4 + 1rem); //放4行 */
  &::-webkit-scrollbar {
    width: 0.5rem;
    height: 0.5rem;
    background: #00000010;
    border-radius: 0.5rem;
    background: transparent !important;
    cursor: pointer;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }

  ::-webkit-scrollbar-thumb {
    cursor: pointer;
    border-radius: 0.5rem;

    background: #c69f7e !important;
  }

  .loading-view {
    text-align: center;
    padding: 1.25rem 0;
    color: #00000050;
  }

  .empty-box,
  .loading-box {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -2rem;

    & > p {
      font-family: 'JetBrains Mono';
      font-size: 1rem;
      font-weight: 400;
      line-height: 1.21rem;
      text-align: center;
      color: #615a57;
      margin: 0.5rem 0 0 0;
    }
  }

  .coming-soon {
  }

  & > .select-spin {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 1rem;

    & > div {
      height: 7.5rem;
      border-radius: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-sizing: border-box;
      border: 0.0625rem solid #cabfab;
      position: relative;
      flex-direction: column;
      box-shadow: 0.25rem 0.25rem 0.25rem 0 #fff inset;
      filter: drop-shadow(0 0.125rem 0.5rem rgba(0, 0, 0, 0.15));
      background: #fbf4e8;
      overflow: hidden;
      padding: 0.1875rem;

      & > img {
        width: 100%;
        height: 100%;
      }
      & > .remainTimeBox {
        box-sizing: border-box;
        width: 5.5rem;
        height: 1.5rem;
        display: flex;
        padding: 0.25rem 0.5rem;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        border-radius: 1.5rem;
        border: 0.0625rem solid #cabfab;
        background: #fff;
        flex-wrap: nowrap;
        position: absolute;
        bottom: 0.375rem;
        & > div {
          & > svg {
            width: 1rem;
            height: 1rem;
          }
        }
        & > span {
          display: flex;
          height: 0.9375rem;
          white-space: nowrap;
          justify-content: center;
          align-items: center;

          color: #140f08;
          font-family: 'JetBrains Mono';
          font-weight: 500;
          font-size: 0.75rem;
          line-height: 100%;
          letter-spacing: -0.04em;
        }
      }

      & > span {
        color: #ffffff;
      }

      &.active {
        box-shadow: none;
        border: 0.25rem solid #ff8316;
        padding: 0rem;
      }
      &.disabled {
        cursor: not-allowed;
        filter: grayscale(100%);
      }
    }
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    padding: 0.375rem 0.5rem 0.375rem 0.5rem;
    height: calc(100% - 3.125rem);
    border-bottom-right-radius: 1rem;
    border-bottom-left-radius: 1rem;
    &::-webkit-scrollbar {
      width: 0.375rem !important;
      height: 0.375rem !important;
      border-radius: 0.375rem !important;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 0.375rem !important;
    }

    & > .select-spin {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 0.5rem;

      & > div {
        height: 5.125rem;
        border-radius: 0.625rem;
        border: 0.125rem solid transparent;

        &.active {
          border: 0.125rem solid #ff8316;
        }
      }
    }
  }
`;
