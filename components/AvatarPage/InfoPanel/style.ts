import styled, { css } from 'styled-components';
import { THEME_MEDIA_ENUM } from '../../../constant';

const commonBackgroundStyle = css`
  border-radius: 1.1875rem;
  border: 0.375rem solid #fcdba8;
  background: #fff0c9;
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    border-radius: 1.0625rem;
  }
`;

export const InfoPanelInnerWrapper = styled.div<{ $show: boolean }>`
  width: 100%;
  height: 100%;
  border-radius: 1.25rem;
  position: relative;
  border: 0.0625rem solid #140f08;
  box-sizing: border-box;
  box-shadow: 0 0.125rem 0.5rem 0 rgba(0, 0, 0, 0.35);
  transition: all 0.2s linear 0.1s;

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    border-radius: 1.125rem;
  }
`;

export const InfoPanelOuterWrapper = styled.div<{ $show: boolean }>`
  border-radius: 2rem;
  /* background: #935338; */
  /* padding: 0.75rem; */
  border: 0.75rem solid #935338;
  box-sizing: border-box;
  transition: all 0.2s linear 0.1s;

  &:not(:first-of-type) {
    margin-top: 1rem;
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    margin-top: 0.3125rem;
    border-radius: 1.5rem;
    border: 0.375rem solid #935338;
  }
  ${({ $show }) =>
    $show
      ? css``
      : css`
          border: none !important;
          margin-top: 0 !important;
          ${InfoPanelInnerWrapper} {
            border: none !important;
            box-shadow: none !important;
          }
        `}
`;

export const InfoPanelBox = styled.div<{ initAni: boolean }>`
  position: fixed;
  right: 0;
  top: 50%;
  transform: translate(100%, -50%);
  transition: transform ${({ initAni }) => (initAni ? '0.2s ease-in-out' : 'none')};
  overflow-y: auto;
  box-sizing: border-box;
  z-index: 3;
  max-height: 45rem;
  height: auto;
  box-sizing: border-box;
  &::-webkit-scrollbar {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 0.5rem;
    background: transparent !important;
    cursor: pointer;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent !important;
    cursor: pointer;
  }

  ::-webkit-scrollbar-thumb {
    cursor: pointer;
    border-radius: 0.5rem;
    background: #c69f7e !important;
  }

  &.show {
    transform: translate(0, -50%);
  }
  & > div {
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: end;
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    & > div {
      padding-right: 0.625rem;
    }
  }
  ${THEME_MEDIA_ENUM.FRONTEND_LARGE} {
    right: 25%;
    top: 150%;
    transform: translate(50%, -50%);
    display: inline-flex;
    transition: top 0.2s ease-in-out;
    &.show {
      top: 50%;
      transform: translate(50%, -50%);
    }
  }
`;

export const InfoPanelView = styled.div`
  width: 19.25rem;
  padding: 1.125rem;
  box-sizing: border-box;
  flex-shrink: 0;
  ${commonBackgroundStyle}

  & > h2 {
    font-family: 'JetBrains Mono';
    text-align: right;
    color: #140f08;
    padding: 0;
    margin: 0;

    font-size: 1.5rem;
    font-weight: 700;
    line-height: normal;
    text-transform: capitalize;
  }

  .info-panel-content {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: end;
    overflow: hidden;

    & > p {
      width: 100%;
      padding: 0;
      margin: 0;
      font-family: 'JetBrains Mono';
      font-size: 1.25rem;
      font-weight: 400;
      line-height: 1.25rem;
      text-align: right;
      color: #686663;
      display: flex;
      flex-wrap: wrap;
      justify-content: end;
      /* color: #140F0850; */
      //white-space: nowrap;
      //overflow: hidden;
      //text-overflow: ellipsis;
      & > span {
        white-space: nowrap;
      }
    }
  }

  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 13.125rem;
    min-height: auto;
    border-radius: 1.0625rem;

    padding: 0.75rem;

    & > h2 {
      font-size: 1rem;
      line-height: 1.375rem;
    }
    .info-panel-content {
      margin-top: 0.625rem;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      & > p {
        font-size: 0.875rem;
        line-height: 1rem;
      }
    }
  }
`;
export const InfoPanelEditView = styled.div`
  width: 15.875rem;
  background: #fafafa80;
  backdrop-filter: blur(0.25rem);
  border-radius: 2rem;
  padding: 1.125rem;
  box-sizing: border-box;
  flex-shrink: 0;
  /* margin-top: 1rem; */
  ${commonBackgroundStyle}

  .set-title {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 0.625rem;
    & > span {
      font-family: 'JetBrains Mono';
      font-size: 1.125rem;
      line-height: 1.36125rem;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #686663;
    }
  }

  & > .set-panel-box {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    & > .color-set,
    .texture-set {
      display: flex;
      align-items: center;
      justify-content: end;
      gap: 0.5rem;
      & > span {
        font-family: 'JetBrains Mono';
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.25rem;
        text-align: right;
        color: #686663;
      }
      & > .color-value {
        display: flex;
        align-items: center;
        height: 1.75rem;
        box-sizing: border-box;
        gap: 0.25rem;
        background: #ffffff30;
        border-radius: 0.25rem;
        padding: 0.25rem;
        cursor: pointer;
        border-radius: 0.25rem;
        border: none;
        background: rgba(255, 255, 255, 0.8);

        & > div {
          width: 1.25rem;
          height: 1.25rem;
          border-radius: 0.125rem;
        }
        & > span {
          font-family: 'JetBrains Mono';
          font-size: 1.25rem;
          font-weight: 400;
          line-height: 1.25rem;
          color: #686663;
        }
        &.active {
          border: 0.0625rem solid #140f08;
          & > span {
            color: #140f08;
          }
        }
      }
      & > .texture-value {
        width: 1.75rem;
        height: 1.75rem;
        background: #ffffff1a;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
        box-sizing: border-box;
        cursor: pointer;
        & > img {
          width: 1.25rem;
          height: 1.25rem;
        }
        &.active {
          border: 0.0625rem solid #140f08;
        }
      }
    }
  }
  .empty-box {
    width: 1.75rem;
    height: 1.75rem;
    background: #fafafa80;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    box-sizing: border-box;
    cursor: pointer;
    &.active {
      border: 0.0625rem solid #140f08;
    }
  }
  .eyes-icon {
    cursor: pointer;
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 13.125rem;
    min-height: auto;
    border-radius: 1.0625rem;

    padding: 0.75rem;
    & > .set-title {
      & > span {
        font-size: 1rem;
      }
    }
    & > .set-panel-box {
      margin-top: 0.5rem;
      & > .color-set,
      .texture-set {
        gap: 0.5rem;
        & > span {
          font-size: 0.875rem;
          line-height: 1rem;
        }
        & > .color-value {
          display: flex;
          align-items: center;
          height: 1.5rem;
          padding: 0.25rem;

          & > div {
            width: 1rem;
            height: 1rem;
          }
          & > span {
            font-size: 0.875rem;
            line-height: 1rem;
          }
        }
        & > .texture-value {
          width: 1.5rem;
          height: 1.5rem;
          & > img {
            width: 1rem;
            height: 1rem;
          }
          &.active {
            border: 0.0625rem solid #ffffff;
            color: #ffffff;
          }
        }
        & > .empty-icon {
          margin-left: 0rem;
        }
      }
    }
    .empty-box {
      width: 1.5rem;
      height: 1.5rem;
      cursor: pointer;
      svg {
        width: 1rem;
        height: 1rem;
      }
    }
  }
`;
export const SelectColorPanelView = styled.div<{ show: boolean }>`
  width: 19rem;
  max-height: ${({ show }) => (show ? '31.25rem' : '0')};
  transition: 0.2s linear;
  overflow: hidden;
  /* margin-top: 1rem; */
  flex-shrink: 0;
  ${commonBackgroundStyle}
  ${({ show }) =>
    !show &&
    css`
      border: none;
    `}

  .select-color-panel {
    box-sizing: border-box;
    padding: 1.125rem;
    .react-colorful__saturation {
      border-bottom: none;
      border-radius: 0;
      margin-bottom: -0.25rem;
    }
    .react-colorful__pointer {
      width: 1.5rem;
      height: 1.5rem;
      background: transparent;
      border: 0.375rem solid #fff;
      & > div {
        background: transparent !important;
      }
    }
    .react-colorful__hue {
      margin-top: 0.625rem;
      height: 1.625rem;
      border-radius: 3.125rem;
    }
    .select-color-btn {
      margin-top: 0.625rem;
      width: 100%;
      height: 1.75rem;
      background: rgba(255, 255, 255, 0.5);
      border: 0.0625rem solid #140f08;
      border-radius: 1rem;
      box-sizing: border-box;
      padding: 0.25rem;
      display: flex;
      align-items: center;
      & > div {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.125rem;
        border-radius: 50%;
      }
      & > span {
        flex: 1;
        text-align: center;
        font-family: 'JetBrains Mono';
        font-size: 1.25rem;
        font-weight: 400;
        color: #140f08;
      }
    }
    .action-btns {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 0.375rem;
      margin-top: 0.625rem;
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 2.25rem;
        border-radius: 1rem;
        cursor: pointer;
        &:not(.cancel) {
          background: #ff8316;
          box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
          transition:
            background 0.1s,
            transform 0.1s;
          &:active {
            background: #e56f0c;
            transform: scale(0.96);
            box-shadow: 0 -0.125rem 0 0 rgba(0, 0, 0, 0.18) inset;
          }
        }
        &.cancel {
          border-radius: 16px;
          background: #c9b7a5;
          box-shadow: none;
        }
      }
    }
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 13.125rem;
    .select-color-panel {
      border-radius: 0.75rem;
      padding: 0.75rem;
      .react-colorful__pointer {
        width: 1.125rem;
        height: 1.125rem;
      }
      .react-colorful__alpha,
      .react-colorful__hue {
        height: 1.25rem;
      }
      .select-color-btn {
        margin-top: 0.75rem;
        height: 1.375rem;
        padding: 0.25rem;
        & > div {
          width: 1rem;
          height: 1rem;
        }
        & > span {
          font-size: 1rem;
          line-height: 1.125rem;
        }
      }
      .action-btns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 0.75rem;
        margin-top: 0.75rem;
        & > div {
          height: 2rem;
          & > img {
            width: 1.25rem;
            height: 1.25rem;
          }
        }
      }
    }
  }
`;
export const SelectTexturePanelView = styled.div<{ show: boolean }>`
  width: 19rem;
  max-height: ${({ show }) => (show ? '31.25rem' : '0')};
  transition: 0.2s linear;
  overflow: hidden;
  flex-shrink: 0;
  ${commonBackgroundStyle}
  ${({ show }) =>
    !show &&
    css`
      border: none;
    `}
  .select-texture-panel {
    box-sizing: border-box;
    padding: 1.125rem;
    & > h2 {
      color: #140f08;
      text-align: center;
      font-family: 'JetBrains Mono';
      font-size: 1rem;
      font-style: normal;
      font-weight: 700;
      line-height: 120%;
    }
    & > input {
      width: 100%;
      height: 3rem;
      font-family: 'JetBrains Mono';
      font-size: 1.25rem;
      font-weight: 400;
      line-height: 1.5125rem;
      color: #140f08;
      padding: 0 1rem;
      box-sizing: border-box;
      outline: none;
      border-radius: 1rem;
      border: 0.0625rem solid rgba(20, 15, 8, 0.4);
      background: rgba(250, 250, 250, 0.5);
      &::placeholder {
        color: #140f0866;
      }
    }
    .texture-show {
      margin-top: 1rem;
      width: 100%;
      height: 16rem;
      display: flex;
      align-items: center;
      justify-content: center;
      & > .loading-view {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        & > div {
          width: 0.75rem;
          height: 0.75rem;
          border-radius: 50%;
          background: #ffffff;
          opacity: 0.4;
          &:nth-child(1) {
            opacity: 1;
          }
        }
      }
      & > img {
        width: 12.5rem;
      }
    }
    .action-btns {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 1rem;
      margin-top: 0.625rem;
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 2.25rem;

        border-radius: 1rem;
        cursor: pointer;
        &:not(.cancel) {
          background: #ff8316;
          box-shadow: 0 -0.25rem 0 0 rgba(0, 0, 0, 0.25) inset;
          transition:
            background 0.1s,
            transform 0.1s;
          &:active {
            background: #e56f0c;
            transform: scale(0.96);
            box-shadow: 0 -0.125rem 0 0 rgba(0, 0, 0, 0.18) inset;
          }
        }
        &.cancel {
          border-radius: 16px;
          background: #c9b7a5;
          box-shadow: none;
        }

        & > img {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
  ${THEME_MEDIA_ENUM.ORD_BROWSER} {
    width: 13.125rem;
    .select-texture-panel {
      border-radius: 1.0625rem;
      padding: 0.75rem;
      & > h2 {
        font-size: 1rem;
        line-height: 1rem;
      }
      & > input {
        margin-top: 0.75rem;
        border-radius: 0.75rem;
        height: 2.25rem;
        font-size: 0.875rem;
        line-height: 0.875rem;
        padding: 0 0.75rem;
      }
      .texture-show {
        margin-top: 0.75rem;
        height: 11.5rem;
        & > .loading-view {
          display: flex;
          align-items: center;
          gap: 1.125rem;
          & > div {
            width: 0.625rem;
            height: 0.625rem;
          }
        }
        & > img {
          width: 100%;
        }
      }
      .action-btns {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 0.75rem;
        margin-top: 0.75rem;
        & > div {
          height: 2rem;
          & > img {
            width: 1.25rem;
            height: 1.25rem;
          }
        }
      }
    }
  }
`;
