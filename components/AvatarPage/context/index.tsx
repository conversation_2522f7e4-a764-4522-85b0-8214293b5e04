import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { UsableAddressDataType } from '../../../constant/type';

interface IAvatarPageContext {
  usableAddressData: UsableAddressDataType;
  setUsableAddressData: (data: UsableAddressDataType) => void;
  isLogin: boolean;
}

const defaultUsableAddressDataContext: IAvatarPageContext = {
  usableAddressData: {} as UsableAddressDataType,
  setUsableAddressData: () => {},
  isLogin: false,
};

const AvatarPageContext = createContext<IAvatarPageContext>(defaultUsableAddressDataContext);

export const UsableAddressProvider = ({
  children,
  defaultUsableAddressDataContext,
  isLogin: defaultLogin = false,
}: {
  children: React.ReactNode;
  defaultUsableAddressDataContext?: UsableAddressDataType;
  isLogin?: boolean;
}) => {
  const [isLogin, setIsLogin] = useState<boolean>(defaultLogin);

  const [usableAddressData, setUsableAddressData] = useState<UsableAddressDataType>(
    {} as UsableAddressDataType
  );

  useEffect(() => {
    setIsLogin(defaultLogin);
  }, [defaultLogin]);

  useEffect(() => {
    // Initialize with the default context data if provided
    if (defaultUsableAddressDataContext) {
      setUsableAddressData(defaultUsableAddressDataContext);
    }
  }, [defaultUsableAddressDataContext]);

  const memoValue = useMemo(() => {
    return { usableAddressData, setUsableAddressData, isLogin };
  }, [usableAddressData, isLogin]);

  return (
    <AvatarPageContext.Provider value={memoValue}>
      <>{children}</>
    </AvatarPageContext.Provider>
  );
};

export const useUsableAddress = () => {
  const context = useContext(AvatarPageContext);
  if (!context) {
    throw new Error('useUsableAddress must be used within a UsableAddressProvider');
  }
  return context;
};
