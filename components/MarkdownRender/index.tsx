'use client';

import { useEffect, useState } from 'react';
import useMarked from '@rootHooks/useMarked';
import styles from './index.module.css';

interface MarkdownRenderProps {
  context: string;
}

export default function MarkdownRender({ context }: MarkdownRenderProps) {
  const { markdownToHtml } = useMarked();
  const [html, setHtml] = useState<string>('');

  async function markdownRender() {
    const result = await markdownToHtml(context);
    setHtml(result);
  }

  useEffect(() => {
    markdownRender();
  }, [context, markdownToHtml]);

  return <div className={styles.markdownContent} dangerouslySetInnerHTML={{ __html: html }} />;
}
