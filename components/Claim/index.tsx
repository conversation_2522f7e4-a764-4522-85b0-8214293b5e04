'use client';
import { forwardRef, Ref, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import Dialog from '@/commons/Dialog';
import ModalContent from '@/commons/ModalContent';
import Image from 'next/image';
import styled from 'styled-components';
import Rank1 from '/public/image/rank1.png';
import Rank2 from '/public/image/rank2.png';
import Rank3 from '/public/image/rank3.png';
import Rank4 from '/public/image/rank4.png';
import Rank5 from '/public/image/rank5.png';
import { motion } from 'motion/react';
import { claimWater } from '@/server';
import toast from 'react-hot-toast';

// Community rank image mapping
const COMMUNITY_RANK_IMAGES = {
  potato: Rank2.src,
  wangcai: Rank1.src,
  TheLonelyBit: Rank3.src,
  Pizza: Rank4.src,
  DomoDucks: Rank5.src,
};

// Community types type definition
type CommunityType = keyof typeof COMMUNITY_RANK_IMAGES;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
`;

const MenuContent = styled(motion.div)`
  display: flex;
  align-items: center;
  width: calc(100% - 3.75rem);
  justify-content: space-around;
  padding: 1.875rem 1.5rem;
  background-color: #f5e5c7;
  border-radius: 1.75rem;
  box-shadow: inset 0 0rem 1.25rem rgba(0, 0, 0, 0.15);
`;

const TextContainer = styled.div`
  padding: 1.875rem 2.25rem;
  box-sizing: border-box;
`;

const Items = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 0.625rem;
  border-top: 0.1875rem solid #8c8475;
  box-shadow: 0 0rem 0.25rem rgba(0, 0, 0, 0.1) inset;
  background: #c2b8a2;
  border-radius: 1rem;
  /* width: 6.25rem; */
  height: 2.1875rem;
  position: relative;

  .icon {
    /* position: absolute;
        left: -1.125rem;
        top: 50%;
        transform: translate(0%, -50%); */
  }

  .value {
    color: #fff;
    font-size: 1.25rem;
    font-weight: bold;
    /* padding-left: 0.625rem; */
    padding-right: 0.625rem;
  }
`;

interface ModalProps {
  onClose: () => void;
}

interface ClaimData {
  faucetAmount: string;
  faucetType: string;
  tickBalance: string;
  tickIcon: string;
}

export interface ClaimRef {
  open: (data: ClaimData, communityType: CommunityType) => void;
  close: () => void;
  reset: () => void;
}

const Claim = forwardRef<ClaimRef, ModalProps>(({ onClose }: ModalProps, ref: Ref<ClaimRef>) => {
  const [isMounted, setIsMounted] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [claimData, setClaimData] = useState<ClaimData | null>(null);
  const [communityType, setCommunityType] = useState<CommunityType>('potato');

  const [confirmLoading, setConfirmLoading] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  const communityText = useMemo(() => {
    return `You'll receive rewards from the Pizzaswap community pool.
      There are currently ${communityType} x ${claimData?.tickBalance ?? 0} remaining.`;
  }, [communityType, claimData]);

  useImperativeHandle(ref, () => ({
    open: (claimData: ClaimData, communityType: CommunityType) => {
      setCommunityType(communityType);
      setIsOpen(true);
      setClaimData(claimData);
    },
    // 提交成功后重置积分和资源
    reset: () => {
      setClaimData(null);
    },
    close: () => {
      setIsOpen(false);
      setClaimData(null);
    },
  }));

  const rankImg = useMemo(() => {
    return COMMUNITY_RANK_IMAGES[communityType] || '';
  }, [communityType]);

  if (!isMounted) {
    return null;
  }

  const onConfirm = async () => {
    setConfirmLoading(true);
    const res = await claimWater(communityType);
    const { code, msg } = res.data;
    if (code === 1) {
      toast.success('Claim success');
      setIsOpen(false);
      setClaimData(null);
    } else {
      toast.error(msg);
    }
    setConfirmLoading(false);
  };

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      <ModalContent
        confirmText="Claim"
        onConfirm={onConfirm}
        confirmLoading={confirmLoading}
        confirmDisabled={confirmLoading}
        onClose={() => {
          setIsOpen(false);
          setConfirmLoading(false);
        }}
        onCancel={() => {
          setIsOpen(false);
          setConfirmLoading(false);
        }}
        title={
          <Image
            src="/image/claim.png"
            alt="claim"
            width={360}
            height={62}
            style={{ width: '22.5rem', height: '3.875rem' }}
          />
        }
        footerStyle={{
          padding: '0.625rem 2.625rem 1.875rem',
        }}>
        <ContentContainer>
          <TextContainer>{communityText}</TextContainer>
          <MenuContent
            initial={{ y: '3.125rem', opacity: 0 }}
            animate={{ y: '0rem', opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}>
            <Items>
              <div className="icon">
                <Image
                  src={rankImg}
                  alt="rank"
                  width={50}
                  height={50}
                  style={{
                    width: '3.125rem',
                    height: '3.125rem',
                  }}
                />
              </div>
              <div className="value">{claimData?.faucetAmount}</div>
            </Items>
          </MenuContent>
        </ContentContainer>
      </ModalContent>
    </Dialog>
  );
});
Claim.displayName = 'Claim';

export default Claim;
