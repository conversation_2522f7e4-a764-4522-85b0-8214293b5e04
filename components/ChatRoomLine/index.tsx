import styled from 'styled-components';
import { useEffect, useRef, useState } from 'react';
import ArrowLeftRight from '@/commons/ArrowLeftRight';
import { useNetWork } from '@/world/hooks/useNetWork';

// 定义dot颜色列表 - 空闲、繁忙、火爆
const dotColorList = {
  FREE: '#16FF26', // 空闲 (0-60%)
  BUSY: '#FF8316', // 繁忙 (60%-100%)
  FULL: '#FF2424', // 火爆 (100%)
};

const ChatRoomView = styled.div<{ isExpanded: boolean }>`
  position: relative;
  width: 12.5rem;
  height: ${(props) => (props.isExpanded ? '17.5rem' : '3.125rem')};
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 1.25rem;
  transition: height 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border: 0.0625rem solid #fff;
`;

const ToggleButton = styled.button<{ isExpanded: boolean }>`
  position: absolute;
  top: 0rem;
  left: 0rem;
  ${(props) =>
    props.isExpanded
      ? `
    width: 100%;
  `
      : `
    width: 100%;
  `}
  background: none; // 移除背景色
  border: none;
  color: white;
  cursor: pointer;
  padding: 0; // 移除内边距
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ChevronLeftIconContainer = styled.div`
  font-size: 1.875rem;
  line-height: 1.5625rem;
  width: 100%;
  height: 3.125rem;
`;

const LineListContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 0.5rem;

  .line-item {
    width: 100%;
    height: 2.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;

    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
      border-radius: 0.625rem;
    }
  }

  .dot {
    width: 0.625rem;
    height: 0.625rem;
    border-radius: 50%;
  }

  .line-divider {
    width: 94%;
    height: 0.0625rem;
    background-color: #9a9a9a;
    margin-top: auto;
  }

  .current-room {
    width: 100%;
    height: 2.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
  }
`;

const CurrentRoomContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 0.375rem;

  .dot {
    width: 0.625rem;
    height: 0.625rem;
    border-radius: 50%;
  }
`;

interface GameNodeInfo {
  id: string;
  mode: number;
  modeIndex: number; // 线路
  playerCount: number; // 人数
  maxPlayerCount?: number; // 最大人数
}

/**
 * @deprecated
 */
const ChatRoomLine = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEnterRoom, setIsEnterRoom] = useState(false);
  const [lineList, setLineList] = useState<GameNodeInfo[]>([]);
  const [currentRoom, setCurrentRoom] = useState<GameNodeInfo | null>(null);
  const { enterRoom, getRoomList, watchRoomStatus } = useNetWork();
  const [isLoading, setIsLoading] = useState(false);
  const mapIdRef = useRef<any>('');
  const mapIndexRef = useRef<any>('');

  // 根据服务器人数百分比获取dot颜色
  const getDotColor = (item: GameNodeInfo) => {
    // 如果没有maxPlayerCount，默认为空闲状态
    if (!item.maxPlayerCount || item.maxPlayerCount <= 0) {
      return dotColorList.FREE;
    }

    // 计算百分比
    const percentage = (item.playerCount / item.maxPlayerCount) * 100;

    // 根据百分比选择颜色
    if (percentage === 100) {
      return dotColorList.FULL; // 火爆 (100%)
    } else if (percentage >= 60) {
      return dotColorList.BUSY; // 繁忙 (60%-100%)
    } else {
      return dotColorList.FREE; // 空闲 (0-60%)
    }
  };

  // 获取线路列表
  const getLineList = async () => {
    setIsLoading(true);
    const res = await getRoomList();
    setLineList(res);

    // 根据mapIndex设置当前房间
    const currentRoom = res.find((item) => item.modeIndex === mapIndexRef.current);
    if (currentRoom) {
      setCurrentRoom(currentRoom);
    }

    setIsLoading(false);
  };
  useEffect(() => {
    if (isExpanded) {
      getLineList();
    }
  }, [isExpanded]);

  useEffect(() => {
    // 判断是否进入房间
    watchRoomStatus((data) => {
      mapIdRef.current = data.mapId;
      mapIndexRef.current = data.mapIndex;
      getLineList();
      setIsEnterRoom(data.isEnterRoom);
    });
  }, []);

  if (!isEnterRoom) {
    return null;
  }

  return (
    <ChatRoomView isExpanded={isExpanded}>
      <ToggleButton isExpanded={isExpanded} onClick={() => setIsExpanded(!isExpanded)}>
        {isExpanded ? (
          <ArrowLeftRight />
        ) : (
          <ChevronLeftIconContainer>
            <ArrowLeftRight />
          </ChevronLeftIconContainer>
        )}
      </ToggleButton>
      {isExpanded ? (
        <LineListContainer>
          {isLoading ? (
            <div>Loading...</div>
          ) : (
            <>
              {lineList.map((item, index) => (
                <div
                  key={index}
                  className="line-item"
                  onClick={() => {
                    enterRoom(mapIdRef.current, item.modeIndex);
                    setIsExpanded(false);
                  }}>
                  <div className="dot" style={{ backgroundColor: getDotColor(item) }} />
                  <div>Change Server:</div>
                  <div>{item.modeIndex}</div>
                </div>
              ))}

              {/* 分割线 */}
              {currentRoom && <div className="line-divider"></div>}

              {/* 当前房间显示在底部 */}
              {currentRoom && (
                <div className="current-room">
                  {/*<div*/}
                  {/*  className="dot"*/}
                  {/*  style={{ backgroundColor: getDotColor(currentRoom) }}*/}
                  {/*/>*/}
                  <div>Current Server:</div>
                  <div>{currentRoom.modeIndex}</div>
                </div>
              )}
            </>
          )}
        </LineListContainer>
      ) : (
        <CurrentRoomContainer>
          {isLoading ? (
            <div>Loading...</div>
          ) : currentRoom ? (
            <>
              {/*<div*/}
              {/*  className="dot"*/}
              {/*  style={{ backgroundColor: getDotColor(currentRoom) }}*/}
              {/*/>*/}
              <div>Current Server: {currentRoom.modeIndex}</div>
            </>
          ) : (
            <div>No Room</div>
          )}
        </CurrentRoomContainer>
      )}
    </ChatRoomView>
  );
};

export default ChatRoomLine;
