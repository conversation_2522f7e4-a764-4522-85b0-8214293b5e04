import styled from 'styled-components';
import Bg from '/public/image/header/modal-bg.png';

export const PlantingPotatoesView = styled.div<{ already: boolean }>`
  width: 200px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ already }) =>
    already ? '#FAFAFA80' : 'linear-gradient(180deg, #FF5039 0%, #FF334A 100%)'};
  border-radius: 24px;
  backdrop-filter: ${({ already }) => (already ? 'blur(16px)' : 'none')};
  gap: 8px;
  cursor: pointer;
  position: relative;
  border: ${({ already }) => (already ? '0' : '3px')} solid #fff89a;
  box-sizing: border-box;
  & > img.potato-tree {
    width: 48px;
    height: 48px;
  }
  & > .snow {
    position: absolute;
    top: 0;
    left: 50%;
    width: calc(100% + 6px);
    transform: translate(-50%, -8px);
  }
  & > span {
    font-family: 'JetBrains Mono';
    font-size: 18px;
    font-weight: 500;
    line-height: 23.76px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: ${({ already }) => (already ? '#686663' : '#FFF89A')};
  }
`;
export const PotatoBtnView = styled.button`
  width: 224px;
  height: 56px;
  border-radius: 12px;
  background: linear-gradient(180deg, #a6e81e 0%, #86d81a 48%, #61d018 100%);
  box-shadow: 0px -4px 1px 1px #0000002e inset;
  cursor: pointer;
  border: 0;
  outline: none;
  &[disabled] {
    background: linear-gradient(
      180deg,
      rgba(166, 232, 30, 0.5) 0%,
      rgba(134, 216, 26, 0.5) 48%,
      rgba(97, 208, 24, 0.5) 100%
    );
    box-shadow: 0px 2px 2px 0px #deef5a inset;
    cursor: not-allowed;
  }
  span {
    font-family: 'JetBrains Mono';
    font-size: 20px;
    font-weight: 700;
    line-height: 26.4px;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    text-shadow:
      1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
      1px 1px 0 #000;
  }
`;
export const AdoptPotatoPetModalView = styled.div`
  width: 864px;
  min-height: 500px;
  position: relative;
  padding: 64px 42px;
  box-sizing: border-box;
  background-image: url('${Bg.src}');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  & > .modal-header {
    width: 353px;
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, -22%);
    & > .title-img {
      width: 100%;
    }
    & > .title-snow {
      width: 316px;
      position: absolute;
      top: -13px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  & > .plant-view {
    display: grid;
    grid-template-columns: 1fr 0px 1fr 0px 1fr;

    & > .plant-item {
      flex: 1;

      & > img {
        width: 100%;
      }
    }

    & > .jump1 {
      position: relative;

      & > img {
        position: absolute;
        left: 0;
        width: 87px;
        margin-top: 69px;
        transform: translateX(-50%);
      }
    }

    & > .jump2 {
      position: relative;

      & > img {
        position: absolute;
        top: 35px;
        left: 0;
        width: 87px;
        margin-top: 69px;
        transform: translateX(-45%) rotate(146deg) scaleX(-1);
      }
    }
  }
  & > .desc {
    font-family: 'JetBrains Mono';
    font-size: 16px;
    font-weight: 400;
    line-height: 21.12px;
    letter-spacing: -0.04em;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
    margin-top: 10px;
  }
  & > .turnstile-view {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  & > .action {
    width: 485px;
    margin: 24px auto 0 auto;

    & > p {
      text-align: center;
      font-family: 'JetBrains Mono';
      font-size: 16px;
      font-weight: 400;
      line-height: 21.12px;
      letter-spacing: -0.04em;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #140f08;
    }

    & > .action-btns {
      margin-top: 24px;
      padding: 0 6.5px;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 24px;

      .refuse-btn {
        height: 56px;
        border-radius: 12px;
        background: #d4caa9;
        font-family: 'JetBrains Mono';
        font-size: 20px;
        font-weight: 400;
        line-height: 26.4px;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
        cursor: pointer;
        border: 0;
        outline: none;
      }
    }
  }
`;
