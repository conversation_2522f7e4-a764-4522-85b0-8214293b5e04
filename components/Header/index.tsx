import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Styled<PERSON>ogo } from './style';
import LogoIcon from '/public/image/logo.png';
import { useDispatch, useSelector } from 'react-redux';
import { setFishEasterEggModal } from '../../store/app';
import { useRouter } from 'next/router';
import classNames from 'classnames';
import Search from './Search';
import { useEffect, useMemo, useState } from 'react';
import PlantingPotatoes from './PlantingPotatoes';
import { DOMAIN_JUMP, WEBSITE_DOMAIN } from '../../constant';
import { IAppState } from '../../constant/type';
import Ranking from '../Ranking/inex';
import Setting from '@/commons/Setting';
import FishEggButton from '../FishEggButton';
import FishEggProcessModal from '../FishEggProcessModal';
import { resetGameState } from '@/store/game';
import useBagInventory from '@/hooks/useBagInventory';
import UserProfile from './components/UserProfile';

export default function Header({
  isFixed,
  isClaimPotato = true,
}: {
  isFixed: boolean;
  isClaimPotato?: boolean;
}) {
  const router = useRouter();
  const { btcAddress, domainOwner, isRecording, easterEggInfo, fishEasterEggModal } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const { getBagInventoryListDta } = useBagInventory(false);
  const [hoverRouter, setHoverRouter] = useState<number>(-1);
  const [hoverRouterClone, setHoverRouterClone] = useState<number>(-1);
  const dispatch = useDispatch();

  const routers = [
    {
      name: 'Home',
      path: '/home',
    },
    {
      name: 'My Avatar',
      path: '/',
    },
  ];

  const showLine = hoverRouter === -1 && !routers.find((item) => item.path === router.pathname);
  const activeRouter = useMemo(() => {
    return routers.findIndex((item) => item.path === router.pathname);
  }, [router.pathname]);
  useEffect(() => {
    if (activeRouter !== -1) {
      setHoverRouter(activeRouter);
      setHoverRouterClone(activeRouter);
    }
  }, [activeRouter]);
  const leftStep = 100 / routers.length;
  const goLink = (path: string) => {
    if (!btcAddress || !domainOwner) {
      // 如果当前是用户域名跳转到主域名
      const domain = window.location.hostname;
      if (domain.includes(DOMAIN_JUMP)) {
        window.location.href = `${WEBSITE_DOMAIN}${path}`;
        return;
      }
    }
    router.push(path);
  };

  // 是否显示钓鱼彩蛋按钮是否显示钓鱼彩蛋按钮
  const isShowFishEggButton = useMemo(() => {
    if (easterEggInfo) {
      let finishCount = 0;
      easterEggInfo.forEach((item) => {
        if (item.isSuccess) {
          finishCount++;
        }
      });
      if (finishCount < 4) {
        return true;
      }
    }
    return false;
  }, [easterEggInfo]);

  // 钱包登陆后初始化请求一次背包里的装备栏目数据列表
  useEffect(() => {
    if (btcAddress) {
      getBagInventoryListDta();
    }
  }, [btcAddress]);

  const isHomePage = useMemo(() => {
    return router.pathname === '/home';
  }, [router]);

  return (
    <HeaderView
      isFixed={isFixed}
      style={{
        display: isRecording ? 'none' : 'flex',
      }}>
      <StyledLogo
        onClick={(e) => {
          e.preventDefault();
          router.push('/home');
        }}>
        <img src={LogoIcon.src} alt="SatWorld" draggable={false} />
        {/*<span>SatWorld</span>*/}
      </StyledLogo>
      <div className="navs">
        <Search />
        {isHomePage && (
          <div className="nav-list">
            <div
              className={classNames(
                'bg-box',
                (hoverRouter !== -1 || activeRouter !== -1) && 'active'
              )}
              style={{
                left:
                  activeRouter === -1 && hoverRouter === -1 && hoverRouterClone !== -1
                    ? hoverRouterClone * leftStep + leftStep / 2 + '%'
                    : hoverRouter * leftStep + leftStep / 2 + '%',
              }}
            />
            {routers.map((item, index) => (
              <div key={index} className="nav-item-box">
                <div
                  className={classNames(
                    'nav-item',
                    ((router.pathname === item.path && hoverRouter === -1) ||
                      hoverRouter === index) &&
                      'active'
                  )}
                  onMouseEnter={() => {
                    setHoverRouter(index);
                    setHoverRouterClone(index);
                  }}
                  onMouseLeave={() => {
                    setHoverRouter(activeRouter);
                  }}
                  // onClick={() => toast('BUIDLing', {duration: 6000, id: 'buidl'})}
                >
                  <span onClick={() => goLink(item.path)}>{item.name}</span>
                </div>
                {showLine && index !== routers.length - 1 && (
                  <div className="line">
                    <div />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      {/* <PlantingPotatoes isClaimPotato={isClaimPotato} /> */}
      {/* {isShowFishEggButton && <FishEggButton />} */}
      <Ranking />
      <UserProfile />
      {/* <ConnectWalletBtn /> */}
      <Setting />
      {fishEasterEggModal && (
        <FishEggProcessModal
          isOpen={fishEasterEggModal}
          onClose={() => {
            dispatch(setFishEasterEggModal(false));
          }}
        />
      )}
    </HeaderView>
  );
}
