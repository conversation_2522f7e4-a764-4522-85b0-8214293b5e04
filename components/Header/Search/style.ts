import styled from 'styled-components';

export const SearchView = styled.div<{ isHover: boolean }>`
  width: 17.5rem;
  /* width: 10vw; */
  /* min-width: 100px; */
  /* max-width: 15vw; */
  height: 3rem;
  border-radius: 1rem;
  border: 0.0625rem solid ${({ isHover }) => (isHover ? '#7C7C7C' : 'transparent')};
  background: #fafafa80;
  box-sizing: border-box;

  display: flex;
  align-items: center;
  transition: all 0.1s;
  padding: 0.5rem 1rem;
  box-sizing: border-box;
  position: relative;
  & > input {
    width: 100%;
    flex: 1;
    background: transparent;
    outline: none;
    border: 0;
    color: #140f08;
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.02em;
    &::placeholder {
      font-family: 'JetBrains Mono';
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      color: #7c7c7c;
    }
    &.transparent {
      caret-color: transparent;
      &::placeholder {
        color: transparent;
      }
    }
  }
  & > .input-div {
    position: absolute;
    left: 0;
    top: 0;
    width: 13.125rem;
    height: 100%;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 0 1rem;
    box-sizing: border-box;
    & > span {
      color: #140f08;
      font-size: 1rem;
      font-weight: 400;
      // 文字间距
      letter-spacing: 0.02em;
      &.suffix {
        background: #e2af6e;
      }
    }
  }
  & > .right-tag {
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 0.5rem;
    transition: all 0.1s;
    &.right-tag1 {
      width: 2rem;
      & > div {
        width: 0.0625rem;
        height: 1rem;
        background: #615a57;
        transform: rotate(45deg);
      }
    }
    &.right-tag2 {
      width: 2.5rem;
      & > span {
        font-family: 'JetBrains Mono';
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 0.875rem;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #140f08;
      }
    }
  }
`;
