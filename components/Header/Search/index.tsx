import { SearchView } from './style';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { useRouter } from 'next/router';
import { checkIsBtcAddress } from '../../../utils';
import toast from 'react-hot-toast';
import { useSelector } from 'react-redux';
import { IAppState } from '../../../constant/type';
import { DOMAIN_JUMP, WEBSITE_DOMAIN } from '../../../constant';
import { getDomainOwner } from '../../../server';

export default function Search() {
  const [isHover, setIsHover] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const isActive = isHover || isFocus;

  const router = useRouter();
  const [searchValue, setSearchValue] = useState<string>('');
  const [suffix, setSuffix] = useState<string>('');

  const inputRef: any = useRef();

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement> | boolean) => {
    const domain = searchValue + suffix;

    if (typeof event === 'boolean' || event.key === 'Enter') {
      if (checkIsBtcAddress(searchValue)) {
        if (searchValue === btcAddress) {
          setSearchValue('');
          router.replace('/');
        } else {
          // 判断当前是否用户域名
          if (window.location.href.includes(DOMAIN_JUMP)) {
            window.location.href = `${WEBSITE_DOMAIN}/avatar?address=${searchValue}`;
          } else {
            router.push(`/avatar?address=${searchValue}`);
          }
        }
      } else if (domain.includes(DOMAIN_JUMP)) {
        if (domain.includes(DOMAIN_JUMP)) {
          let match = domain.match(/^([^.]+)\./);
          const name = match ? match[1].replace('https://', '') : null; // 如果匹配成功，返回第一个捕获组
          if (!name) {
            return;
          }
          getDomainOwner(name).then((res) => {
            if (res.data.data.address) {
              window.location.href = `${domain.includes('https') ? domain : 'https://' + (domain.endsWith('.io') ? domain : domain + '.io')}`;
            } else {
              toast.error('Invalid address/domain');
            }
          });
        } else {
          router.push(`/avatar?address=${searchValue}`);
        }
      } else {
        toast.error('Invalid address/domain');
      }
    } else if (event.key === 'Backspace') {
      event.preventDefault(); // 阻止默认的删除行为

      // 检查是否有文本被选中且是全选
      const isAllSelected =
        inputRef.current &&
        inputRef.current.selectionStart === 0 &&
        inputRef.current.selectionEnd === (searchValue + suffix).length;

      if (isAllSelected) {
        // 如果是全选，清空所有内容
        setSearchValue('');
        setSuffix('');
      } else {
        // 如果有suffix，只清空suffix
        if (suffix) {
          setSuffix('');
        } else if (searchValue.length > 0) {
          // 如果没有suffix，删除searchValue的最后一个字符
          setSearchValue(searchValue.slice(0, -1));
        }
      }
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData('text');

    // 清理粘贴的文本（去除可能的空格和换行）
    const cleanText = pastedText.trim();

    // 如果是比特币地址或域名，直接设置
    if (checkIsBtcAddress(cleanText) || cleanText.includes(DOMAIN_JUMP)) {
      setSearchValue(cleanText);
    } else {
      // 如果不是完整域名但可能是域名前缀，也允许粘贴
      setSearchValue(cleanText);
    }
  };

  useMemo(() => {
    // 判断尾部重叠部分 设置后缀
    if (!searchValue) {
      setSuffix('');
      return;
    }

    // 如果已经包含完整域名后缀，不需要显示后缀
    if (searchValue.includes(DOMAIN_JUMP) || searchValue.startsWith('bc1p')) {
      setSuffix('');
      return;
    }

    const fullSuffix = DOMAIN_JUMP; // 假设是 ".unisat.io"
    let newSuffix = fullSuffix;

    // 从输入值的末尾开始，逐个字符检查是否与后缀的开头部分匹配
    for (let i = 1; i <= Math.min(searchValue.length, fullSuffix.length); i++) {
      const inputEnd = searchValue.slice(-i);
      const suffixStart = fullSuffix.slice(0, i);

      if (inputEnd === suffixStart) {
        // 如果找到匹配，则截取后缀中剩余的部分
        newSuffix = fullSuffix.slice(i);
      }
    }

    setSuffix(newSuffix);
  }, [searchValue]);

  // 当失去焦点时，自动补全后缀
  useMemo(() => {
    if (!isFocus) {
      setSearchValue(searchValue + suffix);
      setSuffix('');
    }
  }, [isFocus]);

  useEffect(() => {
    if (isFocus && suffix && inputRef.current) {
      const startPos = searchValue.length;
      const endPos = searchValue.length + suffix.length;
      inputRef.current.setSelectionRange(startPos, endPos);
    }
  }, [isFocus, suffix, searchValue]);

  return (
    <SearchView
      onMouseLeave={() => setIsHover(false)}
      onMouseEnter={() => setIsHover(true)}
      isHover={isActive}
    >
      <input
        type="text"
        ref={inputRef}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        value={searchValue + suffix}
        onChange={(e) => {
          setSuffix(''); // 在输入变化时先清空suffix
          setSearchValue(e.target.value);
          // suffix会通过useMemo自动重新计算和添加
        }}
        onKeyDown={handleKeyDown}
        placeholder="Search address/domain"
      />
      {isActive ? (
        <div
          className={classNames('right-tag', isActive ? 'right-tag2' : 'right-tag1')}
          style={{
            cursor: 'pointer',
          }}
          onClick={() => {
            if (!searchValue) {
              return;
            }
            handleKeyDown(true);
          }}
        >
          <span>Go</span>
        </div>
      ) : (
        <div className={classNames('right-tag', isActive ? 'right-tag2' : 'right-tag1')}>
          <div />
        </div>
      )}
    </SearchView>
  );
}
