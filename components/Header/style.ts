import styled from 'styled-components';

export const HeaderView = styled.div<{ isFixed: boolean }>`
  position: ${({ isFixed }) => (isFixed ? 'absolute' : 'relative')};
  left: 0;
  top: 0;
  width: 100%;
  z-index: 2;
  box-sizing: border-box;

  display: flex;
  height: 7.5rem;
  padding: 3rem 4rem 0;
  gap: 1rem;
  justify-content: space-between;
  flex-shrink: 0;
  align-items: center;

  pointer-events: none;
  & * {
    pointer-events: all;
    &:not(.navs input) {
      user-select: none;
    }
  }

  .navs {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-right: auto;
    .nav-list {
      display: flex;
      align-items: center;
      position: relative;
      .line {
        width: 0.0625rem;
        height: 2rem;
        background: #140f08;
      }
      .bg-box {
        position: absolute;
        bottom: 0;
        width: 0;
        height: 0;
        border-radius: 1.5rem;
        background: #140f08;
        transition: all 0.3s linear;
        transform: translateX(-50%);
        &.active {
          width: 9rem;
          height: 3rem;
        }
      }
      .nav-item-box {
        display: flex;
        align-items: center;
      }
      .nav-item {
        width: 9rem;
        /* width: 10vw; */
        height: 3rem;
        border-radius: 1.5rem;
        background: transparent;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        & > span {
          width: 100%;
          height: 100%;
          font-family: 'JetBrains Mono';
          font-size: 1.25rem;
          font-weight: 500;
          text-align: center;
          //text-underline-position: from-font;
          //text-decoration-skip-ink: none;
          color: #140f08;
          display: flex;
          align-items: center;
          justify-content: center;
          text-decoration: none;
          transition: all 0.3s linear;
          cursor: pointer;
        }
        &.active,
        &:hover {
          & > span {
            color: #d1d1d1;
          }
        }
      }
    }
  }

  &:hover {
    z-index: 4;
  }
`;

export const StyledLogo = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  height: 4.5rem;

  & > img {
    height: 4.5rem;
  }
`;
