import { useAppDispatch } from '@/hooks/useStore';
import { setShowConnectWallet } from '@/store/app';
import styled from 'styled-components';

export const StyledConnectBtn = styled.div`
  min-height: 4rem;
  padding: 0 2.625rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: #140f08;
  border-radius: 1.5rem;
  cursor: pointer;
  position: relative;
  justify-content: center;
  overflow: hidden;
  box-shadow:
    0px 0.25rem 0px 0px #00000040,
    0px 0.25rem 0px 0px #ffffff40 inset;

  & > span {
    //styleName: button;
    font-family: 'JetBrains Mono';
    font-size: 1.125rem;
    font-weight: 700;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #ffffff;
    position: relative;
    z-index: 1;
  }

  & > .o1 {
    width: 0;
    height: 0;
    position: absolute;
    left: 50%;
    bottom: 0px;
    transform: translate(-50%, 50%);
    background: 1.5rem;
    border-radius: 100%;
    background: rgba(255, 131, 22, 0.47);
    transition: all 0.4s linear;
    & > div {
      width: 0;
      height: 0;
      background: #ff8316;
      transition: all 0.4s linear;
      position: absolute;
      left: 50%;
      border-radius: 50%;
      bottom: -1.25rem;
      transform: translateX(-50%);
    }
  }

  &:hover {
    & > .o1 {
      width: 18.75rem;
      height: 18.75rem;
      & > div {
        width: 15.625rem;
        height: 15.625rem;
      }
    }
  }
`;

export const ConnectBtn = () => {
  const dispatch = useAppDispatch();

  return (
    <StyledConnectBtn
      onClick={() => {
        dispatch(setShowConnectWallet(true));
      }}>
      <div className="o1">
        <div />
      </div>
      <span>Connect</span>
    </StyledConnectBtn>
  );
};
