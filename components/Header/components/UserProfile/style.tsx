import styled from 'styled-components';

const StyledSvg = styled.svg`
  width: 1.125rem;
  height: 1.0625rem;
  filter: drop-shadow(0px 0.25rem 0px #d74304);
`;

const StyledDropDownIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.5s;
`;
export const DropDownIcon = () => {
  return (
    <StyledDropDownIcon className="dropDownIcon">
      <StyledSvg
        xmlns="http://www.w3.org/2000/svg"
        width="18"
        height="17"
        viewBox="0 0 18 17"
        fill="none">
        <path
          d="M7.33278 11.1603C8.22583 12.1457 9.77416 12.1458 10.6672 11.1603L16.4667 4.76092C17.7772 3.31477 16.7511 1 14.7994 1H3.20055C1.2489 1 0.222757 3.31477 1.53333 4.76092L7.33278 11.1603Z"
          fill="#FFC812"
        />
        <path
          d="M7.14746 11.3281C8.13975 12.4231 9.86025 12.4231 10.8525 11.3281L16.6523 4.92871C18.1081 3.32195 16.968 0.750283 14.7998 0.75H3.2002C1.03204 0.750283 -0.108097 3.32195 1.34766 4.92871L7.14746 11.3281Z"
          stroke="#140F08"
          strokeWidth="0.5"
        />
      </StyledSvg>
    </StyledDropDownIcon>
  );
};
