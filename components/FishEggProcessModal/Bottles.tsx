import { FC, useMemo, useState } from 'react';
import styled from 'styled-components';
import Image from 'next/image';

const BottlesContainer = styled.div<{ wavyTop: string }>`
  --wavyHeight: 30px;

  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  @keyframes rotate {
    0% {
      transform: translate(-50%, -68%) rotate(0deg);
    }
    25% {
      transform: translate(-48%, -72%) rotate(90deg);
    }
    50% {
      transform: translate(-50%, -76%) rotate(180deg);
    }
    75% {
      transform: translate(-52%, -72%) rotate(270deg);
    }
    100% {
      transform: translate(-50%, -68%) rotate(360deg);
    }
  }

  @keyframes rotateReverse {
    0% {
      transform: translate(-50%, -76%) rotate(0deg);
    }
    25% {
      transform: translate(-52%, -72%) rotate(-90deg);
    }
    50% {
      transform: translate(-50%, -68%) rotate(-180deg);
    }
    75% {
      transform: translate(-48%, -72%) rotate(-270deg);
    }
    100% {
      transform: translate(-50%, -76%) rotate(-360deg);
    }
  }

  .bottle {
    width: 160px;
    height: 260px;
    position: relative;
    overflow: hidden;
    z-index: 100;
    &::before {
      content: '';
      background-image: url('/image/bot.png');
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      left: 50%;
      top: 0;
      height: 260px;
      width: 160px;
      z-index: 200;
      transform: translate(-50%, 0%);
    }
  }

  .liquid {
    position: absolute;
    width: calc(100% - 16px);
    bottom: 8px;
    left: 8px;
    right: 8px;
    background: rgba(59, 57, 247, 1);
    transition: all 0.5s ease-in-out;
    overflow: hidden;
    z-index: 0;

    .wavy {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      &::before,
      &::after {
        content: '';
        position: absolute;
        width: 300px;
        height: 300px;
        /* top: -100%; */
        top: ${({ wavyTop }) => wavyTop};
        left: 50%;
        background-color: rgba(254, 241, 223, 0.4);
        border-radius: 40%;
        transform: translate(-50%, -70%) rotate(0deg);
        animation: rotate 6s linear infinite;
        z-index: 0;
      }

      &::after {
        border-radius: 47%;
        background-color: rgba(254, 241, 223, 1);
        transform: translate(-50%, -73%) rotate(0deg);
        animation: rotate 7s linear infinite;
        z-index: 0;
      }
    }
  }
`;

interface BottlesProps {
  chargeLevel: number; // 0-100 之间的值
  onClaim?: () => void;
}

const Bottles: FC<BottlesProps> = ({ chargeLevel: initialChargeLevel, onClaim }) => {
  const [chargeLevel, setChargeLevel] = useState(initialChargeLevel);

  // 将充电等级转换为离散的 5 个等级
  const getDiscreteChargeLevel = (level: number) => {
    if (level <= 0) return 0;
    if (level <= 25) return 25;
    if (level <= 50) return 50;
    if (level <= 75) return 75;
    return 100;
  };

  const discreteLevel = getDiscreteChargeLevel(chargeLevel);

  const liquidHeight = discreteLevel === 100 ? 'calc(100% - 30px)' : `${discreteLevel}%`;

  const liquidBorderRadius = discreteLevel === 100 ? '20px' : '0px 0px 20px 20px';

  const wavyTop = useMemo(() => {
    if (discreteLevel <= 25) {
      return '-100%';
    }
    if (discreteLevel <= 50) {
      return '-50%';
    }
    if (discreteLevel <= 75) {
      return '-30%';
    }
    return '0%';
  }, [discreteLevel]);

  const isShowWavy = useMemo(() => {
    if (chargeLevel === 0 || chargeLevel === 100) {
      return false;
    }
    return true;
  }, [chargeLevel]);

  return (
    <BottlesContainer wavyTop={wavyTop}>
      {chargeLevel === 100 ? (
        <Image
          src="/image/tudou.png"
          alt="bottle"
          width={220}
          height={360}
          onClick={() => {
            onClaim?.();
          }}
        />
      ) : (
        <div className="bottle">
          <div
            className="liquid"
            style={{
              borderRadius: liquidBorderRadius,
              height: liquidHeight,
            }}
          >
            {isShowWavy && <div className="wavy"></div>}
          </div>
        </div>
      )}
    </BottlesContainer>
  );
};

export default Bottles;
