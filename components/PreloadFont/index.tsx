import styled from 'styled-components';

const LoadingFont = styled.div<{ $fontName: string }>`
  visibility: hidden;
  position: absolute;
  z-index: -9999999;
  pointer-events: none;
  display: block;

  &::after {
    content: '1';
    width: 0;
    height: 0;
    position: absolute;
    display: block;
    font-family: '${(props) => props.$fontName}';
    /* font-family: 'Shrikhand'; */
  }
  /* &::after {
    content: '';
    position: absolute;
    display: block;
    font-family: 'Bevan';
  } */
`;
const preloadFontName = ['Baloo 2', 'Bevan', 'JetBrains Mono', 'Shrikhand'];

const StyledDiv = styled.div`
  visibility: hidden;
  position: absolute;
  z-index: -9999999;
  pointer-events: none;
  display: block;
`;

/**
 * @description 利用加载模型的时间，提前加载字体资源
 */
export default function PreloadFont() {
  return (
    <StyledDiv>
      {preloadFontName.map((item) => {
        return <LoadingFont key={'preloadFont' + item} $fontName={item} />;
      })}
    </StyledDiv>
  );
}
