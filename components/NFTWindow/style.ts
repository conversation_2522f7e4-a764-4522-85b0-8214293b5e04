import styled from 'styled-components';

export const NFTWindowView = styled.div`
  font-family: 'JetBrains Mono';
  display: flex;
  position: fixed;
  justify-content: center;
  align-items: center;
  //height: 100vh;
  margin: 0;
  user-select: none;

  .currency-box {
    transform: translate(-90px, 0);
    @keyframes op {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }
    animation: op 0.3s linear;
    animation-fill-mode: none;

    .fixed-nail {
      position: absolute;
      left: 0;
      top: 0;
      transform: translate(50%, -100%);
    }

    .fixed-nail-more {
      position: absolute;
      left: -13px;
      top: 50px;
      width: 50%;
      height: 100px;
      transform: translate(0%, -100%);
    }

    .currency-box-bg {
      position: absolute;
      left: -13px;
      top: 14px;
      border-radius: 24px;
      background: linear-gradient(270deg, rgba(250, 250, 250, 0) 0%, rgba(250, 250, 250, 0.5) 73%);
      width: 100%;
      height: 100%;
    }

    .currency-box-content {
      border-radius: 24px;
      background: #fafafa4d;
      backdrop-filter: blur(8px);
      width: 100%;
      padding: 18px;
      position: relative;

      & > p {
        font-family: 'JetBrains Mono';
        font-size: 20px;
        font-weight: 400;
        line-height: 26.4px;
        text-align: left;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #686663;
        margin: 0;
        // 字间距
        letter-spacing: 0.05em;

        & > a {
          color: #3f3b37;
          text-decoration: none;
          &:hover {
            text-decoration: underline;
          }
        }
      }

      & > .currency-box-btns {
        margin-top: 24px;
        display: flex;
        flex-direction: column;
        gap: 16px;

        & > button {
          width: 208px;
          height: 56px;
          border-radius: 12px;
          outline: none;
          cursor: pointer;
          background: #ffffff;
          font-family: JetBrains Mono;
          font-size: 20px;
          font-weight: 400;
          line-height: 26.4px;
          letter-spacing: -0.05em;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #686663;
          border: 0;
        }
      }
    }
  }
`;
