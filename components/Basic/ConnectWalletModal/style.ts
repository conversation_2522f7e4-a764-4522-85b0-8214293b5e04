import styled from 'styled-components';

export const ConnectWalletModalView = styled.div<{ marginTop?: string }>`
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: ${(props) => props.marginTop || '24px'};
  .wallet-item {
    display: flex;
    align-items: center;
    border: 1px solid #9a9a9a;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 16px;
    cursor: pointer;
    & > img {
      width: 56px;
      height: 56px;
    }
    & > span {
      flex: 1;
      text-align: center;
      font-family: 'JetBrains Mono';
      font-size: 18px;
      font-weight: 400;
      line-height: 21.78px;
      color: #140f08;
    }
    &:hover {
      background: #ffffff80;
    }
  }
`;
