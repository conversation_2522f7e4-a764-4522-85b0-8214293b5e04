import React from 'react';
import { motion } from 'framer-motion';
import { SUPPORT_WALLET_ENUM } from '../../../constant/type';
import OkxSvg from '/public/image/okx.svg';
import UnisatSvg from '/public/image/unisat.svg';
import styled from 'styled-components';

const WalletSelection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

// 定义钱包数据类型
interface WalletData {
  id: SUPPORT_WALLET_ENUM;
  name: string;
  logo: string;
  layoutId: string;
  logoLayoutId: string;
}

// 定义组件Props
interface WalletListProps {
  onSelectWallet: (wallet: SUPPORT_WALLET_ENUM) => void;
  wallets?: WalletData[]; // 可选参数，允许自定义钱包列表
}

// 默认钱包列表
const defaultWallets: WalletData[] = [
  {
    id: SUPPORT_WALLET_ENUM.unisat,
    name: 'UniSat Wallet',
    logo: UnisatSvg.src,
    layoutId: 'wallet-container',
    logoLayoutId: 'unisat-logo',
  },
  {
    id: SUPPORT_WALLET_ENUM.okx,
    name: 'OKX Wallet',
    logo: OkxSvg.src,
    layoutId: 'wallet-container-okx',
    logoLayoutId: 'okx-logo',
  },
];

const WalletList: React.FC<WalletListProps> = ({ onSelectWallet, wallets = defaultWallets }) => {
  return (
    <WalletSelection>
      {wallets.map((wallet) => (
        <motion.div
          key={wallet.id}
          className="wallet-item"
          onClick={() => onSelectWallet(wallet.id)}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          layoutId={wallet.layoutId}
        >
          <motion.img src={wallet.logo} alt={wallet.name} layoutId={wallet.logoLayoutId} />
          <span>{wallet.name}</span>
        </motion.div>
      ))}
    </WalletSelection>
  );
};

export default WalletList;
