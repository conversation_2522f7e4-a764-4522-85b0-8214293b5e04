'use client';

import Modal from '../../EditAvatarPage/BasicComponents/Modal';
import { ConnectWalletModalView } from './style';
import { useDispatch, useSelector } from 'react-redux';
import { setShowConnectWallet } from '../../../store/app';
import { IAppState, SUPPORT_WALLET_ENUM } from '../../../constant/type';
import useConnectWallet from '../../../hooks/useConnectWallet';
import OkxSvg from '/public/image/okx.svg';
import UnisatSvg from '/public/image/unisat.svg';
import { useEffect, useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import WalletList from './WalletList';
import SelectWalletStatus from './SelectWalletStatus';

export default function ConnectWalletModal() {
  const dispatch = useDispatch();
  const { connectWallet, btcAddress, isPending, isConnected } = useConnectWallet();
  const [selectedWallet, setSelectedWallet] = useState<SUPPORT_WALLET_ENUM | null>(null);

  const [connectionStage, setConnectionStage] = useState<
    'idle' | 'initiate' | 'waiting' | 'result'
  >('idle');

  const [prevConnectionStage, setPrevConnectionStage] = useState<string>('idle');
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [lastStateChangeTime, setLastStateChangeTime] = useState<number>(0);

  const { showConnectWallet } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);

  const onClose = () => {
    dispatch(setShowConnectWallet(false));
    // 重置所有状态
    setSelectedWallet(null);
    setConnectionStage('idle');
    setIsSuccess(false);
    setPrevConnectionStage('idle');

    // console.log("关闭模态框: 重置所有状态");
  };

  // 监听钱包地址变化
  useEffect(() => {
    if (btcAddress) {
      // 连接成功后延迟关闭，让用户看到成功动画
      setTimeout(() => {
        onClose();
      }, 1500);
    }
  }, [btcAddress]);

  const handleWalletSelect = (wallet: SUPPORT_WALLET_ENUM) => {
    setSelectedWallet(wallet);

    // 明确设置为初始发起阶段
    setConnectionStage('initiate');
    setLastStateChangeTime(Date.now());

    // 短暂延迟后调用实际连接函数
    setTimeout(() => {
      connectWallet(wallet);
    }, 800); // 给用户足够时间看到"发起"状态
  };

  useEffect(() => {
    // console.log("状态检查:", {
    //   isPending,
    //   isConnected,
    //   connectionStage,
    //   prevConnectionStage,
    // });

    // 记录上一个状态
    if (connectionStage !== prevConnectionStage) {
      setPrevConnectionStage(connectionStage);
    }

    const now = Date.now();
    // 防抖动: 如果距离上次状态变更不足300ms，则忽略此次更新
    if (now - lastStateChangeTime < 300) {
      return;
    }

    // 根据实际钱包状态更新UI状态
    if (isPending) {
      // 只有当前不是waiting状态时才更新，避免重复设置
      if (connectionStage !== 'waiting') {
        // console.log("设置状态: waiting (钱包连接中)");
        setConnectionStage('waiting');
        setLastStateChangeTime(now);
      }
    } else if (isConnected) {
      // 连接成功
      if (connectionStage !== 'result' || !isSuccess) {
        // console.log("设置状态: result, success=true (连接成功)");
        setConnectionStage('result');
        setIsSuccess(true);
        setLastStateChangeTime(now);
      }
    } else if (!isPending && prevConnectionStage === 'waiting') {
      // 如果之前是waiting状态，现在不是pending了，但也没连接成功，说明连接失败
      // console.log("设置状态: result, success=false (连接失败)");
      setConnectionStage('result');
      setIsSuccess(false);
      setLastStateChangeTime(now);
    }
  }, [
    isPending,
    isConnected,
    connectionStage,
    prevConnectionStage,
    lastStateChangeTime,
    isSuccess,
  ]);

  // const handleRetry = () => {
  //   if (selectedWallet) {
  //     // 重置到初始状态
  //     console.log("重试: 设置状态为initiate");
  //     setConnectionStage("initiate");
  //     setLastStateChangeTime(Date.now());

  //     // 短暂延迟后重新连接
  //     setTimeout(() => {
  //       connectWallet(selectedWallet);
  //     }, 800);
  //   }
  // };

  // 选择的钱包图标
  const selectedLogo =
    selectedWallet === SUPPORT_WALLET_ENUM.unisat
      ? UnisatSvg.src
      : selectedWallet === SUPPORT_WALLET_ENUM.okx
        ? OkxSvg.src
        : null;

  return (
    <Modal
      visible={showConnectWallet}
      onClose={isPending ? undefined : onClose} // 连接中不允许关闭
      title={'Connect Wallet'}
      width="376px"
    >
      <ConnectWalletModalView marginTop={selectedWallet ? '0px' : '24px'}>
        <AnimatePresence mode="wait">
          {!selectedWallet ? (
            // 选择钱包界面
            <WalletList onSelectWallet={handleWalletSelect} />
          ) : (
            // 连接状态界面
            <SelectWalletStatus
              selectedWallet={selectedWallet}
              connectionStage={connectionStage}
              isSuccess={isSuccess}
              selectedLogo={selectedLogo}
            />
          )}
        </AnimatePresence>
      </ConnectWalletModalView>
    </Modal>
  );
}
