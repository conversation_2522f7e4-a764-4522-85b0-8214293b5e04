import { ScrollLoadView } from './style';
import { useEffect, useRef } from 'react';

export default function ScrollLoad({
  getData,
  loadLoading,
}: {
  getData: Function;
  loadLoading: boolean;
}) {
  const ref: any = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          getData();
        }
      },
      {
        root: window.document,
        rootMargin: '0px',
        threshold: 0.9,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, []);

  return <ScrollLoadView ref={ref}>{/*{loadLoading ? 'Loading...' : ''}*/}</ScrollLoadView>;
}
