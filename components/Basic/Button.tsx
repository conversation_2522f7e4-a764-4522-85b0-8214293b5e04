import styled, { css } from 'styled-components';

type ButtonType = 'primary' | 'cancel';
const BasicButton = styled.button<{ $type?: ButtonType }>`
  display: flex;
  width: 12.5rem;
  height: 3.75rem;
  padding: 1.875rem;
  justify-content: center;
  align-items: center;
  gap: 0.625rem;
  border-radius: 1rem;

  font-weight: 700;
  border-radius: 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  line-height: 100%;
  cursor: pointer;
  min-width: 11.25rem;
  border: none;
  color: white;
  text-transform: capitalize;
  ${({ $type = 'primary' }) =>
    $type === 'primary'
      ? css`
          background: hsl(28.07deg 100% 54.31%);
          box-shadow: 0px -0.25rem 0px 0px rgba(0, 0, 0, 0.25) inset;
          position: relative;
          transition:
            transform 0.1s,
            box-shadow 0.1s,
            border-bottom 0.1s;

          &:hover {
            background: hsl(28.07deg 100% 59.31%);
            cursor: pointer;
          }

          &:active {
            box-shadow: none;
            background: #fc7922;
          }

          &:disabled {
            background: #c9b7a5;
            cursor: not-allowed;
          }
        `
      : css`
          background: #c9b7a5;
        `}
`;

export default BasicButton;
