import styled from 'styled-components';

export const ConfirmModalView = styled.div`
  border: 4px solid #140f08;
  background: #fff2e2;
  border-radius: 48px;
  box-sizing: border-box;
  position: relative;
  max-width: 1280px;

  .close-btn {
    position: absolute;
    width: 24px;
    height: 24px;
    cursor: pointer;
    top: 20px;
    right: 28px;
  }

  .modal-content {
    width: 100%;
    height: 100%;
    border: 8px solid #ff8316;
    border-radius: 48px;
    background: #fff2e2;
    padding: 32px 40px;
    box-sizing: border-box;
    & > h2 {
      font-family: JetBrains Mono;
      font-size: 20px;
      font-weight: 700;
      line-height: 26.4px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      margin: 0;
    }
    & > .content {
      font-family: JetBrains Mono;
      font-size: 16px;
      font-weight: 400;
      line-height: 21.12px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      padding: 24px 0;
      color: #686663;
      margin: 0;
    }
    .btns {
      display: flex;
      gap: 16px;
      button {
        flex: 1;
        padding: 0;
        margin: 0;
        outline: none;
        border: 0;
        height: 56px;
        cursor: pointer;
        border-radius: 16px;
        font-family: JetBrains Mono;
        font-size: 20px;
        font-weight: 700;
        line-height: 20px;
        text-align: center;
        text-underline-position: from-font;
        text-decoration-skip-ink: none;
        color: #ffffff;
      }
      .cancel-btn {
        background-color: #c9b7a5;
      }
      .confirm-btn {
        background-color: #ff8316;
        box-shadow: 0px -4px 0px 0px #00000040 inset;
      }
    }
  }
`;
