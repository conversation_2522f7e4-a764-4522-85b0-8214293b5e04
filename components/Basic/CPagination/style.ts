import styled from 'styled-components';

export const PaginationView = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 32px;
  .rc-pagination-item-active {
    border-color: transparent;
    & > a {
      color: #ff8316;
    }
  }
  .rc-pagination-item {
    border-color: transparent;
    font-family: 'JetBrains Mono';
    font-size: 14px;
    font-weight: 400;
    line-height: 16.94px;
    color: #828282;
    &:hover {
      a {
        color: #ff8316;
      }
    }
  }
  .rc-pagination-next,
  .rc-pagination-prev {
    .rc-pagination-item-link {
      border-color: transparent;
      &:hover {
        border-color: transparent;
        &:after {
          color: #ff8316;
          font-size: 22px;
          transform: translateY(-6px);
        }
      }
      &:after {
        color: #140f08;
        font-size: 22px;
        transform: translateY(-6px);
      }
    }
    &[aria-disabled='true'] {
      .rc-pagination-item-link {
        &:after {
          color: #c2c2c2;
        }
      }
    }
  }
`;
