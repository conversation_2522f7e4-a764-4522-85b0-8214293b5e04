import { PaginationView } from './style';
import Pagination from 'rc-pagination';
import 'rc-pagination/assets/index.css';
import en_US from 'rc-pagination/lib/locale/en_US';

export default function CPagination({
  total,
  page,
  setPage,
  pageSize,
}: {
  total: number;
  page: number;
  setPage: Function;
  pageSize: number;
}) {
  return (
    <PaginationView>
      <Pagination
        total={total}
        current={page}
        pageSize={pageSize}
        onChange={(page_) => {
          setPage(page_);
        }}
        locale={en_US}
      />
    </PaginationView>
  );
}
