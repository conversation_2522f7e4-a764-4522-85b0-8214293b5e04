import { ResourceView } from './style';
import classNames from 'classnames';
import MouseSvg from '/public/image/home/<USER>';
import ScrollLoad from '../../Basic/ScrollLoad';
import { useState } from 'react';

export default function Resource() {
  const [isShow, setIsShow] = useState(false);
  return (
    <ResourceView isShow={isShow}>
      <ScrollLoad
        loadLoading={false}
        getData={() => {
          setIsShow(true);
        }}
      />
      <div className="cards-grid">
        <div className="grid-items">
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className="grid-items">
          <div></div>
          <div></div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items')}>
          <div></div>
          <div className="table-data-item">
            <div className="o-top" />
            <div className="o-l-top" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items')}>
          <div></div>
          <div className="table-data-item"></div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items', 'grid-items-new')}>
          <div></div>
          <div className="table-data-item">
            <div className="o-l-bottom" />
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className="grid-items">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
      <div className="resource-view">
        <div className="resource-view-box">
          <p className="title">Resource</p>
          <div className="resource-cards">
            <div className="resource-card-item resource-card-item1">
              <div className="resource-card-item-box">
                <span>Source Code</span>
                <img src={MouseSvg.src} alt="" />
                <div className="o-top" />
                <div className="o-l-top" />
                <div className="o-l-bottom" />
                <div className="o-bottom" />
              </div>
            </div>
            <div className="resource-card-item resource-card-item2">
              <div className="resource-card-item-box">
                <span>Changelog</span>
                <img src={MouseSvg.src} alt="" />
                <div className="o-top" />
                <div className="o-l-top" />
                <div className="o-l-bottom" />
                <div className="o-bottom" />
              </div>
            </div>
            <div className="resource-card-item resource-card-item3">
              <div className="resource-card-item-box">
                <span>Documentation</span>
                <img src={MouseSvg.src} alt="" />
                <div className="o-top" />
                <div className="o-l-top" />
                <div className="o-l-bottom" />
                <div className="o-bottom" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </ResourceView>
  );
}
