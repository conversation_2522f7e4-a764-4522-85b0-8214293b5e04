import { BannerView } from './style';
import BannerText from '/public/image/home/<USER>';
import AvatarPage from '../../../AvatarOrdinalsBrowser/components/AvatarPage';
import { HOME_RUN_AVATAR_METADATA } from '../../../constant';
import { useRef } from 'react';

export default function Banner({ pageRef }: { pageRef: any }) {
  const containerRef: any = useRef(null);

  const handleScroll = (event: any) => {
    pageRef.current.scrollTop += event.deltaY;
  };

  return (
    <BannerView>
      <div className="banner-box" ref={containerRef}>
        <img src={BannerText.src} alt="" />
        <div className="avatar" onWheel={handleScroll}>
          <AvatarPage
            collectionData={null}
            isFrontend={true}
            hiddenMenu={true}
            exportPreviewImage={() => {}}
            transparentBackground={true}
            defaultAvatarMetadata={HOME_RUN_AVATAR_METADATA}
          />
        </div>
      </div>
      <div className="description">
        A decentralized avatar and identity application built for Bitcoin/Fractal ecosystem_
      </div>
    </BannerView>
  );
}
