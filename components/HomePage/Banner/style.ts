import styled from 'styled-components';

export const BannerView = styled.div`
  width: 100%;
  padding-bottom: 220px;
  min-height: calc(100vh - 520px);
  box-sizing: border-box;
  & > .banner-box {
    position: relative;
    width: 100%;
    height: 400px;
    background: #140f08;
    padding: 30px 12px 10px 12px;
    box-sizing: border-box;
    & > img {
      width: 100%;
      position: relative;
      z-index: 2;
      pointer-events: none;
    }
    & > .avatar {
      width: 590px;
      height: 623px;
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      & > img {
        width: 100%;
        height: 100%;
      }
      & > div {
        width: 100% !important;
        height: 100% !important;
        .loading-popup {
          opacity: 0 !important;
        }
      }
    }
  }
  & > .description {
    margin-top: 8px;
    font-family: 'JetBrains Mono';
    font-size: 40px;
    font-weight: 400;
    line-height: 52.8px;
    letter-spacing: -0.05em;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    color: #140f08;
    max-width: 1132px;
  }
`;
