import styled from 'styled-components';

export const CardsView = styled.div`
  position: relative;
  padding-bottom: 120px;
  .cards-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1px;
    .grid-items {
      display: grid;
      grid-template-columns: 1fr 2fr 2fr 2fr 2fr 1fr;
      gap: 1px;
      & > div {
        height: 140px;
        border-right: 1px solid #f6ac6d;
        border-bottom: 1px solid #f6ac6d;
        box-sizing: border-box;
        position: relative;
        &:nth-last-child(1) {
          border-right: 0;
          border-image: linear-gradient(90deg, #f6ac6d, transparent) 1;
        }
        &:nth-child(1) {
          border-image: linear-gradient(270deg, #f6ac6d, transparent) 1;
        }
        &.table-data-item {
          display: flex;
          flex-direction: column;
          justify-content: end;
          padding: 16px;
          box-sizing: border-box;
          transition: all 0.3s linear;
          & > h6 {
            font-family: 'JetBrains Mono';
            font-size: 32px;
            font-weight: 500;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #ffffff;
            margin: 0;
            transition: all 0.3s linear;
          }
          & > p {
            font-family: 'JetBrains Mono';
            font-size: 32px;
            font-weight: 400;
            line-height: 32px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            color: #140f08;
            margin: 0;
            transition: all 0.3s linear;
          }

          & > h6,
          & > p {
            opacity: 0.6;
          }
        }
        & > .o-top {
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          right: -2px;
          top: -2px;
          box-shadow: 0px 0px 8px 0px #ff8316;
        }
        & > .o-bottom {
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          right: -2px;
          bottom: -2px;
          box-shadow: 0px 0px 8px 0px #ff8316;
        }
        & > .o-l-top {
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          left: -2px;
          top: -2px;
          box-shadow: 0px 0px 8px 0px #ff8316;
        }
        & > .o-l-bottom {
          width: 4px;
          height: 4px;
          background: #ffffff;
          position: absolute;
          left: -2px;
          bottom: -2px;
          box-shadow: 0px 0px 8px 0px #ff8316;
        }
      }
      &.active.grid-items-new {
        & > div {
          background: #ff00001a;
          &:nth-child(1) {
            background: transparent;
          }
          &.table-data-item {
            background: #ff330099;
            & > h6,
            & > p {
              opacity: 1;
            }

            & > h6 {
              color: #ffffff;
            }
            & > p {
              color: #140f08;
            }
            & > .o-bottom,
            & > .o-top,
            & > .o-l-top,
            & > .o-l-bottom {
              background: #140f08;
            }
          }
        }
      }
      &.active {
        & > div {
          &:nth-child(1) {
            background: transparent;
          }
          &.table-data-item {
            background: #ffffff99;
            & > h6,
            & > p {
              opacity: 1;
            }
            & > h6 {
              color: #ff8316;
            }
            & > p {
              color: #140f08;
            }
            & > .o-bottom,
            & > .o-top,
            & > .o-l-top,
            & > .o-l-bottom {
              background: #140f08;
            }
          }
        }
      }

      &:nth-last-child(1) {
        & > div {
          border-bottom: 0;
        }
      }
      &:nth-child(1) {
        & > div {
          border-image: linear-gradient(360deg, #f6ac6d, transparent) 1;
          &:nth-last-child(1) {
            border-right: 0;
            border-image: linear-gradient(90deg, #f6ac6d, transparent) 1;
          }
          &:nth-child(1) {
            border-image: linear-gradient(270deg, #f6ac6d, transparent) 1;
          }
        }
      }
      &:nth-last-child(1) {
        & > div {
          border-image: linear-gradient(180deg, #f6ac6d, transparent) 1;
        }
      }
    }
  }
`;
export const TableDataView = styled.div`
  width: 66%;
  height: 100%;
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
  .table-data-item {
    width: 100%;
    max-width: 640px;
    padding: 19.2px;
    box-sizing: border-box;
    background: #ffffff99;
    opacity: 0.6;
    border: 0.8px solid rgba(255, 255, 255, 0.2);
    display: grid;
    grid-template-columns: 1fr;
    gap: 12.8px;
    border-radius: 19.2px;
    transition: all 0.3s linear;
    .table-data-1 {
      grid-template-columns: 262fr 326fr;
    }
    .table-data-2 {
      grid-template-columns: 320fr 268fr;
    }
    .table-data-3 {
      grid-template-columns: 1fr;
    }
    .table-data-4 {
      grid-template-columns: 1fr 1fr;
    }
    .table-data-5 {
      grid-template-columns: 1fr;
    }
    .table-data-line {
      display: grid;
      gap: 12.8px;
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        border: 0.8px solid #5f5d5b;
        box-sizing: border-box;
        border-radius: 12.8px;
        min-height: 58px;
        transition: all 0.3s linear;
        & > img {
          height: 36px;
          transition: all 0.3s linear;
        }
        & > span {
          font-family: 'JetBrains Mono';
          font-size: 19.2px;
          font-weight: 400;
          line-height: 19.2px;
          text-align: left;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
          color: #140f08;
          transition: all 0.3s linear;
        }
      }
    }
    &.active {
      opacity: 1;
      backdrop-filter: blur(4px);
      max-width: 800px;
      .table-data-line {
        & > div {
          min-height: 72px;
          & > img {
            height: 48px;
          }
          & > span {
            font-size: 24px;
            line-height: 24px;
          }
        }
      }
    }
  }
  .table-data-item2 {
    align-self: end;
  }
`;
