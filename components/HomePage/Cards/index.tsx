import { CardsView, TableDataView } from './style';
import { useState } from 'react';
import classNames from 'classnames';
import Icon1 from '/public/image/home/<USER>';
import Icon2 from '/public/image/home/<USER>';
import Icon3 from '/public/image/home/<USER>';
import Icon4 from '/public/image/home/<USER>';
import Icon5 from '/public/image/home/<USER>';
import Icon6 from '/public/image/home/<USER>';
import Icon7 from '/public/image/home/<USER>';
import Icon8 from '/public/image/home/<USER>';
import Icon9 from '/public/image/home/<USER>';

export default function cards() {
  const [hoverIndex, setHoverIndex] = useState<number>(0);
  return (
    <CardsView>
      <div className="cards-grid">
        <div className="grid-items">
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className="grid-items">
          <div></div>
          <div></div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items', hoverIndex === 1 && 'active')}>
          <div></div>
          <div
            className="table-data-item"
            onMouseLeave={() => setHoverIndex(0)}
            onMouseEnter={() => setHoverIndex(1)}
          >
            <h6>SatWorl</h6>
            <p>Dapp</p>
            <div className="o-top" />
            <div className="o-l-top" />
            {hoverIndex !== 2 && (
              <>
                <div className="o-l-bottom" />
                <div className="o-bottom" />
              </>
            )}
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items', hoverIndex === 2 && 'active')}>
          <div></div>
          <div
            className="table-data-item"
            onMouseLeave={() => setHoverIndex(0)}
            onMouseEnter={() => setHoverIndex(2)}
          >
            <h6>SatWorl</h6>
            <p>Core</p>
            {hoverIndex === 2 && (
              <>
                <div className="o-top" />
                <div className="o-l-top" />
                <div className="o-l-bottom" />
                <div className="o-bottom" />
              </>
            )}
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className={classNames('grid-items', 'grid-items-new', hoverIndex === 3 && 'active')}>
          <div></div>
          <div
            className="table-data-item"
            onMouseLeave={() => setHoverIndex(0)}
            onMouseEnter={() => setHoverIndex(3)}
          >
            <h6>UniSat</h6>
            <p>Data Indexing</p>
            {hoverIndex !== 2 && (
              <>
                <div className="o-top" />
                <div className="o-l-top" />
              </>
            )}
            <div className="o-l-bottom" />
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className="grid-items">
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div>
            <div className="o-bottom" />
          </div>
          <div></div>
        </div>
        <div className="grid-items">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
      <TableData hoverIndex={hoverIndex} />
    </CardsView>
  );
}

function TableData({ hoverIndex }: { hoverIndex: number }) {
  return (
    <TableDataView>
      <div className={classNames('table-data-item', hoverIndex === 1 && 'active')}>
        <div className="table-data-line table-data-1">
          <div>
            <img src={Icon1.src} alt="" />
            <span>Avatar</span>
          </div>
          <div>
            <img src={Icon2.src} alt="" />
            <span>Avatar</span>
          </div>
        </div>
        <div className="table-data-line table-data-2">
          <div>
            <img src={Icon3.src} alt="" />
            <span>Avatar</span>
          </div>
          <div>
            <img src={Icon4.src} alt="" />
            <span>Avatar</span>
          </div>
        </div>
      </div>
      <div
        className={classNames({
          'table-data-item': true,
          'table-data-item2': true,
          active: hoverIndex === 2,
        })}
      >
        <div className="table-data-line table-data-3">
          <div>
            <img src={Icon5.src} alt="" />
            <span>Inscription On-chain Framework</span>
          </div>
          <div>
            <img src={Icon6.src} alt="" />
            <span>Multi-platform Data Backend</span>
          </div>
        </div>
      </div>
      <div
        className={classNames({
          'table-data-item': true,
          active: hoverIndex === 3,
        })}
      >
        <div className="table-data-line table-data-4">
          <div>
            <img src={Icon7.src} alt="" />
            <span>Massive Asset Data</span>
          </div>
          <div>
            <img src={Icon8.src} alt="" />
            <span>Fast-response API</span>
          </div>
        </div>
        <div className="table-data-line table-data-5">
          <div>
            <img src={Icon9.src} alt="" />
            <span>Bitcoin/Fractal</span>
          </div>
        </div>
      </div>
    </TableDataView>
  );
}
