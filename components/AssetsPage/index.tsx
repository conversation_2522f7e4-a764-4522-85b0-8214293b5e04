import { AssetsPageView, AvatarItemView } from './style';
import AccountInfo from './AccountInfo';
import DefaultPreviewImg from '/public/image/default-preview.png';
import { useMemo, useState } from 'react';
import { getCollectionList } from '../../server';
import { useSelector } from 'react-redux';
import { IAppState } from '../../constant/type';
import EmptyImg from '/public/image/empty.png';
import { useRouter } from 'next/router';
import NotConnect from '../Basic/NotConnect';

interface IItem {
  id: number;
  collectionName: string;
  state: 0 | 1;
  avatar: string;
}

export default function AssetsPage() {
  const { btcAddress } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [list, setList] = useState<IItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useMemo(() => {
    if (!btcAddress) {
      return;
    }
    setLoading(true);
    getCollectionList({
      state: 1,
    })
      .then((res: any) => {
        setList(res.data.data);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [btcAddress]);
  return (
    <AssetsPageView>
      {!btcAddress ? (
        <NotConnect />
      ) : (
        <div className="assets-page">
          <AccountInfo />
          <div className="tabs">
            <div>Avatar</div>
          </div>
          {loading ? (
            <div className="loading-view">
              <p>Loading...</p>
            </div>
          ) : list.length === 0 ? (
            <div className="empty-view">
              <img src={EmptyImg.src} alt="" />
              <p>No Data</p>
            </div>
          ) : (
            <div className="avatar-card">
              {list.map((item: IItem) => (
                <AvatarItem key={item.id} item={item} />
              ))}
            </div>
          )}
        </div>
      )}
    </AssetsPageView>
  );
}

function AvatarItem({ item }: { item: IItem }) {
  const router = useRouter();

  return (
    <AvatarItemView onClick={() => router.push('/?collectionId=' + item.id)}>
      <div className="preview-img">
        <img src={item.avatar || DefaultPreviewImg.src} alt="" />
      </div>
      <div className="avatar-item-info">
        <h2>#{item.id}</h2>
        <p>{item.collectionName}</p>
      </div>
    </AvatarItemView>
  );
}
