import styled from 'styled-components';

export const AvatarPage = styled.div<{ duration: number }>`
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 1000;
  overflow: hidden;
  background: #000;
  text-align: center;
  &::before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
  }
  .scene {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    perspective: 15px;
    perspective-origin: 50% 50%;
  }

  .wrap {
    position: absolute;
    width: 1000px;
    height: 1000px;
    left: -500px;
    top: -500px;
    transform-style: preserve-3d;
    animation: move ${({ duration }) => duration}s infinite linear;
    animation-fill-mode: forwards;
  }

  .wrap:nth-child(2) {
    animation: move ${({ duration }) => duration}s infinite linear;
    animation-delay: ${({ duration }) => duration / 2}s;
  }

  .wall {
    width: 100%;
    height: 100%;
    position: absolute;
    background: url('/image/sg.jpg');
    background-size: cover;
    opacity: 0;
    animation: fade ${({ duration }) => duration}s infinite linear;
  }

  .wrap:nth-child(2) .wall {
    animation-delay: ${({ duration }) => duration / 2}s;
  }

  .wall-right {
    transform: rotateY(90deg) translateZ(500px);
  }

  .wall-left {
    transform: rotateY(-90deg) translateZ(500px);
  }

  .wall-top {
    transform: rotateX(90deg) translateZ(500px);
  }

  .wall-bottom {
    transform: rotateX(-90deg) translateZ(500px);
  }

  .wall-back {
    transform: rotateX(180deg) translateZ(500px);
  }

  .spaceship {
    position: absolute;
    z-index: 10;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%); /* 只需要水平居中 */
    width: 50px;
    height: 50px;
    animation: shipRocking 2s ease-in-out infinite;
    background: pink;
  }

  @keyframes shipRocking {
    0% {
      transform: translateX(-50%) rotate(0deg) translateY(0px);
    }
    15% {
      transform: translateX(-54%) rotate(-5deg) translateY(-4px);
    }
    30% {
      transform: translateX(-46%) rotate(4deg) translateY(6px);
    }
    45% {
      transform: translateX(-52%) rotate(-4deg) translateY(2px);
    }
    60% {
      transform: translateX(-48%) rotate(6deg) translateY(-2px);
    }
    75% {
      transform: translateX(-56%) rotate(-3deg) translateY(4px);
    }
    90% {
      transform: translateX(-44%) rotate(3deg) translateY(-6px);
    }
    100% {
      transform: translateX(-50%) rotate(0deg) translateY(0px);
    }
  }

  @keyframes fade {
    0% {
      opacity: 0;
    }
    25% {
      opacity: 1;
    }
    75% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes move {
    0% {
      transform: translateZ(-500px) rotate(0deg);
    }
    100% {
      transform: translateZ(500px) rotate(0deg);
    }
  }
`;
