// "use client";

import { useEffect, useState } from 'react';
/**
 * 实现时空穿梭隧道
 * 1. 创建一个隧道 ✅
 * 2. 需要一张充满星空的图片 ✅
 * 3. 利用5个元素设定星空图，在3D效果作用下，在上下左右中5个方向铺满一屏 ✅
 * 4. 父容器设置perspective值，设定容器translateZ动画，实现效果 ✅
 * 5. 通过两组同样的动画，其中一组设置负延迟动画补间 ✅
 */
import { AvatarPage } from './style';

interface MachineLoadingProps {
  isLoading?: boolean;
  duration?: number;
  children?: React.ReactNode;
}

const MachineLoading = ({ duration = 12, children }: MachineLoadingProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // 或者返回一个简单的loading placeholder
  }
  return (
    <AvatarPage duration={duration}>
      <div className="scene">
        <div className="wrap">
          <div className="wall wall-right"></div>
          <div className="wall wall-left"></div>
          <div className="wall wall-top"></div>
          <div className="wall wall-bottom"></div>
          <div className="wall wall-back"></div>
        </div>
        <div className="wrap">
          <div className="wall wall-right"></div>
          <div className="wall wall-left"></div>
          <div className="wall wall-top"></div>
          <div className="wall wall-bottom"></div>
          <div className="wall wall-back"></div>
        </div>
      </div>
      {children && <>{children}</>}
      {/* <div className="spaceship"></div> */}
    </AvatarPage>
  );
};

export default MachineLoading;
