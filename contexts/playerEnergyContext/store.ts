import { createStore } from 'zustand/vanilla';
import { PlayerEnergyInfo } from '@/server/index.types';

export interface IPlayerEnergyZustandStore extends PlayerEnergyInfo {
  showEnergyModal: () => void;
}

type UpdatePlayerInfoParams =
  | Partial<IPlayerEnergyZustandStore>
  | ((prev: IPlayerEnergyZustandStore) => Partial<IPlayerEnergyZustandStore>);

export const initialState: IPlayerEnergyZustandStore = {
  energy: 0,
  totalEnergy: 0,
  freeTime: 0,
  showEnergyModal: () => false,
};

export const playerEnergyZustandStore = createStore<
  IPlayerEnergyZustandStore & {
    updatePlayerInfo: (arg: UpdatePlayerInfoParams) => void;
    resetPlayerInfo: () => void;
  }
>((set, get) => ({
  energy: 0,
  totalEnergy: 0,
  freeTime: 0,
  showEnergyModal: () => false,
  updatePlayerInfo: (payload) => {
    if (typeof payload === 'object') {
      const { energy, totalEnergy, freeTime, showEnergyModal } = payload ?? {};
      set((state) => {
        return {
          energy: energy ?? state.energy,
          totalEnergy: totalEnergy ?? state.totalEnergy,
          freeTime: freeTime ?? state.freeTime,
          showEnergyModal: showEnergyModal ?? state.showEnergyModal,
        };
      });
    } else if (typeof payload === 'function') {
      const newState = payload(get());
      const { energy, totalEnergy, freeTime, showEnergyModal } = newState ?? {};
      set((state) => {
        return {
          energy: energy ?? state.energy,
          totalEnergy: totalEnergy ?? state.totalEnergy,
          freeTime: freeTime ?? state.freeTime,
          showEnergyModal: showEnergyModal ?? state.showEnergyModal,
        };
      });
    }
  },
  resetPlayerInfo: () => {
    set({
      energy: initialState.energy,
      totalEnergy: initialState.totalEnergy,
      freeTime: initialState.freeTime,
      // showEnergyModal: initialState.showEnergyModal,
    });
  },
}));
