import { PlayerEnergyInfo } from '@/server/index.types';
import {
  createContext,
  Dispatch,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { playerEnergyZustandStore, initialState, IPlayerEnergyZustandStore } from './store';
import EnergyModal, { IEnergyModalRef } from '@/components/Header/components/EnergyModal';

type UpdatePlayerInfoParams =
  | Partial<PlayerEnergyInfo>
  | ((prev: PlayerEnergyInfo) => Partial<PlayerEnergyInfo>);

interface IPlayerEnergyInfoContext {
  contextDispatch: Dispatch<UnionActionType>;
  subscribe: (args: any) => any;
  getContextState: () => IPlayerEnergyZustandStore;
}

const defaultPlayerEnergyInfoContext: IPlayerEnergyInfoContext = {
  getContextState: () => initialState,
  contextDispatch: () => false,
  subscribe: () => false,
};
const PlayerEnergyInfoContext = createContext<IPlayerEnergyInfoContext>(
  defaultPlayerEnergyInfoContext
);

const PLAYER_CONTEXT_ACTION_TYPE = {
  /**
   * 这仅用于context内部调用
   * 为了保证zustand的状态和context的状态一致,外部调用这个context的dispatch的时候请使用 `UPDATE` 方法
   */
  PRIVATE_UPDATE: 'PRIVATE_UPDATE',

  UPDATE: 'UPDATE',
  RESET: 'RESET',
} as const;

interface IUpdatePlayerInfo {
  /**
   * 这仅用于context内部调用
   * 为了保证zustand的状态和context的状态一致,外部调用这个context的dispatch的时候请使用 `UPDATE` 方法
   */
  type: typeof PLAYER_CONTEXT_ACTION_TYPE.PRIVATE_UPDATE;
  payload: Partial<IPlayerEnergyZustandStore>;
}
interface IRestPlayerInfo {
  type: typeof PLAYER_CONTEXT_ACTION_TYPE.RESET;
}
interface IPrivateUpdatePlayerInfo {
  type: typeof PLAYER_CONTEXT_ACTION_TYPE.UPDATE;
  payload: UpdatePlayerInfoParams;
}

type UnionActionType = IUpdatePlayerInfo | IRestPlayerInfo | IPrivateUpdatePlayerInfo;

const playerEnergyInfoReducer = (state: IPlayerEnergyZustandStore, action: UnionActionType) => {
  const { type } = action;
  const payload = (action as Exclude<UnionActionType, IRestPlayerInfo>).payload;

  switch (type) {
    case PLAYER_CONTEXT_ACTION_TYPE.PRIVATE_UPDATE:
      return {
        ...state,
        ...payload,
      };
    case PLAYER_CONTEXT_ACTION_TYPE.RESET:
      playerEnergyZustandStore.getState().resetPlayerInfo();
      return state;

    case PLAYER_CONTEXT_ACTION_TYPE.UPDATE:
      if (typeof payload === 'object') {
        playerEnergyZustandStore.getState().updatePlayerInfo(payload);
        return state;
      }
      if (typeof payload === 'function') {
        const prevState = playerEnergyZustandStore.getState();
        const newState = payload(prevState);
        playerEnergyZustandStore.getState().updatePlayerInfo(newState);
      }
      return state;

    default:
      return state;
  }
};
const PlayerEnergyInfoContextProvider: React.FC<any> = ({ children }) => {
  const [playerInfoState, dispatch] = useReducer(playerEnergyInfoReducer, initialState);

  const latestStateRef = useRef<IPlayerEnergyZustandStore>();
  latestStateRef.current = playerInfoState;

  const subscribersRef = useRef<((...args: any[]) => void)[]>([]);

  // state 变化，遍历执行回调
  useEffect(() => {
    subscribersRef.current.forEach((sub) => sub());
  }, [playerInfoState]);

  const energyModalRef = useRef<IEnergyModalRef>(null);

  const showEnergyModal = useCallback(() => {
    energyModalRef.current?.open();
  }, []);

  useEffect(() => {
    playerEnergyZustandStore.setState({ showEnergyModal: showEnergyModal });
    const unsubscribe = playerEnergyZustandStore.subscribe((state) => {
      const { energy, totalEnergy, freeTime, showEnergyModal } = state;
      dispatch({
        type: 'PRIVATE_UPDATE',
        payload: { energy, totalEnergy, freeTime, showEnergyModal },
      });
    });
    return () => {
      unsubscribe();
    };
  }, [showEnergyModal]);

  const memoContextValue = useMemo(() => {
    return {
      contextDispatch: dispatch,
      subscribe: (cb: any) => {
        subscribersRef.current.push(cb);
        return () => {
          subscribersRef.current = subscribersRef.current.filter((item) => item !== cb);
        };
      },
      getContextState: () => latestStateRef.current as IPlayerEnergyZustandStore,
    };
  }, []);

  return (
    <PlayerEnergyInfoContext.Provider value={memoContextValue}>
      {children}
      <EnergyModal ref={energyModalRef} />
    </PlayerEnergyInfoContext.Provider>
  );
};

export default PlayerEnergyInfoContextProvider;

export function usePlayerEnergyDispatch() {
  const context = useContext(PlayerEnergyInfoContext);
  return context.contextDispatch;
}
export function usePlayerEnergySelector<T>(selector: (state: IPlayerEnergyZustandStore) => T) {
  const [, forceRender] = useState({});
  const store = useContext(PlayerEnergyInfoContext);

  const selectedStateRef = useRef<T>();
  selectedStateRef.current = selector(store.getContextState());

  const checkForUpdates = useCallback(() => {
    const newState = selector(store.getContextState());
    if (newState !== selectedStateRef.current) forceRender({});
  }, [store]);

  useEffect(() => {
    const subscription = store.subscribe(checkForUpdates);
    return () => subscription();
  }, [store, checkForUpdates]);

  return selectedStateRef.current as T;
}
