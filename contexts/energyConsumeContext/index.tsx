import { EnergyConsumeConfig } from '@/server/index.types';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { energyConsumeZustandStore, initialState } from './store';

interface IEnergyConsumeContext {
  energyConsumeConfigs: EnergyConsumeConfig[];
  updateEnergyConsume: (arg: EnergyConsumeConfig[]) => void;
  resetEnergyConsume: () => void;
}

const defaultEnergyConsumeContext: IEnergyConsumeContext = {
  energyConsumeConfigs: [],
  updateEnergyConsume: () => false,
  resetEnergyConsume: () => false,
};
const EnergyConsumeContext = createContext<IEnergyConsumeContext>(defaultEnergyConsumeContext);

const EnergyConsumeContextProvider: React.FC<any> = ({ children }) => {
  const [energyConsume, setEnergyConsume] = useState<EnergyConsumeConfig[]>(initialState);

  useEffect(() => {
    const unsubscribe = energyConsumeZustandStore.subscribe((state) => {
      setEnergyConsume(state.energyConsumeConfigs);
    });
    return () => {
      unsubscribe();
    };
  }, []);

  const updateEnergyConsume = useCallback((state: EnergyConsumeConfig[]) => {
    energyConsumeZustandStore.getState().updateEnergyConsume(state);
  }, []);
  const resetEnergyConsume = useCallback(() => {
    energyConsumeZustandStore.getState().resetEnergyConsume();
  }, []);

  const memoContextValue = useMemo(() => {
    return {
      energyConsumeConfigs: energyConsume,
      updateEnergyConsume,
      resetEnergyConsume,
    };
  }, [energyConsume, resetEnergyConsume, updateEnergyConsume]);

  return (
    <EnergyConsumeContext.Provider value={memoContextValue}>
      {children}
    </EnergyConsumeContext.Provider>
  );
};

export default EnergyConsumeContextProvider;

export function useEnergyConsume() {
  const context = useContext(EnergyConsumeContext);
  return context;
}
