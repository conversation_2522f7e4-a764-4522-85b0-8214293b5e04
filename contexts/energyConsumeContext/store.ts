import { createStore } from 'zustand/vanilla';
import { EnergyConsumeConfig } from '@/server/index.types';

export enum ACTION_ENUM {
  Axe = 'Axe',
  Pickaxe = 'Pickaxe',
  FishingPole = 'FishingPole',
}

export const initialState: EnergyConsumeConfig[] = [
  {
    action: ACTION_ENUM.Axe,
    consume: 0,
  },
];

export const energyConsumeZustandStore = createStore<{
  energyConsumeConfigs: EnergyConsumeConfig[];
  updateEnergyConsume: (arg: EnergyConsumeConfig[]) => void;
  resetEnergyConsume: () => void;
}>((set) => ({
  energyConsumeConfigs: [],
  updateEnergyConsume: (payload) => {
    set((state) => {
      return {
        energyConsumeConfigs: payload ?? state.energyConsumeConfigs,
      };
    });
  },
  resetEnergyConsume: () => {
    set({
      energyConsumeConfigs: [],
    });
  },
}));
