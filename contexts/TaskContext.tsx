import React, { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { FormattedTaskReward } from 'components/EditAvatarPage/StoreMenu/components/TaskModal';
import { TaskSubItem } from 'components/EditAvatarPage/StoreMenu/components/TaskCard';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { IAppState, TaskReward } from '@/constant/type';
import { IncentivesConfig, IncentivesData } from '@/world/Config/IncentivesConfig';
import { useDispatch, useSelector } from 'react-redux';
import { getTaskList, refreshTask } from '@/server';
import toast from 'react-hot-toast';
import { setTaskInfo } from '@/store/app';
import { TaskNewStatusManager } from '@/utils/TaskNewStatusManager';
import { groupBy } from 'es-toolkit';

const taskNewStatusManager = new TaskNewStatusManager();

// 加载dayjs的duration插件
dayjs.extend(duration);

// 本地存储的key
const LOCAL_STORAGE_KEY = 'tracked_tasks';

// 任务类型定义
export interface TrackedTask {
  id: string;
  title: string;
  taskItems: TaskSubItem[];
  timestamp: number;
  timeRemaining?: string;
  rewards?: FormattedTaskReward[];
  taskDesc?: string;
  status?: 'active' | 'tracking' | 'completed' | 'waiting_settlement';
  clientRefresh?: boolean;
  taskType?: string;
  taskProgress?: TaskSubItem[];
  newTag?: boolean;
}

// Context接口定义
interface TaskContextType {
  trackedTasks: TrackedTask[];
  addTask: (task: TrackedTask) => void;
  removeTask: (taskId: string) => void;
  updateTaskProgress: (taskId: string, updatedItems: TaskSubItem[]) => void;
  updateTask: (taskId: string, updatedTask: Partial<TrackedTask>) => void;
  syncTaskProgress: (serverTasks: any[], configData?: any) => void;
  isTaskTracked: (taskId: string) => boolean;
  getTaskCount: () => number;
  refreshTasks: () => void;
  incentivesConfig: IncentivesData[];
  trackedTasksRef: TrackedTask[];
  onCheckTask: (taskId?: string, clientRefresh?: string[]) => void;
  loading: boolean;
  isTaskItemNew: (taskType: string, itemId: string) => boolean;
  clearNewStatusForTaskType: (taskType: string) => void;
  syncNewStatusForTaskType: (taskType: string, taskItems: { id: string }[]) => void;
  markAllAsRead?: () => void;
  getTaskNewStatus: () => void;
  hasNewInTaskType: (taskType: string) => boolean;
  setLastViewedTaskType: (taskType: string) => void;
  reloadTaskNewStatus: () => void;
  markTaskAsRead: (taskType: string, taskId: string) => void;
  hasAnyNewTask: () => boolean;
  getIncentivesConfig: () => Promise<void>;
}

// 创建Context
const TaskContext = createContext<TaskContextType | null>(null);

// Provider Props
interface TaskProviderProps {
  children: ReactNode;
}

// 从localStorage读取任务
const getTasksFromStorage = (): TrackedTask[] => {
  if (typeof window === 'undefined') return [];

  try {
    const data = localStorage.getItem(LOCAL_STORAGE_KEY);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Error reading tasks from localStorage:', error);
    return [];
  }
};

// 保存任务到localStorage
const saveTasksToStorage = (tasks: TrackedTask[]): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(tasks));
  } catch (error) {
    console.error('Error saving tasks to localStorage:', error);
  }
};

// Provider组件
export const TaskProvider = ({ children }: TaskProviderProps) => {
  const trackedTasksRef = useRef<TrackedTask[]>([]);
  // 添加一个状态变量来跟踪任务变化
  const [trackedTasks, setTrackedTasks] = useState<TrackedTask[]>([]);

  const { btcAddress, userBasicInfo } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  // 初始化 - 从localStorage加载数据
  useEffect(() => {
    const storedTasks = getTasksFromStorage();
    trackedTasksRef.current = storedTasks;
    setTrackedTasks(storedTasks); // 同步到状态变量
  }, []);

  const [incentivesConfig, setIncentivesConfig] = useState<IncentivesData[]>([]);

  const setLastViewedTaskType = (taskType: string) => {
    taskNewStatusManager.setLastViewedTaskType(taskType);
  };

  async function getIncentivesConfig() {
    IncentivesConfig.getInstance().getActionList((actionList) => {
      setIncentivesConfig(actionList);
    });
  }

  useEffect(() => {
    if (btcAddress) {
      getIncentivesConfig();
    }
  }, [btcAddress]);

  // 判断某个item是否new
  const isTaskItemNew = (taskType: string, itemId: string) => {
    return taskNewStatusManager.isTaskItemNew(taskType, itemId);
  };

  // 清除某个taskType下所有new（通常在tab切换时调用）
  const clearNewStatusForTaskType = (taskType: string) => {
    taskNewStatusManager.onTaskChange(taskType);
  };

  // 同步某个taskType的任务列表（通常在任务数据更新时调用）
  const syncNewStatusForTaskType = (taskType: string, taskItems: { id: string }[]) => {
    taskNewStatusManager.syncTaskItems(taskType, taskItems);
  };

  // 可选：全部标为已阅
  const markAllAsRead = () => {
    taskNewStatusManager.markAllAsRead();
  };

  const markTaskAsRead = (taskType: string, taskId: string) => {
    taskNewStatusManager.markTaskAsRead(taskType, taskId);
  };

  // 添加任务
  const addTask = (task: TrackedTask) => {
    // 检查是否已存在相同ID的任务
    const existingTaskIndex = trackedTasksRef.current.findIndex((t) => t.id === task.id);
    if (existingTaskIndex >= 0) {
      // 更新已存在的任务
      trackedTasksRef.current[existingTaskIndex] = {
        ...task,
        timestamp: Date.now(), // 更新时间戳
      };
    } else {
      // 添加新任务 (先进先出原则，新任务添加到末尾)
      trackedTasksRef.current = [
        ...trackedTasksRef.current,
        {
          ...task,
          timestamp: Date.now(),
        },
      ];
    }

    // 触发更新以通知依赖组件
    saveTasksToStorage(trackedTasksRef.current);
    setTrackedTasks([...trackedTasksRef.current]); // 更新状态变量触发重新渲染
  };

  // 移除任务
  const removeTask = (taskId: string) => {
    trackedTasksRef.current = trackedTasksRef.current.filter((task) => task.id !== taskId);

    // 触发更新
    saveTasksToStorage(trackedTasksRef.current);
    setTrackedTasks([...trackedTasksRef.current]); // 更新状态变量触发重新渲染
  };

  // 更新任务进度
  const updateTaskProgress = (taskId: string, updatedItems: TaskSubItem[]) => {
    const taskIndex = trackedTasksRef.current.findIndex((task) => task.id === taskId);
    if (taskIndex < 0) return; // 任务不存在

    // 更新任务项
    trackedTasksRef.current[taskIndex] = {
      ...trackedTasksRef.current[taskIndex],
      taskItems: updatedItems,
    };

    // 检查是否所有子任务都已完成
    const allCompleted = updatedItems.every(
      (item) => item.isCompleted || item.currentProgress >= item.totalProgress
    );

    // 如果所有子任务完成，移除该任务
    if (allCompleted) {
      trackedTasksRef.current = trackedTasksRef.current.filter((task) => task.id !== taskId);
    }

    // 触发更新
    saveTasksToStorage(trackedTasksRef.current);
    setTrackedTasks([...trackedTasksRef.current]); // 更新状态变量触发重新渲染
  };

  // 更新整个任务
  const updateTask = (taskId: string, updatedTaskData: Partial<TrackedTask>) => {
    const taskIndex = trackedTasksRef.current.findIndex((task) => task.id === taskId);
    if (taskIndex < 0) return; // 任务不存在

    trackedTasksRef.current[taskIndex] = {
      ...trackedTasksRef.current[taskIndex],
      ...updatedTaskData,
      timestamp: Date.now(), // 更新时间戳
    };

    // 触发更新
    saveTasksToStorage(trackedTasksRef.current);
    setTrackedTasks([...trackedTasksRef.current]); // 更新状态变量触发重新渲染
  };

  // 检查任务是否被跟踪
  const isTaskTracked = (taskId: string): boolean => {
    return trackedTasksRef.current.some((task) => task.id === taskId);
  };

  // 获取任务数量
  const getTaskCount = () => {
    return trackedTasksRef.current.length;
  };

  // 刷新任务 - 从localStorage重新加载
  const refreshTasks = () => {
    const storedTasks = getTasksFromStorage();
    trackedTasksRef.current = storedTasks;
    setTrackedTasks(storedTasks); // 同步到状态变量
  };

  // 从服务器同步任务进度
  const syncTaskProgress = (serverTasks: any[], configData?: IncentivesData[]) => {
    // 如果没有服务器任务数据，则直接返回
    if (!serverTasks || !Array.isArray(serverTasks) || serverTasks.length === 0) {
      return;
    }

    // 创建服务器任务ID的Set，用于快速查找
    const serverTaskIds = new Set(serverTasks.map((task) => task.id));

    // 找出本地追踪但服务器上不存在的任务IDs
    const orphanTaskIds = new Set<string>();

    // 获取当前追踪的任务
    const currentTrackedTasks = getTasksFromStorage();

    currentTrackedTasks.forEach((localTask) => {
      if (!serverTaskIds.has(localTask.id)) {
        orphanTaskIds.add(localTask.id);
      }
    });

    // 如果有孤儿任务，从本地追踪列表中移除
    if (orphanTaskIds.size > 0) {
      // 过滤掉孤儿任务，创建新的任务列表
      const filteredTasks = currentTrackedTasks.filter((task) => !orphanTaskIds.has(task.id));

      // 直接保存到localStorage
      saveTasksToStorage(filteredTasks);

      // 更新状态
      trackedTasksRef.current = filteredTasks;
      // 同时更新状态变量
      setTrackedTasks([...filteredTasks]);
    }

    const isServerTasks = serverTasks.map((task) => {
      return {
        ...task,
        taskItems: task.taskProgress.map((progress: any) => {
          return {
            ...progress,
            description: progress.conditionDesc,
            currentProgress: progress.currentCount,
            totalProgress: progress.requiredCount,
            isCompleted: progress.currentCount >= progress.requiredCount,
            conditionType: progress.conditionType,
            // 判断是否在clientRefresh中
            check: task.clientRefresh.includes(progress.conditionType),
            id: task.id,
            clientRefresh: task.clientRefresh,
          };
        }),
        taskType: task.taskType, // daily | community | outside | ...
      };
    });

    const taskGroup = groupBy(isServerTasks, (task) => task.taskType);

    // 同步每个 taskType 的“new”状态
    Object.entries(taskGroup).forEach(([taskType, tasks]) => {
      syncNewStatusForTaskType(taskType, tasks);
    });

    // 处理每个服务器任务
    isServerTasks.forEach((task) => {
      // 检查该任务是否被本地追踪
      const res = isTaskTracked(task.id);
      if (res) {
        // 获取倒计时显示
        const getTimeRemaining = (expireAt: string): string => {
          // 将剩余时间时间戳(毫秒)转换为天和小时
          const remainTimeMs = parseInt(expireAt);

          if (remainTimeMs <= 0) return 'Expired';

          // 使用dayjs的duration功能进行转换
          const duration = dayjs.duration(remainTimeMs);
          const days = Math.floor(duration.asDays());
          const hours = duration.hours();

          return `${days}D ${hours}H`;
        };

        // 检查任务是否已过期
        const isExpired = (expireAt: string): boolean => {
          const remainTimeMs = parseInt(expireAt);
          return remainTimeMs <= 0;
        };

        // 如果任务已过期，直接从追踪列表中移除
        if (task.expireAt && isExpired(task.expireAt)) {
          removeTask(task.id);
          return; // 跳过后续处理
        }

        // 准备更新数据
        const updatedTaskData: Partial<TrackedTask> = {
          title: task.taskTitle,
          taskDesc: task.taskDesc,
          timeRemaining: getTimeRemaining(task.expireAt),
          taskItems: task.taskItems,
          status: task.status,
          clientRefresh: task.clientRefresh,
          taskType: task.taskType,
          taskProgress: task.taskProgress,
          id: task.id,
        };

        // 获取任务奖励的资源URL
        const getTaskRewardUrl = (
          reward: TaskReward,
          incentivesConfig: IncentivesData[]
        ): string => {
          if (!incentivesConfig || incentivesConfig.length === 0) {
            return '';
          }
          // 根据type和tag匹配获取资源
          const config = incentivesConfig.find(
            (item) => item.type === reward.itemType && item.tag === reward.itemTag
          );
          if (config && config.link) {
            return config.link;
          }

          return '';
        };

        // 任务奖励
        const getTaskRewards = (
          taskRewards: TaskReward[],
          incentivesConfig: IncentivesData[]
        ): FormattedTaskReward[] => {
          if (!taskRewards || !Array.isArray(taskRewards) || taskRewards.length === 0) {
            return [];
          }

          return formatTaskRewards(taskRewards, incentivesConfig);
        };

        // 转换奖励数据的函数
        const formatTaskRewards = (
          rewards: TaskReward[],
          incentivesConfig: IncentivesData[]
        ): FormattedTaskReward[] => {
          return rewards.map((reward) => {
            return {
              ...reward,
              url: getTaskRewardUrl(reward, incentivesConfig),
            };
          });
        };

        updatedTaskData.rewards = getTaskRewards(task.taskRewards, configData);
        // 更新任务
        updateTask(task.id, updatedTaskData);
      }
    });

    // 确保在所有任务处理后再次更新状态变量，以捕获所有更改
    setTrackedTasks([...trackedTasksRef.current]);
  };

  // 当任务更新时，同步到localStorage
  useEffect(() => {
    saveTasksToStorage(trackedTasksRef.current);
  }, [trackedTasksRef.current]);

  // 刷新当前任务进度
  const onCheckTask = async (taskId?: string, clientRefresh?: string[]) => {
    try {
      if (!taskId || !clientRefresh) return;
      setLoading(true);
      const res = await refreshTask({ id: taskId, clientRefresh });
      const { code, msg } = res.data;
      if (code === 1) {
        // 任务完成，移除任务
        // removeTask(taskId);
        const taskData = await getTaskList();
        if (taskData.data.code === 1) {
          const taskList = taskData.data.data;
          syncTaskProgress(taskList, incentivesConfig);
          dispatch(setTaskInfo(taskList));
          setLoading(false);
        }
      } else {
        toast.error(msg);
      }
      setLoading(false);
    } catch (error) {
      console.log('error=======', error);
      setLoading(false);
    }
  };

  // 获取任务新状态
  const getTaskNewStatus = () => {
    return taskNewStatusManager.getTaskNewStatus();
  };

  const hasNewInTaskType = (taskType: string) => {
    return taskNewStatusManager.hasNewInTaskType(taskType);
  };

  const reloadTaskNewStatus = () => {
    taskNewStatusManager.reload();
  };

  const hasAnyNewTask = () => {
    return taskNewStatusManager.hasAnyNewTask();
  };

  // 提供Context值
  const contextValue: TaskContextType = {
    trackedTasks: trackedTasks, // 使用状态变量代替直接使用ref
    addTask,
    removeTask,
    updateTaskProgress,
    updateTask,
    syncTaskProgress,
    isTaskTracked,
    getTaskCount,
    refreshTasks,
    incentivesConfig,
    trackedTasksRef: trackedTasksRef.current,
    onCheckTask,
    loading,
    isTaskItemNew,
    clearNewStatusForTaskType,
    syncNewStatusForTaskType,
    markAllAsRead,
    getTaskNewStatus,
    hasNewInTaskType,
    setLastViewedTaskType,
    reloadTaskNewStatus,
    markTaskAsRead,
    hasAnyNewTask,
    getIncentivesConfig,
  };

  return <TaskContext.Provider value={contextValue}>{children}</TaskContext.Provider>;
};

// 自定义Hook - 便于在组件中使用
export const useTaskContext = (): TaskContextType => {
  const context = useContext(TaskContext);

  if (!context) {
    throw new Error('useTaskContext must be used within a TaskProvider');
  }

  return context;
};
