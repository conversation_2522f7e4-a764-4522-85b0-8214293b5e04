import { keyframes } from 'styled-components';

// combo跳动
const heartBeat = keyframes`
  0% {
    transform: scale(0.5);
  }

  14% {
    transform: scale(1.7);
  }

  28% {
    transform: scale(1);
  }

  42% {
    transform: scale(1);
  }

  70% {
    transform: scale(1);
  }
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
    visibility: hidden;
  }
  to {
    opacity: 1;
    visibility: visible;
  }
`;

// 定义隐藏动画
const fadeOut = keyframes`
  from {
    opacity: 1;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
`;

// 创建扫光动画
const shine = keyframes`
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
`;

// 放大动画
const zoomInDown = keyframes`
  from {
    opacity: 0;
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -62.5rem, 0);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }

  60% {
    opacity: 1;
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 3.75rem, 0);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
`;
const fadeOutUp = keyframes`
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
`;

const shakeX = keyframes`
    from,
  to {
    transform: translate3d(0, 0, 0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translate3d(-0.625rem, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translate3d(0.625rem, 0, 0);
  }
`;
const flash = keyframes`
  from,
  50%,
  to {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }  
`;

const shakeY = keyframes`
  0%, 50%, 100% {
    transform: translateY(0);
  }
  15%, 35% {
    transform: translateY(-1.125px);
  }
  25% {
    transform: translateY(0);
  }
`;

export { heartBeat, fadeIn, fadeOut, shine, zoomInDown, fadeOutUp, shakeX, flash, shakeY };
