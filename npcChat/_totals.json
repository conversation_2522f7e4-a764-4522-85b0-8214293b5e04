[{"id": 1, "chatText": "**Hey there, SatWorld adventurers**! We're throwing a **Community Party and you're invited!**<br><br>Join in on some fun tasks to earn **community-exclusive rewards**. **The more you join the fun, the better the prizes!**<br><br>Wanna join the party? Just link your **Twitter and let's get started!**", "chatUrl": "", "optionList": [10001.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 101, "chatText": "I'm your guide for the **Community Party Event!**<br>You can talk to me to learn the **Event Rules**.<br><br>Once you've gathered some in-game **resources**, don't forget to use the teleporter next to me to head over to the **Community Party Hall**.<br>Submit your resources to the **Community** you want to help build, and team up with other adventurers to grow it together!", "chatUrl": "", "optionList": [10102.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 102, "chatText": "Hey **Adventurers**! You here to try your luck too?<br>In my Smithy, even the plainest materials aren't just junk — **mix 'em right, throw in some skill, and with a bit of luck**… heh, you might just forge a **shiny legendary item**!<br>**The stuff you're carrying? It's all about the right combo!**<br>Now toss in what you think will work — let's see if we strike a miracle… or blow the place up!", "chatUrl": "", "optionList": [10202.0], "showRefreshTime": 0.0, "chatAction": "Action_26", "replayAction": 1.0}, {"id": 103, "chatText": "Hey there! Welcome to **Pizza Day 2025** — **Pizza Rush is on**!<br><br>Sign up with me and get your exclusive gear: **a cool hat and a super-roomy backpack!**<br><br>Once the game starts, a wave of delicious, steamy **Pizza Boxes** will rain down! Grab as many as you can and pack them into your backpack — **the more you collect, the higher you climb on the leaderboard**!<br><br>When the game ends, generous **$Pizza** token rewards will be distributed based on the rankings, and sent to your **PizzaSwap** within **12 hours**!<br><br>Gear up and jump in — who will rise as **the King of Pizza Rush**?", "chatUrl": "", "optionList": [10301.0, 10302.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 201, "chatText": "**GMGM. TheLonelyBit** is a trailblazing project built on **Fractal Bitcoin** to **Redefine BTC innovation with breakthrough ideas, tools,epic rewards & NFT mastery.**<br><br>Please submit the resources to **TheLonelyBit Community** to help us build our community party hub in **SatWorld**.", "chatUrl": "", "optionList": [20102.0, 20103.0, 20104.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 202, "chatText": "Hey there, welcome to **TheLonelyBit Community!**<br>**Website: thelonelybit.org**<br>**Telegram: t.me/FractalBits**<br>**X (Twitter): x.com/Fractal_TLB**<br><br>Every day, you can claim some **$TheLonelyBit Tokens** just by completing simple tasks — **easy rewards, just for having fun!**<br><br>We also welcome all **SatWorld Adventurers** to chip in with **token donations** to our community wallet.**Every bit helps us build cool stuff together for the future.**", "chatUrl": "", "optionList": [20201.0, 20202.0, 20206.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 203, "chatText": "Choose a **Server Line** to join the **multiplayer mode(Test).**<br>Here, you can see other players from **TheLonelyBit Community** and **chat with them** in real time.", "chatUrl": "", "optionList": [], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 204, "chatText": "Please select the type of **Tool** you want to **purchase**:", "chatUrl": "", "optionList": [20401.0, 20402.0, 20403.0, 20404.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 301, "chatText": "**Wang, wang, wang!! Wangcai** started as a wild experiment and turned into the most iconic **community-driven dog** in the inscription game — **100% powered by the people**.<br><br>Please submit the resources to **Wangcai Community** to help us build our community party hub in **SatWorld**.", "chatUrl": "", "optionList": [30102.0, 30103.0, 30104.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 302, "chatText": "Hey there, welcome to **Wangcai Community!**<br>**Telegram: t.me/wangcai_fractal**<br>**X (Twitter): x.com/wangcai_fractal**<br><br>Every day, you can claim some **$Wangcai Tokens** just by completing simple tasks — **easy rewards, just for having fun!**<br><br>We also welcome all **SatWorld Adventurers** to chip in with **token donations** to our community wallet.**Every bit helps us build cool stuff together for the future.**", "chatUrl": "", "optionList": [30201.0, 30202.0, 30206.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 303, "chatText": "Choose a **Server Line** to join the **multiplayer mode(Test).**<br>Here, you can see other players from **Wangcai Community** and **chat with them** in real time.", "chatUrl": "", "optionList": [], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 304, "chatText": "Please select the type of **Tool** you want to **purchase**:", "chatUrl": "", "optionList": [30401.0, 30402.0, 30403.0, 30404.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 401, "chatText": "**GMGM. Potato** is a vibrant community on Fractal Bitcoin, where members have spontaneously created **Potato-themed Games and Fractal Potato NFTs.**<br><br>Please submit the resources to **Potato Community** to help us build our community party hub in **SatWorld**.", "chatUrl": "", "optionList": [40102.0, 40103.0, 40104.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 402, "chatText": "Hey there, welcome to **Potato Community!**<br>**Telegram: t.me/fractalpotato**<br>**X (Twitter): x.com/brcpotato**<br><br>Every day, you can claim some **$Potato Tokens** just by completing simple tasks — **easy rewards, just for having fun!**<br><br>We also welcome all **SatWorld Adventurers** to chip in with **Token Donations** to our community wallet.**Every bit helps us build cool stuff together for the future.**", "chatUrl": "", "optionList": [40201.0, 40202.0, 40206.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 403, "chatText": "Choose a **Server Line** to join the **multiplayer mode(Test).**<br>Here, you can see other players from **Potato Community** and **chat with them** in real time.", "chatUrl": "", "optionList": [], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 404, "chatText": "Please select the type of **Tool** you want to **purchase**:", "chatUrl": "", "optionList": [40401.0, 40402.0, 40403.0, 40404.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 501, "chatText": "**Pizza** is a vibrant token born to celebrate **Bitcoin Pizza Day** and carry the joyful spirit of Bitcoin culture. <br>With a passionate community of Bitcoin lovers and creative players, we spark excitement through fun events like **Pizza Day** celebrations. We're exploring integration with platforms like **PizzaSwap** to showcase the boundless possibilities of the **BRC-20 ecosystem**. <br>**Come join me on this delicious blockchain adventure!**<br><br>Please submit the resources to **Pizza Community** to help us build our community party hub in **SatWorld**.", "chatUrl": "", "optionList": [50102.0, 50103.0, 50104.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 502, "chatText": "Hey there, welcome to **Pizza Community!**<br>**Telegram: t.me/brc_pizza**<br>**X (Twitter): x.com/BRC20__pizza**<br><br>Every day, you can claim some **$Pizza Tokens** just by completing simple tasks — **easy rewards, just for having fun!**<br><br>We also welcome all **SatWorld Adventurers** to chip in with **token donations** to our community wallet.**Every bit helps us build cool stuff together for the future.**", "chatUrl": "", "optionList": [50201.0, 50202.0, 50206.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 503, "chatText": "Choose a **Server Line** to join the **multiplayer mode(Test).**<br>Here, you can see other players from **Pizza Community** and **chat with them** in real time.", "chatUrl": "", "optionList": [], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 504, "chatText": "Please select the type of **Tool** you want to **purchase**:", "chatUrl": "", "optionList": [50401.0, 50402.0, 50403.0, 50404.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 601, "chatText": "**Domo-Inspired Ducks** on Bitcoin with Parent-Child Provenance to **Inscription #256** (The 1st Duck Inscribed on Bitcoin).<br><br>Please submit the resources to **DomoDucks Community** to help us build our community party hub in **SatWorld**.", "chatUrl": "", "optionList": [60102.0, 60103.0, 60104.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 602, "chatText": "Hey there, welcome to **DomoDucks Community!**<br>**Discord: discord.com/invite/H9nG9ybKUt**<br>**X (Twitter): x.com/DomoDucks**<br><br>Every day, you can claim some **$QUAQ Tokens** just by completing simple tasks — **easy rewards, just for having fun!**<br><br>We also welcome all **SatWorld Adventurers** to chip in with **token donations** to our community wallet.**Every bit helps us build cool stuff together for the future.**", "chatUrl": "", "optionList": [60201.0, 60202.0, 60206.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 603, "chatText": "Choose a **Server Line** to join the **multiplayer mode(Test).**<br>Here, you can see other players from **DomoDucks Community** and **chat with them** in real time.", "chatUrl": "", "optionList": [], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 604, "chatText": "Please select the type of **Tool** you want to **purchase**:", "chatUrl": "", "optionList": [60401.0, 60402.0, 60403.0, 60404.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}, {"id": 701, "chatText": "**DeTrading** is enabling cross-chain atomic swaps that do not rely on any central authority, servers, or other swap participants.** <br><br>The **swap** is trustless and requires no collateral, facilitating a simpler way to trade on **Fractal** without involving a third party or additional risk vectors.", "chatUrl": "", "optionList": [70101.0], "showRefreshTime": 0.0, "chatAction": "Action_12", "replayAction": 0.0}]