import { generateUUID } from 'three/src/math/MathUtils';
import { game } from '@/world/Proto/generated/game_messages';

export class ChatData {
  uuid: string;
  playerId: string;
  content: string;
  replyTo: string;
  timestamp: number;
  isTg: boolean;
  isSystem: boolean;
  admin: boolean;
  isTime: boolean;

  constructor() {
    this.uuid = generateUUID();
    this.playerId = '';
    this.content = '';
    this.replyTo = '';
    this.timestamp = Date.now();
    this.isTg = false;
    this.admin = false;
    this.isSystem = false;
    this.isTime = false;
  }

  encode() {
    return {
      uuid: this.uuid,
      playerId: this.playerId,
      content: this.content,
      replyTo: this.replyTo,
      timestamp: this.timestamp,
      isTelegram: this.isTg,
      isAdmin: this.admin,
    };
  }

  decode(data: game.IChatMessage) {
    this.uuid = data.uuid || '';
    this.playerId = data.playerId || '';
    this.content = data.content || '';
    this.replyTo = data.replyTo || '';
    this.timestamp = Number(data.timestamp);
    this.isTg = data.isTelegram || false;
    this.admin = data.isAdmin || false;
    this.isSystem = data.isSystem || false;
  }
}
