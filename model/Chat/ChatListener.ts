import { ChatEvent } from '@/model/Chat/ChatEvent';

export class ChatListener {
  private static instance: ChatListener;
  private listenerMap: Map<ChatEvent, ((data: any) => void)[]> = new Map();

  private constructor() {}

  static getInstance() {
    if (!ChatListener.instance) {
      ChatListener.instance = new ChatListener();
    }
    return ChatListener.instance;
  }

  addListener(event: ChatEvent, listener: (data: any) => void) {
    if (!this.listenerMap.has(event)) {
      this.listenerMap.set(event, []);
    }
    this.listenerMap.get(event)?.push(listener);
  }

  removeListener(event: ChatEvent, listener: (data: any) => void) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  notifyListener(event: ChatEvent, data: any) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        listener(data);
      });
    }
  }
}
