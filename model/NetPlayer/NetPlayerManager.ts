import { OtherPlayerData } from '@/world/Character/OtherPlayer';
import AvatarData from '@/AvatarOrdinalsBrowser/renderAvatar/Avatar/Data/AvatarData';
import createUseGame from '@/src/stores/useGame';
import { toFormatAccount } from '@/utils';
import { GetMyPlayer } from '@/world/Character/MyPlayer';
import { NetPlayerListener } from '@/model/NetPlayer/NetPlayerListener';
import { NetPlayerEvent } from '@/model/NetPlayer/NetPlayerEvent';

export class NetPlayerManager {
  private static instance: NetPlayerManager;

  private otherPlayers: OtherPlayerData[] = [];

  static getInstance() {
    if (!NetPlayerManager.instance) {
      NetPlayerManager.instance = new NetPlayerManager();
    }
    return NetPlayerManager.instance;
  }

  getPlayerList() {
    const btcAddressList = [];
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i];
      if (player.name.length > 0 && player.id.length > 0) {
        btcAddressList.push(player.id);
      }
    }
    return btcAddressList;
  }

  getOtherPlayerList() {
    return this.otherPlayers;
  }

  findOtherPlayer(id: string) {
    return this.otherPlayers.find((player) => player.id === id);
  }

  deleteOtherPlayer(id: string) {
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i];
      if (player.id === id) {
        player.id = '';
        this.otherPlayers.splice(i, 1);
        NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
        break;
      }
    }
  }

  addOtherPlayer(btcAddress: string, avatarData: AvatarData, usePet = '') {
    const useGame = createUseGame();
    const otherPlayer = new OtherPlayerData(btcAddress, avatarData, useGame);
    otherPlayer.usePet = usePet;
    otherPlayer.meshScale = 1;
    if (!btcAddress.includes('pet')) {
      otherPlayer.name = toFormatAccount(btcAddress);
    }
    this.otherPlayers.push(otherPlayer);
    NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
    return otherPlayer;
  }

  checkPlayerDistance(maxPlayers: number) {
    if (this.otherPlayers.length === 0) return;

    const list: { distance: number; player: OtherPlayerData }[] = [];
    const myPlayer = GetMyPlayer();
    this.otherPlayers.forEach((player) => {
      if (player.id.length > 0) {
        const distance = myPlayer.position.distanceTo(player.position);
        list.push({ distance, player });
      }
    });
    list.sort((a, b) => {
      return a.distance - b.distance;
    });

    list.forEach((item, index) => {
      item.player.isVisible = index < maxPlayers;
    });
  }

  clearDeadPlayer() {
    const list = [];
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i];
      if (player.id.length > 0) {
        list.push(player);
      }
    }
    this.otherPlayers = list;
    NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
  }

  clearAllPlayer() {
    this.otherPlayers = [];
    NetPlayerListener.getInstance().notifyListener(NetPlayerEvent.NetPlayerChange, {});
  }

  getDeadPlayerCount() {
    let count = 0;
    for (let i = 0; i < this.otherPlayers.length; i++) {
      const player = this.otherPlayers[i];
      if (player.id === '') {
        count++;
      }
    }
    return count;
  }
}
