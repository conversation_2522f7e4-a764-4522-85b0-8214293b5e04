import { NetPlayerEvent } from '@/model/NetPlayer/NetPlayerEvent';

export class NetPlayerListener {
  private static instance: NetPlayerListener;
  private listenerMap: Map<NetPlayerEvent, ((data: any) => void)[]> = new Map();

  static getInstance() {
    if (!NetPlayerListener.instance) {
      NetPlayerListener.instance = new NetPlayerListener();
    }
    return NetPlayerListener.instance;
  }

  addListener(event: NetPlayerEvent, listener: (data: any) => void) {
    if (!this.listenerMap.has(event)) {
      this.listenerMap.set(event, []);
    }
    this.listenerMap.get(event)?.push(listener);
  }

  removeListener(event: NetPlayerEvent, listener: (data: any) => void) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  notifyListener(event: NetPlayerEvent, data: any) {
    const listeners = this.listenerMap.get(event);
    if (listeners) {
      listeners.forEach((listener) => {
        listener(data);
      });
    }
  }
}
