---
description: 
globs: 
alwaysApply: true
---
# Web3功能

项目集成了多种Web3功能。

## 钱包连接

- 使用**useConnectWallet** hook处理钱包连接
- [components/Basic/ConnectWalletModal](mdc:components/Basic/ConnectWalletModal) 提供钱包连接界面

## 数字资产

- **NFTWindow** - [components/NFTWindow/](mdc:components/NFTWindow) NFT展示窗口
- **FTWindow** - [components/FTWindow/](mdc:components/FTWindow) Token展示窗口
- **Claim** - [components/Claim/](mdc:components/Claim) 领取功能

## 铭文(Inscriptions)

根据URL参数，项目支持通过inscriptionId和collectionId加载不同类型的Avatar：

```
?inscriptionId= // 加载铭文Avatar
?collectionId= // 加载收藏Avatar
```

## Web3开发最佳实践

1. 使用环境变量配置不同网络
2. 处理各种钱包连接错误和边缘情况
3. 实现交易状态反馈
4. 提供详细的交易历史记录
