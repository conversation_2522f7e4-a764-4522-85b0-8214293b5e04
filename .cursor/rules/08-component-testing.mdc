---
description: 
globs: 
alwaysApply: true
---
# 业务组件开发与测试

本规则提供了开发新业务组件时的最佳实践，包括测试流程、数据模拟、边界情况处理和调试方法。

## 组件开发流程

1. **规划设计** - 明确组件功能、接口和依赖
2. **Mock数据准备** - 创建模拟数据
3. **实现组件** - 基础功能实现
4. **测试与边界处理** - 测试各种情况
5. **优化与重构** - 完善代码结构

## Mock数据支撑

### 创建模拟数据

在组件目录下创建`mockData.ts`文件：

```typescript
// components/YourComponent/mockData.ts
export const mockNormalData = {
  id: '123',
  title: '测试标题',
  content: '这是测试内容',
  status: 'active'
};

// 边界情况数据
export const mockEmptyData = {};
export const mockLongTextData = {
  title: '非常非常非常非常非常非常非常非常非常非常非常长的标题',
  content: '非常长的内容'.repeat(50)
};
export const mockErrorData = {
  status: 'error',
  message: '发生错误'
};
```

### 使用模拟数据

在组件中这样使用Mock数据：

```typescript
import { mockNormalData } from './mockData';

// 使用模拟数据进行开发
// const data = mockNormalData; // 开发时可以取消注释这行，上线前注释掉
const data = realData; // 实际数据
```

## 边界情况处理

测试以下边界情况：

1. **空数据** - 组件在没有数据时应该如何显示
2. **加载状态** - 数据加载中的显示状态
3. **错误状态** - 加载失败的处理
4. **异常数据** - 数据格式不符合预期
5. **极限情况** - 超长文本、大量数据项等

示例：

```typescript
function YourComponent({ data }) {
  // 可以在此处添加调试日志
  console.log('[YourComponent] 接收到的数据:', data);
  
  // 处理空数据
  if (!data || Object.keys(data).length === 0) {
    return <EmptyState message="暂无数据" />;
  }
  
  // 处理异常数据
  if (data.status === 'error') {
    return <ErrorMessage message={data.message || '加载失败'} />;
  }
  
  // 处理正常数据
  return (
    <div>
      {/* 处理可能的超长文本 */}
      <h1 title={data.title}>{
        data.title.length > 20 
          ? `${data.title.substring(0, 20)}...` 
          : data.title
      }</h1>
      <div>{data.content}</div>
    </div>
  );
}
```

## 调试技巧

### 日志输出

在组件中添加调试日志，帮助理解组件生命周期和数据流：

```typescript
// debug 辅助函数 - 为日志添加前缀
const debug = (message, data) => {
  console.log(`[YourComponent] ${message}:`, data);
};

function YourComponent(props) {
  debug('组件渲染，接收到的props', props);
  
  useEffect(() => {
    debug('组件已挂载');
    return () => debug('组件将卸载');
  }, []);
  
  // ...组件逻辑
}
```

### 调试面板

在组件中添加可视化调试面板：

```typescript
// 添加一个状态控制调试面板显示
const [showDebugPanel, setShowDebugPanel] = useState(false);

// 在组件内部渲染调试面板
{showDebugPanel && (
  <div className="debug-panel" style={{ border: '1px solid red', padding: '10px', margin: '10px' }}>
    <h4>调试面板</h4>
    <button onClick={() => setData(mockEmptyData)}>测试空数据</button>
    <button onClick={() => setData(mockErrorData)}>测试错误状态</button>
    <button onClick={() => setData(mockLongTextData)}>测试长文本</button>
    <pre>{JSON.stringify(currentData, null, 2)}</pre>
  </div>
)}

// 添加开关调试面板的按钮（可放在组件角落）
<button 
  onClick={() => setShowDebugPanel(!showDebugPanel)}
  style={{ position: 'absolute', right: '5px', top: '5px', fontSize: '10px' }}
>
  {showDebugPanel ? '隐藏调试' : '显示调试'}
</button>
```

## 性能测试

添加简单的性能测试工具：

```typescript
// 添加性能标记
useEffect(() => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    console.log(`[性能] ${componentName} 渲染耗时: ${endTime - startTime}ms`);
  };
}, [dataToWatch]); // 监控特定数据变化的性能
```

## 上线准备

组件发布前的检查清单：

1. 搜索并删除所有包含`console.log`的调试代码
2. 注释或删除模拟数据的直接使用
3. 删除或禁用调试面板
4. 确保所有测试代码已经被移除

可以使用以下命令查找项目中的console.log：

```bash
# 查找项目中的所有console.log
grep -r "console.log" --include="*.tsx" --include="*.ts" ./components/
```
