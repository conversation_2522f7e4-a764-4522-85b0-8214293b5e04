---
description: 
globs: 
alwaysApply: true
---
# 组件结构

项目使用模块化组件结构，每个主要组件都有自己的目录。

## 主要组件目录

- **components/Layout/** - 页面布局组件
- **components/Header/** - 头部导航组件
- **components/HomePage/** - 首页相关组件
- **components/AvatarPage/** - 头像相关组件
- **components/Basic/** - 基础UI组件
- **components/GlobalStyleProvider/** - 全局样式提供者

## 组件开发规范

1. 每个组件应放在自己的目录中
2. 样式使用Styled Components
3. 组件应遵循TypeScript类型定义
4. 使用函数式组件和React Hooks
