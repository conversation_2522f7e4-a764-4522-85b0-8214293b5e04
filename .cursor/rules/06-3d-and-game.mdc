---
description: 
globs: 
alwaysApply: true
---
# 3D和游戏功能

项目包含丰富的3D和游戏功能。

## 3D技术栈

- **Three.js** - 核心3D库
- **@react-three/fiber** - React Three.js集成
- **@react-three/drei** - Three.js辅助组件
- **@react-three/rapier** - 物理引擎
- **cannon-es** - 物理引擎
- **three-mesh-ui** - 3D UI组件

## 游戏功能

- **GameWindow** - [components/GameWindow/](mdc:components/GameWindow) 游戏窗口组件
- **ChangeSceneWindow** - [components/ChangeSceneWindow/](mdc:components/ChangeSceneWindow) 场景切换窗口
- **world/** - 游戏世界相关代码

## 资源加载

- 3D模型使用GLB格式
- 使用webpack的url-loader加载字体和3D模型
- 使用howler.js加载和播放音频

## 性能优化

1. 使用r3f-perf监控3D性能
2. 使用LOD（Level of Detail）技术
3. 使用实例化渲染大量相似对象
4. 延迟加载非关键3D资源
