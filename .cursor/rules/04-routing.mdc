---
description: 
globs: 
alwaysApply: true
---
# 路由和页面结构

项目使用Next.js的文件系统路由。

## 主要页面

- [pages/index.tsx](mdc:pages/index.tsx) - 网站首页
- [pages/home.tsx](mdc:pages/home.tsx) - 主页内容
- [pages/avatar/](mdc:pages/avatar) - 头像相关页面
- [pages/orders.tsx](mdc:pages/orders.tsx) - 订单页面
- [pages/assets.tsx](mdc:pages/assets.tsx) - 资产页面

## URL参数使用

根据README文档，项目支持以下URL参数：

```
?inscriptionId= // 加载铭文Avatar
?collectionId= // 加载收藏Avatar
```

## 路由跳转

使用Next.js的Router或Link组件进行页面跳转：

```typescript
import { useRouter } from 'next/router';
import Link from 'next/link';

// 使用Router
const router = useRouter();
router.push('/avatar');

// 使用Link
<Link href="/avatar">头像</Link>
```
