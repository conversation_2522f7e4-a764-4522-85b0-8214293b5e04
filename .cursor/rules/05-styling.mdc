---
description: 
globs: 
alwaysApply: true
---
# 样式和UI设计

项目使用多种样式解决方案。

## 样式技术栈

- **Styled Components** - 主要的CSS-in-JS解决方案
- **CSS Modules** - 用于某些组件（如 [styles/AnimatedList.module.css](mdc:styles/AnimatedList.module.css)）
- **全局样式** - 通过 [styles/global.ts](mdc:styles/global.ts) 提供

## UI组件库

项目集成了多个UI组件库：

- **rc-slider** - 滑块组件
- **rc-tooltip** - 提示组件
- **react-hot-toast** - 通知提示
- **slick-carousel** - 轮播组件

## 动画解决方案

项目提供多种动画实现方案：

- **motion** - 业务组件UI动画首选库，提供丰富的动画效果和手势交互
- **gsap** - 复杂动画序列
- **lottie** - 矢量动画

### motion/react使用指南

在业务组件中实现UI动画时，优先使用motion库：

```typescript
import { motion } from 'motion';

// 基础动画组件
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0 }}
  transition={{ duration: 0.3 }}
>
  内容
</motion.div>

// 手势响应
<motion.button
  whileHover={{ scale: 1.05 }}
  whileTap={{ scale: 0.95 }}
  onClick={handleClick}
>
  点击按钮
</motion.button>

// 滚动动画
<motion.section
  initial={{ opacity: 0 }}
  whileInView={{ opacity: 1 }}
  viewport={{ once: true }}
>
  滚动显示内容
</motion.section>
```

## 三维和动画

项目包含3D和动画相关的技术：

- **@react-three/fiber** - React Three.js集成
- **@react-three/drei** - Three.js辅助组件
- **@splinetool** - 3D设计工具

## 样式最佳实践

1. 组件样式应与组件放在同一目录
2. 使用主题变量保持样式一致性
3. 响应式设计优先
4. 使用GlobalStyleProvider提供全局样式
5. 业务组件的UI动画优先使用motion/react库实现
