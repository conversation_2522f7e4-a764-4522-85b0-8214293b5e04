---
description: 
globs: 
alwaysApply: true
---
# 项目结构

这是一个基于Next.js的Web3项目，名为Uniwords。以下是主要目录的说明：

## 主要入口文件
- [pages/_app.tsx](mdc:pages/_app.tsx) - 应用入口
- [pages/index.tsx](mdc:pages/index.tsx) - 主页

## 目录结构
- **pages/** - Next.js页面
- **components/** - React组件
- **hooks/** - 自定义React钩子
- **utils/** - 工具函数
- **styles/** - 样式文件
- **public/** - 静态资源
- **store/** - Redux状态管理
- **types/** - TypeScript类型定义
- **commons/** - 公共组件
- **AvatarOrdinalsBrowser/** - 第三方组件子仓库
- **Config/** - 基于游戏资源相关的cdn配置
- **Global/** - 全局组件或者全局属性
- **animates/** - 动画相关的css
- **constant/** - 常量相关的配置文件

## 配置文件
- [next.config.js](mdc:next.config.js) - Next.js配置
- [tsconfig.json](mdc:tsconfig.json) - TypeScript配置
