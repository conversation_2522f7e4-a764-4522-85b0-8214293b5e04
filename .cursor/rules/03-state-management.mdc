---
description: 
globs: 
alwaysApply: true
---
# 状态管理

项目使用Redux进行全局状态管理，并结合React Hooks进行局部状态管理。

## Redux存储结构

- **store/index.ts** - Redux存储配置
- **store/reducers/** - Redux reducers
- **store/actions/** - Redux actions

## 自定义Hooks

项目定义了许多自定义hooks用于特定功能：

- **useConnectWallet** - 处理钱包连接
- **useDomainOwner** - 处理域名所有权
- **useTtsWhiteList** - 处理白名单逻辑
- **useJump** - 处理页面跳转逻辑

## 状态管理最佳实践

1. 全局状态应使用Redux
2. 组件内部状态应使用useState和useReducer
3. 复杂业务逻辑应封装在自定义hooks中
4. 避免状态提升过高，保持状态靠近使用它的组件
