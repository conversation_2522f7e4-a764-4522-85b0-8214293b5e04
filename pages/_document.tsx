import { WEBSITE_DOMAIN } from '@/constant';
import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  const image = `${WEBSITE_DOMAIN}/image/logo.png`;

  return (
    <Html lang="en">
      <Head>
        <link rel="shortcut icon" href={'/image/ico.png'} type="image/x-icon" />
        <meta property="og:title" content="SatWorld" />
        <meta property="og:url" content={WEBSITE_DOMAIN} />
        <meta property="og:image" content={image} />
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="675" />
        <meta name="description" content="SatWorld Avatar" />
        <meta name="twitter:card" content="summary_large_image" />
        {/*<meta name="twitter:site" content={CONFIG.title}></meta>*/}
        {/*<meta name="twitter:creator" content='@'></meta>*/}
        <meta property="twitter:title" content="SatWorld" />
        <meta property="twitter:image" content={image} />
        <meta name="twitter:image:alt" content={image} />
        <meta name="twitter:url" content={WEBSITE_DOMAIN} />

        {/* <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        <link
          href="https://fonts.googleapis.com/css2?family=Baloo+2:wght@400..800&family=Bevan&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Shrikhand&display=swap"
          rel="stylesheet"
          // crossOrigin="anonymous"
        /> */}
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
