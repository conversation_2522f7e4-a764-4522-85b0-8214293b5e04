import { FrontendPageView } from '@/components/EditAvatarPage/style';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { checkIsBtcAddress, getNFTImgLink } from '@/utils';
import { useDispatch, useSelector } from 'react-redux';
import {
  IAppState,
  IBasicSummaryData,
  IBindInfo,
  IVTT,
  IWallNft,
  SCENE_TYPE,
} from '@/constant/type';
import { getAvatar, getBasicSummaryData } from '@/server';
import GameWindow from '../../components/GameWindow';
import { DOMAIN_JUMP, LOCAL_HOSTS_DOMAIN } from '@/constant';
import toast from 'react-hot-toast';
import { IAvatarMetadata } from '@/AvatarOrdinalsBrowser/constant/type';
import GlobalSpaceEvent, { GlobalDataKey } from '../../world/Global/GlobalSpaceEvent';
import { setIsRunJumpLogic, setPageLoadingRate, setSceneType } from '@/store/app';
import EnterButton from '../../components/AvatarPage/EnterButton';
import { ButlerData, ButlerUtil } from '@/world/Global/GlobalButlerUtil';
import StorageMenu from '../../components/EditAvatarPage/StoreMenu';
import AssistantQuestion from '../../components/EditAvatarPage/RecordingModal/AssistantQuestion';
// import * as THREE from "three";
import GlobalSpace, { GAME_OP_TYPE } from '../../world/Global/GlobalSpace';

export default function AddressAvatar() {
  const searchParams = useSearchParams();
  const { btcAddress, sceneType } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [address, setAddress] = useState<string>('');
  const [basicSummaryData, setBasicSummaryData] = useState<IBasicSummaryData | null>(null); //被访问者的
  const [tempAvatar, setTempAvatar] = useState<IAvatarMetadata | undefined>(undefined);
  const [visitorAvatarMetadata, setVisitorAvatarMetadata] = useState<IAvatarMetadata | undefined>(
    undefined
  );
  // 是否靠近了管家
  const [isNearTheButler, setIsNearTheButler] = useState<boolean>(false);

  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setIsRunJumpLogic(true));
    // GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Avatar)
  }, []);
  // 监听场景加载
  useEffect(() => {
    const unsubscribe = GlobalSpaceEvent.ListenKeyDataChange<boolean>(
      GlobalDataKey.SceneLoading,
      (SceneLoading: boolean) => {
        dispatch(setPageLoadingRate(SceneLoading ? 0 : 100));
      }
    );
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SceneLoading, unsubscribe);
    };
  }, []);
  useEffect(() => {
    setAddress('');
    const address_ = searchParams.get('address');
    if (LOCAL_HOSTS_DOMAIN) {
      // 如果是本地测试
      setAddress(LOCAL_HOSTS_DOMAIN.split('.')[0]);
      return;
    }
    if (address_ && (checkIsBtcAddress(address_) || address_.includes(DOMAIN_JUMP))) {
      if (address_.includes(DOMAIN_JUMP)) {
        const addressArr = address_.split('.');
        if (addressArr.length > 1) {
          setAddress(addressArr[0].replace('https://', ''));
        }
      } else {
        setAddress(address_);
      }
    } else {
      const domain = window.location.hostname;
      if (!domain.includes(DOMAIN_JUMP)) {
        return;
      }
      const match = domain.match(/^([^.]+)\./);
      const name = match ? match[1].replace('https://', '') : null; // 如果匹配成功，返回第一个捕获组
      if (name) {
        setAddress(name);
      }
    }
  }, [searchParams]);
  // 查看btc账号下我的临时装扮
  const getTempAvatarData = async (btcAddress_: string = btcAddress): Promise<IAvatarMetadata> => {
    if (!btcAddress_) {
      return {} as IAvatarMetadata;
    }
    try {
      const res = await getAvatar(btcAddress_);
      if (!res.data || !res.data.data) {
        return {} as IAvatarMetadata;
      }
      return res.data.data as IAvatarMetadata;
    } catch (e) {
      return {} as IAvatarMetadata;
    }
  };

  useEffect(() => {
    if (address) {
      getBasicSummaryData({
        address,
      }).then((res: any) => {
        if (res.data.code === 1) {
          const data: any = res.data.data;
          // data.inscriptionResult = data.inscriptionResult.map((i: string) => getServerLink(i))
          setBasicSummaryData(data);
          // tempAvatarResult
          if (data.tempAvatarResult) {
            getTempAvatarData(data.tempAvatarResult.address).then((avatarData) => {
              setTempAvatar(avatarData);
            });
          } else {
            setTempAvatar({} as IAvatarMetadata);
          }
        } else {
          setTempAvatar({} as IAvatarMetadata);
          setBasicSummaryData(null);
          toast.error(res.data.msg);
        }
      });
    }
  }, [address]);
  useEffect(() => {
    getTempAvatarData(btcAddress).then((avatarData) => {
      setVisitorAvatarMetadata(avatarData);
    });
  }, [btcAddress]);

  const defaultInscriptionId = basicSummaryData?.initInscriptionId?.inscriptionId || '';

  const showWallNftList: IWallNft[] = useMemo(() => {
    if (!basicSummaryData) {
      return [];
    }
    // TODO 用户的也要获取用户自己设置的位置
    return basicSummaryData.inscriptionResult.map((i: string, idx: number) => {
      return {
        id: idx,
        position: idx + 1,
        content: getNFTImgLink(i),
      };
    });
  }, [basicSummaryData]);

  const butlerDataParams: ButlerData | null = useMemo(() => {
    const summaryTtsResult: IBindInfo | null = basicSummaryData?.ttsResult || null;
    if (!summaryTtsResult || !summaryTtsResult.data) {
      return null;
    }
    summaryTtsResult.subtitle = summaryTtsResult.subtitle || [];
    const vttData = summaryTtsResult.subtitle.map((i: IVTT) => ({
      start: i.start_time,
      end: i.end_time,
      content: i.text,
    }));

    return {
      butlerPosition: summaryTtsResult.data.butlerPosition,
      butlerQuaternion: summaryTtsResult.data.butlerQuaternion,
      butlerSceneType: summaryTtsResult.data.butlerSceneType as SCENE_TYPE,
      usePet: summaryTtsResult.data.usePet || '',
      vttData: vttData,
      mp3Url: summaryTtsResult.cdn,
      visitorPositionList: summaryTtsResult.data.visitorPositionList || [],
    };
  }, [basicSummaryData]);

  const screenTypeCallback = (screenType: SCENE_TYPE) => {
    dispatch(setSceneType(screenType));
  };
  useEffect(() => {
    let uuid = '';
    ButlerUtil.setNearButler(() => {
      uuid = GlobalSpace.addGameOp(GAME_OP_TYPE.ChatOp, () => {
        setIsNearTheButler(true);
        GlobalSpace.removeGameOp(uuid);
      });
    });
    ButlerUtil.setFarButler(() => {
      setIsNearTheButler(false);
      GlobalSpace.removeGameOp(uuid);
      ButlerUtil.closeAnswer();
    });
  }, []);
  return (
    <>
      <FrontendPageView>
        <GameWindow
          collectionData={null}
          defaultInscriptionId={defaultInscriptionId}
          isFrontend={true}
          basicSummaryData={basicSummaryData}
          showWallNftList={showWallNftList}
          // butlerData={null}
          butlerData={butlerDataParams}
          defaultAvatarMetadata={tempAvatar}
          isVisitor={true}
          visitorAvatarMetadata={visitorAvatarMetadata ?? undefined}
          screenTypeCallback={screenTypeCallback}
        />
        <EnterButton />
        <StorageMenu basicSummaryData={basicSummaryData} />
        {/*AI提问框*/}
        {sceneType === SCENE_TYPE.Island && isNearTheButler && <AssistantQuestion />}
      </FrontendPageView>
    </>
  );
}
