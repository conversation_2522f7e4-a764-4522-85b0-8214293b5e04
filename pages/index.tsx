import EditAvatarPage from '../components/EditAvatarPage';
import { useRouter } from 'next/router';
import { DOMAIN_JUMP } from '../constant';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '../constant/type';
import { setIsRunJumpLogic } from '../store/app';

export default function Home() {
  // 从 Redux store 获取 isRunJumpLogic 状态，用于控制是否已执行跳转逻辑
  const { isRunJumpLogic } = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const router = useRouter(); // Next.js 路由器，用于页面跳转
  const dispatch = useDispatch(); // Redux dispatch 函数，用于触发 action

  // 检查域名并决定是否需要跳转到 /avatar 路径
  const getDomainAddress = () => {
    const domain = window.location.hostname; // 获取当前网站的域名

    // 测试代码：如果本地存储中有 jumpAvatarTest 项，直接跳转到 /avatar
    const jumpAvatarTest = localStorage.getItem('jumpAvatarTest');
    if (jumpAvatarTest) {
      dispatch(setIsRunJumpLogic(true)); // 标记跳转逻辑已执行
      router.replace(`/avatar`); // 跳转到 /avatar 页面
      return;
    }
    // 如果当前域名不包含 DOMAIN_JUMP 常量定义的域名，标记跳转逻辑已执行并返回
    if (!domain.includes(DOMAIN_JUMP)) {
      dispatch(setIsRunJumpLogic(true));
      return;
    }

    // 尝试匹配二级域名，例如从 "subdomain.example.com" 提取 "subdomain"
    let match = domain.match(/^([^.]+)\./);
    const name = match ? match[1].replace('https://', '') : null; // 如果匹配成功，返回第一个捕获组

    // 如果存在二级域名，跳转到 /avatar 页面；否则标记跳转逻辑已执行
    if (name) {
      router.replace(`/avatar`);
    } else {
      dispatch(setIsRunJumpLogic(true));
    }
  };

  // 当组件挂载或 isRunJumpLogic 变化时执行
  useEffect(() => {
    // 如果跳转逻辑尚未执行，则执行 getDomainAddress 函数
    if (!isRunJumpLogic) {
      getDomainAddress();
    }
  }, [isRunJumpLogic]);

  // 渲染页面内容
  return (
    <>
      {
        // 如果跳转逻辑尚未执行，显示空 div；否则显示 EditAvatarPage 组件
        !isRunJumpLogic ? <div /> : <EditAvatarPage />
      }
    </>
  );
}
