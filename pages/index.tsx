import AvatarPage from '../components/AvatarPage';
import Head from 'next/head';
import { useEffect, useState } from 'react';

export default function Home() {
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(false);
  }, []);
  return (
    <>
      <Head>
        <title>UniWorlds</title>
        <meta name="viewport" content="initial-scale=1.0, width=device-width" />
        <link
          rel="shortcut icon"
          href={
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAFR0lEQVR4Xu2c3XHbOhCF/RKT8r2mMObqPSWkhJSgEtxB0kHSQdxB0oHSgUpICSnBJdyLBQVptfwHVgRJ7TdzJhNTXIA4BBYAKT08KIqiKIqiKIqiKIoyMeV2c9ht87/S4uUoLcA2P1r9Jy1ejtICqAFpATUgLTAjA3Zm81YW+c9OPeev/LxFA3MywCZvHocLTeDnLRpQA9ICakBaQA1IC6gBaQE1IC2gBqQF1IC0gBqQFpiRAXcJqAFpATUgLaAGpAXUgLRAQgN2/3z4BP/mn6XE44tg8vzj7jl/hSL/jlMw2GYHqBrNyf2t2Pxwx5+f9lgRY+Fx2jjFqTVgrHg5TYBg2aLPobEBbYN+K7fZOy9oqKpzs8PO5K878+FTmynWwF/8gbqEeDlNwBwNeCmyLyENj+eUxeatNI97Y/KPPO4cgbkZAEX2gwfukzPLZF95rCUAczKgtEMFD9qn0haa6m4vnx/3fNjpE99GgDkZAAGVSfmAGnMLr88AHWkM/H/DZ4IkYUAtaJfw7o+NMVa0rLs3wOaL39ExRoqWdfcG8PE0JMZY0bJWZwAG4EE7pT3gStEGlEX2xoN2K3vnMeqfkRUta3UGgMk/86C9Mtf7H7XjwqJlrc4AYx7M2BXwzq58aQx+XFq0rNUZgOCGGg/creyd7vHUj8uK1nWVBoT0Aty+8OfXjgmL1nWVBiDje0F+zgX23/HnjhCt52oNCOkF1Z5QNRSB4EVx0Xqu1gDkxWy+8gJ6dRqKKgNHrikGitYRDceNwLGiMWCuBiAQUrmi2pbGC72FCbyOsUDINbZI3ICqEccNRTgrwqdfl/NlTeB1jAVzV/XELl6ledrz+NEEDUVualp1dWkTWPXuA+vuL94QfaIPaiRN8HVyD49wtpZQvi43JzSpchNsz/jDPzNWvk4gOHaH6txAUxCWD+qPLMdv+F3Lx4F7MwAxNrnySgwRmuATMxKzWDvHuEcDkJAH95WydzpLwFdXQoY1fz7cqwFIzB1sk9c3HyckOZ/rcM8GIHEmXDbvkDF54Vz+vRuAxJhQS864kBnQG85lqwEVsSbQ94qGTFXP5aoBF2JMcLJDUtuLu22AGnBNrAl8SOoD1IA61dRy/GLtSmSW1AWoAc3gYm1IMu2SO5+9bcEBNaCdkPl9k/DNu7ZhCdSAbtwGXjF+F5XLGXl6yEMBNWAYscnZq2nKKmFwjMhlnsEbb+yM7uZI5AWiIx2WvBGC8QeLXOIVfKU/C6TvWJ4fXPyBq2kpkcu7wm2tDJzNTQ4+4oyeqhJxI5DTdBi/Slv7vKRomRR8Puw+0zOTS4bULInpyL8o7XtF3/ZGqGhZFPzuGh6vFpczywcU6d6A8smaXzia8WJnU5Jm0PgUb8BJR358VkjnBi9nhB2e8KcIGst0+SI7xNwAPK6n+jUBUpdbvK4izW0T6OZP1StaFnV2rK56R34cYwiPgzQPr9dvkM8aqXVDh45dZiBu2oyJvNjgg6JWU+rnGXyntjn5T/kaSyy3Gpbqsj2jwB/jftr33aF4HHsKGoO9BWc69Lj7tRQbr16G14J6gWc6I7ywAbODG45sg2L+aGs0/Dse90NXPVaDGrZUFgHeeYMv8kbCF2+92oakAZr3jKgPNzZP2iPk1dajFsX0Q5Og5ro6DiHF/k+scJHGr2MVTLX/E6019YAmLvs/aZN2k7Cn8vqumsv+z0zMWOiviokgtf8TLLOglfAU+P2fCQw5rn7cl6DqIY973LOpknnYtvXJTGz079rwAvg9IJT/NiT9ntjlG5LL+alORVEURVEURVHS8j8V0L5Y0GW1OAAAAABJRU5ErkJggg=='
          }
          type="image/x-icon"
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&display=swap"
          rel="stylesheet"></link>
      </Head>
      {loading ? (
        <div
          style={{
            position: 'fixed',
            left: 0,
            top: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 10,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#cccccc',
          }}>
          <p
            style={{
              fontSize: '16px',
              fontWeight: 400,
              lineHeight: '19.36px',
              textAlign: 'center',
              color: '#615A57',
            }}>
            Loading...
          </p>
        </div>
      ) : (
        <>
          <AvatarPage
            collectionData={null}
            isFrontend={false}
            exportPreviewImage={() => {}}
            transparentBackground={false}
          />
        </>
      )}
    </>
  );
}
