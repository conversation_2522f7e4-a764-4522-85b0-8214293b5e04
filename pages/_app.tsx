import type { AppProps } from 'next/app';
import store, { persistor } from '../store';
import { PersistGate } from 'redux-persist/integration/react';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';
import ConnectWalletModal from '../components/Basic/ConnectWalletModal';
import useConnectWallet from '../hooks/useConnectWallet';
import useDomainOwner from '../hooks/useDomainOwner';
import useTtsWhiteList from '../hooks/useTtsWhiteList';
import useJump from '../hooks/useJump';
import { TaskProvider } from 'contexts/TaskContext';
import TaskUpdateListener from '../components/TaskUpdateListener';
import 'rc-tooltip/assets/bootstrap.css';
import React, { ReactElement, ReactNode } from 'react';
import dynamic from 'next/dynamic';
import type { NextPage } from 'next';
import Layout from '@/components/Layout';
import LogicLayout from '@/components/Layout/LogicLayout';
import PlayerEnergyInfoContextProvider from '@/contexts/playerEnergyContext';
import EnergyConsumeContextProvider from '@/contexts/energyConsumeContext';
import Head from 'next/head';
import '@/styles/globals.css';

const PreloadFont = dynamic(() => import('@/components/PreloadFont'), { ssr: false });

export type NextPageWithLayout<P = object, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};

type AppPropsWithLayout = AppProps & {
  Component: NextPageWithLayout;
};

function GlobalHook() {
  useConnectWallet(true); // 处理钱包连接相关的逻辑
  useDomainOwner(); // 处理域名所有权相关的逻辑
  useTtsWhiteList(); // 处理白名单相关的逻辑
  useJump(); // 处理页面跳转相关的逻辑
  return null; // 不渲染任何 UI
}

export default function App({ Component, pageProps }: AppPropsWithLayout) {
  /**
   * 如果页面有自己的Layout 则使用自己的layout，否则使用统一的Layout
   */
  const getLayout =
    Component.getLayout ??
    ((page) => {
      return <Layout>{page}</Layout>;
    });

  return (
    <>
      <Head>
        <title>SatWorld Avatar</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
      </Head>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <PlayerEnergyInfoContextProvider>
            <EnergyConsumeContextProvider>
              <TaskProvider>
                <LogicLayout>
                  {getLayout(<Component {...pageProps} />)}
                  <Toaster />
                  <ConnectWalletModal />
                  <GlobalHook />
                  <TaskUpdateListener />
                  <PreloadFont />
                </LogicLayout>
              </TaskProvider>
            </EnergyConsumeContextProvider>
          </PlayerEnergyInfoContextProvider>
        </PersistGate>
      </Provider>
      {/* <GlobalStyle /> */}
    </>
  );
}
