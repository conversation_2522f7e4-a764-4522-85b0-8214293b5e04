import { IAssistantQuestionHistory } from '@/constant/type';

// 模拟的 IAssistantQuestionHistory 数据
const mockAssistantQuestionHistory: IAssistantQuestionHistory[] = [
  {
    _id: 'hist_001',
    prompt: '什么是区块链技术？它有哪些主要应用？',
    domain: 'blockchain',
    content:
      '区块链是一种分布式账本技术，它允许数据以去中心化的方式存储和验证。其核心特点包括：不可篡改性、透明性和去中心化。\n\n主要应用领域包括：\n1. 加密货币：如比特币、以太坊等\n2. 智能合约：自动执行的合约\n3. 供应链管理：提高透明度和可追溯性\n4. 身份验证：安全的数字身份管理\n5. 投票系统：确保选举的透明和安全',
    address: '******************************************',
    fileIds: ['file_001', 'file_002'],
    __v: 0,
    files: [
      {
        _id: 'ref_001',
        fileId: 'file_001',
        fileUrl: 'https://example.com/documents/blockchain-basics.pdf',
        title: '区块链技术基础',
        webLink: 'https://example.com/view/blockchain-basics',
        fileType: 'document',
      },
      {
        _id: 'ref_002',
        fileId: 'file_002',
        fileUrl: 'https://example.com/documents/blockchain-applications.pdf',
        title: '区块链应用案例分析',
        webLink: 'https://example.com/view/blockchain-applications',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_002',
    prompt: 'DeFi 是什么？它与传统金融有什么区别？',
    domain: 'defi',
    content:
      'DeFi（去中心化金融）是建立在区块链技术上的金融应用生态系统，旨在创建一个开放、无许可和透明的金融服务平台。\n\n与传统金融的主要区别：\n\n1. 去中心化：不依赖中央机构或中介\n2. 开放性：任何人都可以参与，无需许可\n3. 透明度：所有交易和协议代码都是公开的\n4. 自动化：通过智能合约实现自动执行\n5. 互操作性：不同协议可以无缝集成\n\nDeFi 的主要应用包括借贷平台、去中心化交易所、稳定币、衍生品等。',
    address: '******************************************',
    fileIds: ['file_003', 'file_004'],
    __v: 0,
    files: [
      {
        _id: 'ref_003',
        fileId: 'file_003',
        fileUrl: 'https://example.com/documents/defi-introduction.pdf',
        title: 'DeFi 入门指南',
        webLink: 'https://example.com/view/defi-introduction',
        fileType: 'document',
      },
      {
        _id: 'ref_004',
        fileId: 'file_004',
        fileUrl: 'https://defirate.com/',
        title: 'DeFi Rate - 去中心化金融资讯',
        webLink: 'https://defirate.com/',
        fileType: 'url',
      },
    ],
  },
  {
    _id: 'hist_003',
    prompt: 'NFT 市场的最新趋势是什么？',
    domain: 'nft',
    content:
      'NFT（非同质化代币）市场在过去一年经历了显著变化。最新趋势包括：\n\n1. 实用性 NFT 的兴起：越来越多的 NFT 项目注重实际用途，而不仅仅是数字艺术\n\n2. 游戏内 NFT：区块链游戏中的 NFT 资产交易日益活跃\n\n3. 品牌参与：主流品牌如 Nike、Adidas、Gucci 等积极进入 NFT 领域\n\n4. 社交 NFT：作为社区身份和会员资格的象征\n\n5. 音乐和媒体 NFT：艺术家通过 NFT 直接与粉丝互动\n\n6. 跨链 NFT：支持在不同区块链之间转移 NFT 的解决方案\n\n7. NFT 租赁市场：允许临时使用高价值 NFT',
    address: '******************************************',
    fileIds: ['file_005'],
    __v: 0,
    files: [
      {
        _id: 'ref_005',
        fileId: 'file_005',
        fileUrl: 'https://example.com/documents/nft-market-trends-2023.pdf',
        title: '2023 年 NFT 市场趋势报告',
        webLink: 'https://example.com/view/nft-market-trends-2023',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_004',
    prompt: '以太坊 2.0 的主要改进是什么？',
    domain: 'ethereum',
    content:
      '以太坊 2.0（现在通常称为"合并"后的以太坊）引入了几项重要改进：\n\n1. 共识机制转变：从工作量证明(PoW)转向权益证明(PoS)，大幅降低能源消耗\n\n2. 可扩展性提升：通过分片技术提高网络吞吐量\n\n3. 安全性增强：新的验证者机制提供更强的经济安全性\n\n4. 环境友好：能源消耗减少约 99.95%\n\n5. 质押奖励：ETH 持有者可以通过质押获得奖励\n\n6. 为未来升级奠定基础：为后续的扩容解决方案如 rollups 提供更好的支持',
    address: '******************************************',
    fileIds: ['file_006', 'file_007'],
    __v: 0,
    files: [
      {
        _id: 'ref_006',
        fileId: 'file_006',
        fileUrl: 'https://ethereum.org/en/eth2/',
        title: '以太坊官方网站 - 以太坊 2.0',
        webLink: 'https://ethereum.org/en/eth2/',
        fileType: 'url',
      },
      {
        _id: 'ref_007',
        fileId: 'file_007',
        fileUrl: 'https://example.com/documents/ethereum-merge-explained.pdf',
        title: '以太坊合并详解',
        webLink: 'https://example.com/view/ethereum-merge-explained',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_005',
    prompt: 'Web3 开发需要学习哪些技术？',
    domain: 'web3',
    content:
      'Web3 开发涉及多种技术和工具，主要包括：\n\n**基础知识：**\n- 区块链基本原理\n- 密码学基础\n- 分布式系统\n\n**编程语言：**\n- Solidity（以太坊智能合约开发）\n- Rust（用于 Solana、Polkadot 等）\n- JavaScript/TypeScript（前端开发）\n\n**开发框架和工具：**\n- Hardhat/Truffle（以太坊开发环境）\n- ethers.js/web3.js（与区块链交互的 JS 库）\n- IPFS（分布式存储）\n- The Graph（索引和查询区块链数据）\n\n**前端框架：**\n- React/Vue.js/Angular\n- Web3 钱包集成（MetaMask 等）\n\n**安全最佳实践：**\n- 智能合约安全模式\n- 常见漏洞防范\n- 形式化验证\n\n**测试和部署：**\n- 单元测试和集成测试\n- 测试网络部署\n- Gas 优化',
    address: '******************************************',
    fileIds: [],
    __v: 0,
    files: [],
  },
  {
    _id: 'hist_006',
    prompt: '比特币减半事件对市场有什么影响？',
    domain: 'bitcoin',
    content:
      '比特币减半事件是指矿工获得的区块奖励减少一半，大约每四年发生一次。这一事件对市场的影响通常包括：\n\n1. **供应减少**：新比特币的产出速率降低，理论上如果需求保持不变，可能导致价格上涨\n\n2. **历史表现**：过去的减半事件通常在随后的 12-18 个月内伴随着显著的价格上涨，但过去的表现不能保证未来的结果\n\n3. **矿工影响**：一些效率较低的矿工可能因为收入减少而退出市场，导致算力重新分配\n\n4. **市场心理**：减半事件通常会引起媒体关注和市场讨论，可能影响投资者情绪\n\n5. **长期通缩效应**：减半机制强化了比特币的稀缺性，支持其作为"数字黄金"的叙事\n\n需要注意的是，市场对减半的反应可能已经部分被提前计入价格，且其影响会受到更广泛的经济和监管环境的影响。',
    address: '******************************************',
    fileIds: ['file_008'],
    __v: 0,
    files: [
      {
        _id: 'ref_008',
        fileId: 'file_008',
        fileUrl: 'https://example.com/documents/bitcoin-halving-analysis.pdf',
        title: '比特币减半事件市场影响分析',
        webLink: 'https://example.com/view/bitcoin-halving-analysis',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_007',
    prompt: '如何评估一个加密货币项目的价值？',
    domain: 'crypto',
    content:
      '评估加密货币项目价值需要考虑多个因素：\n\n**基础分析因素：**\n\n1. **团队背景**：开发团队的经验、专业知识和过往记录\n\n2. **技术创新**：项目的技术优势、独特性和解决的实际问题\n\n3. **使用案例**：项目解决的实际问题和潜在市场规模\n\n4. **代币经济学**：代币供应机制、分配、通胀率和实用性\n\n5. **网络活动**：活跃用户数、交易量、开发活动等指标\n\n6. **竞争格局**：与竞争项目的比较和市场定位\n\n7. **社区支持**：社区规模、参与度和增长趋势\n\n8. **合作伙伴关系**：与行业参与者的战略合作\n\n**风险评估因素：**\n\n1. **监管风险**：项目面临的监管不确定性\n\n2. **安全风险**：代码审计、漏洞历史和安全措施\n\n3. **中心化程度**：决策和代币分布的去中心化程度\n\n4. **市场风险**：波动性、流动性和市场操纵风险\n\n综合这些因素，可以更全面地评估一个加密货币项目的长期价值和投资潜力。',
    address: '******************************************',
    fileIds: ['file_009', 'file_010'],
    __v: 0,
    files: [
      {
        _id: 'ref_009',
        fileId: 'file_009',
        fileUrl: 'https://example.com/documents/crypto-valuation-methods.pdf',
        title: '加密货币项目估值方法',
        webLink: 'https://example.com/view/crypto-valuation-methods',
        fileType: 'document',
      },
      {
        _id: 'ref_010',
        fileId: 'file_010',
        fileUrl: 'https://messari.io/',
        title: 'Messari - 加密货币研究平台',
        webLink: 'https://messari.io/',
        fileType: 'url',
      },
    ],
  },
  {
    _id: 'hist_008',
    prompt: '智能合约审计的最佳实践是什么？',
    domain: 'smart-contracts',
    content:
      '智能合约审计的最佳实践包括：\n\n**审计准备：**\n- 完整的技术文档和规范\n- 清晰的代码注释\n- 单元测试覆盖率高\n- 使用标准库和模式\n\n**审计过程：**\n\n1. **自动化分析**\n   - 使用静态分析工具（如 Slither、Mythril）\n   - 形式化验证工具（如 Certora、SMTChecker）\n   - 代码覆盖率分析\n\n2. **手动代码审查**\n   - 业务逻辑验证\n   - 安全模式实现\n   - 权限控制检查\n   - 算术操作安全性\n   - 重入攻击防护\n   - Gas 优化\n\n3. **动态测试**\n   - 模糊测试（Fuzzing）\n   - 模拟攻击场景\n   - 边界条件测试\n   - 压力测试\n\n**部署后监控：**\n- 漏洞赏金计划\n- 持续监控异常交易\n- 升级和应急响应计划\n\n**常见审计机构：**\n- OpenZeppelin\n- Trail of Bits\n- ConsenSys Diligence\n- ChainSecurity\n- CertiK\n\n定期审计和遵循这些最佳实践可以显著降低智能合约的安全风险。',
    address: '******************************************',
    fileIds: ['file_011'],
    __v: 0,
    files: [
      {
        _id: 'ref_011',
        fileId: 'file_011',
        fileUrl: 'https://example.com/documents/smart-contract-security-best-practices.pdf',
        title: '智能合约安全最佳实践指南',
        webLink: 'https://example.com/view/smart-contract-security-best-practices',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_009',
    prompt: 'Layer 2 扩容解决方案有哪些类型？',
    domain: 'scaling',
    content:
      'Layer 2 扩容解决方案是构建在现有区块链（如以太坊）之上的协议，旨在提高交易吞吐量和降低费用。主要类型包括：\n\n1. **Rollups**：将多笔交易批量处理并提交一个证明到主链\n   - **Optimistic Rollups**：假设交易有效，但允许在欺诈证明期间进行挑战（如 Optimism、Arbitrum）\n   - **ZK-Rollups**：使用零知识证明来验证交易批次的有效性（如 zkSync、StarkNet）\n\n2. **状态通道**：参与者之间创建私有通道进行多次交易，只有最终结果提交到主链（如 Lightning Network、Raiden）\n\n3. **侧链**：独立的区块链，与主链有桥接机制（如 Polygon PoS）\n\n4. **Plasma**：创建子链层次结构，每个子链都有自己的区块验证机制（如 OMG Network）\n\n5. **Validium**：类似于 ZK-Rollups，但数据存储在链下（如 DeversiFi）\n\n各解决方案在安全性、去中心化程度、吞吐量和复杂性方面有所不同。目前，Rollups（特别是 Optimistic 和 ZK-Rollups）被认为是最有前景的 Layer 2 解决方案。',
    address: '******************************************',
    fileIds: ['file_012', 'file_013'],
    __v: 0,
    files: [
      {
        _id: 'ref_012',
        fileId: 'file_012',
        fileUrl: 'https://ethereum.org/en/layer-2/',
        title: '以太坊官方网站 - Layer 2 扩容',
        webLink: 'https://ethereum.org/en/layer-2/',
        fileType: 'url',
      },
      {
        _id: 'ref_013',
        fileId: 'file_013',
        fileUrl: 'https://example.com/documents/layer2-comparison.pdf',
        title: 'Layer 2 扩容解决方案比较分析',
        webLink: 'https://example.com/view/layer2-comparison',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_010',
    prompt: '区块链治理模型有哪些？',
    domain: 'governance',
    content:
      '区块链治理模型是决定协议如何演进和管理的机制。主要模型包括：\n\n1. **链下治理**：\n   - **核心开发者治理**：由核心开发团队主导决策（如比特币）\n   - **基金会主导**：由非营利基金会协调（如早期以太坊）\n   - **多方利益相关者**：开发者、矿工、用户等多方协商（如许多早期项目）\n\n2. **链上治理**：\n   - **代币投票**：代币持有者按持有量投票决策（如 Compound、Uniswap）\n   - **二次投票**：投票权重与代币数量的平方根成正比（如 Gitcoin）\n   - **委托投票**：代币持有者可将投票权委托给代表（如 Tezos）\n\n3. **混合模型**：\n   - **提案+投票**：社区提出提案，代币持有者投票（如 MakerDAO）\n   - **多层治理**：不同类型决策采用不同治理机制（如 Polkadot）\n\n4. **创新模型**：\n   - **预测市场治理**：使用预测市场机制评估提案（如 Futarchy）\n   - **信誉系统**：基于过往贡献分配治理权重（如一些 DAO）\n   - **分叉选择**：通过支持不同分叉表达偏好（如 Bitcoin/Bitcoin Cash）\n\n每种模型都有其优缺点，涉及去中心化程度、决策效率、参与门槛和抗审查能力等方面的权衡。成功的治理模型通常能够平衡这些因素，并随着项目发展而演进。',
    address: '******************************************',
    fileIds: [],
    __v: 0,
    files: [],
  },
  {
    _id: 'hist_011',
    prompt: '隐私币如门罗币(Monero)和零币(Zcash)有什么区别？',
    domain: 'privacy-coins',
    content:
      '门罗币(Monero)和零币(Zcash)都是注重隐私的加密货币，但它们采用了不同的技术路线：\n\n**门罗币(Monero)特点：**\n\n1. **强制隐私**：所有交易默认都是私密的，没有可选性\n2. **环签名技术**：使用环签名(Ring Signatures)混淆发送方\n3. **隐匿地址**：通过一次性地址(Stealth Addresses)保护接收方隐私\n4. **RingCT**：隐藏交易金额\n5. **Kovri项目**：通过I2P网络路由流量进一步增强隐私\n\n**零币(Zcash)特点：**\n\n1. **可选隐私**：提供透明交易(t-addr)和隐私交易(z-addr)两种选择\n2. **zk-SNARKs**：使用零知识证明技术保护交易隐私\n3. **可选披露**：允许用户选择性地披露交易详情给特定方(如审计)\n4. **信任设置**：初始参数生成需要可信设置(trusted setup)\n5. **更高兼容性**：透明地址使其与交易所和服务更兼容\n\n**主要区别：**\n\n1. **隐私默认设置**：Monero所有交易默认隐私；Zcash隐私是可选的\n2. **技术方案**：Monero使用环签名和RingCT；Zcash使用零知识证明\n3. **可验证性**：Zcash理论上提供更强的数学隐私保证\n4. **使用率**：Monero的隐私保护使用率为100%；Zcash的屏蔽交易使用率较低\n5. **资源需求**：Zcash的隐私交易计算要求更高',
    address: '******************************************',
    fileIds: ['file_014', 'file_015'],
    __v: 0,
    files: [
      {
        _id: 'ref_014',
        fileId: 'file_014',
        fileUrl: 'https://example.com/documents/privacy-coins-comparison.pdf',
        title: '隐私币技术对比研究',
        webLink: 'https://example.com/view/privacy-coins-comparison',
        fileType: 'document',
      },
      {
        _id: 'ref_015',
        fileId: 'file_015',
        fileUrl: 'https://www.getmonero.org/resources/research-lab/',
        title: 'Monero研究实验室',
        webLink: 'https://www.getmonero.org/resources/research-lab/',
        fileType: 'url',
      },
    ],
  },
  {
    _id: 'hist_012',
    prompt: '稳定币的不同机制及其优缺点是什么？',
    domain: 'stablecoins',
    content:
      '稳定币是设计用来保持价格稳定的加密货币，通常锚定法定货币如美元。根据其稳定机制，可分为以下几类：\n\n**1. 法币抵押型**\n- **代表项目**：USDT、USDC、BUSD\n- **机制**：由公司持有等值法币储备作为抵押\n- **优点**：简单直接、容易理解、价格稳定性高\n- **缺点**：中心化、需信任发行机构、需第三方审计、受监管风险影响\n\n**2. 加密资产超额抵押型**\n- **代表项目**：DAI、sUSD\n- **机制**：用户存入超额的加密资产作为抵押来铸造稳定币\n- **优点**：去中心化程度高、透明度高、无需信任特定实体\n- **缺点**：资本效率低(需超额抵押)、受制于抵押品价格波动、系统复杂\n\n**3. 算法型**\n- **代表项目**：曾经的UST、AMPL、FEI\n- **机制**：通过算法动态调整供应量来维持价格稳定\n- **优点**：无需抵押物、资本效率高、完全去中心化\n- **缺点**：历史上失败率高、机制复杂、依赖持续需求\n\n**4. 混合型**\n- **代表项目**：FRAX、USN\n- **机制**：部分抵押加部分算法调节\n- **优点**：综合多种机制的优势、资本效率较高\n- **缺点**：系统复杂度增加、参数调整难度大\n\n**5. 商品抵押型**\n- **代表项目**：Paxos Gold (PAXG)、Tether Gold (XAUT)\n- **机制**：以实物商品(如黄金)作为抵押\n- **优点**：价值与实物商品挂钩、通胀对冲属性\n- **缺点**：仍依赖中心化托管、审计挑战\n\n**选择考虑因素**：\n- 价格稳定性需求\n- 去中心化重要性\n- 资本效率要求\n- 风险承受能力\n- 监管合规需求\n\n历史表明，法币抵押型和加密资产超额抵押型稳定币在维持价格稳定性方面表现较好，而纯算法型稳定币面临较大挑战。',
    address: '******************************************',
    fileIds: ['file_016'],
    __v: 0,
    files: [
      {
        _id: 'ref_016',
        fileId: 'file_016',
        fileUrl: 'https://example.com/documents/stablecoin-mechanisms.pdf',
        title: '稳定币机制全面分析',
        webLink: 'https://example.com/view/stablecoin-mechanisms',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_013',
    prompt: '跨链技术有哪些主要方案？各有什么优缺点？',
    domain: 'cross-chain',
    content:
      '跨链技术致力于解决不同区块链网络之间的互操作问题，主要方案及其优缺点如下：\n\n**1. 侧链/中继链**\n- **代表项目**：Polkadot、Cosmos\n- **工作原理**：通过专门的中继链连接多个区块链\n- **优势**：可扩展性好、专为互操作性设计、通常有共享安全性\n- **劣势**：引入新的中间层、可能存在中心化风险、需要专用代币\n\n**2. 哈希时间锁定合约(HTLC)**\n- **代表项目**：闪电网络、原子交换\n- **工作原理**：通过哈希锁和时间锁创建条件交易\n- **优势**：无需信任、适用于简单资产交换、成熟技术\n- **劣势**：功能有限、参与方必须在线、不适合复杂交互\n\n**3. 公证人机制**\n- **代表项目**：Binance Bridge、WBTC\n- **工作原理**：通过受信任的中介处理跨链资产转移\n- **优势**：实现简单、用户体验好、可扩展性高\n- **劣势**：中心化、需要信任第三方、单点故障风险\n\n**4. 桥接协议**\n- **代表项目**：Multichain(原AnySwap)、Hop Protocol\n- **工作原理**：专用桥接合约和中继器在链间传递信息\n- **优势**：用户友好、支持多种资产、适应性强\n- **劣势**：安全审计复杂、历史上曾有重大漏洞\n\n**5. 轻客户端验证**\n- **代表项目**：BTC Relay、Rainbow Bridge\n- **工作原理**：在目标链上实现源链的轻客户端验证\n- **优势**：安全性高、去中心化程度高、无需信任\n- **劣势**：实现复杂、资源需求较高、扩展性有限\n\n**6. 消息传递协议**\n- **代表项目**：LayerZero、Axelar、Chainlink CCIP\n- **工作原理**：通过安全的消息传递层在链间传输数据\n- **优势**：功能全面、支持复杂交互、模块化设计\n- **劣势**：相对较新、技术复杂度高\n\n**7. 零知识证明**\n- **代表项目**：ZK-Bridge、zkSync\n- **工作原理**：使用零知识证明验证链上状态而无需完整数据\n- **优势**：高安全性、数据效率高、隐私保护\n- **劣势**：计算要求高、技术实现复杂\n\n**选择考虑因素**：\n- 安全需求\n- 去中心化偏好\n- 速度和成本要求\n- 支持资产和功能\n- 技术成熟度\n\n不同场景可能需要不同的跨链解决方案，理想的选择取决于特定用例的需求平衡。',
    address: '******************************************',
    fileIds: ['file_017', 'file_018'],
    __v: 0,
    files: [
      {
        _id: 'ref_017',
        fileId: 'file_017',
        fileUrl: 'https://example.com/documents/cross-chain-comparison.pdf',
        title: '跨链技术方案比较研究',
        webLink: 'https://example.com/view/cross-chain-comparison',
        fileType: 'document',
      },
      {
        _id: 'ref_018',
        fileId: 'file_018',
        fileUrl: 'https://li.fi/knowledge-hub/bridges-explained/',
        title: '桥接协议解析 - Li.Fi知识库',
        webLink: 'https://li.fi/knowledge-hub/bridges-explained/',
        fileType: 'url',
      },
    ],
  },
  {
    _id: 'hist_014',
    prompt: 'DAO（去中心化自治组织）的治理模式和挑战有哪些？',
    domain: 'dao',
    content:
      'DAO（去中心化自治组织）是基于区块链的组织形式，通过代码而非中心化管理层来治理。\n\n**常见DAO治理模式：**\n\n1. **代币投票**\n   - **机制**：代币持有者按持有量投票决策\n   - **例子**：Uniswap、Compound、Aave\n   - **优点**：简单直接、容易实现\n   - **缺点**：富者更富效应、鲸鱼操控风险\n\n2. **二次投票**\n   - **机制**：投票权重与代币数量的平方根成正比\n   - **例子**：Gitcoin、Optimism\n   - **优点**：减轻财富集中问题、更加平等\n   - **缺点**：身份确认困难(Sybil攻击)、实施复杂\n\n3. **声誉系统**\n   - **机制**：基于贡献和参与分配非转让投票权\n   - **例子**：DAOhaus、Aragon\n   - **优点**：奖励积极参与者、减少财务投机\n   - **缺点**：声誉评估主观、系统设计复杂\n\n4. **委托投票**\n   - **机制**：代币持有者可将投票权委托给代表\n   - **例子**：Compound、The Graph\n   - **优点**：提高专业性、增加参与率\n   - **缺点**：可能形成新的权力中心\n\n5. **多签钱包**\n   - **机制**：需要多方签名才能执行决策\n   - **例子**：许多DAO的财库管理\n   - **优点**：安全性高、防止单点决策\n   - **缺点**：效率较低、扩展性有限\n\n**DAO面临的主要挑战：**\n\n1. **治理参与度低**：典型的DAO投票率往往不到20%\n\n2. **专业性与决策质量**：非专业成员可能难以评估复杂提案\n\n3. **法律定位不明确**：大多数司法管辖区尚未明确DAO的法律地位\n\n4. **代码安全性**：智能合约漏洞可能导致资金损失(如The DAO事件)\n\n5. **治理攻击**：闪电贷攻击、贿赂、投票市场等\n\n6. **效率与响应速度**：去中心化决策通常较慢，难以应对紧急情况\n\n7. **人为因素**：社会层面的协调无法完全编码，仍依赖社区文化\n\n8. **长期激励对齐**：难以设计既有短期参与激励又保障长期发展的机制\n\n**DAO创新方向：**\n\n1. **混合治理**：结合链上和链下治理机制\n\n2. **模块化DAO**：专门的子DAO负责特定领域决策\n\n3. **渐进式去中心化**：初期采用较中心化模式，逐步过渡到更去中心化\n\n4. **DAO法律包装**：如DAO LLC、基金会等法律结构结合\n\n随着技术和实践的发展，DAO治理将继续演化，寻找平衡去中心化、效率和责任的最佳方案。',
    address: '******************************************',
    fileIds: ['file_019'],
    __v: 0,
    files: [
      {
        _id: 'ref_019',
        fileId: 'file_019',
        fileUrl: 'https://example.com/documents/dao-governance-models.pdf',
        title: 'DAO治理模式研究与挑战分析',
        webLink: 'https://example.com/view/dao-governance-models',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_015',
    prompt: '全球主要国家对加密货币的监管态度如何？',
    domain: 'regulation',
    content:
      '全球主要国家对加密货币的监管态度各不相同，从积极拥抱到严格禁止不等：\n\n**积极拥抱型国家/地区：**\n\n1. **瑞士**\n   - 建立了"加密谷"(Crypto Valley)生态系统\n   - 清晰的监管框架，区分不同类型代币\n   - FINMA监管机构提供明确指导\n   - 加密相关银行服务可获得\n\n2. **新加坡**\n   - 支持区块链创新同时强调消费者保护\n   - 支付服务法(PSA)纳入数字支付代币服务\n   - 金融管理局(MAS)积极监管但不扼杀创新\n   - 明确的税务处理指南\n\n3. **阿联酋(迪拜)**\n   - 建立专门的虚拟资产监管机构(VARA)\n   - 制定全面的加密法规框架\n   - 吸引全球加密公司设立总部\n   - 税收优惠政策\n\n**平衡监管型国家/地区：**\n\n1. **美国**\n   - 多头监管：SEC、CFTC、FinCEN等机构分别监管\n   - 各州态度各异(纽约BitLicense vs 怀俄明友好政策)\n   - SEC倾向将多数代币视为证券\n   - 稳定币和加密ETF逐步获得监管明确性\n\n2. **欧盟**\n   - 制定统一的加密资产市场监管法规(MiCA)\n   - 对稳定币有严格的资本和监督要求\n   - 实施旅行规则(Travel Rule)追踪交易\n   - 注重AML/KYC合规和消费者保护\n\n3. **英国**\n   - 加密资产纳入金融行为监管局(FCA)监管\n   - 要求加密企业注册反洗钱监管\n   - 稳定币监管框架推进中\n   - 探索CBDC可能性(Britcoin)\n\n4. **日本**\n   - 全球首个认可比特币为法定支付方式的主要经济体\n   - 加密交易所需获得金融服务厅(FSA)许可\n   - 自律组织JVCEA协助制定行业标准\n   - STO(证券型代币)法规框架完善\n\n**限制监管型国家：**\n\n1. **中国**\n   - 禁止加密货币交易和挖矿活动\n   - 但积极推进CBDC(数字人民币)研发和应用\n   - 区分区块链技术(鼓励)和加密货币(限制)\n   - 香港地区采取更开放态度，推出加密许可框架\n\n2. **印度**\n   - 征收30%加密收益税\n   - 监管立场时有变化\n   - 中央银行对私人加密货币持批评态度\n   - 推动CBDC发展\n\n3. **俄罗斯**\n   - 对加密挖矿和投资采取限制性监管\n   - 禁止使用加密货币作为支付手段\n   - 探索数字卢布\n   - 考虑在国际贸易中使用加密货币规避制裁\n\n**监管趋势：**\n\n1. 全球监管框架逐渐明确化和协调化\n2. 稳定币成为监管重点关注领域\n3. 反洗钱和KYC要求普遍强化\n4. DeFi和NFT监管逐渐成形\n5. CBDC研发全球普遍推进\n\n加密行业参与者需密切关注不同司法管辖区的监管发展，并调整合规策略。监管环境不断演变，最新法规可能与本概述有所不同。',
    address: '******************************************',
    fileIds: ['file_020', 'file_021'],
    __v: 0,
    files: [
      {
        _id: 'ref_020',
        fileId: 'file_020',
        fileUrl: 'https://example.com/documents/global-crypto-regulation.pdf',
        title: '全球加密货币监管格局分析',
        webLink: 'https://example.com/view/global-crypto-regulation',
        fileType: 'document',
      },
      {
        _id: 'ref_021',
        fileId: 'file_021',
        fileUrl:
          'https://www.loc.gov/item/global-legal-monitor/2022-11-10/cryptocurrency-regulations-around-the-world/',
        title: '美国国会图书馆 - 全球加密货币监管',
        webLink:
          'https://www.loc.gov/item/global-legal-monitor/2022-11-10/cryptocurrency-regulations-around-the-world/',
        fileType: 'url',
      },
    ],
  },
  {
    _id: 'hist_016',
    prompt: '量子计算对区块链安全的威胁有多大？有哪些应对方案？',
    domain: 'quantum-security',
    content:
      '量子计算对区块链安全确实构成了潜在威胁，但威胁程度和时间表存在不确定性。\n\n**主要威胁：**\n\n1. **公钥密码学破解**\n   - 量子计算机通过Shor算法可破解RSA和椭圆曲线密码学(ECC)\n   - 比特币、以太坊等使用的ECDSA签名算法将变得不安全\n   - 影响：可能导致私钥被提取，资金被盗取\n\n2. **哈希函数弱化**\n   - 量子计算机通过Grover算法可加速哈希函数的暴力破解\n   - 将SHA-256的安全性从256位降低到约128位\n   - 影响：降低工作量证明(PoW)挖矿难度，可能影响区块链不可变性\n\n**风险时间表：**\n\n专家普遍认为，能够破解当前密码学系统的量子计算机(逻辑量子比特>4000)可能需要5-20年才能出现。IBM和Google等公司目前的量子计算机仍远未达到破解密码学所需的规模和稳定性。\n\n**量子安全区块链方案：**\n\n1. **量子抗性密码学**\n   - **格密码(Lattice-based)**：如NTRU、LWE算法\n   - **哈希基础签名**：Lamport签名、WOTS等\n   - **多元二次方程**：Rainbow签名\n   - **超奇异椭圆曲线**：SIDH协议\n\n2. **已进行量子抵抗升级的区块链项目**\n   - **QRL (Quantum Resistant Ledger)**：从零开始构建的量子抗性区块链\n   - **IOTA**：计划从WOTS升级到Winternitz-XMSS\n   - **Ethereum**：ETH2.0考虑量子抗性，可能在未来升级\n   - **Algorand**：采用哈希基反签名方案的区块链\n\n3. **混合解决方案**\n   - **双重签名验证**：同时使用传统和量子抗性签名\n   - **渐进式升级**：逐步引入量子安全功能\n\n4. **区块链特有保护措施**\n   - **地址重用限制**：不重复使用公钥可减轻风险\n   - **提前转移资金**：将资金从可能受影响的地址转移到新量子安全地址\n   - **对抗量子挖矿的共识机制调整**：调整PoW难度或转向其他机制\n\n**应对建议：**\n\n1. **行业层面**\n   - 积极研发和标准化量子抗性算法\n   - 主要区块链项目应制定量子抗性过渡计划\n   - 建立量子计算监测机制，跟踪技术进展\n\n2. **用户层面**\n   - 关注所使用区块链的量子抵抗计划\n   - 考虑将长期资产储存在已实施量子安全措施的方案中\n   - 定期更新钱包和密钥管理软件\n\n虽然量子计算对区块链的威胁是真实的，但这不是一个需要立即恐慌的问题。区块链社区有足够的时间实施解决方案，而量子抗性密码学研究也在稳步推进。未来的区块链生态系统很可能会整合多层量子安全措施。',
    address: '******************************************',
    fileIds: ['file_022'],
    __v: 0,
    files: [
      {
        _id: 'ref_022',
        fileId: 'file_022',
        fileUrl: 'https://example.com/documents/quantum-threats-blockchain.pdf',
        title: '量子计算对区块链安全的影响与对策',
        webLink: 'https://example.com/view/quantum-threats-blockchain',
        fileType: 'document',
      },
    ],
  },
  {
    _id: 'hist_017',
    prompt: '比特币闪电网络的工作原理是什么？它解决了什么问题？',
    domain: 'lightning-network',
    content:
      '**比特币闪电网络(Lightning Network)**是一个建立在比特币区块链之上的第二层扩容解决方案，旨在实现快速、低成本的小额支付。\n\n**闪电网络解决的核心问题：**\n\n1. **可扩展性限制**：比特币主网每秒仅能处理约7笔交易\n2. **高交易费用**：网络拥堵时费用可能高达数十美元\n3. **确认延迟**：交易最终确认可能需要10-60分钟\n4. **微支付不可行**：费用使小额交易在经济上不可行\n\n**工作原理：**\n\n1. **支付通道技术**\n   - 两方在主链上创建多签名交易，锁定资金\n   - 之后可以在不提交到主链的情况下进行无限次链下交易\n   - 仅在通道开启和关闭时需要主链交互\n   - 使用智能合约确保资金安全，防止欺诈\n\n2. **通道网络和路由**\n   - 用户无需与每个交易对手建立直接通道\n   - 可通过已存在的通道网络路由支付\n   - 例如：A可通过B和C的连接向D付款(A→B→C→D)\n   - 洋葱路由技术确保中间节点无法了解完整路径\n\n3. **哈希时间锁定合约(HTLC)**\n   - 使用哈希锁确保资金只能被预定收款人提取\n   - 时间锁机制防止中间节点持有资金不转发\n   - 原子性支付：要么全部成功，要么全部失败\n\n4. **通道状态管理**\n   - 每次交易更新通道的余额状态\n   - 双方签名确认新状态，旧状态作废\n   - 如一方尝试广播旧状态，另一方可提交惩罚交易\n\n5. **通道关闭机制**\n   - 协作关闭：双方同意关闭并分配资金\n   - 强制关闭：单方面关闭，触发时间锁和争议解决\n\n**闪电网络的主要优势：**\n\n1. **即时交易**：毫秒级确认，无需等待区块\n2. **极低费用**：通常只有几聪(satoshi)或更少\n3. **扩展性**：理论上可支持数百万TPS\n4. **微支付可行性**：支持极小额支付\n5. **隐私增强**：链下交易细节不在主链公开\n\n**当前挑战与限制：**\n\n1. **流动性管理**：通道需要预先锁定资金\n2. **通道容量限制**：单个通道能处理的最大金额有限\n3. **路由复杂性**：大规模网络中找到最佳路径具有挑战\n4. **在线要求**：接收方需要在线签收付款\n5. **用户体验**：相比主链交易更复杂\n\n**闪电网络生态系统发展：**\n\n1. **支付应用**：如Strike、Wallet of Satoshi、Phoenix\n2. **闪电网络服务提供商**：如ACINQ、Lightning Labs\n3. **商家采用**：越来越多商家开始接受闪电网络支付\n4. **创新应用**：流式支付、微型内容货币化、游戏内交易\n\n闪电网络代表了比特币扩容的重要方向，尽管仍在发展中，但已显示出将比特币转变为可行的日常支付系统的潜力。',
    address: '******************************************',
    fileIds: ['file_023', 'file_024'],
    __v: 0,
    files: [
      {
        _id: 'ref_023',
        fileId: 'file_023',
        fileUrl: 'https://example.com/documents/lightning-network-explained.pdf',
        title: '比特币闪电网络技术解析',
        webLink: 'https://example.com/view/lightning-network-explained',
        fileType: 'document',
      },
      {
        _id: 'ref_024',
        fileId: 'file_024',
        fileUrl: 'https://lightning.network/lightning-network-paper.pdf',
        title: '闪电网络白皮书',
        webLink: 'https://lightning.network/lightning-network-paper.pdf',
        fileType: 'document',
      },
    ],
  },
];

export default mockAssistantQuestionHistory;
