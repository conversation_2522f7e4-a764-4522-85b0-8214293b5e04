<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>Three.js GLTF 模型查看器</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Microsoft YaHei', sans-serif;
        overflow: hidden;
        background-color: #f0f0f0;
      }

      .container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      .header {
        background-color: #333;
        color: white;
        padding: 15px;
        text-align: center;
      }

      .controls {
        padding: 10px;
        background-color: #ddd;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
      }

      .file-input {
        display: none;
      }

      .file-label {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .file-label:hover {
        background-color: #45a049;
      }

      .model-info {
        margin-left: 20px;
        font-size: 14px;
      }

      #scene-container {
        flex: 1;
        overflow: hidden;
        position: relative;
      }

      .animations-panel {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 15px;
        border-radius: 5px;
        width: 350px; /* 增加固定宽度 */
        max-width: 40%; /* 在小屏幕上限制最大宽度 */
        max-height: 80%;
        overflow-y: auto;
        display: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 添加阴影增强视觉效果 */
      }

      .animations-panel h3 {
        margin-top: 0;
        margin-bottom: 10px;
        text-align: center;
      }

      .animation-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        transition: background-color 0.3s;
        flex-wrap: wrap; /* 允许内容换行 */
        gap: 5px; /* 元素之间的间距 */
      }

      .animation-item.active {
        background-color: rgba(76, 175, 80, 0.3);
        border-left: 3px solid #4caf50;
      }

      .animation-name {
        flex: 1;
        min-width: 150px; /* 最小宽度确保名称有足够空间 */
        white-space: normal; /* 允许文本换行 */
        word-break: break-word; /* 长单词可以换行 */
        margin-right: 10px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 3px;
        transition: background-color 0.2s;
        line-height: 1.3; /* 增加行高 */
      }

      .animation-name:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .animation-name:hover::after {
        content: '编辑';
        font-size: 10px;
        margin-left: 5px;
        color: #aaa;
      }

      .animation-name-edit {
        flex: 1;
        min-width: 150px; /* 与名称元素保持一致 */
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 3px;
        color: white;
        padding: 4px 8px; /* 与名称元素保持一致 */
        margin-right: 10px;
        font-family: inherit;
        font-size: inherit;
        line-height: 1.3; /* 与名称元素保持一致 */
      }

      .animation-controls {
        display: flex;
        gap: 5px;
        flex-wrap: wrap; /* 允许按钮换行 */
        justify-content: flex-end; /* 按钮靠右对齐 */
        min-width: 120px; /* 确保按钮区域有足够空间 */
      }

      .btn {
        cursor: pointer;
        padding: 3px 8px;
        border-radius: 3px;
        border: none;
        font-size: 12px;
      }

      .btn-play {
        background-color: #4caf50;
        color: white;
      }

      .btn-stop {
        background-color: #f44336;
        color: white;
      }

      .btn-delete {
        background-color: #9e9e9e;
        color: white;
      }

      .no-animations {
        font-style: italic;
        text-align: center;
        color: #ccc;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 20px;
        border-radius: 5px;
        display: none;
      }

      .instructions {
        position: absolute;
        bottom: 10px;
        left: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        padding: 10px;
        border-radius: 5px;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Three.js GLTF 模型查看器</h1>
      </div>
      <div class="controls">
        <input accept=".gltf,.glb" class="file-input" id="file-input" type="file" />
        <label class="file-label" for="file-input">选择 GLTF/GLB 文件</label>
        <button class="file-label" id="export-btn" style="display: none; background-color: #2196f3">
          导出为 GLB
        </button>
        <div class="model-info" id="model-info">未加载模型</div>
      </div>
      <div id="scene-container">
        <div class="animations-panel" id="animations-panel">
          <h3>动作列表</h3>
          <div id="animations-list">
            <div class="no-animations">没有可用的动作</div>
          </div>
        </div>
      </div>
      <div class="loading" id="loading">正在加载模型，请稍候...</div>
      <div class="instructions">
        <p>操作说明：</p>
        <p>- 左键拖动：旋转模型</p>
        <p>- 右键拖动：平移模型</p>
        <p>- 滚轮：缩放模型</p>
      </div>
    </div>

    <!-- 引入 Three.js 库 -->
    <script type="importmap">
      {
        "imports": {
          "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
          "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
      }
    </script>
    <script type="module">
      import * as THREE from 'three';
      import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
      import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
      import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
      import { GLTFExporter } from 'three/addons/exporters/GLTFExporter.js';

      // 全局变量
      let scene, camera, renderer, controls;
      let currentModel = null;
      let originalGltf = null; // 原始的GLTF对象
      let currentFileName = ''; // 当前文件名
      let mixer = null; // 动画混合器
      let animations = []; // 动画列表
      let activeAction = null; // 当前活动的动作
      let activeAnimationIndex = -1; // 当前活动的动画索引
      let clock = new THREE.Clock(); // 时钟对象，用于动画
      let hasModelChanged = false; // 模型是否被编辑过
      let currentEditingNameElement = null; // 当前正在编辑的名称元素

      const container = document.getElementById('scene-container');
      const loadingElement = document.getElementById('loading');
      const modelInfoElement = document.getElementById('model-info');
      const animationsPanel = document.getElementById('animations-panel');
      const animationsList = document.getElementById('animations-list');

      // 初始化场景
      function init() {
        // 创建场景
        scene = new THREE.Scene();
        scene.background = new THREE.Color(0xf0f0f0);

        // 创建相机
        const aspect = container.clientWidth / container.clientHeight;
        camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
        camera.position.set(0, 5, 10);

        // 创建渲染器
        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(container.clientWidth, container.clientHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.outputColorSpace = THREE.SRGBColorSpace;
        container.appendChild(renderer.domElement);

        // 添加轨道控制器
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        // 添加灯光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // 添加坐标轴辅助
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);

        // 添加网格地面
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);

        // 监听窗口大小变化
        window.addEventListener('resize', onWindowResize);

        // 开始动画循环
        animate();
      }

      // 窗口大小变化时调整渲染器和相机
      function onWindowResize() {
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(container.clientWidth, container.clientHeight);
      }

      // 动画循环
      function animate() {
        requestAnimationFrame(animate);

        // 更新控制器
        controls.update();

        // 更新动画混合器
        if (mixer) {
          const delta = clock.getDelta();
          mixer.update(delta);
        }

        // 渲染场景
        renderer.render(scene, camera);
      }

      // 加载 GLTF 模型
      function loadGLTFModel(file) {
        // 显示加载提示
        loadingElement.style.display = 'block';
        modelInfoElement.textContent = '正在加载: ' + file.name;

        // 保存当前文件名
        currentFileName = file.name;

        // 重置模型状态
        hasModelChanged = false;
        document.getElementById('export-btn').style.display = 'none';

        // 移除之前的模型
        if (currentModel) {
          scene.remove(currentModel);
          currentModel = null;
          originalGltf = null;
        }

        // 创建文件URL
        const fileURL = URL.createObjectURL(file);

        // 设置GLTF加载器
        const loader = new GLTFLoader();

        // 可选：添加Draco解码器支持压缩模型
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('https://unpkg.com/three@0.160.0/examples/jsm/libs/draco/');
        loader.setDRACOLoader(dracoLoader);

        // 加载模型
        loader.load(
          fileURL,
          function (gltf) {
            // 加载成功
            // 保存原始 GLTF 对象
            originalGltf = gltf;
            currentModel = gltf.scene;

            // // 计算模型边界框并居中
            const box = new THREE.Box3().setFromObject(currentModel);
            const size = box.getSize(new THREE.Vector3()).length();
            const center = new THREE.Vector3();

            // 调整相机位置
            camera.position.copy(center);
            camera.position.x += size / 2.0;
            camera.position.y += size / 5.0;
            camera.position.z += size / 2.0;
            camera.lookAt(center);

            // 调整控制器目标
            controls.target.copy(center);

            // 添加模型到场景
            scene.add(currentModel);

            // 更新模型信息
            const vertices = countVertices(currentModel);
            modelInfoElement.textContent = `${file.name} | 顶点数: ${vertices} | 大小: ${formatFileSize(file.size)}`;

            // 处理动画
            handleAnimations(gltf);

            // 隐藏加载提示
            loadingElement.style.display = 'none';

            // 释放文件URL
            URL.revokeObjectURL(fileURL);
          },
          function (xhr) {
            // 加载进度
            const percent = Math.floor((xhr.loaded / xhr.total) * 100);
            modelInfoElement.textContent = `正在加载: ${file.name} (${percent}%)`;
          },
          function (error) {
            // 加载错误
            console.error('加载模型时出错:', error);
            modelInfoElement.textContent = '加载失败: ' + file.name;
            loadingElement.style.display = 'none';
            URL.revokeObjectURL(fileURL);
          }
        );
      }

      // 计算模型顶点数
      function countVertices(object) {
        let vertexCount = 0;
        object.traverse(function (child) {
          if (child.isMesh) {
            const geometry = child.geometry;
            if (geometry.attributes.position) {
              vertexCount += geometry.attributes.position.count;
            }
          }
        });
        return vertexCount;
      }

      // 格式化文件大小
      function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
        else return (bytes / 1048576).toFixed(2) + ' MB';
      }

      // 文件选择处理
      document.getElementById('file-input').addEventListener('change', function (event) {
        const file = event.target.files[0];
        if (file) {
          if (
            file.name.toLowerCase().endsWith('.gltf') ||
            file.name.toLowerCase().endsWith('.glb')
          ) {
            loadGLTFModel(file);
          } else {
            alert('请选择 .gltf 或 .glb 格式的文件');
          }
        }
      });

      // 处理模型动画
      function handleAnimations(gltf) {
        // 重置之前的动画状态
        if (mixer) {
          mixer.stopAllAction();
          mixer.uncacheRoot(mixer.getRoot());
          mixer = null;
        }

        // 清空动画列表
        animations = [];
        activeAction = null;
        activeAnimationIndex = -1;

        // 关闭正在编辑的名称
        if (currentEditingNameElement) {
          finishEditingName(false);
        }

        // 检查模型是否有动画
        if (gltf.animations && gltf.animations.length > 0) {
          // 创建动画混合器
          mixer = new THREE.AnimationMixer(currentModel);

          // 复制动画数组，而不是直接引用
          animations = gltf.animations.map((anim) => anim);

          // 显示动画面板
          animationsPanel.style.display = 'block';

          // 更新动画列表
          updateAnimationsList();
        } else {
          // 隐藏动画面板
          animationsPanel.style.display = 'none';
        }
      }

      // 更新动画列表显示
      function updateAnimationsList() {
        // 清空列表
        animationsList.innerHTML = '';

        // 检查动画数组是否有效
        if (!animations || animations.length === 0) {
          animationsList.innerHTML = '<div class="no-animations">没有可用的动作</div>';
          return;
        }

        // 检查当前活动的动画索引是否有效
        if (activeAnimationIndex >= animations.length) {
          activeAnimationIndex = -1;
          activeAction = null;
        }

        // 创建每个动画的列表项
        animations.forEach((animation, index) => {
          if (!animation) return; // 跳过无效的动画

          const item = document.createElement('div');
          item.className = 'animation-item';
          item.dataset.index = index;

          // 如果是当前活动的动画，添加active类
          if (index === activeAnimationIndex) {
            item.classList.add('active');
          }

          const nameSpan = document.createElement('div');
          nameSpan.className = 'animation-name';
          nameSpan.textContent = animation.name || `动作 ${index + 1}`;
          nameSpan.title = '点击编辑动作名称';
          nameSpan.dataset.index = index;
          nameSpan.onclick = function (e) {
            e.stopPropagation();
            startEditingName(this);
          };

          const controls = document.createElement('div');
          controls.className = 'animation-controls';

          const playBtn = document.createElement('button');
          playBtn.className = 'btn btn-play';
          playBtn.textContent = index === activeAnimationIndex ? '正在播放' : '播放';
          playBtn.disabled = index === activeAnimationIndex; // 如果已经在播放，禁用按钮
          playBtn.onclick = () => playAnimation(index);

          const stopBtn = document.createElement('button');
          stopBtn.className = 'btn btn-stop';
          stopBtn.textContent = '停止';
          stopBtn.disabled = index !== activeAnimationIndex; // 如果不是当前播放的动画，禁用停止按钮
          stopBtn.onclick = () => stopAnimation();

          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'btn btn-delete';
          deleteBtn.textContent = '删除';
          deleteBtn.onclick = () => deleteAnimation(index);

          controls.appendChild(playBtn);
          controls.appendChild(stopBtn);
          controls.appendChild(deleteBtn);

          item.appendChild(nameSpan);
          item.appendChild(controls);

          animationsList.appendChild(item);
        });
      }

      // 播放指定索引的动画
      function playAnimation(index) {
        // 检查参数和状态是否有效
        if (!mixer || !animations || index < 0 || index >= animations.length) {
          console.warn('无法播放动画: 无效的索引或状态');
          return;
        }

        const animation = animations[index];
        if (!animation) {
          console.warn(`无法播放动画: 索引 ${index} 处的动画无效`);
          return;
        }

        // 停止当前正在播放的动画
        if (activeAction) {
          activeAction.stop();
          activeAction = null;
        }

        try {
          // 创建新的动作并播放
          activeAction = mixer.clipAction(animation);
          activeAction.reset();
          activeAction.play();

          // 更新当前活动的动画索引
          activeAnimationIndex = index;

          // 更新动画列表显示，以反映当前活动状态
          updateAnimationsList();
        } catch (e) {
          console.error('播放动画时出错:', e);
          activeAction = null;
          activeAnimationIndex = -1;
        }
      }

      // 停止当前正在播放的动画
      function stopAnimation() {
        if (!mixer || !activeAction) return;

        const index = activeAnimationIndex;

        try {
          // 停止动画
          activeAction.stop();

          // 记录日志
          let animationName = '未知动画';
          if (index >= 0 && index < animations.length && animations[index]) {
            animationName = animations[index].name || `动作 ${index + 1}`;
          }
        } catch (e) {
          console.warn('停止动画时出错:', e);
        } finally {
          // 无论是否出错，都重置状态
          activeAction = null;
          activeAnimationIndex = -1;

          // 更新动画列表显示
          updateAnimationsList();
        }
      }

      // 删除指定索引的动画
      function deleteAnimation(index) {
        if (index >= animations.length || index < 0) return;

        // 保存要删除的动画信息（用于日志）
        const animationToRemove = animations[index];
        const animationName = animationToRemove.name || `动作 ${index + 1}`;

        // 处理正在播放的动画
        if (mixer) {
          // 如果正在播放该动画，先停止
          if (index === activeAnimationIndex) {
            if (activeAction) {
              activeAction.stop();
              activeAction = null;
            }
            activeAnimationIndex = -1;
          }
          // 如果删除的是当前活动动画之前的动画，需要调整索引
          else if (index < activeAnimationIndex) {
            activeAnimationIndex--;
          }

          // 从混合器中移除该动画的动作
          try {
            const action = mixer.clipAction(animationToRemove);
            action.stop();
            mixer.uncacheAction(animationToRemove);
          } catch (e) {
            console.warn('清除动画动作时出错:', e);
          }
        }

        // 从列表中移除该动画
        animations.splice(index, 1);

        // 从原始 GLTF 对象中也删除该动画
        if (originalGltf && originalGltf.animations) {
          originalGltf.animations.splice(index, 1);

          // 标记模型已被编辑
          hasModelChanged = true;

          // 显示导出按钮
          document.getElementById('export-btn').style.display = 'inline-block';
        }

        // 更新动画列表显示
        updateAnimationsList();

        // 如果没有动画了，隐藏面板
        if (animations.length === 0) {
          animationsPanel.style.display = 'none';
        }
      }

      // 导出模型为 GLB 文件
      function exportToGLB() {
        if (!originalGltf || !currentModel) {
          alert('没有可导出的模型');
          return;
        }

        // 显示加载提示
        loadingElement.style.display = 'block';
        loadingElement.textContent = '正在导出模型...';

        // 创建导出器
        const exporter = new GLTFExporter();

        // 准备导出选项
        const options = {
          binary: true, // 导出为二进制 GLB 格式
          animations: originalGltf.animations, // 使用编辑后的动画列表
          onlyVisible: false, // 导出所有对象，包括不可见的
        };

        // 开始导出
        exporter.parse(
          currentModel.children,
          function (result) {
            // 导出成功
            saveArrayBuffer(result, getExportFileName());
            loadingElement.style.display = 'none';
          },
          function (error) {
            // 导出失败
            console.error('导出模型时出错:', error);
            alert('导出模型失败: ' + error.message);
            loadingElement.style.display = 'none';
          },
          options
        );
      }

      // 保存二进制数据为文件
      function saveArrayBuffer(buffer, filename) {
        const blob = new Blob([buffer], { type: 'application/octet-stream' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();

        // 清理
        setTimeout(() => {
          URL.revokeObjectURL(link.href);
        }, 100);
      }

      // 生成导出文件名
      function getExportFileName() {
        const baseName = currentFileName.replace(/\.[^\.]+$/, ''); // 移除扩展名
        return `${baseName}_edited.glb`;
      }

      // 开始编辑动作名称
      function startEditingName(element) {
        // 如果已经有正在编辑的名称，先完成编辑
        if (currentEditingNameElement) {
          finishEditingName();
        }

        // 保存当前正在编辑的元素
        currentEditingNameElement = element;

        // 获取动画索引和当前名称
        const index = parseInt(element.dataset.index);
        const currentName = element.textContent;

        // 创建输入框
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'animation-name-edit';
        input.value = currentName;
        input.dataset.index = index;
        input.dataset.originalName = currentName;

        // 设置输入框事件
        input.addEventListener('keydown', function (e) {
          if (e.key === 'Enter') {
            e.preventDefault();
            finishEditingName(true); // 保存并完成编辑
          } else if (e.key === 'Escape') {
            e.preventDefault();
            finishEditingName(false); // 取消编辑
          }
        });

        input.addEventListener('blur', function () {
          finishEditingName(true); // 失去焦点时保存
        });

        // 替换原来的名称元素
        element.parentNode.replaceChild(input, element);

        // 设置焦点并选中文本
        input.focus();
        input.select();
      }

      // 完成编辑动作名称
      function finishEditingName(save = true) {
        if (!currentEditingNameElement) return;

        // 获取输入框
        const input = document.querySelector('.animation-name-edit');
        if (!input) return;

        // 获取动画索引和新名称
        const index = parseInt(input.dataset.index);
        const newName = input.value.trim();
        const originalName = input.dataset.originalName;

        // 创建新的名称元素
        const nameSpan = document.createElement('div');
        nameSpan.className = 'animation-name';
        nameSpan.dataset.index = index;
        nameSpan.title = '点击编辑动作名称';
        nameSpan.onclick = function (e) {
          e.stopPropagation();
          startEditingName(this);
        };

        // 如果保存并且名称有变化
        if (save && newName && newName !== originalName) {
          // 更新动画名称
          animations[index].name = newName;

          // 更新原始 GLTF 对象中的动画名称
          if (originalGltf && originalGltf.animations && originalGltf.animations[index]) {
            originalGltf.animations[index].name = newName;

            // 标记模型已被编辑
            hasModelChanged = true;

            // 显示导出按钮
            document.getElementById('export-btn').style.display = 'inline-block';
          }

          nameSpan.textContent = newName;
        } else {
          // 使用原始名称
          nameSpan.textContent = originalName;
        }

        // 替换输入框
        input.parentNode.replaceChild(nameSpan, input);

        // 重置当前编辑状态
        currentEditingNameElement = null;
      }

      // 添加全局点击事件处理，在点击其他地方时完成名称编辑
      document.addEventListener('click', function (e) {
        // 检查是否点击了名称编辑区域以外的地方
        if (currentEditingNameElement && !e.target.classList.contains('animation-name-edit')) {
          finishEditingName(true);
        }
      });

      // 添加导出按钮事件监听器
      document.getElementById('export-btn').addEventListener('click', exportToGLB);

      // 初始化场景
      init();
    </script>
  </body>
</html>
