{"name": "starter-next-ts-template", "description": "A Nextjs starter template", "version": "0.1.0", "private": true, "author": "Nextjs", "license": "Apache-2.0", "scripts": {"dev": "next dev -p 3456", "build": "next build && next export && rimraf ./out/image ./out/404.html", "start": "next dev", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@types/styled-components": "^5.1.26", "assert": "^2.0.0", "cannon-es": "^0.20.0", "cannon-es-debugger": "^1.0.0", "cbor": "^9.0.2", "dat.gui": "^0.7.9", "gsap": "^3.12.5", "next": "13.0.2", "react": "18.2.0", "react-colorful": "^5.6.1", "react-dom": "18.2.0", "styled-components": "^5.3.6", "three": "^0.169.0", "url-loader": "^4.1.1"}, "devDependencies": {"@rollup/plugin-typescript": "8.4.0", "@types/dat.gui": "^0.7.13", "@types/lodash": "^4.14.191", "@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.8", "@types/three": "^0.169.0", "@typescript-eslint/eslint-plugin": "5.36.1", "@typescript-eslint/parser": "5.36.1", "eslint": "8.27.0", "eslint-config-next": "13.0.2", "husky": "8.0.1", "prettier": "3.5.x", "rollup": "2.79.0", "tslib": "2.4.0", "typescript": "4.8.4", "vite": "3.1.4", "vite-plugin-eslint": "1.8.1", "vite-plugin-wasm": "2.1.0", "vitest": "0.22.0"}, "browser": {"fs": false, "path": false, "os": false, "http2": false, "net": false, "tls": false, "dns": false}, "resolutions": {"styled-components": "^5"}}