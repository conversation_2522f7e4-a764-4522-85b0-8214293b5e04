#!/bin/bash

# 检查参数数量
if [ "$#" -ne 1 ]; then
  echo "Usage: $0 The number of parameters does not match."
  exit 1
fi

# 参数赋值
TAG_NAME=$1
ROOT_PATH='/root/jenkins/satworld_tag'

git tag -d "$TAG_NAME"
git push origin --delete "$TAG_NAME"  # 删除远程标签
git for-each-ref --sort=-creatordate --format='%(refname:short) %(creatordate)' refs/tags

cd "$ROOT_PATH/AvatarOrdinalsBrowser" || exit
# 更新AvatarOrdinalsBrowser子模块并安装依赖
git reset --hard HEAD
git pull

git tag -d "$TAG_NAME"
git push origin --delete "$TAG_NAME"  # 删除远程标签
git for-each-ref --sort=-creatordate --format='%(refname:short) %(creatordate)' refs/tags


exit 0