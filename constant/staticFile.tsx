export const SVG_FILE = {
  yesIcon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.5 11.25L9 17.25L19.5 6.75" stroke="white" stroke-width="2" stroke-linecap="round"/>
    </svg>
    `,
  noIcon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round"/>
<path d="M18 6L6 18" stroke="white" stroke-width="2" stroke-linecap="round"/>
</svg>
`,
  emptyIcon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="20" height="20" rx="2" fill="#BB3530"/>
    <rect width="20" height="20" rx="2" fill="black"/>
    <path d="M8.42614 12.6534V12.5682C8.43561 11.6638 8.5303 10.9441 8.71023 10.4091C8.89015 9.87405 9.14583 9.44081 9.47727 9.10938C9.80871 8.77794 10.2064 8.47254 10.6705 8.19318C10.9498 8.02273 11.2008 7.8215 11.4233 7.58949C11.6458 7.35275 11.821 7.08049 11.9489 6.77273C12.0814 6.46496 12.1477 6.12405 12.1477 5.75C12.1477 5.28598 12.0388 4.88352 11.821 4.54261C11.6032 4.2017 11.312 3.93892 10.9474 3.75426C10.5829 3.5696 10.178 3.47727 9.73295 3.47727C9.3447 3.47727 8.97064 3.55777 8.6108 3.71875C8.25095 3.87973 7.95028 4.13305 7.70881 4.47869C7.46733 4.82434 7.32765 5.27652 7.28977 5.83523H5.5C5.53788 5.0303 5.74621 4.34138 6.125 3.76847C6.50852 3.19555 7.01278 2.75758 7.63778 2.45455C8.26752 2.15152 8.96591 2 9.73295 2C10.5663 2 11.2907 2.16572 11.9063 2.49716C12.5265 2.8286 13.0047 3.28314 13.3409 3.86079C13.6818 4.43845 13.8523 5.09659 13.8523 5.83523C13.8523 6.35606 13.7718 6.82718 13.6108 7.24858C13.4545 7.66998 13.2273 8.0464 12.929 8.37784C12.6354 8.70928 12.2803 9.00284 11.8636 9.25852C11.447 9.51894 11.1132 9.79356 10.8622 10.0824C10.6113 10.3665 10.429 10.705 10.3153 11.098C10.2017 11.491 10.1402 11.9811 10.1307 12.5682V12.6534H8.42614ZM9.33523 16.858C8.98485 16.858 8.68419 16.7325 8.43324 16.4815C8.18229 16.2306 8.05682 15.9299 8.05682 15.5795C8.05682 15.2292 8.18229 14.9285 8.43324 14.6776C8.68419 14.4266 8.98485 14.3011 9.33523 14.3011C9.68561 14.3011 9.98627 14.4266 10.2372 14.6776C10.4882 14.9285 10.6136 15.2292 10.6136 15.5795C10.6136 15.8116 10.5545 16.0246 10.4361 16.2188C10.3224 16.4129 10.1686 16.5691 9.97443 16.6875C9.78504 16.8011 9.57197 16.858 9.33523 16.858Z" fill="white"/>
</svg>`,
  actionSelectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
  <path d="M14.4227 5.94141C14.4227 7.43325 15.0153 8.86399 16.0702 9.91888C17.1251 10.9738 18.5558 11.5664 20.0477 11.5664C21.5395 11.5664 22.9703 10.9738 24.0251 9.91888C25.08 8.86399 25.6727 7.43325 25.6727 5.94141C25.6727 4.44956 25.08 3.01882 24.0251 1.96393C22.9703 0.909038 21.5395 0.316406 20.0477 0.316406C18.5558 0.316406 17.1251 0.909038 16.0702 1.96393C15.0153 3.01882 14.4227 4.44956 14.4227 5.94141Z" fill="url(#paint0_linear_2226_5509)"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.978 11.1469L4.12707 8.4038C2.9927 8.0513 1.70364 8.43005 1.22551 9.51755C0.65176 10.8254 0.616135 11.9513 0.747385 12.7894C0.91801 13.8807 1.84051 14.6072 2.85864 15.0357L12.4586 19.0669L13.2555 36.1913C13.3371 37.9594 14.6468 39.4229 16.4121 39.5541C17.237 39.6165 18.198 39.6674 19.2498 39.6852V29.1602C19.2498 28.7459 19.5855 28.4102 19.9998 28.4102C20.414 28.4102 20.7498 28.7459 20.7498 29.1602V39.6864C21.7293 39.6717 22.7082 39.6276 23.6852 39.5541C25.4505 39.4219 26.7593 37.9594 26.8408 36.1913L27.6377 19.0669L37.2002 14.9907C38.1396 14.5904 39.0011 13.9388 39.2102 12.9404C39.393 12.0741 39.3986 10.8666 38.7283 9.44912C38.2305 8.3963 36.964 8.04755 35.853 8.3963L27.1352 11.1357C26.2257 11.4215 25.2779 11.5669 24.3246 11.5669H15.753C14.8122 11.567 13.8767 11.4254 12.978 11.1469Z" fill="url(#paint1_linear_2226_5509)"/>
  <defs>
    <linearGradient id="paint0_linear_2226_5509" x1="20.0477" y1="0.316406" x2="20.0477" y2="11.5664" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
    <linearGradient id="paint1_linear_2226_5509" x1="20" y1="8.26562" x2="20" y2="39.6864" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
  </defs>
</svg>
`,
  actionUnselectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
  <path d="M14.4227 5.94141C14.4227 7.43325 15.0154 8.86399 16.0703 9.91888C17.1251 10.9738 18.5559 11.5664 20.0477 11.5664C21.5396 11.5664 22.9703 10.9738 24.0252 9.91888C25.0801 8.86399 25.6727 7.43325 25.6727 5.94141C25.6727 4.44956 25.0801 3.01882 24.0252 1.96393C22.9703 0.909038 21.5396 0.316406 20.0477 0.316406C18.5559 0.316406 17.1251 0.909038 16.0703 1.96393C15.0154 3.01882 14.4227 4.44956 14.4227 5.94141Z" fill="white"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.978 11.1469L4.12707 8.4038C2.9927 8.0513 1.70364 8.43005 1.22551 9.51755C0.65176 10.8254 0.616135 11.9513 0.747385 12.7894C0.91801 13.8807 1.84051 14.6072 2.85864 15.0357L12.4586 19.0669L13.2555 36.1913C13.3371 37.9594 14.6468 39.4229 16.4121 39.5541C17.237 39.6165 18.198 39.6674 19.2498 39.6852V29.1602C19.2498 28.7459 19.5855 28.4102 19.9998 28.4102C20.414 28.4102 20.7498 28.7459 20.7498 29.1602V39.6864C21.7293 39.6717 22.7082 39.6276 23.6852 39.5541C25.4505 39.4219 26.7593 37.9594 26.8408 36.1913L27.6377 19.0669L37.2002 14.9907C38.1396 14.5904 39.0011 13.9388 39.2102 12.9404C39.393 12.0741 39.3986 10.8666 38.7283 9.44912C38.2305 8.3963 36.964 8.04755 35.853 8.3963L27.1352 11.1357C26.2257 11.4215 25.2779 11.5669 24.3246 11.5669H15.753C14.8122 11.567 13.8767 11.4254 12.978 11.1469Z" fill="white"/>
</svg>
`,
  kingSelectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M2.38817 19.8385L0 4.58073L7.82609 11.5252L15 0.414062L22.1739 11.5252L30 4.58073L27.6118 19.8385C27.4849 20.6496 26.7862 21.2474 25.9652 21.2474H4.03478C3.21382 21.2474 2.51512 20.6496 2.38817 19.8385ZM4.16694 22.9157C2.32599 22.9157 0.833605 24.4081 0.833605 26.249C0.833605 28.09 2.32599 29.5824 4.16694 29.5824H25.8336C27.6746 29.5824 29.1669 28.09 29.1669 26.249C29.1669 24.4081 27.6745 22.9157 25.8336 22.9157H4.16694Z" fill="url(#paint0_linear_2226_5302)"/>
  <defs>
    <linearGradient id="paint0_linear_2226_5302" x1="15" y1="0.414062" x2="15" y2="29.5824" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
  </defs>
</svg>
`,
  kingUnselectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M2.38817 19.8385L0 4.58073L7.82609 11.5252L15 0.414062L22.1739 11.5252L30 4.58073L27.6118 19.8385C27.4849 20.6496 26.7862 21.2474 25.9652 21.2474H4.03478C3.21382 21.2474 2.51512 20.6496 2.38817 19.8385ZM4.16694 22.9157C2.32599 22.9157 0.833605 24.4081 0.833605 26.249C0.833605 28.09 2.32599 29.5824 4.16694 29.5824H25.8336C27.6746 29.5824 29.1669 28.09 29.1669 26.249C29.1669 24.4081 27.6745 22.9157 25.8336 22.9157H4.16694Z" fill="white"/>
</svg>
`,
  petSelectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="42" height="32" viewBox="0 0 42 32" fill="none">
  <ellipse cx="15.0721" cy="6.63421" rx="4.5972" ry="5.56503" transform="rotate(-7.26609 15.0721 6.63421)" fill="url(#paint0_linear_9429_6331)"/>
  <ellipse cx="7.08756" cy="15.3825" rx="4.5972" ry="5.56503" transform="rotate(-27.3432 7.08756 15.3825)" fill="url(#paint1_linear_9429_6331)"/>
  <ellipse cx="4.5972" cy="5.56503" rx="4.5972" ry="5.56503" transform="matrix(-0.991969 -0.126478 -0.126478 0.991969 32.0742 1.69531)" fill="url(#paint2_linear_9429_6331)"/>
  <ellipse cx="4.5972" cy="5.56503" rx="4.5972" ry="5.56503" transform="matrix(-0.888271 -0.459319 -0.459319 0.888271 41.5521 12.5508)" fill="url(#paint3_linear_9429_6331)"/>
  <path d="M31 24.5625C31 28.3767 26.5228 31.4688 21 31.4688C15.4772 31.4688 11 28.3767 11 24.5625C11 20.7483 15.4772 14.4688 21 14.4688C26.5228 14.4688 31 20.7483 31 24.5625Z" fill="url(#paint4_linear_9429_6331)"/>
  <defs>
    <linearGradient id="paint0_linear_9429_6331" x1="15.0721" y1="1.06918" x2="15.0721" y2="12.1992" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
    <linearGradient id="paint1_linear_9429_6331" x1="7.08756" y1="9.81743" x2="7.08756" y2="20.9475" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
    <linearGradient id="paint2_linear_9429_6331" x1="4.5972" y1="0" x2="4.5972" y2="11.1301" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
    <linearGradient id="paint3_linear_9429_6331" x1="4.5972" y1="0" x2="4.5972" y2="11.1301" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
    <linearGradient id="paint4_linear_9429_6331" x1="21" y1="14.4687" x2="21" y2="31.4688" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
  </defs>
</svg>
`,
  petUnselectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="42" height="32" viewBox="0 0 42 32" fill="none">
  <ellipse cx="15.0721" cy="6.63421" rx="4.5972" ry="5.56503" transform="rotate(-7.26609 15.0721 6.63421)" fill="white"/>
  <ellipse cx="7.08756" cy="15.3825" rx="4.5972" ry="5.56503" transform="rotate(-27.3432 7.08756 15.3825)" fill="white"/>
  <ellipse cx="4.5972" cy="5.56503" rx="4.5972" ry="5.56503" transform="matrix(-0.991969 -0.126478 -0.126478 0.991969 32.0742 1.69531)" fill="white"/>
  <ellipse cx="4.5972" cy="5.56503" rx="4.5972" ry="5.56503" transform="matrix(-0.888271 -0.459319 -0.459319 0.888271 41.5521 12.5508)" fill="white"/>
  <path d="M31 24.5625C31 28.3767 26.5228 31.4688 21 31.4688C15.4772 31.4688 11 28.3767 11 24.5625C11 20.7483 15.4772 14.4688 21 14.4688C26.5228 14.4688 31 20.7483 31 24.5625Z" fill="white"/>
</svg>

`,
  shirtSelectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">
  <path d="M11.5492 0.816406H7.41829C6.38906 0.816406 4.15953 3.60401 3.48927 4.27147C2.50772 5.25302 1.60469 6.26823 0.735319 7.261C-0.142469 8.25938 -0.260255 9.77096 0.522183 10.8451C0.811039 11.2461 1.12514 11.6387 1.4841 11.9949C1.84027 12.351 2.23008 12.6679 2.63392 12.9568C3.70522 13.7364 5.21961 13.6186 6.21799 12.7437C6.29295 12.6792 6.38487 12.6376 6.4828 12.6238C6.58073 12.6101 6.68055 12.6248 6.77037 12.6661C6.8602 12.7075 6.93624 12.7738 6.98946 12.8572C7.04267 12.9405 7.07081 13.0374 7.07054 13.1363C7.00323 19.2443 6.99482 24.3351 7.21076 29.484C7.22732 30.0058 7.40074 30.5104 7.70841 30.9321C8.01607 31.3539 8.44369 31.6731 8.93549 31.8481C14.4041 33.7411 19.5615 33.5308 24.9684 31.8201C25.4843 31.6531 25.9361 31.3311 26.2623 30.898C26.5885 30.4649 26.7733 29.9418 26.7913 29.3999C27.0045 24.2593 26.9988 19.2107 26.9315 13.1363C26.9305 13.0374 26.9581 12.9404 27.011 12.8568C27.0638 12.7732 27.1397 12.7067 27.2295 12.6652C27.3192 12.6238 27.4191 12.6091 27.517 12.623C27.6148 12.6369 27.7066 12.6788 27.7813 12.7437C28.7797 13.6215 30.2913 13.7392 31.3654 12.9596C31.7734 12.6687 32.1578 12.3462 32.5152 11.9949C32.8741 11.6387 33.1882 11.2489 33.4799 10.8451C34.2595 9.77096 34.1417 8.25938 33.2668 7.261C32.3974 6.26823 31.4916 5.25302 30.51 4.27147C29.8482 3.60962 27.6242 0.816406 26.581 0.816406H21.8359C21.5827 1.99253 20.9342 3.04637 19.9982 3.80228C19.0623 4.55819 17.8956 4.9705 16.6926 4.9705C15.4895 4.9705 14.3228 4.55819 13.3869 3.80228C12.4509 3.04637 11.8024 1.99253 11.5492 0.816406Z" fill="url(#paint0_linear_2226_5203)"/>
  <defs>
    <linearGradient id="paint0_linear_2226_5203" x1="17" y1="0.816406" x2="17" y2="35.2476" gradientUnits="userSpaceOnUse">
      <stop stop-color="#C26B31"/>
      <stop offset="1" stop-color="#8D4A1D"/>
    </linearGradient>
  </defs>
</svg>
`,
  shirtUnselectIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">
  <path d="M11.5492 0.816406H7.41829C6.38906 0.816406 4.15953 3.60401 3.48927 4.27147C2.50772 5.25302 1.60469 6.26823 0.735319 7.261C-0.142469 8.25938 -0.260255 9.77096 0.522183 10.8451C0.811039 11.2461 1.12514 11.6387 1.4841 11.9949C1.84027 12.351 2.23008 12.6679 2.63392 12.9568C3.70522 13.7364 5.21961 13.6186 6.21799 12.7437C6.29295 12.6792 6.38487 12.6376 6.4828 12.6238C6.58073 12.6101 6.68055 12.6248 6.77037 12.6661C6.8602 12.7075 6.93624 12.7738 6.98946 12.8572C7.04267 12.9405 7.07081 13.0374 7.07054 13.1363C7.00323 19.2443 6.99482 24.3351 7.21076 29.484C7.22732 30.0058 7.40074 30.5104 7.70841 30.9321C8.01607 31.3539 8.44369 31.6731 8.93549 31.8481C14.4041 33.7411 19.5615 33.5308 24.9684 31.8201C25.4843 31.6531 25.9361 31.3311 26.2623 30.898C26.5885 30.4649 26.7733 29.9418 26.7913 29.3999C27.0045 24.2593 26.9988 19.2107 26.9315 13.1363C26.9305 13.0374 26.9581 12.9404 27.011 12.8568C27.0638 12.7732 27.1397 12.7067 27.2295 12.6652C27.3192 12.6238 27.4191 12.6091 27.517 12.623C27.6148 12.6369 27.7066 12.6788 27.7813 12.7437C28.7797 13.6215 30.2913 13.7392 31.3654 12.9596C31.7734 12.6687 32.1578 12.3462 32.5152 11.9949C32.8741 11.6387 33.1882 11.2489 33.4799 10.8451C34.2595 9.77096 34.1417 8.25938 33.2668 7.261C32.3974 6.26823 31.4916 5.25302 30.51 4.27147C29.8482 3.60962 27.6242 0.816406 26.581 0.816406H21.8359C21.5827 1.99253 20.9342 3.04637 19.9982 3.80228C19.0623 4.55819 17.8956 4.9705 16.6926 4.9705C15.4895 4.9705 14.3228 4.55819 13.3869 3.80228C12.4509 3.04637 11.8024 1.99253 11.5492 0.816406Z" fill="white"/>
</svg>
`,
  menuEmptyIcon: `<svg width="128" height="101" viewBox="0 0 128 101" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g opacity="0.5">
        <path d="M21.118 44.6646L29.1439 50.7606M22.0831 51.7256L28.1791 43.6997" stroke="url(#paint0_linear_1128_1878)" stroke-width="3.63636" stroke-linecap="round"/>
        <circle cx="105.899" cy="46.4852" r="3.48485" stroke="url(#paint1_linear_1128_1878)" stroke-width="2.72727"/>
        <circle cx="39.8379" cy="31.9391" r="2.87879" stroke="url(#paint2_linear_1128_1878)" stroke-width="1.51515"/>
        <circle cx="74.2324" cy="12.3939" r="1.36364" fill="#CAC8C7"/>
        <circle cx="106.202" cy="65.8785" r="4.54545" fill="#837C73"/>
        <circle cx="28.3233" cy="36.4851" r="2.72727" fill="#837C73"/>
        <circle cx="85.2934" cy="15.5757" r="4.54545" fill="#837C73"/>
        <g filter="url(#filter0_d_1128_1878)">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M90.2017 17.5545C90.0005 16.8287 89.2491 16.4035 88.5234 16.6046C87.7976 16.8058 87.3724 17.5572 87.5735 18.283L88.0373 19.9563L86.3642 20.4201C85.6384 20.6212 85.2132 21.3726 85.4143 22.0984C85.6155 22.8242 86.3669 23.2494 87.0927 23.0483L88.7658 22.5845L89.2297 24.2581C89.4308 24.9838 90.1823 25.4091 90.908 25.2079C91.6338 25.0068 92.059 24.2553 91.8579 23.5296L91.394 21.856L93.0678 21.3921C93.7935 21.1909 94.2188 20.4395 94.0176 19.7138C93.8164 18.988 93.065 18.5627 92.3393 18.7639L90.6655 19.2278L90.2017 17.5545Z" fill="url(#paint3_linear_1128_1878)"/>
        </g>
        <ellipse cx="65.2939" cy="55.5756" rx="33.3333" ry="32.4242" fill="url(#paint4_linear_1128_1878)"/>
        <path d="M32.7329 72.6906C34.9859 71.1934 34.8574 68.7076 34.5116 67.6518C38.2172 61.8932 59.2653 66.932 66.2319 67.2199C73.1985 67.5079 73.7914 64.6285 81.3509 62.757C88.9104 60.8854 96.9146 64.4846 96.4699 67.6518C96.1141 70.1856 97.4086 71.5869 98.1004 71.9708C99.3356 72.7866 101.628 75.0805 100.917 77.7294C100.027 81.0406 94.0983 81.9044 95.1359 82.4803C96.1734 83.0562 97.9521 85.0717 96.4699 87.6631C94.9876 90.2545 88.6265 89.8179 86.9835 89.5346C86.1381 89.3889 78.2382 87.6631 73.7914 88.6708C69.3446 89.6786 57.3384 89.8226 52.7434 87.6631C48.1484 85.5036 43.1087 89.3907 39.996 86.5113C37.5058 84.2079 40.3418 81.8085 42.0711 80.8967C39.9466 80.8487 34.9563 80.4072 31.9918 79.0251C28.2861 77.2975 29.9166 74.5622 32.7329 72.6906Z" fill="#CFCFCF"/>
        <path opacity="0.6" d="M93.4746 70.986C82.8371 84.3492 48.1716 72.4445 34.8383 69.0607C24.7777 44.2122 51.808 28.4042 66.6565 30.1213C93.4746 33.2228 104.535 57.0911 93.4746 70.986Z" fill="url(#paint5_linear_1128_1878)"/>
        <g filter="url(#filter1_d_1128_1878)">
            <path d="M54.1575 63.1514C54.7916 67.592 61.5793 76.5354 72.5725 74.1891" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter2_d_1128_1878)">
            <path d="M55.7247 39.9351L58.9882 51.7088" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M51.4706 47.4536L63.2444 44.1902" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <g filter="url(#filter3_d_1128_1878)">
            <path d="M79.9661 57.2075L83.2295 68.9813" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
            <path d="M75.7119 64.7261L87.4857 61.4626" stroke="#837C73" stroke-width="4.84848" stroke-linecap="round"/>
        </g>
        <path d="M43.1725 78.3025C35.7786 79.5146 31.4049 76.9893 30.1422 75.5752C28.1725 79.9691 39.3846 80.7267 42.1119 80.8782C44.8392 81.0297 51.3543 76.9612 43.1725 78.3025Z" fill="url(#paint6_linear_1128_1878)"/>
        <path opacity="0.7" d="M49.0809 84.818C43.99 87.2423 40.394 85.8281 39.2324 84.818C39.2324 86.9393 43.0203 88.6059 46.0506 87.3938C48.4748 86.4241 51.2021 86.9897 52.2627 87.3938C59.2324 91.4847 74.687 88.6059 76.9597 88.3029C79.2324 87.9998 85.293 89.9695 90.8991 89.9695C95.8991 89.9695 98.0203 84.818 96.5051 86.4847C94.99 88.1513 88.7779 89.0604 84.2324 87.3938C80.5961 86.0604 77.8688 86.2321 76.9597 86.4847C66.6567 88.9089 56.9092 86.8887 53.3233 85.5756C51.6264 84.3635 49.788 84.5655 49.0809 84.818Z" fill="url(#paint7_linear_1128_1878)"/>
        <path d="M93.9295 81.3328C92.5962 81.8176 93.98 82.2419 94.8386 82.3934C94.5356 82.3157 95.0166 82.5897 97.869 80.8782C101.657 78.6055 101.101 76.9155 100.748 75.5752C100.899 78.3025 95.5962 80.7267 93.9295 81.3328Z" fill="url(#paint8_linear_1128_1878)"/>
    </g>
    <defs>
        <filter id="filter0_d_1128_1878" x="84.1524" y="16.5547" width="11.1272" height="11.1274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter1_d_1128_1878" x="51.7329" y="60.7271" width="25.6885" height="19.9088" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="1.21212" dy="2.42424"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter2_d_1128_1878" x="47.8337" y="37.5103" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <filter id="filter3_d_1128_1878" x="72.075" y="54.7827" width="19.0477" height="19.0478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.21212"/>
            <feGaussianBlur stdDeviation="0.606061"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1128_1878"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1128_1878" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_1128_1878" x1="21.884" y1="51.5739" x2="27.9801" y2="43.548" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FFB06A"/>
        </linearGradient>
        <linearGradient id="paint1_linear_1128_1878" x1="105.899" y1="41.6367" x2="105.899" y2="51.3337" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF7E20"/>
        </linearGradient>
        <linearGradient id="paint2_linear_1128_1878" x1="39.8379" y1="28.3027" x2="39.8379" y2="35.5755" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F9B34C"/>
            <stop offset="1" stop-color="#FF8F3E"/>
        </linearGradient>
        <linearGradient id="paint3_linear_1128_1878" x1="88.5237" y1="16.6046" x2="90.9083" y2="25.2078" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FF8A00"/>
            <stop offset="1" stop-color="#FF9142"/>
        </linearGradient>
        <linearGradient id="paint4_linear_1128_1878" x1="65.2939" y1="23.1514" x2="74.8895" y2="72.5453" gradientUnits="userSpaceOnUse">
            <stop stop-color="#ECECEC"/>
            <stop offset="0.684045" stop-color="#CECECE"/>
        </linearGradient>
        <linearGradient id="paint5_linear_1128_1878" x1="65.1104" y1="44.6668" x2="65.1104" y2="74.9455" gradientUnits="userSpaceOnUse">
            <stop stop-color="#F3F3F3" stop-opacity="0"/>
            <stop offset="1" stop-color="#F3F3F3"/>
        </linearGradient>
        <linearGradient id="paint6_linear_1128_1878" x1="38.5137" y1="77.2254" x2="38.5137" y2="80.6308" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
        <linearGradient id="paint7_linear_1128_1878" x1="61.8082" y1="85.8786" x2="97.1112" y2="86.1816" gradientUnits="userSpaceOnUse">
            <stop stop-color="#AEAEAE"/>
            <stop offset="0.832676" stop-color="#818181" stop-opacity="0.553704"/>
            <stop offset="1" stop-color="#F3F3F3" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="paint8_linear_1128_1878" x1="97.1789" y1="77.6953" x2="97.1789" y2="82.0702" gradientUnits="userSpaceOnUse">
            <stop stop-color="#989898" stop-opacity="0"/>
            <stop offset="1" stop-color="#A5A5A5"/>
        </linearGradient>
    </defs>
</svg>
`,
  setIcon: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17.448 3.6269C15.1965 5.87832 15.2562 7.05154 16.3522 8.14748C17.4481 9.2434 18.6213 9.30314 20.8728 7.05171C20.9615 6.96299 21.1106 6.98026 21.1714 7.08994C22.293 9.11345 22.034 11.0884 20.3689 12.7534C18.8061 14.3163 16.9702 14.6404 15.0774 13.7468C15.0077 13.7139 14.9241 13.7319 14.8728 13.7895C14.175 14.5715 12.607 16.2152 11.83 16.9922C11.0531 17.7691 9.39095 19.3558 8.60885 20.0535C7.67712 20.8848 6.22617 21.0634 5.25025 20.2842C4.8724 19.9825 4.51716 19.6272 4.21542 19.2494C3.43631 18.2735 3.61488 16.8226 4.44614 15.8908C5.14386 15.1087 6.73046 13.4465 7.50743 12.6696C8.2844 11.8926 9.9281 10.3246 10.7101 9.62688C10.7677 9.57554 10.7858 9.492 10.7528 9.42225C9.85924 7.52946 10.1833 5.69358 11.7462 4.13069C13.4113 2.46562 15.3862 2.20667 17.4097 3.32821C17.5194 3.38903 17.5367 3.53817 17.448 3.6269Z" fill="#686663"/>
</svg>
`,
  eyesSvg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.919 10.7996C22.183 11.1287 22.3291 11.5563 22.3291 11.9997C22.3291 12.4431 22.183 12.8707 21.919 13.1998C20.2473 15.2238 16.442 19.1644 11.9999 19.1644C7.55783 19.1644 3.75261 15.2238 2.08085 13.1998C1.81681 12.8707 1.67065 12.4431 1.67065 11.9997C1.67065 11.5563 1.81681 11.1287 2.08085 10.7996C3.75261 8.77556 7.55783 4.83496 11.9999 4.83496C16.442 4.83496 20.2473 8.77556 21.919 10.7996Z" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12 15.184C13.7586 15.184 15.1842 13.7583 15.1842 11.9997C15.1842 10.2411 13.7586 8.81543 12 8.81543C10.2413 8.81543 8.81567 10.2411 8.81567 11.9997C8.81567 13.7583 10.2413 15.184 12 15.184Z" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  arrowSvg: `<svg width="10" height="17" viewBox="0 0 10 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_1372_1518)">
<path d="M0 0.5L7.31672 4.15836C8.96121 4.9806 10 6.6614 10 8.5C10 10.3386 8.96121 12.0194 7.31672 12.8416L0 16.5V0.5Z" fill="#FAFAFA" fill-opacity="0.6"/>
</g>
<defs>
<filter id="filter0_b_1372_1518" x="-8" y="-7.5" width="26" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1372_1518"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1372_1518" result="shape"/>
</filter>
</defs>
</svg>
`,
  eyesCloseSvg: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.52893 7.55176C5.07844 10.4314 8.29014 12.4097 12.0009 12.4097C15.7116 12.4097 18.9233 10.4314 20.4728 7.55176" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M5.23195 9.76465L1.65112 12.7656" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.47822 12.1055L7.75403 16.4478" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M18.7681 9.76465L22.349 12.7656" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.5159 12.1055L16.2401 16.4478" stroke="#686663" stroke-width="1.71" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  clockSvgIcon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<mask id="mask0_6582_22798" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
<circle cx="8" cy="8" r="7.5" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_6582_22798)">
<circle cx="8" cy="8" r="7.25" fill="#CA5500" stroke="#140F08" stroke-width="0.5"/>
<circle cx="7.5" cy="7.5" r="6.9" fill="#FFCC22" stroke="#140F08" stroke-width="0.2"/>
<g filter="url(#filter0_i_6582_22798)">
<circle cx="7.5" cy="7.5" r="5.5" fill="#EDE1CC"/>
</g>
<circle cx="7.5" cy="7.5" r="5.4" stroke="#140F08" stroke-width="0.2"/>
<circle cx="8" cy="8" r="7.25" stroke="#140F08" stroke-width="0.5"/>
<path d="M8.31996 4.01815C8.44551 3.78588 8.73002 3.69126 8.96967 3.80208L9.02463 3.82749C9.26428 3.93831 9.37646 4.21636 9.28081 4.46246L7.99904 7.7602L6.63756 7.13063L8.31996 4.01815Z" fill="#47351B"/>
<path d="M4.47454 7.85231C4.21844 7.80824 4.03878 7.57534 4.06119 7.31644L4.06815 7.23603C4.09055 6.97712 4.30755 6.77855 4.56742 6.77914L7.08549 6.78486L6.95616 8.27928L4.47454 7.85231Z" fill="#47351B"/>
<circle cx="7" cy="7.5" r="0.9" fill="#FF8316" stroke="#140F08" stroke-width="0.2"/>
</g>
<defs>
<filter id="filter0_i_6582_22798" x="2" y="2" width="11" height="11" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.5" dy="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_6582_22798"/>
</filter>
</defs>
</svg>
  `,
};
