import { ButlerData } from '@/world/Global/GlobalButlerUtil';
import { LoadingPageType } from '@/world/Config/DoorConfig';

export enum SCENE_TYPE {
  None,
  Room,
  Island,
  Community,
  OnlineWorld = 999,
}

export interface ICatResult {
  tokenId: string;
  minterAddr: string;
  tokenAddr: string;
  max: string;
  name: string;
  limit: string;
  symbol: string;
  premine: string;
  decimals: number;
  minterMd5: string;
  revealTxid: string;
  revealHeight: number;
  genesisTxid: string;
  minterPubKey: string;
  tokenPubKey: string;
  firstMintHeight: number;
  balance: number;
}

export interface INftResult {
  id: number;
  address: string;
  position: number;
  content: string;
  created: string;
  updated: string;
}

export interface IRunesResult {
  rune: string;
  runeid: string;
  spacedRune: string;
  amount: string;
  symbol: string;
  divisibility: number;
}

export interface IBrc20Result {
  ticker: string;
  overallBalance: string;
  transferableBalance: string;
  availableBalance: string;
  availableBalanceSafe: string;
  availableBalanceUnSafe: string;
  decimal: number;
  selfMint: boolean;
}

export interface IBasicSummaryData {
  tempAvatarResult: {
    address: string;
    content: string;
  };
  ttsResult: IBindInfo;
  unusableInfo: UnusableInfo | null;
  initInscriptionId: {
    inscriptionId: string;
  } | null;
  petInfo: {
    claim: false; //是否已领取宠物
  };
  brc20Result: IBrc20Result[];
  catResult: ICatResult[];
  inscriptionResult: string[];
  nftResult: INftResult[];
  runesResult: IRunesResult[];
}

export enum TOKEN_TYPE_ENUM {
  Brc20 = 'Brc20',
  Runes = 'Runes',
  Cat20 = 'Cat20',
}

export interface IWallNft {
  id: number;
  position: number;
  content: string;
}

export enum SUPPORT_WALLET_ENUM {
  'unisat' = 'unisat',
  'okx' = 'okx',
}

export type UnusableInfo = {
  shirt: string[];
  pants: string[];
  shoes: string[];
  hat: string[];
};

export interface TaskReward {
  itemTag: string; // 物品tag
  itemType: string; // 物品类型
  quantity: number; // 奖励数量
}

export interface TaskInfo {
  id: string; // 任务进度ID
  taskId: string; // 任务ID
  taskType: string; // 任务类型
  taskTitle: string; // 任务标题
  taskDesc: string; // 任务描述
  taskProgress: {
    conditionType: string; // 条件类型
    conditionDesc: string; // 条件描述
    targetId: string; // 目标ID
    requiredCount: number; // 目标完成数量
    currentCount: number; // 当前完成数量
  }[];
  taskRewards: TaskReward[];
  clientRefresh: string; // 客户端主动刷新的条件类型
  status: string; // 当前任务状态
  expireAt: string; // 任务过期时间
  newTag: boolean; // 是否为新任务
}

export interface IAppState {
  btcWallet: SUPPORT_WALLET_ENUM;
  btcAddress: string;
  showConnectWallet: boolean;
  defaultInscriptionId: string;
  pageLoadingRate: number;
  potatoTime: IPotatoTime | null;
  sceneType: SCENE_TYPE;
  isRunJumpLogic: boolean;
  domainOwner: boolean;
  storageMenu: STORAGE_MENU_ENUM | null;
  isRecording: boolean;
  ttsBuildData: IttsBuildData | null;
  usedCharacter: CHARACTER_ENUM;
  isTtsWhiteList: boolean;
  axeParams: {
    currentDurability?: number; // 斧头耐久度
    userItemId?: string; // 斧头ID
    tag?: number; // 斧头工具编号
  } | null;
  treeList: { status: string; id: string; tag: string; score: number }[] | null;
  rockList: { status: string; id: string; tag: string; score: number }[] | null;
  easterEgg: {
    rule: string; //2002,2018,2013,2006,2005,2017,2015,2012,2004,2009,2016,2008,2014,2010,2001,2011,2007,2003
    status: string; // 是否完成彩蛋，waiting未完成，finish已完成
    goOn: boolean;
  } | null;
  userBasicInfo: {
    twitterFlag?: boolean; // 是否绑定twitter
    refreshFlag?: boolean; // 用户是否是机器人
    // ttl?: number;
    activityInfos?: {
      activityType: string; // 活动类型
      startTime: number; // 活动开始时间
      endTime: number; // 活动结束时间
      totalCount: number; // 领取的数量上限
      receivedCount: number; // 已领取数量
    }[];
    userTaskProgresses?: any;
    rankInfos?: {
      activityType: string; // 活动类型
      rank?: number; // 排名
      score?: number; // 积分
      address?: string; // 地址
      rankType: string; // 排名类型
      rankInfo?: {
        rank: number;
        score: number;
        address: string;
      }[];
    }[];
    toolConfig?: {
      dailyBuyTotalCount: number;
      dailyCurrentBuyCount: number;
    };
    workbenchConfig?: {
      workbenchTotalCount: number; //合成总次数
      workbenchCurrentCount: number; //合成当前次数
    } | null;
  } | null;
  userScore: {
    score: number;
    userItemId: string;
    tag: string | number;
    currentDurability?: number;
    rank?: number;
    totalScore?: number;
    easterEgg?: boolean;
  } | null;
  leftTime?: number;
  menuType: string;
  /**
   * @deprecated 弃用此属性，迁移至`whackAMoleEasterEgg`
   */
  dogEasterEgg:
    | {
        tag: number;
        interval: number;
        pause: number;
      }[]
    | null;
  whackAMoleEasterEgg: {
    rewardType: RewardType;
    ruleInfo: { tag: number; interval: number; pause: number }[] | null;
  } | null;
  easterEggInfo?: { domainName: string; isSuccess: boolean }[];
  fishEasterEggModal: boolean;
  isFinished: boolean;
  menuVisible: boolean; // 菜单是否可见
  loaderType: LoadingPageType; // 加载器类型
  leaderboard:
    | {
        score: number;
        activityTag: string;
        rank: number;
        rankType: string;
        rankInfo?: { rank: number; score: number; address: string }[]; // 社区排名信息
      }[]
    | null;
  rockLeftTime?: number;
  treeLeftTime?: number;
  randomEventResult: {
    quantity: number;
    tag?: string;
    eventType?: string;
  } | null; //随机彩蛋
  unusableInfo: UnusableInfo | null;
  taskInfo: TaskInfo[] | null;
  easterEggReward: {
    quantity: number;
    tag?: string;
    eventType?: string;
  } | null;
  isMobile: boolean;
}

export interface IGameState {
  bagInventoryList: IBagInventoryItem[]; //背包物品
  equipmendResult: IBagInventoryItem[]; //已装备的物品
  syntheticsList: ISyntheticItem[];
  materialList: IBagInventoryItem[]; //材料列表
}

export interface ISettingState {
  onlineMultiplayer: boolean;
  soundEffects: boolean;
  music: boolean;
  maxPlayers: number;
  isPizzaActivity: boolean;
}

export interface IttsBuildData {
  cdn: string;
  content: string;
  data: ButlerData | null;
  created: string;
  subtitle: IVTT[];
  requestId: string;
}

export interface IttsBuildDataHistory {
  id: string;
  requestId: string;
  content: string;
  image: string;
  data: ButlerData | null;
  created: string;
}

export enum CHARACTER_ENUM {
  'character' = 'character',
  'potato' = 'potato',
}

export interface IPotatoTime {
  activityTime: number;
  plantTime: number | null;
}

export interface ICollection {
  id: number;
  inscribeAddress: string;
  collectionName: string;
  metadata: string;
  avatar: string;
  state: number;
  created: string;
  updated: string;
  inscriptionId: string;
}

export enum NETWORK_ENUM {
  FRACTAL_BITCOIN_TESTNET = 'FRACTAL_BITCOIN_TESTNET',
  FRACTAL_BITCOIN_MAINNET = 'FRACTAL_BITCOIN_MAINNET',
}

export enum ORDER_STATUS {
  'pending' = 'pending',
  'payment' = 'payment',
  'inscribing' = 'inscribing',
  'minted' = 'minted',
}

export enum STORAGE_MENU_ENUM {
  PATH_MENU = 'PATH_MENU', //装饰菜单栏
  NFT_MENU = 'NFT_MENU', //nft菜单
  PLAY_MENU = 'PLAY_MENU', //可以移动
  BAG_MENU = 'BAG_MENU', //背包菜单
  RECORDING_MENU = 'RECORDING_MENU', //录像菜单
}

export enum INVENTORY_TYPE_ENUM {
  equipment = 'equipment', //装备
  resource = 'resource', //耗材
  material = 'material', //材料
}

//背包物品
export interface IBagInventoryItem {
  userItemId: string;
  itemId: string;
  name: string;
  description: string;
  icon: string;
  type: INVENTORY_TYPE_ENUM;
  maxDurability: number;
  isNew: boolean;
  shortcut: null | string; //快捷键
  tag: string;
  quantity: number;
  currentDurability: number; //耐久度
}

// 可合成的物品
export interface ISyntheticItem {
  itemId: string;
  name: string;
  description: string;
  icon: string;
  canSynthesize: number;
  synthetics: {
    itemId: string;
    name: string;
    description: string;
    icon: string;
    currentQuantity: number;
    needQuantity: number;
  }[];
}

export interface IVTT {
  start_time: number;
  end_time: number;
  text: string;
}

export interface IBindInfo {
  requestId: string;
  data: ButlerData | null;
  cdn: string;
  content: string;
  subtitle: IVTT[];
  video: string;
  domain: string;
}

export interface IAssistant {
  _id: string;
  fileId: string;
  fileUrl: string;
  title: string;
  webLink: string;
  fileType: 'url' | 'document';
}

export interface IAssistantQuestionHistory {
  _id: string;
  prompt: string;
  domain: string;
  content: string;
  address: string;
  fileIds: string[];
  __v: 0;
  files: ({
    _id: string;
    fileId: string;
    fileUrl: string;
    title: string;
    webLink: string;
    fileType: 'url' | 'document';
  } | null)[];
}

export interface IAssistantAnswer {
  content: string;
  soundUrl: string;
  citationFiles: {
    fileId: string;
    fileUrl: string;
    title: string;
    webLink: string;
  }[];
}

export enum RewardType {
  potato = 'potato',
  TheLonelyBit = 'TheLonelyBit',
  wangcai = 'wangcai',
  sQUAQ___000 = 'sQUAQ___000',
  sPIZZA___000 = 'sPIZZA___000',
}

export enum EasterEggType {
  // 顺序伐木
  ORDER_TREE = 'order_tree',
  // 打地鼠
  WHACK_A_MOLE = 'whack_a_mole',
  // 披萨狂潮
  PIZZA_RUSH = 'pizza_rush',
}
