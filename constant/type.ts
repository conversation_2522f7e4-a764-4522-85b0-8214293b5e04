export enum FATHER_TYPE_ENUM {
  Action = 'Action',
  Pet = 'Pet',
  Pants = 'Pants',
  Hat = 'Headwear',
  Shoes = 'Shoes',
  Shirt = 'Shirt',
  Gloves = 'Gloves',
}

export enum PATH_ID_ENUM {
  actionId = 'actionId',
  petId = 'petId',
  shirtId = 'shirtId',
  pantsId = 'pantsId',
  hatId = 'hatId',
  shoesId = 'shoesId',
  glovesId = 'glovesId',
  shirtTextureId = 'shirtTextureId',
  shirtColor = 'shirtColor',
}

export interface IAvatarMetadata {
  actionId: string;
  shirtId: string;
  pantsId: string;
  hatId: string;
  shoesId: string;
  glovesId: string;
  shirtTextureId: string;
  shirtColor: string;
  petId: string;
}

export type UsableAddressDataType = {
  [key in PATH_ID_ENUM]: {
    tag: string;
    // 剩余时间，单位毫秒
    expireAt: number;
    inscriptionId: string;
  }[];
};

export interface IFatherInscription {
  type: FATHER_TYPE_ENUM;
  pathIdKey: PATH_ID_ENUM;
  inscriptionId: string;
  childrenInscription: IChildrenInscription[];
  texture?: {
    pathIdKey: string;
  };
  color?: {
    pathIdKey: string;
  };
}

export interface IChildrenInscription {
  inscriptionId: string;
  metadata: {
    collection: string;
    version: string;
    icon: string;
  };
}

export interface IInitDefaultOptions {
  onLoad: Function;
  transparentBackground: boolean;
}

export interface ICollection {
  id: number;
  inscribeAddress: string;
  collectionName: string;
  metadata: string;
  avatar: string;
  state: number;
  created: string;
  updated: string;
  inscriptionId: string;
}
