######### avatar相关 ########
# avatar ord浏览器根域名 空 | https://ordinals-testnet.fractalbitcoin.io | http://localhost | https://ordinals.fractalbitcoin.io
ORD_SERVER=https://ordinals-testnet.fractalbitcoin.io

# avatar使用指定配置 LOCAL_TEST_CONFIG | CHAIN_TEST_CONFIG | CHAIN_PRO_CONFIG
USE_CONFIG=CHAIN_PRO_CONFIG

######### 官网相关 ########
# NFT图加载 浏览器根域名 空 | https://ordinals-testnet.fractalbitcoin.io | http://localhost | https://ordinals.fractalbitcoin.io
ORD_NFT_IMG_SERVER=https://ordinals.fractalbitcoin.io

# 铭文版本
AVATAR_VERSION=v0.3.2

# 连接unisat钱包的BTC网络， FRACTAL_BITCOIN_MAINNET | FRACTAL_BITCOIN_TESTNET
APP_NETWORK=FRACTAL_BITCOIN_MAINNET

# 后端接口地址 https://alpha-api.satworld.io | https://alpha-api-testnet.satworld.io | https://alpha-api-pre.satworld.io
REQUEST_URL=https://alpha-api-pre.satworld.io

# 线上域名地址 https://alpha.satworld.io | https://pre-version.satworld.io | https://beta-version.satworld.io
DOMAIN=https://alpha.satworld.io

# 分享地址
SHARE_DOMAIN=https://share.satworld.xyz

# cdn根域名 + 根目录 空 | https://static.satworld.io
CDN_SERVER=https://static.satworld.io

# cdn资源版本号
CDN_VERSION=v0.0.1

# cdn配置版本号
CDN_CONFIG_VERSION=pre-version

# AI接口地址 https://ai-pre.satworld.xyz
AI_REQUEST_URL=https://ai-pre.satworld.xyz

# 环境参数 test(测试服) | test1(测试服) | release(预发布服) | online(正式服)
ENVIRONMENT=test

# 本地localhost测试需要添加此字段 (用于本地获取owner 跳转 权限判断等，【【部署线上需要去掉】】)
LOCAL_HOSTS_DOMAIN=3.uniworlds.xyz

# 后端背包系统接口地址 https://alpha-gapi-pre.satworld.io
GAME_REQUEST_URL=https://alpha-gapi-pre.satworld.io

# 长连接服务地址  wss://socket.satworld.io｜wss://socket-test.satworld.io｜ws://localhost:3000
GAME_SOCKET_URL=wss://socket.satworld.io