/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  pageExtensions: ['mdx', 'md', 'jsx', 'js', 'tsx', 'ts'],
  productionBrowserSourceMaps: false,
  exportPathMap: async function (defaultPathMap) {
    return {
      ...defaultPathMap,
      '/': { page: '/' },
    };
  },
  images: {
    unoptimized: true,
  },
  assetPrefix: './',
  basePath: '',
  env: {
    ORD_SERVER: process.env.ORD_SERVER,
    USE_CONFIG: process.env.USE_CONFIG,
    ORD_NFT_IMG_SERVER: process.env.ORD_NFT_IMG_SERVER,
  },
  webpack: function (config, options) {
    config.experiments = {
      asyncWebAssembly: true,
      topLevelAwait: true,
      layers: true,
    };
    config.optimization.splitChunks = {
      cacheGroups: {
        default: false,
        vendors: false,
      },
    };
    config.optimization.runtimeChunk = false;
    config.module.rules.push({
      test: /\.(ttf|woff|woff2|eot|otf)$/,
      use: [
        options.defaultLoaders.babel,
        {
          loader: 'url-loader',
        },
      ],
    });
    config.module.rules.push({
      test: /\.(glb)$/,
      use: [
        options.defaultLoaders.babel,
        {
          loader: 'url-loader',
        },
      ],
    });
    return config;
  },
  compiler: {
    styledComponents: true,
  },
};
module.exports = nextConfig;
