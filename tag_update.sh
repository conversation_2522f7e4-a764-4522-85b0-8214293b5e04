#!/bin/bash

# 检查参数数量
if [ "$#" -ne 2 ]; then
  echo "Usage: $0 The number of parameters does not match."
  exit 1
fi

# 参数赋值
BUILD_BRANCH=$1
TAG_NAME=$2
ROOT_PATH='/root/jenkins/satworld_tag'

git log -10 --pretty=format:"%h - %an, %ad : %s" --date=iso
git tag "$TAG_NAME"
git for-each-ref --sort=-creatordate --format='%(refname:short) %(creatordate)' refs/tags
git push origin --tags

cd "$ROOT_PATH/AvatarOrdinalsBrowser" || exit
# 更新AvatarOrdinalsBrowser子模块并安装依赖
git pull
git reset --hard HEAD
git checkout "$BUILD_TAG"
git pull
git log -10 --pretty=format:"%h - %an, %ad : %s" --date=iso
git tag "$TAG_NAME"
git for-each-ref --sort=-creatordate --format='%(refname:short) %(creatordate)' refs/tags
git push origin --tags

exit 0