import * as THREE from 'three';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

let index = 0;
export default function createUseGame() {
  index += 1;
  const _index = index;
  return  /* @__PURE__ */ create(
    //@ts-ignore
    /* @__PURE__ */ subscribeWithSelector<UseGameState>((set, get) => {
      return {
        /**
         * Point to move point
         */
        moveToPoint: null as (THREE.Vector3 | null),

        /**
         * Point to transform point
         */
        transformPoint: null as (THREE.Vector3 | null),

        /**
         * Point camera point
         */
        cameraDirection: null as (THREE.Vector3 | null),

        /**
         * Character animations state manegement
         */
        // Initial animation
        curAnimation: null as (string | null),
        lockAnimation: null as (string | null),
        isWeary: false,

        animationSet: {} as AnimationSet,

        initializeAnimationSet: (animationSet: AnimationSet, focus = false) => {
          set((state) => {
            const oldIdle = state.animationSet.idle;
            if (focus || Object.keys(state.animationSet).length === 0) {
              if (oldIdle === state.curAnimation) {
                setTimeout(() => {
                  set((state) => {
                    if (oldIdle === state.curAnimation)
                      return { curAnimation: state.animationSet.idle };
                    return {};
                  });
                }, 1);
              }
              return { animationSet };
            }
            return {};
          });
        },

        reset: () => {
          set((state) => {
            if (state.lockAnimation && state.lockAnimation.length > 0)
              return { curAnimation: state.lockAnimation };
            return { curAnimation: state.animationSet.idle };
          });
        },

        idle: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.jumpIdle) {
              return { curAnimation: state.animationSet.jumpLand };
            } else if (
              state.curAnimation !== state.animationSet.action1 &&
              state.curAnimation !== state.animationSet.action2 &&
              state.curAnimation !== state.animationSet.action3 &&
              state.curAnimation !== state.animationSet.action4 &&
              (!state.curAnimation || !state.curAnimation.includes('Action_') ||
                state.curAnimation === state.animationSet.run ||
                state.curAnimation === state.animationSet.walk ||
                state.curAnimation === state.animationSet.wearyWalk)
            ) {
              if (state.isWeary) {
                return { curAnimation: state.animationSet.wearyIdle };
              }
              return { curAnimation: state.animationSet.idle };
            }
            return {};
          });
        },

        walk: () => {
          set((state) => {
            if (state.curAnimation !== state.animationSet.action4) {
              if (state.isWeary) {
                return { curAnimation: state.animationSet.wearyWalk };
              }
              return { curAnimation: state.animationSet.walk };
            }
            return {};
          });
        },

        run: () => {
          set((state) => {
            if (state.curAnimation !== state.animationSet.action4) {
              return { curAnimation: state.animationSet.run };
            }
            return {};
          });
        },

        jump: () => {
          set((state) => {
            return { curAnimation: state.animationSet.jump };
          });
        },

        jumpIdle: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.jump) {
              return { curAnimation: state.animationSet.jumpIdle };
            }
            return {};
          });
        },

        jumpLand: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.jumpIdle) {
              return { curAnimation: state.animationSet.jumpLand };
            }
            return {};
          });
        },

        fall: () => {
          set((state) => {
            return { curAnimation: state.animationSet.fall };
          });
        },

        action1: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.idle) {
              return { curAnimation: state.animationSet.action1 };
            }
            return {};
          });
        },

        action2: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.idle) {
              return { curAnimation: state.animationSet.action2 };
            }
            return {};
          });
        },

        action3: () => {
          set((state) => {
            if (state.curAnimation === state.animationSet.idle) {
              return { curAnimation: state.animationSet.action3 };
            }
            return {};
          });
        },

        action4: () => {
          set((state) => {
            if (
              state.curAnimation === state.animationSet.idle ||
              state.curAnimation === state.animationSet.walk ||
              state.curAnimation === state.animationSet.run
            ) {
              return { curAnimation: state.animationSet.action4 };
            }
            return {};
          });
        },

        /**
         * Additional animations
         */
        // triggerFunction: ()=>{
        //    set((state) => {
        //        return { curAnimation: state.animationSet.additionalAnimation };
        //    });
        // }

        /**
         * Set/get point to move point
         */
        setMoveToPoint: (point: THREE.Vector3, ignoreAction = true) => {

          set((state) => {
            if (
              ignoreAction ||
              state.curAnimation === state.animationSet.idle ||
              state.curAnimation === state.animationSet.walk ||
              state.curAnimation === state.animationSet.run
            ) {
              return { moveToPoint: point };
            }
            return {};
          });
        },

        getMoveToPoint: () => {
          return {
            moveToPoint: get().moveToPoint,
          };
        },
        /**
         * Set/get point to move point
         */
        setTransformPoint: (point: THREE.Vector3) => {
          set(() => {
            return { transformPoint: point };
          });
        },

        getTransformPoint: () => {
          return {
            transformPoint: get().transformPoint,
          };
        },
        /**
         * Set/get point to move point
         */
        setFollowCamDirection: (direction: THREE.Vector3) => {
          set(() => {
            return { cameraDirection: direction };
          });
        },

        getFollowCamDirection: () => {
          return {
            cameraDirection: get().cameraDirection,
          };
        },

        getCurAnimation: () => {
          return { curAnimation: get().curAnimation };
        },

        setCurAnimation: (animation: string) => {
          set((state) => {
            return { curAnimation: animation };
          });
        },
        setLockAnimation: (animation: string) => {
          set((state) => {
            return { lockAnimation: animation };
          });
        },
        setWeary: (isWeary: boolean) => {
          const curAnimation = get().curAnimation
          const animationSet = get().animationSet
          let animation = curAnimation
          if(isWeary && curAnimation === animationSet.idle){
            animation = animationSet.wearyIdle as string
          }
          if (!isWeary && curAnimation === animationSet.wearyIdle) {
            animation = animationSet.idle as string
          }
          set((state) => {
            return animation?{ isWeary, curAnimation: animation }:{ isWeary };
          });
        },
        getIndex: () => {
          return _index;
        },
      };
    }),
  );
}

export type AnimationSet = {
  default?: string;
  idle?: string;
  wearyIdle?: string;
  walk?: string;
  wearyWalk?: string;
  run?: string;
  jump?: string;
  jumpIdle?: string;
  jumpLand?: string;
  fall?: string;
  // Currently support four additional animations
  action1?: string;
  action2?: string;
  action3?: string;
  action4?: string;
};

export type UseGameState = {
  moveToPoint: THREE.Vector3;
  transformPoint: THREE.Vector3;
  cameraDirection: THREE.Vector3;
  curAnimation: string;
  lockAnimation: string;
  isWeary: boolean;
  animationSet: AnimationSet;
  initializeAnimationSet: (animationSet: AnimationSet, force?: boolean) => void;
  reset: () => void;
  setMoveToPoint: (point: THREE.Vector3 | null, ignoreAction?: boolean) => void;
  getMoveToPoint: () => {
    moveToPoint: THREE.Vector3;
  }
  setTransformPoint: (point: THREE.Vector3) => void;
  getTransformPoint: () => {
    transformPoint: THREE.Vector3;
  }
  setFollowCamDirection: (point: THREE.Vector3) => void;
  getFollowCamDirection: () => {
    transformPoint: THREE.Vector3;
  }
  getCurAnimation: () => {
    curAnimation: string
  }
  setCurAnimation: (animation: string) => void;
  setLockAnimation: (animation: string) => void;
  setWeary: (isWeary: boolean) => void;
  getIndex: () => number;
} & {
  [key in keyof AnimationSet]: () => void;
};
