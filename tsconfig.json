{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "baseUrl": ".",
    //    "typeRoots": ["./node_modules/@types", "./types"],
    "paths": {
      "@/*": ["./*"],
      "@root/*": ["./*"],
      "@rootComponents/*": ["./components/*"],
      "@rootHooks/*": ["./hooks/*"],
      "@rootUtils/*": ["./utils/*"],
      "@rootStyles/*": ["./styles/*"]
    },
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
