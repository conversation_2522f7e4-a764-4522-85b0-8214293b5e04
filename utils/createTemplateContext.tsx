import {
  createContext,
  Dispatch,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';

// 1. 泛型化 State 和 Context
interface IContextState {
  [key: string]: any;
}

export interface IContext<S = IContextState> {
  contextDispatch: Dispatch<UnionActionType<S>>;
  subscribe: (args: any) => any;
  getContextState: () => S;
}

const CONTEXT_ACTION_TYPE = {
  UPDATE: 'UPDATE',
  RESET: 'RESET',
} as const;

interface IRestContextState<S> {
  type: typeof CONTEXT_ACTION_TYPE.RESET;
}

type UpdateContextStateParams<S> = Partial<S> | ((prev: S) => Partial<S>);

interface IUpdateContextState<S> {
  type: typeof CONTEXT_ACTION_TYPE.UPDATE;
  payload: UpdateContextStateParams<S>;
}

type UnionActionType<S> = IRestContextState<S> | IUpdateContextState<S>;

function defaultContextReducer<S>(state: S, action: UnionActionType<S>): S {
  const { type } = action;
  const payload = (action as IUpdateContextState<S>).payload;

  switch (type) {
    case CONTEXT_ACTION_TYPE.UPDATE:
      if (typeof payload === 'object') {
        return { ...state, ...payload };
      } else if (typeof payload === 'function') {
        const newState = payload(state);
        return { ...state, ...newState };
      }
      return state;
    case CONTEXT_ACTION_TYPE.RESET:
      return state;
    default:
      return state;
  }
}

export function createTemplateContext<S>(initialState: S, contextReducer = defaultContextReducer) {
  const defaultTemplateContext: IContext<S> = {
    getContextState: () => initialState,
    contextDispatch: () => false,
    subscribe: () => false,
  };
  const TemplateContext = createContext<IContext<S>>(defaultTemplateContext);

  const TemplateContextProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [contextState, dispatch] = useReducer(contextReducer, initialState);

    const latestStateRef = useRef<S>();
    latestStateRef.current = contextState as S;

    const subscribersRef = useRef<((...args: any[]) => void)[]>([]);

    useEffect(() => {
      subscribersRef.current.forEach((sub) => sub());
    }, [contextState]);

    const memoContextValue = useMemo(() => {
      return {
        contextDispatch: dispatch,
        subscribe: (cb: any) => {
          subscribersRef.current.push(cb);
          return () => {
            subscribersRef.current = subscribersRef.current.filter((item) => item !== cb);
          };
        },
        getContextState: () => latestStateRef.current as S,
      };
    }, []);

    return <TemplateContext.Provider value={memoContextValue}>{children}</TemplateContext.Provider>;
  };

  function useContextDispatch() {
    const context = useContext(TemplateContext);
    return context.contextDispatch;
  }

  function useContextSelector<T>(selector: (state: S) => T) {
    const [, forceRender] = useState({});
    const store = useContext(TemplateContext);

    const selectedStateRef = useRef<T>();
    selectedStateRef.current = selector(store.getContextState());

    const checkForUpdates = useCallback(() => {
      const newState = selector(store.getContextState());
      if (newState !== selectedStateRef.current) forceRender({});
    }, [store]);

    useEffect(() => {
      const subscription = store.subscribe(checkForUpdates);
      return () => subscription();
    }, [store, checkForUpdates]);

    return selectedStateRef.current as T;
  }

  return {
    TemplateContext,
    TemplateContextProvider,
    useContextDispatch,
    useContextSelector,
  };
}
