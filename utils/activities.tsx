import dayjs from 'dayjs';

// 将时间戳转换成小时数
export const getHours = (timestamp: number) => {
  return dayjs(timestamp).diff(dayjs(), 'hours');
};

// 将时间戳转换成多少天多少小时
export const getDaysAndHours = (timestamp: number) => {
  const days = dayjs(timestamp).diff(dayjs(), 'days');
  const totalHours = dayjs(timestamp).diff(dayjs(), 'hours');
  const hours = totalHours - days * 24; // 计算剩余小时数
  return `${days}D${hours}h`;
};

// 将时间戳转换成分钟数
export const getMinutes = (timestamp: number) => {
  return dayjs(timestamp).diff(dayjs(), 'minutes');
};

// 将时间戳转换成是时分秒格式的时间戳ms
export const getHoursMinutesSeconds = (
  timestamp: number,
  format: 'HH:mm:ss' | 'mm:ss' = 'HH:mm:ss'
) => {
  const hours = dayjs(timestamp).diff(dayjs(), 'hours');
  const minutes = dayjs(timestamp).diff(dayjs(), 'minutes');
  const seconds = dayjs(timestamp).diff(dayjs(), 'seconds');
  if (format === 'HH:mm:ss') {
    return `${hours}h${minutes}m${seconds}s`;
  } else {
    return `${minutes}m${seconds}s`;
  }
};

/**
 * 传入活动开启的时间戳
 * 获取当前时间戳
 * 两者进行相减，返回结果
 * 如果结果大于0，则表示活动未开启
 * 如果结果小于0，则表示活动已开启
 */
export const getActivityTime = (timestamp: number) => {
  return dayjs(timestamp).diff(dayjs(), 'minutes');
};

/**
 * 传入活动结束的时间戳
 * 获取当前时间戳
 * 两者进行相减，返回结果
 * 如果结果大于0，则表示活动未结束
 * 如果结果小于0，则表示活动已结束
 */
export const getActivityEndTime = (timestamp: number) => {
  return dayjs(timestamp).diff(dayjs(), 'minutes');
};

/**
 * 判断活动阶段是否开启
 * @param tab 活动阶段配置
 * @returns boolean 是否开启
 */
export function isTabActive(tab: { type: string; start_time: number; end_time: number }): boolean {
  const currentTime = Date.now(); // 获取当前时间的毫秒时间戳

  // 判断当前时间是否在活动时间范围内
  return currentTime >= tab.start_time && currentTime < tab.end_time;
}

/**
 * 获取当前活跃的阶段
 * @param tabs 所有阶段配置
 * @returns 当前活跃的阶段，如果没有则返回null
 */
export function getCurrentActiveTab(
  tabs: Array<{
    type: string;
    start_time: number;
    end_time: number;
  }>
): {
  type: string;
  start_time: number;
  end_time: number;
} | null {
  for (const tab of tabs) {
    if (isTabActive(tab)) {
      return tab;
    }
  }
  return null;
}

/**
 * 判断特定类型的阶段是否活跃
 * @param tabs 所有阶段配置
 * @param type 阶段类型
 * @returns boolean 是否活跃
 */
export function isTabTypeActive(
  tabs: Array<{
    type: string;
    start_time: number;
    end_time: number;
  }>,
  type: string
): boolean {
  const tab = tabs.find((t) => t.type === type);
  return tab ? isTabActive(tab) : false;
}

/**
 * 获取活动剩余时间（毫秒）
 * @param tab 活动阶段配置
 * @returns number 剩余时间（毫秒），如果活动未开始或已结束则返回0
 */
export function getRemainingTime(tab: {
  type: string;
  start_time: number;
  end_time: number;
}): number {
  const currentTime = Date.now();

  // 活动未开始
  if (currentTime < tab.start_time) {
    return 0;
  }

  // 活动进行中
  if (currentTime >= tab.start_time && currentTime < tab.end_time) {
    return tab.end_time - currentTime;
  }

  // 活动已结束
  return 0;
}
