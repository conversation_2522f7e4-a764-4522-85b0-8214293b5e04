// 安全的客户端事件触发工具
import { clearEvent, emitItemCollected } from '@/store/events';
import store from '@/store';

// 安全检查函数
const isClient = () => typeof window !== 'undefined';

// 事件API
export const Events = {
  // 物品收集事件
  emitItemCollected: (itemImage: string, count: number) => {
    if (isClient()) {
      store.dispatch(emitItemCollected({ itemImage, count }));
    }
  },

  // 清除事件
  clearEvent: (eventType: string) => {
    if (isClient()) {
      store.dispatch(clearEvent(eventType));
    }
  },
};
