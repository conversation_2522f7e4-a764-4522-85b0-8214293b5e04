/**
 * 签名消息 class
 * unisat 签名 window.unisat
 * okx 签名 window.okxwallet
 *
 */

import { SUPPORT_WALLET_ENUM } from '../constant/type';

class SignMessage {
  /**
   * 签名消息
   * @param btcWallet 钱包类型
   * @param message 需要签名的消息
   * @returns 签名结果
   */
  static async signMessage(
    btcWallet: SUPPORT_WALLET_ENUM,
    message: string,
    signMessageType: 'ecdsa' | 'bip322-simple' = 'bip322-simple'
  ): Promise<string> {
    try {
      if (btcWallet === SUPPORT_WALLET_ENUM.unisat) {
        if (!window.unisat) {
          throw new Error('Unisat wallet not installed');
        }
        return await window.unisat.signMessage(message, signMessageType);
      } else if (btcWallet === SUPPORT_WALLET_ENUM.okx) {
        if (!window.okxwallet || !window.okxwallet.fractalBitcoin) {
          throw new Error('OKX wallet not installed');
        }
        // 注意: OKX 钱包需要指定签名算法为 'ecdsa'
        return await window.okxwallet.fractalBitcoin.signMessage(message, signMessageType);
      } else {
        throw new Error('Unsupported wallet type');
      }
    } catch (error) {
      console.error('Sign message error:', error);
      throw error;
    }
  }

  static async multiSignMessage(
      btcWallet: SUPPORT_WALLET_ENUM,
      messages: string[],
      signMessageType: 'ecdsa' | 'bip322-simple' = 'bip322-simple'
  ): Promise<string[]> {
    try {
      if (btcWallet === SUPPORT_WALLET_ENUM.unisat) {
        if (!window.unisat) {
          throw new Error('Unisat wallet not installed');
        }
        const signMessage = messages.map((msg) => {
          return {
            text: msg,
            type: signMessageType,
          }
        });
        return await window.unisat.multiSignMessage(signMessage);
      } else if (btcWallet === SUPPORT_WALLET_ENUM.okx) {
        const sigs: string[] = [];
        for (const msg of messages) {
          sigs.push(await window.okxwallet.fractalBitcoin.signMessage(msg, signMessageType));
        }
        return sigs;
      } else {
        throw new Error('Unsupported wallet type');
      }
    } catch (e: any) {
      console.error(e.message);
      throw e;
    }
  }
}

export default SignMessage;
