import { ComponentType } from 'react';

interface WithNonSelectableProps {
  style?: React.CSSProperties;
}

export const withNonSelectable = <T extends object>(WrappedComponent: ComponentType<T>) => {
  const NonSelectable: React.FC<T & WithNonSelectableProps> = (props) => {
    const nonSelectableStyle: React.CSSProperties = {
      userSelect: 'none',
      pointerEvents: 'none',
      ...props.style,
    };

    return <WrappedComponent {...props} style={nonSelectableStyle} />;
  };

  return NonSelectable;
};
