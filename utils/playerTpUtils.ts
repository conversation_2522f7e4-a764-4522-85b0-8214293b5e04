import { SCENE_TYPE } from '@/constant/type';
import { AppG<PERSON><PERSON><PERSON><PERSON>ey, GetMyPlayer } from '@/world/Character/MyPlayer';
import { LoadingPageType } from '@/world/Config/DoorConfig';
import { NpcConfig } from '@/world/Config/NpcConfig';
import GlobalSpaceEvent, { CharacterType, GlobalDataKey } from '@/world/Global/GlobalSpaceEvent';
import * as THREE from 'three';

export default function playerTpUtil(npcId: number) {
  const myPlayer = GetMyPlayer();

  if (npcId) {
    NpcConfig.getInstance().getData(npcId, (data) => {
      const transformPos = new THREE.Vector3(
        data.transformPosition[0],
        data.transformPosition[1] + 1,

        data.transformPosition[2]
      );
      const direction = transformPos
        .clone()
        .sub(new THREE.Vector3(data.position[0], data.position[1] + 1, data.position[2]));
      myPlayer.callAppApi(AppGameApiKey.setLoaderType, LoadingPageType.Default);

      GlobalSpaceEvent.SetDataValue(GlobalDataKey.TransformData, {
        characterType: CharacterType.Player,
        position: transformPos,
        sceneType: data.transformMapId as SCENE_TYPE,
        camDirection: direction,
      });
    });
  }
}
