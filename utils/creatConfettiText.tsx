import confetti from 'canvas-confetti';

// 创建烟花形状的emoji
function createFireworkEmoji() {
  return confetti.shapeFromText({ text: '🎆', scalar: 1 });
}

// 创建彩虹形状的emoji
function createRainbowEmoji() {
  return confetti.shapeFromText({ text: '🌈', scalar: 1 });
}

// 创建笑脸形状的emoji
function createSmileyEmoji() {
  return confetti.shapeFromText({ text: '😊', scalar: 1 });
}

// 创建礼物形状的emoji
function createGiftEmoji() {
  return confetti.shapeFromText({ text: '🎁', scalar: 1 });
}

// 创建星星形状的emoji
function createStarEmoji() {
  return confetti.shapeFromText({ text: '⭐', scalar: 1 });
}

// 创建气球形状的emoji
function createBalloonEmoji() {
  return confetti.shapeFromText({ text: '🎈', scalar: 1 });
}

// 创建爱心形状的emoji
function createHeartEmoji() {
  return confetti.shapeFromText({
    text: '❤️',
    scalar: 1,
  });
}

// 创建派对形状的emoji
function createPartyEmoji() {
  return confetti.shapeFromText({ text: '🎉', scalar: 1 });
}

// 创建闪烁形状的emoji
function createSparkleEmoji() {
  return confetti.shapeFromText({ text: '✨', scalar: 1 });
}

// 创建❄️形状的emoji
function createSnowflakeEmoji() {
  return confetti.shapeFromText({ text: '❄️', scalar: 1 });
}

export {
  createFireworkEmoji,
  createRainbowEmoji,
  createSmileyEmoji,
  createGiftEmoji,
  createStarEmoji,
  createBalloonEmoji,
  createHeartEmoji,
  createPartyEmoji,
  createSparkleEmoji,
  createSnowflakeEmoji,
};
