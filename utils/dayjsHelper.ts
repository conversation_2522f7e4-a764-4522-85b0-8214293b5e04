import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import duration from 'dayjs/plugin/duration';
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);

/**
 * 计算距离指定 UTC+8 时间还差多少毫秒
 * @param {string} targetTime - 目标时间（格式 "HH:mm:ss"）
 * @returns {number} 剩余毫秒
 */
export function getUTC8MidnightTimestamp(targetTime = '00:00:00') {
  const nowUTC8 = dayjs().tz('Asia/Shanghai');
  const [hours, minutes, seconds] = targetTime.split(':').map(Number);
  const todayMidnightUTC8 = nowUTC8.startOf('day').hour(hours).minute(minutes).second(seconds);
  let nextMidnight;

  if (nowUTC8.isAfter(todayMidnightUTC8)) {
    nextMidnight = todayMidnightUTC8.add(1, 'day');
  } else {
    nextMidnight = todayMidnightUTC8;
  }

  return nextMidnight.diff(nowUTC8);
}

/**
 * @description 返回对应的 天 时 分 秒
 * @returns
 */
export function getMillisecondsTimeInfo(milliseconds: number) {
  const duration = dayjs.duration(milliseconds);
  const days = duration.days().toString().padStart(2, '0');
  const hours = duration.hours().toString().padStart(2, '0');
  const minutes = duration.minutes().toString().padStart(2, '0');
  const seconds = duration.seconds().toString().padStart(2, '0');

  return { days, hours, minutes, seconds };
}

export function formatMilliseconds(milliseconds: number) {
  const duration = dayjs.duration(milliseconds);
  const hours = duration.hours().toString().padStart(2, '0');
  const minutes = duration.minutes().toString().padStart(2, '0');
  const seconds = duration.seconds().toString().padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
}
