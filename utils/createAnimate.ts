import confetti from 'canvas-confetti';

// 随机数
function randomInRange(min: number, max: number) {
  return Math.random() * (max - min) + min;
}

interface AnimateOptions {
  duration?: number;
}

function createAnimate(options: AnimateOptions = {}) {
  const duration = options.duration ?? 15 * 1000;
  const animationEnd = Date.now() + duration;
  let skew = 1;

  // 自定义 pizza 路径
  const pizzaPaths = [
    'M12.9753 50.735C15.7873 52.0953 19.6147 49.2613 21.177 47.6743C36.018 35.4343 34.0651 24.5469 31.7218 18.0856C29.3785 11.6242 11.413 -0.958414 9.06972 0.0618001C6.7264 1.08201 -4.9902 9.92387 2.43031 10.9441C9.85082 11.9643 21.177 18.7657 24.3014 24.5469C27.4258 30.3281 21.177 33.3888 16.8809 36.7895C12.5848 40.1902 7.11704 42.9108 6.33593 44.2711C5.55483 45.6314 9.46036 49.0346 12.9753 50.735Z',
    'M20.4376 5.31427C30.1181 -4.67656 32.8268 2.1958 34.0606 3.75347C41.2309 12.8055 34.0606 16.2405 26.8903 21.2351C19.7199 26.2297 20.4376 30.5999 15.7768 31.2242C11.116 31.8485 4.30512 26.5418 0.720126 23.108C-2.86487 19.6743 7.44017 18.7284 20.4376 5.31427Z',
    'M13.8096 1.38153C16.9644 -1.86494 19.0985 1.38286 20.9796 3.2545C25.9982 8.25141 37.4702 20.1135 35.6777 28.854C33.8852 37.5945 24.9227 42.2769 18.1112 44.1499C11.2997 46.0228 8.79024 49.1445 12.3757 55.6998C15.9611 62.2552 21.3377 60.6944 22.7717 61.3187C23.9189 61.8182 23.7277 62.7755 23.4887 63.1917C20.8597 65.689 15.3866 70.8708 14.5262 71.62C13.6658 72.3692 12.734 71.9322 12.3757 71.62C3.05467 67.2498 -2.68132 56.3241 1.26218 47.2715C5.20567 38.2188 10.9417 37.2823 19.1871 35.0972C27.4326 32.9121 28.8662 31.977 29.5832 30.104C30.3002 28.231 25.2812 22.9243 19.9037 19.8027C16.6772 17.9297 11.6586 15.7433 8.43213 11.9973C5.20564 8.25142 9.86613 5.43962 13.8096 1.38153Z',
    'M12.5897 1.50445C9.2963 -0.416684 6.63496 -0.916615 4.69963 2.5345C-2.8477 15.9133 0.239504 21.0595 3.32705 21.0595C6.41459 21.0595 8.81601 17.9719 9.50214 14.8844C10.1883 11.7968 16.7064 3.90588 12.5897 1.50445Z',
    'M36.7768 10.9779C35.679 7.959 12.5339 2.40142 1.09852 0C-5.41963 0 18.9377 19.5544 25.1127 21.2697C31.2878 22.985 38.149 14.7516 36.7768 10.9779Z',
    'M1.75166 17.3294C-2.3101 15.153 1.75021 11.8897 3.3132 9.50908C3.83416 8.71561 5.65719 6.17646 8.78162 2.36767C12.6871 -2.39333 17.7637 1.00738 22.0598 4.06802C26.3558 7.12867 29.0897 7.46866 22.0597 17.3307C15.0298 27.1928 6.82885 20.05 1.75166 17.3294Z',
    'M28.1419 3.28906C21.4753 -1.25631 8.44528 -0.119967 2.76358 1.01637C-4.81203 4.04502 5.10318 4.54537 9.64854 6.43927C12.8188 7.76022 18.3759 14.7495 20.3339 18.2764C20.7518 19.029 21.5257 19.5782 22.3866 19.5782H38.5421C40.0288 19.5782 40.9713 18.027 40.1967 16.758C37.724 12.7067 33.3861 6.86466 28.1419 3.28906Z',
  ];
  const pizzaColors = ['#35C0FF', '#2FF4A5', '#FFA616', '#FF663C'];

  function frame() {
    const timeLeft = animationEnd - Date.now();
    const ticks = Math.max(200, 500 * (timeLeft / duration));
    skew = Math.max(0.8, skew - 0.001);

    // 1. emoji 粒子
    confetti({
      particleCount: 1, // 粒子数量
      startVelocity: 0, // 初始速度
      ticks: ticks, // 帧数
      // 随机位置
      origin: {
        x: Math.random(),
        y: Math.random() * skew - 0.2,
      },
      // 下落速度
      gravity: randomInRange(0.8, 1.2),
      // 缩放大小
      scalar: randomInRange(2, 2.5),
      // 漂移
      drift: randomInRange(-0.4, 0.4),
      // 形状
      shapes: [confetti.shapeFromText({ text: '🍕', scalar: 2 })],
    });

    // 2. 多色自定义 path 粒子
    for (let i = 0; i < pizzaPaths.length; i++) {
      confetti({
        particleCount: 1,
        startVelocity: 0,
        ticks: ticks,
        origin: {
          x: Math.random(),
          y: Math.random() * skew - 0.2,
        },
        gravity: randomInRange(0.8, 1.2),
        scalar: randomInRange(1.8, 2),
        drift: randomInRange(-0.4, 0.4),
        shapes: [confetti.shapeFromPath({ path: pizzaPaths[i] })],
        colors: [pizzaColors[i % pizzaColors.length]],
      });
    }

    if (timeLeft > 0) {
      requestAnimationFrame(frame);
    }
  }

  frame();
}

export default createAnimate;
