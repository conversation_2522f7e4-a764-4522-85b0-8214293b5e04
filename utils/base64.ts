// Base64 编码（支持 JSON 对象或字符串）
function encodeJSONToBase<PERSON>(data: any) {
  const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
  return Buffer.from(jsonString).toString('base64');
}

// Base64 解码（返回 JavaScript 对象）
function decodeBase64ToJSON(base64Str: string) {
  try {
    const decodedString = Buffer.from(base64Str, 'base64').toString('utf-8');
    return JSON.parse(decodedString);
  } catch (e) {
    console.error('解码失败:', e);
    return null;
  }
}

function decodeBase64ToString(base64Str: string) {
  try {
    const decodedString = Buffer.from(base64Str, 'base64').toString('utf-8');
    return decodedString;
  } catch (e) {
    console.error('解码失败:', e);
    return null;
  }
}

export { encodeJSONToBase64, decodeBase<PERSON>ToJSON, decodeBase<PERSON>ToString };
