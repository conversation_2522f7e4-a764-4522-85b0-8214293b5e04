import SHA256 from 'crypto-js/sha256';

/**
 * 客户端AES加密工具
 */
export class ClientAESCrypto {
  /**
   * 从用户ID派生密钥（注意：必须与服务端使用相同算法）
   * @param {string} userId - 用户ID
   * @param {string} salt - 盐值（应该与服务端一致）
   * @returns {Uint8Array} - 密钥
   */
  static async deriveKeyFromUserId(userId: string, salt: string) {
    // 使用Web Crypto API的PBKDF2
    const encoder = new TextEncoder();
    const userIdBuffer = encoder.encode(userId);
    const saltBuffer = encoder.encode(salt);

    // 导入密钥材料
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      userIdBuffer,
      { name: 'PBKDF2' },
      false,
      ['deriveBits', 'deriveKey']
    );

    // 派生AES-CBC密钥
    const key = await window.crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: saltBuffer,
        iterations: 10000,
        hash: 'SHA-256',
      },
      keyMaterial,
      { name: 'AES-CBC', length: 256 },
      true,
      ['encrypt', 'decrypt']
    );

    // 导出原始密钥字节
    const rawKey = await window.crypto.subtle.exportKey('raw', key);
    return new Uint8Array(rawKey);
  }

  /**
   * 加密数据并转换为Base64格式
   * @param {string} plaintext - 要加密的明文
   * @param {Uint8Array|CryptoKey} key - 加密密钥
   * @returns {Promise<string>} - Base64编码的加密数据（包含IV）
   */
  static async encrypt(plaintext: string, key: Uint8Array | CryptoKey) {
    try {
      // 生成随机IV（16字节）
      const iv = window.crypto.getRandomValues(new Uint8Array(16));

      // 如果提供的是原始密钥字节，需要导入为CryptoKey
      let cryptoKey = key;
      if (key instanceof Uint8Array) {
        cryptoKey = await window.crypto.subtle.importKey(
          'raw',
          key,
          { name: 'AES-CBC', length: 256 },
          false,
          ['encrypt']
        );
      }

      // 将明文转换为ArrayBuffer
      const textEncoder = new TextEncoder();
      const plaintextBuffer = textEncoder.encode(plaintext);

      // 加密
      const encryptedData = await window.crypto.subtle.encrypt(
        { name: 'AES-CBC', iv },
        cryptoKey as CryptoKey,
        plaintextBuffer
      );

      // 将IV与加密数据合并
      const result = new Uint8Array(iv.length + encryptedData.byteLength);
      result.set(iv, 0); // 前16字节为IV
      result.set(new Uint8Array(encryptedData), iv.length); // 后面是加密数据

      // 转换为Base64
      return this._arrayBufferToBase64(result);
    } catch (error) {
      console.error('加密失败:', error);
      throw error;
    }
  }

  /**
   * 解密从服务器接收的数据
   * @param {string} encryptedBase64 - Base64编码的加密数据（包含IV）
   * @param {Uint8Array|CryptoKey} key - 解密密钥
   * @returns {Promise<string>} - 解密后的文本
   */
  static async decrypt(encryptedBase64: string, key: Uint8Array | CryptoKey) {
    try {
      // 解码Base64
      const encryptedData = this._base64ToArrayBuffer(encryptedBase64);

      // 提取IV（前16字节）
      const iv = encryptedData.slice(0, 16);

      // 提取加密数据
      const data = encryptedData.slice(16);

      // 如果提供的是原始密钥字节，需要导入为CryptoKey
      let cryptoKey = key;
      if (key instanceof Uint8Array) {
        cryptoKey = await window.crypto.subtle.importKey(
          'raw',
          key,
          { name: 'AES-CBC', length: 256 },
          false,
          ['decrypt']
        );
      }

      // 解密
      const decrypted = await window.crypto.subtle.decrypt(
        { name: 'AES-CBC', iv },
        cryptoKey as CryptoKey,
        data
      );

      // 转换为文本
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      // console.error("解密失败:", error);
      throw error;
    }
  }

  /**
   * 将Base64字符串转换为ArrayBuffer
   * @private
   */
  static _base64ToArrayBuffer(base64: string) {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * 将ArrayBuffer转换为Base64字符串
   * @param {ArrayBuffer|Uint8Array} buffer - 要转换的缓冲区
   * @returns {string} - Base64编码的字符串
   * @private
   */
  static _arrayBufferToBase64(buffer: ArrayBuffer | Uint8Array) {
    const bytes = buffer instanceof Uint8Array ? buffer : new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }
}

export async function sha256(address: string) {
  // 使用crypto-js/sha256计算哈希
  const hash = SHA256(address);
  // 转换为十六进制字符串
  return hash.toString();
}

// 使用示例
async function example() {
  // 用户ID（假设已经通过登录流程获得）
  const userId = '12345';

  // 注意：在实际使用中，盐值应该从安全的地方获取，而不是硬编码
  // 这里仅作为示例
  const salt = await sha256(userId);

  try {
    // 从用户ID派生密钥
    const key = await ClientAESCrypto.deriveKeyFromUserId(userId, salt);

    // 加密示例
    const dataToEncrypt = '这是需要加密的敏感数据';
    const encryptedBase64 = await ClientAESCrypto.encrypt(dataToEncrypt, key);

    // 将加密数据发送到服务器
    const sendResponse = await fetch('https://your-api.com/crypto/store', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: userId,
        encryptedData: encryptedBase64,
      }),
    });

    // 解密示例（本地验证）
    const decryptedData = await ClientAESCrypto.decrypt(encryptedBase64, key);

    // 从服务器获取加密数据
    const response = await fetch(`https://your-api.com/crypto/encrypt/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: '需要加密的数据' }),
    });

    const result = await response.json();

    if (result.success) {
      // 解密数据
      const decryptedData = await ClientAESCrypto.decrypt(result.encryptedData, key);

      // 使用解密后的数据进行业务处理
      // processData(decryptedData);
    } else {
      console.error('获取加密数据失败:', result.error);
    }
  } catch (error) {
    console.error('处理失败:', error);
  }
}
