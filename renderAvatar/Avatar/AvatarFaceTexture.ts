import * as THREE from 'three';

export default class AvatarFaceTexture {
  private path: string;
  private totalFrames: number;
  private texture: THREE.Texture | null = null;
  private currentFrame: number = 0;
  private rows: number;
  private cols: number;
  private speed: number;
  private totalDelta: number = 0;
  private finish: () => void;

  constructor(path: string, speed: number, rows = 1, cols = 1) {
    this.path = path;
    this.rows = rows;
    this.cols = cols;
    this.speed = speed;
    this.totalFrames = rows * cols;
    this.finish = () => {};
  }

  loadTexture(callback: (texture: THREE.Texture) => void, finish: () => void) {
    this.currentFrame = -1;
    this.totalDelta = 0;
    this.finish = finish;
    if (this.texture) {
      callback(this.texture);
      return;
    }
    const loader = new THREE.TextureLoader();
    loader.load(this.path, (texture: THREE.Texture) => {
      this.texture = texture;
      texture.flipY = false;
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(1 / this.cols, 1 / this.rows); // 每帧是纹理的 1/4
      texture.colorSpace = 'srgb';
      callback(texture);
    });
  }

  update(delta: number) {
    if (!this.texture) {
      return;
    }
    this.totalDelta += delta;
    if (this.totalDelta < this.speed) {
      return;
    }

    if (this.currentFrame >= this.totalFrames - 1) {
      this.finish();
      return;
    }
    this.totalDelta -= this.speed;
    this.currentFrame = (this.currentFrame + 1) % this.totalFrames;
    // 计算当前帧的行和列
    const row = Math.floor(this.currentFrame / this.cols);
    const col = this.currentFrame % this.cols;

    // 更新纹理偏移
    this.texture.offset.set(col / this.cols, row / this.rows); // 注意UV坐标从左下角开始
  }

  cancel() {
    this.finish = () => {};
  }
}
