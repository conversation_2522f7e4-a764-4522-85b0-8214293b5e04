import gsap from 'gsap';
import * as THREE from 'three';
import { GLTF, GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter';
import AvatarFace from './AvatarFace';
import { AvatarUtil } from '../AvatarUtil';

const idleAction = 'Action_00';
const runAction = 'Action_01';
const jumAction = 'Action_04';
const tauntActionList = ['Action_1002'];
const showAction = 'Action_1001';
const speakAction = 'Action_05';

export default class AvatarPetObject {
  mixer: THREE.AnimationMixer;
  sceneGroup: THREE.Group;
  face: AvatarFace | null = null;
  private petGroup: THREE.Group;
  private isMoving: boolean = false;
  private curAction: string = '';
  private actionMap: Map<string, THREE.AnimationAction> = new Map(); //action_01 出场动作 action_02 待机动作 action_03 走路动作
  private tauntTimer: NodeJS.Timeout | undefined = undefined;
  private pathTimer: NodeJS.Timeout | undefined = undefined;
  private actionSpeedMap: Map<string, number> = new Map();
  private defaultQuaternion: THREE.Quaternion;
  private runCancel: Function | null = null;
  private copying: boolean = false;

  constructor(gltf: GLTF) {
    this.sceneGroup = new THREE.Group();
    this.petGroup = gltf.scene;
    this.sceneGroup.add(this.petGroup);

    this.mixer = new THREE.AnimationMixer(this.petGroup);
    this.petGroup.animations = gltf.animations;
    const clips = gltf.animations;
    for (const clip of clips) {
      const nameList = clip.name.split('|');
      if (nameList[1]) {
        this.actionSpeedMap.set(nameList[0], Number(nameList[1]));
      }
      this.actionMap.set(nameList[0], this.mixer.clipAction(clip));
    }

    this.sceneGroup.traverse(function (child: any) {
      const skinnedMesh = child as THREE.SkinnedMesh;
      if (skinnedMesh) {
        child.receiveShadow = true;
        child.castShadow = true;
        skinnedMesh.frustumCulled = false;
      }
    });
    this.petGroup.position.set(1, 0, 0);
    this.defaultQuaternion = this.petGroup.quaternion.clone();
    this.loadFace();
  }

  _updateAnimation(delta: number) {
    delta *= this.actionSpeedMap.get(this.curAction) || 1;
    if (this.mixer) {
      this.mixer.update(delta);
    }
    if (this.face) {
      this.face.update(delta);
    }
  }

  showPet() {
    this.changeAction(showAction);
    this.sceneGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
    this.petGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
    this.petGroup.visible = false;
    setTimeout(() => {
      this.petGroup.visible = true;
    }, 50);
    this.randomTaunt();
    this.randomPath();
  }

  stop() {
    this.tauntTimer && clearTimeout(this.tauntTimer);
    this.pathTimer && clearTimeout(this.pathTimer);
    this.isMoving = false;
    if (this.runCancel) {
      this.runCancel();
      this.runCancel = null;
    }

    this.changeAction(idleAction);
    if (this.mixer) {
      this.mixer.update(1);
    }
  }

  getPetGlb(callback: (result: GLTF) => void) {
    if (this.copying) {
      setTimeout(() => {
        this.getPetGlb(callback);
      }, 100);
      return;
    }
    this.copying = true;
    const exporter = new GLTFExporter();
    this.petGroup.visible = true;
    this.petGroup.position.set(0, 0, 0);
    this.petGroup.quaternion.copy(this.defaultQuaternion);
    this.stop();
    exporter.parse(
      this.petGroup, // 要导出的 Object3D，可以是场景或单个对象
      (result) => {
        this.copying = false;
        this.petGroup.position.set(1, 0, 0);
        this.sceneGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
        this.petGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
        const loader = new GLTFLoader();
        loader.parse(result as any, '', (gltf) => {
          callback(gltf);
        });
      },
      (error) => {
        console.error('An error occurred during GLTF export:', error);
      },
      { binary: true, animations: this.petGroup.animations } // 设置为 true 时导出为 .glb，false 时为 .gltf
    );
  }

  private loadFace() {
    if (!this.face) {
      const faceMesh = this.sceneGroup.getObjectByName('Face') as THREE.SkinnedMesh;
      if (faceMesh) {
        this.face = AvatarUtil.createPetFace(faceMesh);
        this.face.setActionName('idle');
      } else {
        // console.error('cant find faceMesh')
      }
    }
  }

  private changeAction(actionName: string) {
    if (this.curAction === actionName) {
      return;
    }
    const action = this.actionMap.get(actionName);
    const old_action = this.actionMap.get(this.curAction);
    if (action) {
      this.curAction = actionName;
      if (this.face) {
        if (actionName === speakAction) {
          this.face.setActionName('speak');
        } else {
          this.face.setActionName('idle');
        }
      }
      if (old_action) {
        old_action.fadeOut(actionName === showAction ? 0 : 0.2);
      }
      // For jump and jump land animation, only play once and clamp when finish
      if (actionName === runAction || actionName === idleAction) {
        action.reset().fadeIn(0.2).play();
      } else {
        action
          .reset()
          .fadeIn(actionName === showAction ? 0 : 0.2)
          .setLoop(THREE.LoopOnce, 0)
          .play();
        action.clampWhenFinished = true;
        const finish = () => {
          this.checkAction();
          this.mixer.removeEventListener('finished', finish);
        };
        this.mixer.addEventListener('finished', finish);
      }
    }
  }

  private checkAction() {
    if (this.isMoving) {
      this.petGroup.quaternion.copy(this.defaultQuaternion);
      this.changeAction(runAction);
    } else {
      this.sceneGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
      this.petGroup.lookAt(new THREE.Vector3(1, 0, 1.5));
      this.changeAction(idleAction);
    }
  }

  private randomTaunt() {
    const checkDelay = 1000 * (Math.random() * 5 + 5);
    this.tauntTimer = setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * tauntActionList.length);
      //表示不在转圈并且没有播放嘲讽
      if (this.curAction === idleAction) {
        //当前为待机动作是随机播放嘲讽
        this.changeAction(tauntActionList[randomIndex]);
      }
      this.randomTaunt();
    }, checkDelay);
  }

  private randomPath() {
    const checkDelay = 1000 * (Math.random() * 3 + 15);
    this.pathTimer = setTimeout(() => {
      //表示不在转圈并且没有播放嘲讽
      if (this.curAction === idleAction) {
        this.isMoving = true;
        this.checkAction();
        // 使用 GSAP 动画进行自转
        const cancel = gsap.to(this.sceneGroup.rotation, {
          y: '-=6.2832', // 自转 360 度 (2π)
          duration: 7, // 动画持续时间 3 秒
          ease: 'none', // 线性运动
          onComplete: () => {
            this.isMoving = false;
            this.checkAction();
          },
        });
        this.runCancel = () => {
          cancel.pause();
        };
      }
      this.randomPath();
    }, checkDelay);
  }
}
