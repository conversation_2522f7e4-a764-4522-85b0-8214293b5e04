import * as THREE from 'three';
import AvatarFaceTexture from './AvatarFaceTexture';

export default class AvatarFace {
  private skinMesh: THREE.SkinnedMesh;
  private curFaceTexture: AvatarFaceTexture | null = null;

  private textureMap: Map<string, { weight: number; texture: AvatarFaceTexture }[]> = new Map();

  constructor(
    skinMesh: THREE.SkinnedMesh,
    textureList: {
      action: string;
      weight: number;
      texture: AvatarFaceTexture;
    }[]
  ) {
    this.skinMesh = skinMesh;
    this.textureMap.set('idle', []);
    for (let i = 0; i < textureList.length; i++) {
      const item = textureList[i];
      if (!this.textureMap.has(item.action)) {
        this.textureMap.set(item.action, []);
      }
      this.textureMap.get(item.action)?.push(
        item as {
          weight: number;
          texture: AvatarFaceTexture;
        }
      );
    }
  }

  setActionName(action: string) {
    const textureList = this.textureMap.get(action);
    if (textureList) {
      if (this.curFaceTexture) {
        this.curFaceTexture.cancel();
      }
      let totalSum = 0;
      textureList.forEach((item) => {
        totalSum += item.weight;
      });
      let randomSum = Math.floor(Math.random() * totalSum);

      for (let i = 0; i < textureList.length; i++) {
        randomSum -= textureList[i].weight;
        if (randomSum < 0) {
          this.curFaceTexture = textureList[i].texture;
          break;
        }
      }

      if (this.curFaceTexture) {
        this.curFaceTexture.loadTexture(
          (texture: THREE.Texture) => {
            const material = this.skinMesh.material as THREE.MeshStandardMaterial;
            material.map = texture;
          },
          () => {
            this.curFaceTexture?.cancel();
            this.setActionName(action);
          }
        );
      }
    }
  }

  update(delta: number) {
    this.curFaceTexture?.update(delta);
  }
}
