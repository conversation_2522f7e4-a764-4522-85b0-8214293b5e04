export default class AvatarData {
  actionId: string | null;
  petId: string | null;
  shirtId: string | null;
  shirtTextureId: string | null;
  shirtColor: string | null;
  pantsId: string | null;
  shoesId: string | null;
  hatId: string | null;
  faceId: string | null;
  glovesId: string | null;

  constructor() {
    /**
     * A custom skin color, overrides normally available skin color choices. This is not possible in game.
     */
    this.actionId = null;
    this.petId = null;
    this.shirtId = null;
    this.shirtTextureId = null;
    this.shirtColor = null;
    this.pantsId = null;
    this.shoesId = null;
    this.hatId = null;
    this.faceId = null;
    this.glovesId = null;
  }
}
