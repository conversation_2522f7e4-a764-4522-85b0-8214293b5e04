import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { getCdnLink, getOrdLink } from '../../../utils';

export default class AvatarPartSkinMesh {
  private id: string;
  private meshPath: string;
  private loadedObjs: THREE.SkinnedMesh[] = [];

  constructor(id: string, meshPath: string) {
    this.id = id;
    if (id.includes('./assets/')) {
      this.meshPath = getCdnLink(id);
    } else {
      this.meshPath = getOrdLink(meshPath).content;
    }
  }

  load(callback: Function) {
    try {
      if (this.loadedObjs.length > 0) {
        const list: THREE.SkinnedMesh[] = [];
        for (let i = 0; i < this.loadedObjs.length; i++) {
          const mesh = this.loadedObjs[i].clone();
          mesh.material = (mesh.material as THREE.MeshPhysicalMaterial).clone();
          list.push(mesh);
        }
        callback(list);
        return;
      }
      const loader = new GLTFLoader();
      loader.load(this.meshPath, (gltf: any) => {
        // console.debug('[AvatarPartSkinMesh]', 'Loaded glb:', this.meshPath, gltf);

        const avatar = gltf.scene;
        let meshList: THREE.SkinnedMesh[] = [];
        avatar.traverse(function (child: any) {
          if (child.isMesh) {
            meshList.push(child);
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });
        this.loadedObjs = meshList;
        this.load(callback);
      });
    } catch (e) {
      console.error('[AvatarPartSkinMesh]', 'Load error:', e);
      callback(null);
    }
  }
}
