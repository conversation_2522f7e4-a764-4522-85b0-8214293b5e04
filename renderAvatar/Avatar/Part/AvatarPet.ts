import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { getCdnLink, getOrdLink } from '../../../utils';
import AvatarPetObject from '../AvatarPetObject';

export default class AvatarPet {
  private id: string;
  private petPath: string;
  private petObject: AvatarPetObject | null = null;
  private callbackOpen = false;
  private callbackList: ((petObject: AvatarPetObject | null) => void)[] = [];

  constructor(id: string, petPath: string) {
    this.id = id;
    if (id.includes('./assets/Pet/')) {
      this.petPath = getCdnLink(id);
    } else {
      switch (id) {
        case '74f1c602036fc449a2323665ac88e922082e25149863f0fb0c97f83dec04b386i0':
        case '639f1da8f43ad3d2c9c7775fd2055f6c4fc20f57277d4afa311afbfc42e20dbfi0':
          this.petPath = getCdnLink('./assets/Pet/Pet_01.glb');
          break;
        default:
          this.petPath = getOrdLink(petPath).content;
          break;
      }
    }
    this.petObject = null;
  }

  openCallback() {
    if (this.callbackOpen) {
      return false;
      return false;
    }
    this.callbackOpen = true;
    return true;
  }

  closeCallback() {
    this.callbackOpen = false;
  }

  load(callback: (petObject: AvatarPetObject | null) => void) {
    try {
      if (this.petObject) {
        callback(this.petObject);
        return;
      }
      this.callbackList.push(callback);
      if (this.callbackList.length > 1) {
        return;
      }
      const loader = new GLTFLoader();
      loader.load(this.petPath, (gltf) => {
        // console.debug('[AvatarPet]', 'Loaded glb:', this.petPath, gltf);
        this.petObject = new AvatarPetObject(gltf);

        for (let i = 0; i < this.callbackList.length; i++) {
          this.callbackList[i](this.petObject);
        }
        this.callbackList = [];
      });
    } catch (e) {
      console.error('[AvatarPet]', 'Load error:', e);
      if (this.callbackOpen) {
        callback(null);
      }
    }
  }
}
