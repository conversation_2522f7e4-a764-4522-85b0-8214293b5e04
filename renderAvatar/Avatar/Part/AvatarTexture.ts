// @ts-ignore
import * as THREE from 'three';
import { getNFTImgLink } from '../../../utils';

export default class AvatarTexture {
  private id: string;
  private texturePath: string;
  private loadedTexture: null | any;

  constructor(id: string, texturePath: string) {
    this.id = id;
    this.texturePath = texturePath;
    this.loadedTexture = null;
  }

  load(callback: Function) {
    this.loadTexture(callback);
  }

  loadTexture(callback: Function) {
    if (this.loadedTexture) {
      callback(this.loadedTexture);
      return;
    }

    try {
      const loader = new THREE.TextureLoader();
      loader.load(getNFTImgLink(this.texturePath), (texture: any) => {
        texture.flipY = false; // 关闭垂直翻转
        texture.flipX = false; // 关闭水平翻转
        // console.debug('[AvatarTexture]', 'Loaded texture:', this.texturePath, texture);
        this.loadedTexture = texture;
        callback(texture);
      });
    } catch (e) {
      console.error('[AvatarTexture]', 'Texture load error:', e);
      callback(null);
    }
  }
}
