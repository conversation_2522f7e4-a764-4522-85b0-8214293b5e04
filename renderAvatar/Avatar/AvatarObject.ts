import * as THREE from 'three';
import { GLTF } from 'three/examples/jsm/loaders/GLTFLoader';
import AvatarPartsModel from './AvatarPartsModel';
import { AVATAR_INSCRIPTION_ID } from '../../constant';
import AvatarData from './Data/AvatarData';
import AvatarPartData from './Data/AvatarPartData';
import AvatarPartSkinMesh from './Part/AvatarPartSkinMesh';
import AvatarTexture from './Part/AvatarTexture';
import AvatarAction from './Part/AvatarAction';
import AvatarPet from './Part/AvatarPet';
import AvatarPetObject from './AvatarPetObject';
import AvatarFace from './AvatarFace';
import { AvatarUtil } from '../AvatarUtil';

class AvatarNewData {
  actionId: string;
  petId: string;
  shirt: AvatarPartData;
  pants: AvatarPartData;
  shoes: AvatarPartData;
  hat: AvatarPartData;
  gloves: AvatarPartData;

  constructor() {
    this.actionId = '';
    this.petId = '';
    this.shirt = new AvatarPartData();
    this.pants = new AvatarPartData();
    this.shoes = new AvatarPartData();
    this.hat = new AvatarPartData();
    this.gloves = new AvatarPartData();
  }
}

export default class AvatarObject {
  clock: THREE.Clock;
  mixer: THREE.AnimationMixer;
  sceneGroup: THREE.Group;
  petObject: AvatarPetObject | null = null;
  face: AvatarFace | null = null;
  avatarData: AvatarData | null = null;
  private skeleton: any | null;
  private action: any | null;
  private defaultAction: THREE.AnimationAction;
  private defaultClip: THREE.AnimationClip;
  private loadedObjects: Map<string, THREE.Object3D[]>;
  private playAction: AvatarAction | null = null;
  private avatarPet: AvatarPet | null = null;
  private newAvatarData: AvatarNewData;
  private loadingNumberList: number[];
  private numberIndex: number = 0;
  private shirtMesh: THREE.SkinnedMesh | null = null;
  private pantsMesh: THREE.SkinnedMesh | null = null;
  private shoesMesh: THREE.SkinnedMesh | null = null;
  private glovesMesh: THREE.SkinnedMesh | null = null;
  private handMesh: THREE.SkinnedMesh | null = null;
  private firstOnLoad: Function;
  private setAvatarFinish: Function;

  constructor(gltf: GLTF, onLoad: (avatarObject: AvatarObject) => void) {
    this.clock = new THREE.Clock();
    this.loadedObjects = new Map();
    this.newAvatarData = new AvatarNewData();
    this.loadingNumberList = [];

    let isLoaded = false;
    this.firstOnLoad = () => {
      if (isLoaded) {
        return;
      }
      isLoaded = true;
      onLoad(this);
    };
    this.setAvatarFinish = () => {};

    let skeleton = null;
    this.sceneGroup = gltf.scene;
    this.loadFace();
    this.mixer = new THREE.AnimationMixer(this.sceneGroup);

    const clips = gltf.animations;
    let clip = clips[0];
    this.defaultClip = clip;
    this.defaultAction = this.mixer.clipAction(clip);
    this.defaultAction.play();

    const self = this;
    this.sceneGroup.traverse(function (child: any) {
      if (child.isMesh) {
        skeleton = child.skeleton;
        child.receiveShadow = true;
        child.castShadow = true;
        const mesh = child as THREE.SkinnedMesh;
        switch (mesh.name) {
          case 'Hand_00':
            self.glovesMesh = mesh;
            break;
          case 'Shirt_00':
            self.shirtMesh = mesh;
            break;
          case 'Pants_00':
            self.pantsMesh = mesh;
            break;
          case 'Shoes_00':
            self.shoesMesh = mesh;
            break;
        }
      }
    });
    this.skeleton = skeleton;
    this.sceneGroup.position.set(0, 0, 0);
  }

  _updateAnimation(multiple = 1) {
    const delta = this.clock.getDelta();
    if (this.mixer) {
      this.mixer.update(delta * multiple);
    }
    if (this.petObject) {
      this.petObject._updateAnimation(delta * multiple);
    }
    if (this.face) {
      this.face.update(delta * multiple);
    }
  }

  registerLoadingCallback() {
    this.numberIndex++;
    this.loadingNumberList.push(this.numberIndex);
    return this.numberIndex;
  }

  doLoadingCallback(callbackNumber: number) {
    let index = this.loadingNumberList.indexOf(callbackNumber);
    if (index >= 0) {
      this.loadingNumberList.splice(index, 1);
    }
    if (this.loadingNumberList.length === 0) {
      this.firstOnLoad();
      setTimeout(() => {
        this.setAvatarFinish();
      }, 50);
    }
    return index >= 0;
  }

  clearLoadingList() {
    this.loadingNumberList = [];
  }

  updatePartData(
    partData: AvatarPartData,
    meshId: string,
    textureId: string | null,
    color: string | null
  ) {
    if (
      partData.meshId === meshId &&
      partData.textureId === textureId &&
      partData.color === color
    ) {
      return;
    }
    partData.meshId = meshId;
    partData.textureId = textureId;
    partData.color = color;
    partData.isChange = true;
  }

  setAvatarData(avatarData: AvatarData | null) {
    this.avatarData = avatarData;

    // 生成新的数据
    this.newAvatarData.actionId = avatarData && avatarData.actionId ? avatarData.actionId : '';
    this.newAvatarData.petId = avatarData && avatarData.petId ? avatarData.petId : '';

    this.updatePartData(
      this.newAvatarData.shirt,
      avatarData && avatarData.shirtId ? avatarData.shirtId : AVATAR_INSCRIPTION_ID.Shirt,
      avatarData && avatarData.shirtTextureId ? avatarData.shirtTextureId : null,
      avatarData && avatarData.shirtColor ? avatarData.shirtColor : null
    );

    this.updatePartData(
      this.newAvatarData.pants,
      avatarData && avatarData.pantsId ? avatarData.pantsId : AVATAR_INSCRIPTION_ID.Pants,
      null,
      null
    );
    this.updatePartData(
      this.newAvatarData.hat,
      avatarData && avatarData.hatId ? avatarData.hatId : AVATAR_INSCRIPTION_ID.Hat,
      null,
      null
    );
    this.updatePartData(
      this.newAvatarData.shoes,
      avatarData && avatarData.shoesId ? avatarData.shoesId : AVATAR_INSCRIPTION_ID.Shoes,
      null,
      null
    );
    this.updatePartData(
      this.newAvatarData.gloves,
      avatarData && avatarData.glovesId ? avatarData.glovesId : '',
      null,
      null
    );
  }

  load() {
    this.clearLoadingList();

    //确保相同装备时也会触发变更回调  , 同时保证 不会多次触发完成
    const loadNumberPet = this.registerLoadingCallback();
    setTimeout(() => {
      if (!this.doLoadingCallback(loadNumberPet)) {
        return;
      }
    }, 1);
    let keyList = ['shirt', 'pants', 'shoes', 'hat', 'gloves'];

    for (let i = 0; i < keyList.length; i++) {
      let key = keyList[i];
      // @ts-ignore
      let partData = this.newAvatarData[key] as AvatarPartData;
      if (partData && partData.isChange) {
        // this.handlePartLoaded(key + "Mesh", null);
        let meshLoader = AvatarPartsModel.tryGetPart(key, partData.meshId) as AvatarPartSkinMesh;
        if (meshLoader) {
          let loadNumberMesh = this.registerLoadingCallback();
          meshLoader.load((meshObjs: THREE.SkinnedMesh[]) => {
            let loadNumberTexture = this.registerLoadingCallback();
            if (!this.doLoadingCallback(loadNumberMesh) || meshObjs.length === 0) {
              this.doLoadingCallback(loadNumberTexture);
              return;
            }
            partData.isChange = false;
            this.handlePartLoaded(key + 'Mesh', meshObjs);
            const firstMesh = meshObjs[0];
            const material = firstMesh.material as THREE.MeshStandardMaterial;
            if (partData.textureId) {
              let textureLoader = AvatarPartsModel.tryGetTexture(
                partData.textureId
              ) as AvatarTexture;
              textureLoader.load((texture: THREE.Texture) => {
                if (!this.doLoadingCallback(loadNumberTexture)) {
                  return;
                }
                material.map = texture;
                if (material.emissiveMap) {
                  material.emissiveMap = texture;
                }
              });
            } else {
              this.doLoadingCallback(loadNumberTexture);
            }
            if (partData.color) {
              material.color = new THREE.Color(partData.color);
            }
          });
        } else {
          this.handlePartLoaded(key + 'Mesh', []);
        }
      }
    }

    let newAction = AvatarPartsModel.tryGetPart(
      'action',
      this.newAvatarData ? this.newAvatarData.actionId : null,
      false
    ) as AvatarAction;
    if (this.playAction != newAction) {
      if (newAction) {
        let loadNumberAction = this.registerLoadingCallback();
        newAction.load((clip: THREE.AnimationClip) => {
          if (!this.doLoadingCallback(loadNumberAction)) {
            return;
          }
          if (this.mixer) {
            this.StopAction();
            const action = this.mixer.clipAction(clip);
            action.reset();
            action.play();
            this.action = action;
            this.playAction = newAction;
          }
          if (this.face) {
            if (clip.name === 'action_05') {
              this.face.setActionName('speak');
            } else {
              this.face.setActionName('idle');
            }
          }
        });
      } else if (this.defaultAction) {
        this.playAction = null;
        this.StopAction();
        this.defaultAction.reset();
        this.defaultAction.play();
        if (this.face) {
          this.face.setActionName('idle');
        }
      }
    }

    if (this.newAvatarData && this.newAvatarData.petId.length > 0) {
      let newPet = AvatarPartsModel.tryGetPart('pet', this.newAvatarData.petId, false) as AvatarPet;
      if (newPet && newPet !== this.avatarPet) {
        if (this.petObject) {
          this.petObject.stop();
        }
        this.avatarPet = newPet;
        let loadNumberPet = this.registerLoadingCallback();
        newPet.load((petObject) => {
          if (petObject) {
            if (!this.doLoadingCallback(loadNumberPet)) {
              return;
            }
            this.petObject = petObject;
            this.handlePartLoaded('petMesh', [petObject.sceneGroup]);
            petObject.showPet();
          }
        });
      }
    } else {
      if (this.petObject) {
        this.petObject.stop();
      }
      this.handlePartLoaded('petMesh', []);
      this.petObject = null;
      this.avatarPet = null;
    }
  }

  handlePartLoaded(key: string, gameObjects: THREE.Object3D[]) {
    if (this.loadedObjects.has(key)) {
      let oldObjs = this.loadedObjects.get(key);
      if (oldObjs) {
        oldObjs.forEach((oldObj) => {
          this.sceneGroup.remove(oldObj);
        });
      }
    }
    if (gameObjects.length > 0) {
      this.loadedObjects.set(key, gameObjects);
      gameObjects.forEach((gameObject) => {
        this.sceneGroup.add(gameObject);
        const skinMesh = gameObject as THREE.SkinnedMesh;
        if (skinMesh) {
          skinMesh.skeleton = this.skeleton;
        }
      });
    } else {
      this.loadedObjects.delete(key);
    }

    switch (key) {
      case 'shirtMesh':
        if (this.shirtMesh) {
          this.shirtMesh.visible = gameObjects.length === 0;
        }
        break;
      case 'pantsMesh':
        if (this.pantsMesh) {
          this.pantsMesh.visible = gameObjects.length === 0;
        }
        break;
      case 'shoesMesh':
        if (this.shoesMesh) {
          this.shoesMesh.visible = gameObjects.length === 0;
        }
        break;
      case 'glovesMesh':
        if (this.glovesMesh) {
          this.glovesMesh.visible = gameObjects.length === 0;
        }
        break;
    }
  }

  StopAction() {
    if (this.action) {
      this.action.stop();
      this.action = null;
    }
    if (this.defaultAction) {
      this.defaultAction.stop();
    }
  }

  GetActionList(callback: (actionList: THREE.AnimationAction[]) => void) {
    const list = AvatarPartsModel.getActionList();
    if (list.length == 0) {
      setTimeout(() => {
        this.GetActionList(callback);
      }, 50);
      return;
    }
    this.sceneGroup.animations = [this.defaultClip];
    const actionList: THREE.AnimationAction[] = [this.defaultAction];
    let loadNumber = 0;
    if (list.length > 0) {
      for (let i = 0; i < list.length; i++) {
        let action = list[i];
        if (action) {
          action.load((clip: THREE.AnimationClip) => {
            loadNumber++;
            if (this.mixer) {
              this.sceneGroup.animations.push(clip);
              const action = this.mixer.clipAction(clip);
              actionList.push(action);
            }
            if (loadNumber == list.length) {
              callback(actionList);
            }
          });
        }
      }
    } else {
      callback(actionList);
    }
  }

  watchAvatarChange(callback: () => void, immediately: boolean = true) {
    this.setAvatarFinish = callback;
    if (immediately) {
      callback();
    }
  }

  unWatchAvatarChange() {
    this.setAvatarFinish = () => {};
  }

  private loadFace() {
    if (!this.face) {
      const faceMesh = this.sceneGroup.getObjectByName('Face') as THREE.SkinnedMesh;
      if (faceMesh) {
        this.face = AvatarUtil.createAvatarFace(faceMesh);
        this.face.setActionName('idle');
      } else {
        console.error('cant find faceMesh');
      }
    }
  }
}
