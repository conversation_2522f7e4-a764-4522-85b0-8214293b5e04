import * as THREE from 'three';
import AvatarObject from './Avatar/AvatarObject';
import { GLTF, GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { getCdnLink, getOrdLink } from '../utils';
import { AVATAR_INSCRIPTION_ID } from '../constant';
import AvatarData from './Avatar/Data/AvatarData';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter';
import AvatarPartsModel from './Avatar/AvatarPartsModel';
import AvatarPet from './Avatar/Part/AvatarPet';
import AvatarFace from './Avatar/AvatarFace';
import AvatarFaceTexture from './Avatar/AvatarFaceTexture';
import AvatarPetObject from './Avatar/AvatarPetObject';

export default class GlobalAvatarUtil {
  private avatarObject: AvatarObject | null = null;
  private loadingAvatar: boolean = false;
  private taskList: { avatarData: AvatarData; callback: (playerGltf: GLTF) => void }[] = [];

  init() {
    const loader = new GLTFLoader();
    loader.load(
      getOrdLink(AVATAR_INSCRIPTION_ID.Body).content,
      (gltf) => {
        this.avatarObject = new AvatarObject(gltf, () => {});
      },
      (xhr: any) => {},
      (error: any) => {
        console.log(error);
      }
    );
  }

  getAvatarGLB(avatarData: AvatarData, callback: (playerGltf: GLTF) => void) {
    if (!this.avatarObject) {
      setTimeout(() => {
        this.getAvatarGLB(avatarData, callback);
      }, 100);
      return;
    }
    this.addTask(avatarData, callback);
    this.doTask(this.avatarObject);
  }

  getPetGLB(petId: string, callback: (playerGltf: GLTF) => void) {
    if (!this.avatarObject) {
      setTimeout(() => {
        this.getPetGLB(petId, callback);
      }, 100);
      return;
    }
    let newPet = AvatarPartsModel.tryGetPart('pet', petId, false) as AvatarPet;
    if (newPet) {
      newPet.load((petObject) => {
        if (petObject) {
          petObject.getPetGlb((result) => {
            callback(result);
          });
        } else {
          console.error('petObject is null');
        }
      });
    }
  }

  getPetObject(petId: string, callback: (petObject: AvatarPetObject | null) => void) {
    if (!this.avatarObject) {
      setTimeout(() => {
        this.getPetObject(petId, callback);
      }, 100);
      return;
    }

    let newPet = AvatarPartsModel.tryGetPart('pet', petId, false) as AvatarPet;
    if (newPet) {
      newPet.openCallback();
      newPet.load((petObject) => {
        newPet.closeCallback();
        if (petObject) {
          callback(petObject);
        } else {
          callback(null);
        }
      });
    }
  }

  createAvatarFace(faceMesh: THREE.SkinnedMesh) {
    return new AvatarFace(faceMesh, [
      {
        action: 'idle',
        weight: 4,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Face/Face_01a.png'), 0.2, 1, 1),
      },
      {
        action: 'idle',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Face/Face_01b.png'), 0.05, 2, 2),
      },
      {
        action: 'speak',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Face/Face_01c.png'), 0.06, 4, 4),
      },
      {
        action: 'weary',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Face/Face_02.png'), 1, 1, 1),
      },
    ]);
  }

  createPetFace(faceMesh: THREE.SkinnedMesh) {
    return new AvatarFace(faceMesh, [
      {
        action: 'idle',
        weight: 4,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Pet/Pet_01/Face_01a.png'), 0.6, 1, 1),
      },
      {
        action: 'idle',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Pet/Pet_01/Face_01b.png'), 0.1, 2, 3),
      },
      {
        action: 'speak',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink('./assets/Pet/Pet_01/Face_01c.png'), 0.1, 2, 4),
      },
    ]);
  }

  createNpcFace(faceMesh: THREE.SkinnedMesh, url: string) {
    return new AvatarFace(faceMesh, [
      {
        action: 'idle',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink(url), 0.6, 1, 1),
      },
      {
        action: 'speak',
        weight: 1,
        texture: new AvatarFaceTexture(getCdnLink(url), 0.6, 1, 1),
      },
    ]);
  }

  private addTask(avatarData: AvatarData, callback: (playerGltf: GLTF) => void) {
    this.taskList.push({ avatarData, callback });
  }

  private doTask(avatarObject: AvatarObject) {
    if (!this.loadingAvatar) {
      const task = this.taskList.shift();
      if (task) {
        this.loadingAvatar = true;
        avatarObject.watchAvatarChange(() => {
          avatarObject.unWatchAvatarChange();
          avatarObject.GetActionList(() => {
            const exporter = new GLTFExporter();
            avatarObject.handlePartLoaded('petMesh', []);
            exporter.parse(
              avatarObject.sceneGroup, // 要导出的 Object3D，可以是场景或单个对象
              (result) => {
                this.loadingAvatar = false;
                this.doTask(avatarObject);
                const loader = new GLTFLoader();
                loader.parse(result as any, '', (gltf) => {
                  task.callback(gltf);
                });
              },
              (error) => {
                console.error('An error occurred during GLTF export:', error);
              },
              { binary: true, animations: avatarObject.sceneGroup.animations } // 设置为 true 时导出为 .glb，false 时为 .gltf
            );
          });
        }, false);
        avatarObject.setAvatarData(task.avatarData);
        avatarObject.load();
      }
    }
  }
}

export const AvatarUtil = new GlobalAvatarUtil();
