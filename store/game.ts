import { IBagInventoryItem, IGameState, ISyntheticItem } from '../constant/type';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const initialState: IGameState = {
  bagInventoryList: [], //背包的所有物品
  equipmendResult: [], //已经装备到身上的物品
  syntheticsList: [], //可合成的列表
  materialList: [], //材料列表
};

export const GameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    setBagInventoryList(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.bagInventoryList = action.payload;
    },
    setEquipmendResult(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.equipmendResult = action.payload;
    },
    setSyntheticsList(state, action: PayloadAction<ISyntheticItem[]>) {
      state.syntheticsList = action.payload;
    },
    setMaterialList(state, action: PayloadAction<IBagInventoryItem[]>) {
      state.materialList = action.payload;
    },
    resetGameState(state) {
      state.bagInventoryList = [];
      state.equipmendResult = [];
      state.syntheticsList = [];
      state.materialList = [];
    },
  },
});
export const {
  setBagInventoryList,
  setEquipmendResult,
  setSyntheticsList,
  setMaterialList,
  resetGameState,
} = GameSlice.actions;
export default GameSlice.reducer;
