import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ISettingState } from '@/constant/type';

const initialState: ISettingState = {
  onlineMultiplayer: true,
  soundEffects: true,
  music: true,
  maxPlayers: 10,
  isPizzaActivity: false,
};

export const SettingSlice = createSlice({
  name: 'setting',
  initialState,
  reducers: {
    updateSetting(state, action: PayloadAction<ISettingState>) {
      state.soundEffects = action.payload.soundEffects ?? state.soundEffects;
      state.onlineMultiplayer = action.payload.onlineMultiplayer ?? state.onlineMultiplayer;
      state.music = action.payload.music ?? state.music;
      state.maxPlayers = action.payload.maxPlayers ?? state.maxPlayers;
    },
    setIsPizzaActivity(state, action: PayloadAction<boolean>) {
      state.isPizzaActivity = action.payload;
    },
  },
});
export const { updateSetting, setIsPizzaActivity } = SettingSlice.actions;
export default SettingSlice.reducer;
