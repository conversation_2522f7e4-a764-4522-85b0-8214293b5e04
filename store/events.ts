import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 定义事件类型
export interface EventPayload {
  itemCollected: {
    itemImage: string;
    count: number;
  };

  // 其他事件类型...
  [key: string]: any;
}

export type EventType = keyof EventPayload;

// 事件状态类型
export interface IEventState {
  // 当前正在触发的事件
  currentEvents: {
    [K in EventType]?: EventPayload[K] | null;
  };
}

const initialState: IEventState = {
  currentEvents: {
    itemCollected: null,
    // 其他事件初始值...
  },
};

export const EventsSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    // 触发物品收集事件
    emitItemCollected: (state, action: PayloadAction<{ itemImage: string; count: number }>) => {
      state.currentEvents.itemCollected = action.payload;
    },

    // 清除特定事件
    clearEvent: (state, action: PayloadAction<EventType>) => {
      state.currentEvents[action.payload] = null;
    },

    // 清除所有事件
    clearAllEvents: (state) => {
      Object.keys(state.currentEvents).forEach((key) => {
        state.currentEvents[key as EventType] = null;
      });
    },
  },
});

export const { emitItemCollected, clearEvent, clearAllEvents } = EventsSlice.actions;

export default EventsSlice.reducer;
