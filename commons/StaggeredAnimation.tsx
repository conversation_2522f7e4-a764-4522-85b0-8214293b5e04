import { motion } from 'motion/react';
import React, { ReactNode } from 'react';

interface StaggeredAnimationProps {
  children: ReactNode; // 子组件内容
  index: number; // 元素索引，用于计算延迟时间
  isVisible?: boolean; // 是否可见
  staggerDelay?: number; // 元素之间的延迟时间(秒)
  duration?: number; // 单个动画持续时间(秒)
  initialScale?: number; // 初始缩放比例
  finalScale?: number; // 最终缩放比例
  bounceEffect?: boolean; // 是否启用弹跳效果
  className?: string; // 自定义CSS类名
}

/**
 * 可复用的动画包装组件，为子元素添加交错入场动画效果
 *
 * 主要特点:
 * 1. 支持元素从小到大逐个出现的动画效果
 * 2. 可自定义动画延迟、持续时间和缩放比例
 * 3. 支持弹跳效果，使动画更生动
 * 4. 可通过isVisible属性控制动画的显示和隐藏
 */
const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  index,
  isVisible = true, // 默认可见
  staggerDelay = 0.05, // 默认每个元素延迟0.05秒出现
  duration = 0.4, // 默认动画持续0.4秒
  initialScale = 0.5, // 默认初始缩放为50%
  finalScale = 1, // 默认最终缩放为100%
  bounceEffect = true, // 默认启用弹跳效果
  className,
}) => {
  return (
    <motion.div
      className={className}
      // 初始状态：不可见且缩小
      initial={{ opacity: 0, scale: initialScale }}
      // 动画状态：根据isVisible决定是否显示
      animate={
        isVisible
          ? { opacity: 1, scale: finalScale } // 显示时：完全不透明且达到指定缩放
          : { opacity: 0, scale: initialScale } // 隐藏时：完全透明且缩小
      }
      // 退出状态：不可见且缩小
      exit={{ opacity: 0, scale: initialScale }}
      // 过渡效果配置
      transition={{
        delay: index * staggerDelay, // 根据索引计算延迟时间，实现交错效果
        duration, // 动画持续时间
        ease: bounceEffect ? 'backOut' : 'easeOut', // 根据是否需要弹跳效果选择缓动函数
        // 缩放动画的特殊配置
        scale: {
          type: bounceEffect ? 'spring' : 'tween', // 弹跳效果使用弹簧动画，否则使用补间动画
          stiffness: bounceEffect ? 400 : undefined, // 弹簧刚度，数值越大反弹越强
          damping: bounceEffect ? 10 : undefined, // 阻尼系数，数值越小弹跳次数越多
        },
      }}
    >
      {children}
    </motion.div>
  );
};

export default StaggeredAnimation;
