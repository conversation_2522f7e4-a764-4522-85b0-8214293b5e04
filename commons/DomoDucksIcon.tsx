import React from 'react';

interface DomoDucksIconProps {
  style?: React.CSSProperties;
}

const DomoDucksIcon: React.FC<DomoDucksIconProps> = ({ style }) => {
  return (
    <svg
      style={{
        width: '17.3125rem',
        height: '2.5625rem',
      }}
      width="277"
      height="41"
      viewBox="0 0 277 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_4908_19389)">
        <path
          d="M4.25 32V25.2617H7.375V7.64453H4.25V0.90625H18.4297C24.1979 0.90625 28.5859 2.23438 31.5938 4.89062C34.6016 7.53385 36.1055 11.401 36.1055 16.4922C36.1055 21.5703 34.6081 25.4245 31.6133 28.0547C28.6185 30.6849 24.2174 32 18.4102 32H4.25ZM17.5312 25.457H18.6055C20.8971 25.457 22.5378 24.7669 23.5273 23.3867C24.5169 21.9935 25.0117 19.6758 25.0117 16.4336C25.0117 13.2044 24.5169 10.8997 23.5273 9.51953C22.5378 8.13932 20.8971 7.44922 18.6055 7.44922H17.5312V25.457ZM50.4414 32.3516C47.9935 32.3516 45.8776 31.9154 44.0938 31.043C42.3229 30.1706 40.9557 28.9271 39.9922 27.3125C39.0417 25.6979 38.5664 23.7773 38.5664 21.5508C38.5664 19.3112 39.0417 17.3841 39.9922 15.7695C40.9557 14.1549 42.3229 12.9115 44.0938 12.0391C45.8776 11.1536 47.9935 10.7109 50.4414 10.7109C52.8893 10.7109 54.9987 11.1536 56.7695 12.0391C58.5534 12.9115 59.9206 14.1549 60.8711 15.7695C61.8346 17.3841 62.3164 19.3112 62.3164 21.5508C62.3164 23.7773 61.8346 25.6979 60.8711 27.3125C59.9206 28.9271 58.5534 30.1706 56.7695 31.043C54.9987 31.9154 52.8893 32.3516 50.4414 32.3516ZM50.4414 26.668C52.1992 26.668 53.0781 24.9557 53.0781 21.5312C53.0781 18.1068 52.1992 16.3945 50.4414 16.3945C48.6836 16.3945 47.8047 18.1068 47.8047 21.5312C47.8047 24.9557 48.6836 26.668 50.4414 26.668ZM64.8164 32V26.1406H67.6484V17.1758H64.8164V11.8438L76.8281 10.6914V13.25C77.6354 12.4297 78.638 11.7982 79.8359 11.3555C81.0339 10.9128 82.3099 10.6914 83.6641 10.6914C86.3984 10.6914 88.2995 11.6419 89.3672 13.543C90.3698 12.6315 91.5091 11.9284 92.7852 11.4336C94.0742 10.9388 95.3633 10.6914 96.6523 10.6914C99.1523 10.6914 100.962 11.349 102.082 12.6641C103.215 13.9792 103.781 15.9714 103.781 18.6406V26.1406H106.516V32H94.4062V18.6406C94.4062 17.9115 94.2435 17.3841 93.918 17.0586C93.6055 16.7201 93.2083 16.5508 92.7266 16.5508C92.3359 16.5508 91.9258 16.668 91.4961 16.9023C91.0664 17.1367 90.7018 17.4753 90.4023 17.918V26.1406H92.1602V32H81.0273V18.6406C81.0273 17.9115 80.8646 17.3841 80.5391 17.0586C80.2266 16.7201 79.8294 16.5508 79.3477 16.5508C78.957 16.5508 78.5469 16.668 78.1172 16.9023C77.6875 17.1367 77.3229 17.4753 77.0234 17.918V26.1406H78.7812V32H64.8164ZM120.363 32.3516C117.915 32.3516 115.799 31.9154 114.016 31.043C112.245 30.1706 110.878 28.9271 109.914 27.3125C108.964 25.6979 108.488 23.7773 108.488 21.5508C108.488 19.3112 108.964 17.3841 109.914 15.7695C110.878 14.1549 112.245 12.9115 114.016 12.0391C115.799 11.1536 117.915 10.7109 120.363 10.7109C122.811 10.7109 124.921 11.1536 126.691 12.0391C128.475 12.9115 129.842 14.1549 130.793 15.7695C131.757 17.3841 132.238 19.3112 132.238 21.5508C132.238 23.7773 131.757 25.6979 130.793 27.3125C129.842 28.9271 128.475 30.1706 126.691 31.043C124.921 31.9154 122.811 32.3516 120.363 32.3516ZM120.363 26.668C122.121 26.668 123 24.9557 123 21.5312C123 18.1068 122.121 16.3945 120.363 16.3945C118.605 16.3945 117.727 18.1068 117.727 21.5312C117.727 24.9557 118.605 26.668 120.363 26.668ZM134.328 32V25.2617H137.453V7.64453H134.328V0.90625H148.508C154.276 0.90625 158.664 2.23438 161.672 4.89062C164.68 7.53385 166.184 11.401 166.184 16.4922C166.184 21.5703 164.686 25.4245 161.691 28.0547C158.697 30.6849 154.296 32 148.488 32H134.328ZM147.609 25.457H148.684C150.975 25.457 152.616 24.7669 153.605 23.3867C154.595 21.9935 155.09 19.6758 155.09 16.4336C155.09 13.2044 154.595 10.8997 153.605 9.51953C152.616 8.13932 150.975 7.44922 148.684 7.44922H147.609V25.457ZM178.391 32.3711C173.573 32.3711 171.164 29.6628 171.164 24.2461V17.1758H168.43V11.8438L180.539 10.6914V24.2656C180.539 25.0599 180.715 25.6328 181.066 25.9844C181.418 26.3359 181.867 26.5117 182.414 26.5117C183.391 26.5117 184.146 26.0104 184.68 25.0078V17.1758H182.922V11.8438L194.055 10.6914V26.1406H196.789V32H184.875V29.7148C184.094 30.5482 183.163 31.1992 182.082 31.668C181.014 32.1367 179.784 32.3711 178.391 32.3711ZM210.695 32.3516C206.997 32.3516 204.081 31.3815 201.945 29.4414C199.823 27.4883 198.762 24.7865 198.762 21.3359C198.762 19.1745 199.211 17.2995 200.109 15.7109C201.021 14.1224 202.232 12.8919 203.742 12.0195C205.266 11.1471 206.939 10.7109 208.762 10.7109C210.09 10.7109 211.255 10.9453 212.258 11.4141C213.26 11.8698 214.113 12.5013 214.816 13.3086L215.598 11.0625H219.914V19.7344H214.289C214.185 18.6146 213.879 17.7812 213.371 17.2344C212.863 16.6745 212.225 16.3945 211.457 16.3945C210.585 16.3945 209.803 16.7721 209.113 17.5273C208.436 18.2695 208.098 19.513 208.098 21.2578C208.098 23.0677 208.573 24.3633 209.523 25.1445C210.474 25.9128 211.678 26.2969 213.137 26.2969C214.23 26.2969 215.285 26.082 216.301 25.6523C217.329 25.2227 218.215 24.6953 218.957 24.0703L219.484 24.3047V30.1055C218.872 30.5352 218.085 30.9193 217.121 31.2578C216.158 31.5964 215.116 31.8633 213.996 32.0586C212.876 32.2539 211.776 32.3516 210.695 32.3516ZM221.945 32V26.1406H224.777V8.13281H221.945V2.80078L234.152 1.53125V20.9453L238.938 16.9219H235.93V11.0625H249.133V16.9219H245.422L242.023 19.7734L246.613 26.1406H249.543V32H239.621L234.152 24.2461V32H221.945ZM263.469 32.3516C262.141 32.3516 260.852 32.1953 259.602 31.8828C258.352 31.5703 257.245 31.1211 256.281 30.5352L255.676 32H251.379V24.6562H256.379C256.678 25.6719 257.199 26.4727 257.941 27.0586C258.684 27.6445 259.491 27.9375 260.363 27.9375C261.509 27.9375 262.082 27.5078 262.082 26.6484C262.082 26.0234 261.822 25.5417 261.301 25.2031C260.793 24.8646 260.129 24.5781 259.309 24.3438C258.501 24.1094 257.642 23.8555 256.73 23.582C255.819 23.3086 254.953 22.9375 254.133 22.4688C253.326 21.987 252.661 21.3359 252.141 20.5156C251.633 19.6823 251.379 18.5951 251.379 17.2539C251.379 16.043 251.698 14.9427 252.336 13.9531C252.987 12.9635 253.964 12.1758 255.266 11.5898C256.581 10.9909 258.228 10.6914 260.207 10.6914C261.483 10.6914 262.661 10.8281 263.742 11.1016C264.836 11.362 265.858 11.7852 266.809 12.3711L267.277 11.0625H271.574V17.7812H266.574C266.236 16.7917 265.747 16.069 265.109 15.6133C264.484 15.1445 263.807 14.9102 263.078 14.9102C261.802 14.9102 261.164 15.3008 261.164 16.082C261.164 16.6289 261.424 17.0651 261.945 17.3906C262.479 17.7031 263.156 17.9766 263.977 18.2109C264.81 18.4453 265.689 18.7122 266.613 19.0117C267.538 19.3112 268.41 19.7148 269.23 20.2227C270.064 20.7305 270.741 21.4076 271.262 22.2539C271.796 23.1003 272.062 24.2005 272.062 25.5547C272.062 27.625 271.314 29.2786 269.816 30.5156C268.332 31.7396 266.216 32.3516 263.469 32.3516Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_4908_19389"
          x="0.25"
          y="0.90625"
          width="275.812"
          height="39.4688"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4908_19389" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4908_19389"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default DomoDucksIcon;
