import styled from 'styled-components';

const ChevronLeftIconContainer = styled.div<{ isExpanded: boolean }>`
  width: 2.125rem;
  height: 2.125rem;
  background-color: #fff;
  border-radius: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: -1.625rem;
  top: ${(props) => (props.isExpanded ? '14.375rem' : '-0.125rem')};
  & > svg {
    width: 1.5rem;
    height: 1.5rem;
  }
`;

interface ArrowLeftRightProps {
  isExpanded?: boolean;
}

const ArrowLeftRight = ({ isExpanded = false }: ArrowLeftRightProps) => {
  return (
    <ChevronLeftIconContainer isExpanded={isExpanded}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="#FF8316"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round">
        <path d="M8 3 4 7l4 4" />
        <path d="M4 7h16" />
        <path d="m16 21 4-4-4-4" />
        <path d="M20 17H4" />
      </svg>
    </ChevronLeftIconContainer>
  );
};

export default ArrowLeftRight;
