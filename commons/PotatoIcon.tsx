interface PotatoIconProps {
  style?: React.CSSProperties;
}

const PotatoIcon: React.FC<PotatoIconProps> = ({ style }) => {
  return (
    <svg
      style={{
        ...style,
        position: 'relative',
        left: '-0.3125rem',
        width: '9.5rem',
        height: '2.8125rem',
      }}
      width="152"
      height="45"
      viewBox="0 0 152 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_4150_18299)">
        <path
          d="M4.15625 32V25.2617H7.28125V7.64453H4.15625V0.90625H20.9336C25.1784 0.90625 28.3815 1.73307 30.543 3.38672C32.7044 5.02734 33.7852 7.48828 33.7852 10.7695C33.7852 13.9466 32.7109 16.3555 30.5625 17.9961C28.4141 19.6237 25.2695 20.4375 21.1289 20.4375H17.2031V25.2617H22.8672V32H4.15625ZM19.8008 7.44922H17.2031V14.4805H19.6055C20.9336 14.4805 21.9297 14.2005 22.5938 13.6406C23.2578 13.0807 23.5898 12.1888 23.5898 10.9648C23.5898 9.72786 23.2643 8.83594 22.6133 8.28906C21.9753 7.72917 21.0378 7.44922 19.8008 7.44922ZM46.0898 32.3516C43.6419 32.3516 41.526 31.9154 39.7422 31.043C37.9714 30.1706 36.6042 28.9271 35.6406 27.3125C34.6901 25.6979 34.2148 23.7773 34.2148 21.5508C34.2148 19.3112 34.6901 17.3841 35.6406 15.7695C36.6042 14.1549 37.9714 12.9115 39.7422 12.0391C41.526 11.1536 43.6419 10.7109 46.0898 10.7109C48.5378 10.7109 50.6471 11.1536 52.418 12.0391C54.2018 12.9115 55.569 14.1549 56.5195 15.7695C57.4831 17.3841 57.9648 19.3112 57.9648 21.5508C57.9648 23.7773 57.4831 25.6979 56.5195 27.3125C55.569 28.9271 54.2018 30.1706 52.418 31.043C50.6471 31.9154 48.5378 32.3516 46.0898 32.3516ZM46.0898 26.668C47.8477 26.668 48.7266 24.9557 48.7266 21.5312C48.7266 18.1068 47.8477 16.3945 46.0898 16.3945C44.332 16.3945 43.4531 18.1068 43.4531 21.5312C43.4531 24.9557 44.332 26.668 46.0898 26.668ZM71.9297 32.3516C68.8438 32.3516 66.4609 31.6484 64.7812 30.2422C63.1016 28.8229 62.2617 26.7721 62.2617 24.0898V16.9219H59.0391V11.5898L62.2617 11.2773V6.58984L71.6367 4.53906V11.0625H76.3633V16.9219H71.6367V23.8945C71.6367 24.6758 71.8255 25.2878 72.2031 25.7305C72.5938 26.1602 73.1797 26.375 73.9609 26.375C74.4167 26.375 74.8008 26.3359 75.1133 26.2578C75.4388 26.1667 75.7448 26.0755 76.0312 25.9844L76.5586 26.2188V31.8047C75.8164 31.974 75.0677 32.1042 74.3125 32.1953C73.5703 32.2995 72.776 32.3516 71.9297 32.3516ZM85.543 32.3516C84.2018 32.3516 83.0039 32.0846 81.9492 31.5508C80.9076 31.0039 80.0872 30.2552 79.4883 29.3047C78.9023 28.3542 78.6094 27.2604 78.6094 26.0234C78.6094 24.0964 79.3516 22.5599 80.8359 21.4141C82.3203 20.2682 84.2995 19.6953 86.7734 19.6953C87.6328 19.6953 88.4531 19.7474 89.2344 19.8516C90.0156 19.9557 90.8099 20.1315 91.6172 20.3789V19.9492C91.6172 18.7643 91.2526 17.9049 90.5234 17.3711C89.8073 16.8372 88.6549 16.5703 87.0664 16.5703C85.9206 16.5703 84.7617 16.7201 83.5898 17.0195C82.418 17.319 81.3503 17.7357 80.3867 18.2695L79.8594 18.0352V12.332C81.2266 11.7982 82.776 11.3945 84.5078 11.1211C86.2526 10.8477 88.0039 10.7109 89.7617 10.7109C93.6549 10.7109 96.5 11.362 98.2969 12.6641C100.094 13.9661 100.992 16.056 100.992 18.9336V26.1406H103.531V32H92.8672L91.9102 28.9336C91.2982 30.0013 90.4258 30.8411 89.293 31.4531C88.1732 32.0521 86.9232 32.3516 85.543 32.3516ZM89.625 27.3516C89.9766 27.3516 90.3411 27.293 90.7188 27.1758C91.1094 27.0456 91.4089 26.8763 91.6172 26.668V23.4062C91.2917 23.263 90.9596 23.1654 90.6211 23.1133C90.2956 23.0612 89.9896 23.0352 89.7031 23.0352C89.0391 23.0352 88.4792 23.2305 88.0234 23.6211C87.5807 24.0117 87.3594 24.5326 87.3594 25.1836C87.3594 25.8477 87.5677 26.375 87.9844 26.7656C88.401 27.1562 88.9479 27.3516 89.625 27.3516ZM117.789 32.3516C114.703 32.3516 112.32 31.6484 110.641 30.2422C108.961 28.8229 108.121 26.7721 108.121 24.0898V16.9219H104.898V11.5898L108.121 11.2773V6.58984L117.496 4.53906V11.0625H122.223V16.9219H117.496V23.8945C117.496 24.6758 117.685 25.2878 118.062 25.7305C118.453 26.1602 119.039 26.375 119.82 26.375C120.276 26.375 120.66 26.3359 120.973 26.2578C121.298 26.1667 121.604 26.0755 121.891 25.9844L122.418 26.2188V31.8047C121.676 31.974 120.927 32.1042 120.172 32.1953C119.43 32.2995 118.635 32.3516 117.789 32.3516ZM136.09 32.3516C133.642 32.3516 131.526 31.9154 129.742 31.043C127.971 30.1706 126.604 28.9271 125.641 27.3125C124.69 25.6979 124.215 23.7773 124.215 21.5508C124.215 19.3112 124.69 17.3841 125.641 15.7695C126.604 14.1549 127.971 12.9115 129.742 12.0391C131.526 11.1536 133.642 10.7109 136.09 10.7109C138.538 10.7109 140.647 11.1536 142.418 12.0391C144.202 12.9115 145.569 14.1549 146.52 15.7695C147.483 17.3841 147.965 19.3112 147.965 21.5508C147.965 23.7773 147.483 25.6979 146.52 27.3125C145.569 28.9271 144.202 30.1706 142.418 31.043C140.647 31.9154 138.538 32.3516 136.09 32.3516ZM136.09 26.668C137.848 26.668 138.727 24.9557 138.727 21.5312C138.727 18.1068 137.848 16.3945 136.09 16.3945C134.332 16.3945 133.453 18.1068 133.453 21.5312C133.453 24.9557 134.332 26.668 136.09 26.668Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_4150_18299"
          x="0.15625"
          y="0.90625"
          width="151.809"
          height="39.4453"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4150_18299" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4150_18299"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default PotatoIcon;
