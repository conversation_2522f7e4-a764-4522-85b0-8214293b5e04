import React from 'react';

interface PizzaIconProps {
  style?: React.CSSProperties;
}

const PizzaIcon: React.FC<PizzaIconProps> = ({ style }) => {
  return (
    <svg
      style={{
        width: '8.1875rem',
        height: '2.5625rem',
      }}
      width="131"
      height="41"
      viewBox="0 0 131 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_4908_19369)">
        <path
          d="M4.13281 32V25.2617H7.25781V7.64453H4.13281V0.90625H20.9102C25.1549 0.90625 28.3581 1.73307 30.5195 3.38672C32.681 5.02734 33.7617 7.48828 33.7617 10.7695C33.7617 13.9466 32.6875 16.3555 30.5391 17.9961C28.3906 19.6237 25.2461 20.4375 21.1055 20.4375H17.1797V25.2617H22.8438V32H4.13281ZM19.7773 7.44922H17.1797V14.4805H19.582C20.9102 14.4805 21.9062 14.2005 22.5703 13.6406C23.2344 13.0807 23.5664 12.1888 23.5664 10.9648C23.5664 9.72786 23.2409 8.83594 22.5898 8.28906C21.9518 7.72917 21.0143 7.44922 19.7773 7.44922ZM35.9102 32V26.1406H38.9375V17.1758H35.9102V11.8438L48.3125 10.6914V26.1406H51.3398V32H35.9102ZM43.625 9.38281C41.9193 9.38281 40.5521 8.97266 39.5234 8.15234C38.5078 7.33203 38 6.24479 38 4.89062C38 3.53646 38.5078 2.46875 39.5234 1.6875C40.5391 0.893229 41.9128 0.496094 43.6445 0.496094C45.4154 0.496094 46.7891 0.880208 47.7656 1.64844C48.7552 2.41667 49.25 3.4974 49.25 4.89062C49.25 6.27083 48.7422 7.36458 47.7266 8.17188C46.724 8.97917 45.3568 9.38281 43.625 9.38281ZM53.6445 32V26.9219L63.0195 15.6523H58.332L57.9414 18.875H53.6445V11.0625H74.6406V16.1406L65.4609 27.4102H70.3438L70.7344 23.4062H75.0312V32H53.6445ZM77.9805 32V26.9219L87.3555 15.6523H82.668L82.2773 18.875H77.9805V11.0625H98.9766V16.1406L89.7969 27.4102H94.6797L95.0703 23.4062H99.3672V32H77.9805ZM108.879 32.3516C107.538 32.3516 106.34 32.0846 105.285 31.5508C104.243 31.0039 103.423 30.2552 102.824 29.3047C102.238 28.3542 101.945 27.2604 101.945 26.0234C101.945 24.0964 102.688 22.5599 104.172 21.4141C105.656 20.2682 107.635 19.6953 110.109 19.6953C110.969 19.6953 111.789 19.7474 112.57 19.8516C113.352 19.9557 114.146 20.1315 114.953 20.3789V19.9492C114.953 18.7643 114.589 17.9049 113.859 17.3711C113.143 16.8372 111.991 16.5703 110.402 16.5703C109.257 16.5703 108.098 16.7201 106.926 17.0195C105.754 17.319 104.686 17.7357 103.723 18.2695L103.195 18.0352V12.332C104.562 11.7982 106.112 11.3945 107.844 11.1211C109.589 10.8477 111.34 10.7109 113.098 10.7109C116.991 10.7109 119.836 11.362 121.633 12.6641C123.43 13.9661 124.328 16.056 124.328 18.9336V26.1406H126.867V32H116.203L115.246 28.9336C114.634 30.0013 113.762 30.8411 112.629 31.4531C111.509 32.0521 110.259 32.3516 108.879 32.3516ZM112.961 27.3516C113.312 27.3516 113.677 27.293 114.055 27.1758C114.445 27.0456 114.745 26.8763 114.953 26.668V23.4062C114.628 23.263 114.296 23.1654 113.957 23.1133C113.632 23.0612 113.326 23.0352 113.039 23.0352C112.375 23.0352 111.815 23.2305 111.359 23.6211C110.917 24.0117 110.695 24.5326 110.695 25.1836C110.695 25.8477 110.904 26.375 111.32 26.7656C111.737 27.1562 112.284 27.3516 112.961 27.3516Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_4908_19369"
          x="0.132812"
          y="0.5"
          width="130.734"
          height="39.8516"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4908_19369" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4908_19369"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};

export default PizzaIcon;
