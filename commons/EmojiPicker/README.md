# EmojiPicker 表情选择器组件

基于 emoji-mart 5.x 的表情选择器组件，支持多种自定义选项。

## 安装依赖

```bash
# 安装必要依赖
yarn add emoji-mart @emoji-mart/react @emoji-mart/data
```

## 使用方法

基本用法：

```tsx
import { useState } from 'react';
import EmojiPicker from 'commons/EmojiPicker';

const YourComponent = () => {
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [selectedEmoji, setSelectedEmoji] = useState<string | null>(null);

  const handleEmojiSelect = (emoji) => {
    setSelectedEmoji(emoji.native); // emoji.native 是表情的原生格式，例如 "😀"
    setIsPickerOpen(false);
  };

  return (
    <div>
      <div style={{ position: 'relative' }}>
        <button onClick={() => setIsPickerOpen(!isPickerOpen)}>
          {selectedEmoji || '😀'} 选择表情
        </button>

        <EmojiPicker
          isOpen={isPickerOpen}
          onClose={() => setIsPickerOpen(false)}
          onEmojiSelect={handleEmojiSelect}
          position="bottom"
          theme="light"
        />
      </div>

      {selectedEmoji && <div>已选择表情: {selectedEmoji}</div>}
    </div>
  );
};
```

## 组件属性

| 属性名           | 类型                                     | 默认值       | 说明                   |
| ---------------- | ---------------------------------------- | ------------ | ---------------------- |
| onEmojiSelect    | `(emoji: any) => void`                   | 必填         | 选择表情后的回调函数   |
| position         | `'top' \| 'bottom' \| 'left' \| 'right'` | `'bottom'`   | 表情选择器弹出位置     |
| theme            | `'light' \| 'dark' \| 'auto'`            | `'light'`    | 主题样式               |
| isOpen           | `boolean`                                | 必填         | 是否显示选择器         |
| onClose          | `() => void`                             | 必填         | 关闭选择器的回调函数   |
| emojiSize        | `number`                                 | `20`         | 表情图标大小           |
| perLine          | `number`                                 | `8`          | 每行显示的表情数量     |
| showSkinTones    | `boolean`                                | `true`       | 是否显示肤色选择器     |
| previewPosition  | `'top' \| 'bottom' \| 'none'`            | `'bottom'`   | 预览区域位置           |
| skinTonePosition | `'preview' \| 'search' \| 'none'`        | `'preview'`  | 肤色选择器位置         |
| maxFrequentRows  | `number`                                 | `4`          | 最近使用表情的最大行数 |
| locale           | `string`                                 | `'zh'`       | 本地化语言             |
| custom           | `any[]`                                  | `[]`         | 自定义表情             |
| customCategories | `Record<string, any>`                    | `undefined`  | 自定义分类             |
| autoFocus        | `boolean`                                | `false`      | 是否自动聚焦           |
| navPosition      | `'top' \| 'bottom' \| 'none'`            | `'top'`      | 导航栏位置             |
| previewEmoji     | `string`                                 | `'point_up'` | 预览区域默认显示的表情 |
| searchPosition   | `'sticky' \| 'static' \| 'none'`         | `'sticky'`   | 搜索框位置             |

## 示例

查看 `EmojiPickerExample.tsx` 了解更多使用示例。

## 自定义表情示例

添加自定义表情：

```tsx
const customEmojis = [
  {
    id: 'github',
    name: 'GitHub',
    emojis: [
      {
        id: 'octocat',
        name: 'Octocat',
        keywords: ['github'],
        skins: [
          {
            src: 'https://github.githubassets.com/images/icons/emoji/octocat.png',
          },
        ],
      },
    ],
  },
];

<EmojiPicker
  // ...其他属性
  custom={customEmojis}
/>;
```

## 注意事项

- 确保父容器设置为 `position: relative`，以便正确定位表情选择器
- 点击外部区域或按ESC键可关闭选择器
- 支持深色和浅色主题，可通过 `theme` 属性设置
- locale默认为'zh'，支持中文界面，可切换为其他语言
